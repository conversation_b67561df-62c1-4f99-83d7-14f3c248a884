import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import { sentryVitePlugin } from "@sentry/vite-plugin";

const forceHashPlugin = {
    name: 'force-hash-plugin',
    generateBundle(options, bundle) {
        for (const key in bundle) {
            const chunk = bundle[key]
            if (chunk.type === 'chunk') {
                // Her build'te farklı bir yorum ekleyerek hash'i değiştiriyoruz.
                chunk.code += `\n// Build timestamp: ${Date.now()}`
            }
        }
    }
}

const path = require("path");

export default ({ mode }) => {
    process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

    return defineConfig({
        server: {
            port: 3000,
        },
        build: {
            sourcemap: true, // Enable sourcemaps for Sentry
            rollupOptions: {
                output: {
                    entryFileNames: `assets/[name]-[hash].js`,
                    chunkFileNames: `assets/[name]-[hash].js`,
                    assetFileNames: `assets/[name]-[hash].[ext]`,
                },
            },
            chunkSizeWarningLimit: 3000, // Çok yüksek bir limit yerine makul bir değer verin
        },
        resolve: {
            alias: {
                "@": path.resolve(__dirname, "./src"),
                vue: "vue/dist/vue.esm-bundler.js"
            },
        },
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: `@use "@/assets/styles/element/index.scss" as *;`,
                },
            },
        },
        plugins: [
            vue(),
            forceHashPlugin,
            VueI18nPlugin({
                include: [path.resolve(__dirname, "@/locales/**")],
                compositionOnly: false,
                runtimeOnly: false,
            }),
            AutoImport({
                resolvers: [ElementPlusResolver()],
            }),
            Components({
                resolvers: [
                    ElementPlusResolver({
                        importStyle: "sass",
                    }),
                ],
            }),
            // Add Sentry Plugin for sourcemaps
            sentryVitePlugin({
                org: "qdelivery",
                project: "qdelivery-dashboard",
                // Auth tokens can be obtained from https://sentry.io/settings/account/api/auth-tokens/
                // and needs the `project:releases` and `org:read` scopes
                authToken: "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",
                telemetry: false, // Disable telemetry collection
            }),
        ],
    });
};
