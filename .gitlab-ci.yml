stages:
  - build
  - deploy
  - refresh

build-dev-cluster:
  stage: build
  image: docker:24.0.6
  services:
    - docker:dind
  before_script:
    - apk add python3 && apk add py3-pip && pip3 install awscli
    - aws --version
    - docker --version
    - aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - docker build --build-arg ENVIRONMENT=dev -f Dockerfile -t $ECR_REGISTRY:frontend-dev-new .
    - docker push $ECR_REGISTRY:frontend-dev-new
  only:
    - dev

build-sand-cluster:
  stage: build
  image: docker:24.0.6
  services:
    - docker:dind
  before_script:
    - apk add python3 && apk add py3-pip && pip3 install awscli
    - aws --version
    - docker --version
    - aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - docker build --build-arg ENVIRONMENT=sand -f Dockerfile -t $ECR_REGISTRY:frontend-sand-new .
    - docker push $ECR_REGISTRY:frontend-sand-new
  only:
    - main

build-prod-cluster:
  stage: build
  image: docker:24.0.6
  services:
    - docker:dind
  before_script:
    - apk add python3 && apk add py3-pip && pip3 install awscli
    - aws --version
    - docker --version
    - aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - docker build --build-arg ENVIRONMENT=prod -f Dockerfile -t $ECR_REGISTRY:frontend-prod-new .
    - docker push $ECR_REGISTRY:frontend-prod-new
  only:
    - main

deploy-dev-cluster:
  image:
    name: reactivedigital/helm-kubectl:6
    entrypoint: ["/config/load-vars.sh"]
  stage: deploy
  tags:
    - eks-dev
  script:
    - helm upgrade --install qdelivery-fe-dev -f deploy/helm/qdelivery-fe/values.yaml deploy/helm/qdelivery-fe -n qd-dev --create-namespace --set env.configmapEnv=dev --set image.tag=frontend-dev-new
    - kubectl rollout restart deployment qdelivery-fe-dev -n qd-dev
  only:
    - dev

deploy-sand-cluster:
  image:
    name: reactivedigital/helm-kubectl:6
    entrypoint: ["/config/load-vars.sh"]
  stage: deploy
  tags:
    - eks-dev
  script:
    - helm upgrade --install qdelivery-fe-sand -f deploy/helm/qdelivery-fe/sand-values.yaml deploy/helm/qdelivery-fe -n qd-sand --create-namespace --set env.configmapEnv=sand --set image.tag=frontend-sand-new
    - kubectl rollout restart deployment qdelivery-fe-sand -n qd-sand
  only:
    - main

deploy-prod-cluster:
  image:
    name: reactivedigital/helm-kubectl:6
    entrypoint: ["/config/load-vars.sh"]
  stage: deploy
  tags:
    - eks-prod
  script:
    - helm upgrade --install qdelivery-fe-prod -f deploy/helm/qdelivery-fe/prod-values.yaml deploy/helm/qdelivery-fe -n qd-prod --create-namespace --set env.configmapEnv=prod --set image.tag=frontend-prod-new
    - kubectl rollout restart deployment qdelivery-fe-prod -n qd-prod
  only:
    - main
  when: manual

refresh-dev-cluster:
  stage: refresh
  image: curlimages/curl:latest
  script:
    - curl -X POST -H "Accept:application/json" -H "Authorization:Bearer $REFRESH_TOKEN" https://api.dev.qdelivery.app/api/development/frontend-post-deploy -v
  only:
    - dev

refresh-sand-cluster:
  stage: refresh
  image: curlimages/curl:latest
  script:
    - curl -X POST -H "Accept:application/json" -H "Authorization:Bearer $REFRESH_TOKEN" https://api.sandbox.qdelivery.app/api/development/frontend-post-deploy -v
  only:
    - main

refresh-prod-cluster:
  stage: refresh
  image: curlimages/curl:latest
  script:
    - curl -X POST -H "Accept:application/json" -H "Authorization:Bearer $REFRESH_TOKEN" https://api.qdelivery.app/api/development/frontend-post-deploy -v
  only:
    - main
  needs:
    - deploy-prod-cluster
