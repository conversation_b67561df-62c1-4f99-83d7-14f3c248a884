{"name": "app", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build-dev": "vite build --mode development", "build-sand": "vite build --mode sand", "build-prod": "vite build --mode prod", "serve": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "format": "prettier .  --write"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.2.1", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/pro-duotone-svg-icons": "^6.2.1", "@fortawesome/pro-light-svg-icons": "^6.2.1", "@fortawesome/pro-regular-svg-icons": "^6.2.1", "@fortawesome/pro-solid-svg-icons": "^6.2.1", "@fortawesome/pro-thin-svg-icons": "^6.2.1", "@fortawesome/sharp-solid-svg-icons": "^6.2.1", "@fortawesome/vue-fontawesome": "^3.0.8", "@googlemaps/js-api-loader": "^1.13.7", "@googlemaps/markerclusterer": "^2.0.8", "@googlemaps/polyline-codec": "^1.0.28", "@gtm-support/vue-gtm": "^2.2.0", "@intlify/unplugin-vue-i18n": "^0.12.2", "@mapbox/mapbox-gl-draw": "^1.5.0", "@sentry/tracing": "^7.1.1", "@sentry/vite-plugin": "^2.22.0", "@sentry/vue": "^7.118.0", "@stripe/stripe-js": "^4.0.0", "@vueuse/core": "^7.7.0", "@watergis/maplibre-gl-terradraw": "^1.3.14", "ag-grid-community": "^28.1.1", "ag-grid-enterprise": "^28.1.3", "ag-grid-vue3": "^28.1.1", "apexcharts": "^3.36.3", "axios": "^0.24.0", "chart.js": "^3.9.1", "dayjs": "^1.10.8", "dotenv": "^16.0.3", "element-plus": "^2.8.0", "env-cmd": "^10.1.0", "esbuild": "^0.20.0", "flatpickr": "^4.6.13", "json-tree-view-vue3": "^0.2.2", "jsonwebtoken": "^9.0.2", "jsrsasign": "^11.1.0", "laravel-echo": "^1.11.3", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "mapbox-gl": "^2.7.0", "maplibre-gl": "^5.6.0", "mitt": "^3.0.0", "node": "^18.20.3", "pusher": "^5.1.1-beta", "pusher-js": "^7.0.6", "qrcode": "^1.5.1", "safe-buffer": "^5.2.1", "splitpanes": "^3.1.1", "terra-draw": "^1.7.0", "vue": "^3.2.31", "vue-class-component": "^8.0.0-rc.1", "vue-currency-input": "^3.0.0", "vue-i18n": "^9.2.2", "vue-router": "^4.2.4", "vue-stripe-js": "^1.0.2", "vue-toastification": "^2.0.0-rc.5", "vue3-carousel": "^0.3.1", "vue3-virtual-scroll-list": "^0.2.1", "vue3-virtual-scroller": "^0.2.3", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.19", "eslint": "^8.10.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^8.5.0", "postcss": "^8.4.38", "prettier": "2.5.0", "sass": "^1.63.6", "sass-loader": "^13.3.2", "tailwindcss": "^3.4.3", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.3.9"}}