# QDelivery Frontend

This project uses

- [Vue3](https://v3.vuejs.org/guide/introduction.html)
- [Vite](https://vitejs.dev/guide/)
- [ElementPlus](https://element-plus.org/en-US/guide/quickstart.html)
- [Tailwind](https://v2.tailwindcss.com/)
- [AgGrid](https://www.ag-grid.com/vue-data-grid/getting-started/)
- [FontAwesome](https://github.com/FortAwesome/vue-fontawesome)
- [Mitt](https://www.npmjs.com/package/mitt)
- [DayJs](https://www.npmjs.com/package/dayjs)
- [Vuei18n](https://vue-i18n.intlify.dev/)
- [Sentry](https://docs.sentry.io/platforms/javascript/guides/vue/)

## Sentry Integration

This project uses Sentry for error tracking and performance monitoring. Sourcemaps are automatically uploaded to Sentry during the build process to provide better error stack traces.

### Setting up Sentry Auth Token

To enable sourcemaps upload to Sentry, you need to set the `SENTRY_AUTH_TOKEN` environment variable in your `.env` file:

1. Go to [Sentry Auth Tokens](https://sentry.io/settings/account/api/auth-tokens/)
2. Create a new token with the `project:releases` and `org:read` scopes
3. Add the token to your `.env` file:

```
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here
```

### Building with Sourcemaps

Sourcemaps are automatically generated and uploaded to Sentry when you run any of the build commands:

```
npm run build
npm run build-dev
npm run build-sand
npm run build-prod
```
