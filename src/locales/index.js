import en from "./en";
import tr from "./tr";
import az from "./az"
import de from "./de"
import pl from "./pl"
import es from "./es"
import fr from "./fr"

const locales = {en, tr, az,de,pl,es,fr};


function findMissingKeys(jsonList) {
    let allKeys = {};
    let missingKeys = {};

    for (let i = 0; i < jsonList.length; i++) {
        let keys = Object.keys(jsonList[i]);

        for (let j = 0; j < keys.length; j++) {
            let key = keys[j];

            if (!allKeys[key]) {
                allKeys[key] = true;
            }
        }
    }

    for (let i = 0; i < jsonList.length; i++) {
        let keys = Object.keys(jsonList[i]);

        for (let j = 0; j < Object.keys(allKeys).length; j++) {
            let key = Object.keys(allKeys)[j];

            if (!keys.includes(key)) {
                if (!missingKeys[key]) {
                    missingKeys[key] = true;
                }
            }
        }
    }

    return Object.keys(missingKeys);
}


// let jsonList = [en, tr, az];
// let missingKeys = findMissingKeys(jsonList);
//
// console.log("missingKeys: ", JSON.stringify(missingKeys))


export default locales;
