class Connect {
    constructor(name, title, icon, notificationCount, closable, permisson) {
        this.name = name || "";
        this.title = title || this.name;
        this.icon = icon || "";
        this.notificationCount = notificationCount || 0
        this.data = {};
        this.closable = closable || false;
        this.permisson = permisson
    }

    get isClosable() {
        return this.closable;
    }

    setData(data) {
        this.data = data;
    }
}

export default Connect;
