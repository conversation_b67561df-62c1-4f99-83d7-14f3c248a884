import "./socket"
import {createApp} from "vue";
import App from "./App.vue";
import router from "./router/index";
import store from "./store/index";
import { createGtm } from '@gtm-support/vue-gtm';
import {
    GlobalComponents,
    FontAwesome,
    ElementPlus,
    dayjs,
    axios,
    mitt,
    vuei18n,
    helpers,
    Toast,
} from "./plugins";
import {LicenseManager} from 'ag-grid-enterprise'
import * as Sentry from "@sentry/vue";
import {BrowserTracing} from "@sentry/tracing";

const app = createApp(App);


const toastOptions = {
    hideProgressBar: true,
}

LicenseManager.setLicenseKey(import.meta.env.VITE_AG_GRID_KEY)

JSON.parse(import.meta.env.VITE_SENTRY_ENABLED) && Sentry.init({
    app,
    dsn: import.meta.env.VITE_SENTRY_DSN,
    environment: import.meta.env.MODE,
    sendDefaultPii: true,
    integrations: [
        Sentry.browserTracingIntegration({ router })
    ],
    tracesSampleRate: 0.1,
    tracePropagationTargets: ["localhost", "https://api.qdelivery.app/api", "https://api.dev.qdelivery.app/api"]
});

app.use(router);
app.use(
  createGtm({
      id: "GTM-KP4V4Q9S",
      vueRouter: router
  })
)
app.use(store);
app.use(FontAwesome);
app.use(ElementPlus);
app.use(GlobalComponents);
app.use(dayjs);
app.use(axios);
app.use(mitt);
app.use(vuei18n);
app.use(Toast,toastOptions)
app.use(helpers)
app.mount("#app");
