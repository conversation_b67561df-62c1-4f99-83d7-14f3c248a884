import axios from "axios";
import Echo from "laravel-echo";
import Pusher from "pusher-js";

window.axios = axios;
window.Pusher = Pusher;


// window.Echo = import.meta.env.VITE_APP_ENV === 'dev'
//     ? new Echo({
//         broadcaster: "pusher",
//         key: import.meta.env.VITE_PUSHER_APP_KEY,
//         wsHost: "soketi.dev.qdelivery.app",
//         wsPort: 443,
//         wssPort: 443,
//         forceTLS: true,
//         encrypted: true,
//         disableStats: true,
//         enabledTransports: ['ws', 'wss'],
//         cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
//         authEndpoint: "/broadcasting/auth",
//         authorizer: (channel, options) => {
//             return {
//                 authorize: (socketId, callback) => {
//                     axios
//                         .post(
//                             `${import.meta.env.VITE_APP_BASE_API}broadcasting/auth`,
//                             {
//                                 socket_id: socketId,
//                                 channel_name: channel.name,
//                             },
//                             {
//                                 headers: {
//                                     Authorization: `Bearer ${localStorage.getItem("authToken")}`,
//                                     "X-Requested-With": "XMLHttpRequest",
//                                 },
//                             }
//                         )
//                         .then((response) => {
//                             callback(false, response.data);
//                         })
//                         .catch((error) => {
//                             callback(true, error);
//                         });
//                 },
//             };
//         },
//     })
//     : new Echo({
//         broadcaster: "pusher",
//         key: import.meta.env.VITE_PUSHER_APP_KEY,
//         cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
//         forceTLS: true,
//         authEndpoint: "/broadcasting/auth",
//         authorizer: (channel, options) => {
//             return {
//                 authorize: (socketId, callback) => {
//                     axios
//                         .post(
//                             `${import.meta.env.VITE_APP_BASE_API}broadcasting/auth`,
//                             {
//                                 socket_id: socketId,
//                                 channel_name: channel.name,
//                             },
//                             {
//                                 headers: {
//                                     Authorization: `Bearer ${localStorage.getItem("authToken")}`,
//                                     "X-Requested-With": "XMLHttpRequest",
//                                 },
//                             }
//                         )
//                         .then((response) => {
//                             callback(false, response.data);
//                         })
//                         .catch((error) => {
//                             callback(true, error);
//                         });
//                 },
//             };
//         },
//     });

window.Echo = new Echo({
    broadcaster: "pusher",
    key: import.meta.env.VITE_PUSHER_APP_KEY,
    cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
    forceTLS: true,
    authEndpoint: "/broadcasting/auth",
    authorizer: (channel, options) => {
        return {
            authorize: (socketId, callback) => {
                axios
                    .post(
                        `${import.meta.env.VITE_APP_BASE_API}broadcasting/auth`,
                        {
                            socket_id: socketId,
                            channel_name: channel.name,
                        },
                        {
                            headers: {
                                Authorization: `Bearer ${localStorage.getItem("authToken")}`,
                                "X-Requested-With": "XMLHttpRequest",
                            },
                        }
                    )
                    .then((response) => {
                        callback(false, response.data);
                    })
                    .catch((error) => {
                        callback(true, error);
                    });
            },
        };
    },
});

export const pusher = new Pusher(import.meta.env.VITE_PUSHER_APP_KEY, {
        cluster: 'eu',
        forceTLS: true,
    });



