<template>
  <div class="sm:rounded-md sm:overflow-hidden">
    <div class="py-6 px-4 space-y-6 sm:p-6">
      <div v-if="$slots.title || $slots.subTitle">
        <h3
          v-if="$slots.title"
          class="text-lg leading-6 font-medium text-gray-900"
        >
          <slot name="title"></slot>
        </h3>
        <p v-if="$slots.subTitle" class="mt-1 text-sm text-gray-500">
          <slot name="subTitle"></slot>
        </p>
      </div>

      <div class="grid grid-cols-6 gap-4">
        <slot></slot>
      </div>
    </div>
    <div v-if="$slots.actions" class="px-4 py-3 bg-gray-50 text-right sm:px-6">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "FormCard",
};
</script>
