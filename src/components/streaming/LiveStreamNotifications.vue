<script setup>
import {onMounted, ref, inject, onActivated, onDeactivated, computed} from "vue";

import StatusMessage from "@/components/streaming/notificaiton/StatusMessage.vue";
import {useStore} from "vuex";
import TaskDetail from "@/components/deliveries/TaskDetail.vue";
const emitter = inject("emitter")
const api = inject("api")
const dataSource = ref([])
const taskDetailDrawerVisible = ref(false)
const selectedTaskId = ref(null)

const {getters} = useStore();
const companyChannel = computed(
    () => "company." + getters["auth/activeCompany"].id
);
const openTaskDetail = () => {
  taskDetailDrawerVisible.value = true
}
const closeTaskDetail = () => {
  selectedTaskId.value=null
  taskDetailDrawerVisible.value = false
}

const selectTask = (item) =>{
  selectedTaskId.value = item.task_id
  openTaskDetail()
}


onMounted(() => {
  api("customer/task-status-logs")
      .then((response) => dataSource.value = response.data)
})
onActivated(()=>{
  Echo.private(companyChannel.value).listen(".task.status.updated",(data) => {
    dataSource.value.unshift(data)
  })
})
onDeactivated(()=>{
  Echo.private(companyChannel.value).stopListening(".task.status.updated")

})

</script>

<template>

  <div class="w-full h-full overflow-y-auto text-slate-700 bg-white">
    <TransitionGroup name="list" tag="ul">
          <StatusMessage v-for="item in dataSource" :key="item" :status="item.status_category_slug" :item="item"/>
    </TransitionGroup>
  </div>
  <el-drawer
      v-model="taskDetailDrawerVisible"
      class="customized-drawer customized-drawer--big"
      title="Delivery Detail"
      append-to-body
      destroy-on-close
  >
    <TaskDetail :taskId="selectedTaskId"/>
  </el-drawer>
</template>


