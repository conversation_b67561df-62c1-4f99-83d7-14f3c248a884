<script setup>
import {inject, onMounted, ref} from "vue";
import debounce from "lodash.debounce";
const emitter = inject("emitter")
const api = inject("api")
const total_tasks_distance = ref(0)


const getData = () => {
  api("customer/monitor/distance/total-tasks")
      .then((res) => {
        total_tasks_distance.value = res.total_tasks_distance
      })
}

onMounted(() => {
  getData()
})

const debounceGetData = debounce(getData,3000)
emitter.on(".task.updated", () => {
  debounceGetData()
})
emitter.on(".task.created", () => {
  debounceGetData()
})
</script>
<template>
  <div
      class="relative h-48 flex flex-col justify-between bg-slate-50 border border-slate-300 p-2.5 text-slate-700">
    <div class="text-2xl font-bold pt-1">{{ $t('Average Delivery / Distance') }}</div>
    <div class="font-bold text-lime-600 flex items-end justify-center">
      <span style="font-size: 60px">{{ (total_tasks_distance / 1000).toFixed(2) }}</span>
      <span class="text-2xl mb-4">Km</span>
    </div>
    <div class="text-sm"></div>
    <div class="absolute -bottom-4 right-4 opacity-10" style="font-size: 90px">
      <FontAwesomeIcon icon="diamond-turn-right"/>
    </div>
  </div>
</template>
