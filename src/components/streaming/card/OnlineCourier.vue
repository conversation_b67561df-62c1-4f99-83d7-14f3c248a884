<script setup>
import {inject,onMounted, ref} from "vue";
import {useStore} from "vuex";
import debounce from "lodash.debounce";

const {getters} = useStore();

const emitter = inject("emitter")
const api = inject("api")
const emit = defineEmits(["setTotalCouriersCount"])
const loader = ref()
const online_couriers_count = ref(0)
const total_couriers_count = ref(0)
const fetching = ref(false)

const getOnlineCouriersCount = () => {
  return new Promise((resolve, reject) => {
    fetching.value = true
    api("customer/monitor/count/online-couriers")
        .then((res) => {
          online_couriers_count.value = res.online_couriers_count
          resolve()
          fetching.value = false
        })
        .catch((err) => reject(err))
  })

}
const getTotalCourierCount = () => {
  return new Promise((resolve, reject) => {
    api("customer/monitor/count/total-couriers")
        .then((res) => {
          total_couriers_count.value = res.total_couriers_count
          emit("setTotalCouriersCount", res.total_couriers_count)
          resolve()
        }).catch((err) => reject(err))
  })
}
onMounted(() => {

  Promise.all([getOnlineCouriersCount(), getTotalCourierCount()])

})
const debounceOnlineCouriersCount = debounce(getOnlineCouriersCount,3000)
const debounceTotalCouriersCount = debounce(getTotalCourierCount,3000)
emitter.on(".courier.location.updated",()=>{
  debounceOnlineCouriersCount()
})
emitter.on(".courier.created",()=>debounceTotalCouriersCount())


</script>
<template>
  <div
      class="relative h-48 flex flex-col justify-between bg-slate-50 border border-slate-300 p-2.5 text-slate-700">
    <div class="text-2xl font-bold pt-1">{{ $t('Online Driver') }}</div>
    <div class="text-center font-bold text-lime-600"
         style="font-size: 60px">{{ online_couriers_count.toString() }}
    </div>
    <div class="text-sm">{{ total_couriers_count.toString() }} {{ $t('Total Driver') }}</div>
    <div class="absolute -bottom-4 right-4 opacity-10" style="font-size: 90px">
      <FontAwesomeIcon icon="people-carry"/>
    </div>
  </div>
</template>
