<script setup>
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {inject, ref} from "vue";

const api = inject("api")
const loader = ref()

const props = defineProps({
  avarageDeliveryCourier: {type: Number}
})


</script>
<template>
  <div
      className="relative  h-48 flex flex-col justify-between bg-slate-50 border border-slate-300 p-2.5 text-slate-700">
    <LoadingBlock ref="loader"/>
    <div className="text-2xl font-bold pt-1">{{ $t('Average Delivery / Driver') }}</div>
    <div className="text-center font-bold text-lime-600"
         style="font-size: 60px">{{ avarageDeliveryCourier }}
    </div>
    <div className="text-sm"></div>
    <div className="absolute -bottom-4 right-4 opacity-10" style="font-size: 90px">
      <FontAwesomeIcon icon="calendar"/>
    </div>
  </div>
</template>
