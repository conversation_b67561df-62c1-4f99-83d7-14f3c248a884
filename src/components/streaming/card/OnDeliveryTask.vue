<script setup>
import {inject, onMounted, ref} from "vue";
import debounce from "lodash.debounce";
const emitter = inject("emitter")
const api = inject("api")
const emit = defineEmits(["setTotalTasksCount"])
const loader = ref()
const on_delivery_task_count = ref(0)
const total_task_count = ref(0)


const getCountTaskGroup = () => {
  return api("customer/monitor/count/task-groups")
      .then((res) => {
        res.forEach((x) => {
          if (x.status_category_slug === "on_delivery") {
            on_delivery_task_count.value = x.total
          }
        })
      })

}
const getTotalTaskCount = () => {
  return api("customer/monitor/count/total-tasks")
      .then((res) => {
        total_task_count.value = res.total_tasks_count
        emit("setTotalTasksCount",res.total_tasks_count)
      })
}
const getData = () => {

  Promise.all([getCountTaskGroup(), getTotalTaskCount()])

}
onMounted(() => {
  getData()
})


const debounceCountTaskGroup = debounce(getCountTaskGroup,3000)
const debounceTotalTaskCount = debounce(getTotalTaskCount,3000)
emitter.on(".task.updated",()=>{
  debounceCountTaskGroup()
})
emitter.on(".task.created",()=>{
  debounceTotalTaskCount()
})


</script>
<template>
  <div
      class="relative h-48 flex flex-col justify-between bg-slate-50 border border-slate-300 p-2.5 text-slate-700">
<!--    <LoadingBlock ref="loader"/>-->
    <div class="text-2xl font-bold pt-1">{{ $t('On Delivery Task') }}</div>
    <div class="text-center font-bold text-lime-600"
         style="font-size: 60px">{{ on_delivery_task_count.toString() }}
    </div>
    <div class="text-sm">{{ total_task_count.toString()}} {{ $t('Total Daily Task') }}</div>
    <div class="absolute -bottom-4 right-4 opacity-10" style="font-size: 90px">
      <FontAwesomeIcon icon="shopping-bag"/>
    </div>
  </div>
</template>
