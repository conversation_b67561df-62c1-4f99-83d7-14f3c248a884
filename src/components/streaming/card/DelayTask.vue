<script setup>
import {inject, onMounted, ref} from "vue";
import debounce from "lodash.debounce";

const emitter = inject("emitter")
const api = inject("api")
const loader = ref()
const delayed_tasks_count = ref(0)

const getData = () => {
  api("customer/monitor/count/delay-tasks")
      .then((res) => {
        delayed_tasks_count.value = res.delayed_tasks_count
      })
}
onMounted(() => {
  getData()
})

const debounceGetData = debounce(getData,3000)
emitter.on(".task.updated", () => {
  debounceGetData()
})
</script>
<template>
  <div
      class="relative h-48 flex flex-col justify-between bg-slate-50 border border-slate-300 p-2.5 text-slate-700">
<!--    <LoadingBlock ref="loader"/>-->
    <div class="text-2xl font-bold pt-1">{{ $t('Delay Task') }}</div>
    <div class="font-bold text-red-600 flex items-end justify-center">
      <span style="font-size: 60px">{{ delayed_tasks_count }}</span>
    </div>
    <div class="text-sm"></div>
    <div class="absolute -bottom-4 right-4 opacity-10" style="font-size: 90px">
      <FontAwesomeIcon icon="hourglass-end"/>
    </div>
  </div>
</template>
