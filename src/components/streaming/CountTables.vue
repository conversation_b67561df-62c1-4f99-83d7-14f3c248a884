<script setup>
import {computed, ref} from 'vue';
import OnlineCourier from "@/components/streaming/card/OnlineCourier.vue";
import OnDeliveryTask from "@/components/streaming/card/OnDeliveryTask.vue";
import AverageDeliveryCourier from "@/components/streaming/card/AverageDeliveryCourier.vue";
import DelayTask from "@/components/streaming/card/DelayTask.vue";
import AverageDeliveryDistance from "@/components/streaming/card/AverageDeliveryDistance.vue";


const total_delivery_count = ref(0)
const total_couriers_count = ref(0)
const avarageDeliveryCourier = computed(() => {
  if (total_couriers_count.value !== 0 && total_delivery_count.value !== 0) {
    return (total_couriers_count.value / total_delivery_count.value ).toFixed(2)
  } else {
    return 0
  }
})

const setTotalTasksCount = (param) => {
  total_delivery_count.value = param
}
const setTotalCouriersCount = (param) => {
  total_couriers_count.value = param
}




</script>
<template>
  <div class="w-full h-full bg-white">
    <div class="flex flex-col p-2.5">
      <div class="h-full grid gap-16 grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 md:container md:mx-auto">
        <div class="col-span-1">
          <OnlineCourier @setTotalCouriersCount="setTotalCouriersCount"/>
        </div>
        <div class="col-span-1">
          <OnDeliveryTask @setTotalTasksCount="setTotalTasksCount"/>
        </div>
        <div class="col-span-1">
          <AverageDeliveryCourier :avarageDeliveryCourier="avarageDeliveryCourier"/>
        </div>
        <div class="col-span-1">
          <DelayTask/>
        </div>
        <div class="col-span-1">
          <AverageDeliveryDistance/>
        </div>
      </div>
    </div>
  </div>
</template>
