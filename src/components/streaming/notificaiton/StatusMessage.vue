<script setup>
import {computed} from "vue";
import dayjs from "dayjs";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const props = defineProps({
  item: {type: Object},
})
const message = computed(() => {
  if (props.item.type_slug === 'assignable') {
    switch (props.item.status_category_slug) {
      case "created":
        return t("New Delivery")
      case "pool":
        return t("A New Delivery has been thrown into the Pool");
      case "assigned":
        return t("Assigned to", {message: props.item.courier.name});
      case "in_progress":
        return t("In progress by", {message: props.item.courier.name});
      case "on_delivery":
        return t("On delivery by", {message: props.item.courier.name});
      case "completed":
        return t("Completed by", {message: props.item.courier.name});
      case "cancelled":
        return t("Cancelled by", {message: props.item.courier.name});
      case "failed":
        return t("Task Failed");
    }
  } else if (props.item.type_slug === 'pool') {
    return t("A New Delivery has been thrown into the Pool");
  }
});

</script>

<template>
  <li class="px-2 py-2.5 flex flex-col ">
    <div class="flex items-center text-slate-700">
      <div class="mr-2">
        {{ dayjs(item.updated_at).format("HH:mm") }}
      </div>
      <div class="flex items-center text-md">
        <span class="cursor-pointer ml-1" @click="selectTask(item)">
            {{ " #" + item.task.tracking_code }}
        </span>
      </div>
    </div>
    <div class="text-md">
      {{ message }}
    </div>
  </li>
</template>
