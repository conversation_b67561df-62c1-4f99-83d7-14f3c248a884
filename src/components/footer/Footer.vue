<template>
  <footer class="px-4 py-2 bg-indigo-600 relative z-10">
    <div class="items-center text-white flex justify-between">
      <div>QDelivery &copy; 2021</div>
      <div>
        <ToggleOperationLayout />
      </div>
    </div>
  </footer>
</template>

<script>
import ToggleOperationLayout from "@/components/ui/ToggleOperationLayout.vue";

export default {
  name: "MainFooter",
  components: {
    ToggleOperationLayout,
  },
};
</script>
