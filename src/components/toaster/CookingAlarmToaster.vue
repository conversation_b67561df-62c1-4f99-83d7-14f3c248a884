<template>
  <div class="inline-block">
    <div>{{ message }}</div>
    <span
        class="underline text-xs cursor-pointer font-bold mt-2"
        @click.stop="onClick"
    >
      Siparişe Git
    </span>
  </div>
</template>

<script>

export default {

  emits: ["onClick", "close-toast"],
  setup(props, {attrs,emit}) {


    const onClick = () => {
      emit("onClick")
      emit("close-toast")
    }


    let message = attrs.message


    return {
      message,
      onClick
    }
  }
};
</script>
