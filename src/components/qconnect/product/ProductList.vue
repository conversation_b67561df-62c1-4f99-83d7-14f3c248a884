<script>
import EditButton from "@/renderers/EditButton.vue";

export default {
  components: {
    EditButton
  }
}
</script>
<script setup>
import {computed, inject, onMounted, ref} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import formatter from "@/class/formatter";
import {useStore} from "vuex";
import Formatter from "@/class/formatter";

const {t} = useI18n()
const {getters} = useStore();

const emits = defineEmits(["openProductEditDrawer"])
const props = defineProps({
  products: {type: Array, default: () => []}
})

const gridApi = ref(null);

const columnDefs = [
  {
    field: "id",
    headerName: "Id",
    filter: 'agTextColumnFilter',
  },
  {
    field: "name",
    headerName: t('Name'),
    filter: 'agTextColumnFilter',
  },
  {
    field: "amount",
    headerName: t("Amount"),
    filter: 'agTextColumnFilter',
    valueFormatter: (props) => Formatter.currency(Number(props.value), props.data.currency_unit)
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "EditButton",
    cellRendererParams: {
      clicked: (params, data) => {
        emits("openProductEditDrawer", data)
      },
    },
  },
]

</script>

<template>
  <DataGrid
      v-model="gridApi"
      :dataSource="products"
      :columns="columnDefs"
  />
</template>
