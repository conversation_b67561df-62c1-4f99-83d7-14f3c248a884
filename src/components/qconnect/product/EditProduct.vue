<script setup>
import {useI18n} from "vue-i18n";
import {inject, onMounted, reactive, ref} from "vue";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()

const api = inject("api")

const emit = defineEmits(["close"])
const props = defineProps({
  product: {
    type: Object, default: () => {
    }
  }
})

const loader = ref()
const dialogVisible = ref(false)

const form = reactive({
  name: "",
  description: "",
  amount: 0,
  currency_unit: "TRY",
})

onMounted(() => {
  setValues()
})

const setValues = () => {
  form.name = props.product.name
  form.description = props.product.description
  form.amount = props.product.amount
}

const canSave = () => {
  return new Promise((resolve, reject) => {
    if (!form.name) {
      reject(t("Name is required"))
    }

    if (!form.amount) {
      reject(t("Product Unit Price must be different from zero"))
    }

    resolve()

  })
}

const onSave = () => {

  // TODO Burada default TRY yazılmış değişebilir.
  loader.value.show()
  api.patch(`customer/company-products/${props.product.id}`, {
    name: form.name,
    description: form.description,
    amount: form.amount,
    currency_unit: "TRY",
  })
      .then(() => emit('close', true))
      .finally(() => {
        loader.value.hide()
      })
}


const onSubmit = () => {
  canSave()
      .then(() => onSave())
      .catch((err) => toast.error(err))
}

const onRemove = () => {
  dialogVisible.value = true
}

const remove = () => {
  dialogVisible.value = false
  loader.value.show()
  api.delete(`customer/company-products/${props.product.id}`)
      .then(() => emit('close', true))
      .finally(() => {
        loader.value.hide()
      })
}

</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item
                  :label="t('Product Name')"
              >
                <el-input
                    v-model="form.name"
                    :placeholder="t('Product Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Product Description')"
              >
                <el-input
                    v-model="form.description"
                    :placeholder="t('Product Description')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Currency Unit')"
              >
                <el-input
                    v-model="form.currency_unit"
                    :placeholder="t('Currency Unit')"
                    :disabled="true"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Product Unit Price')"
              >
                <el-input
                    v-model="form.amount"
                    type="number"
                    :placeholder="t('Product Unit Price')"
                >
                  <template #suffix>
                    <div class="center border-l border-slate-300 pl-2 font-bold text-slate-700">
                      <FontAwesomeIcon icon="turkish-lira-sign"/>
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-between absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >

        <el-button
            type="danger"
            size="small"
            @click="onRemove"
        >
          <template #icon>
            <FontAwesomeIcon icon="trash"/>
          </template>

          <span class="hidden lg:inline-block ml-2"> {{ t('Delete') }}</span>
        </el-button>

        <CustomButton
            :title="t('Save')"
            size="small"
            @click="onSubmit"
        >
          <div class="text-white text-sm">
            <FontAwesomeIcon icon="check-square"/>
          </div>
        </CustomButton>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" width="30%" center>
      <div class="text-center text-lg text-slate-700 font-bold">
        {{ t('Are you sure you want to delete the product') }}
      </div>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t('Cancel') }}</el-button>
        <el-button @click="remove" type="danger">
          {{ t('Delete') }}
        </el-button>
      </span>
      </template>
    </el-dialog>
  </div>
</template>
