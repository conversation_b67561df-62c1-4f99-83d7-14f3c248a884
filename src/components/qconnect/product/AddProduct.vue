<script setup>
import {useI18n} from "vue-i18n";
import { computed, inject, reactive, ref,onMounted,watch } from "vue";
import {useToast} from "vue-toastification";
import { useStore } from "vuex";

const toast = useToast()
const {t} = useI18n()

const api = inject("api")

const emit = defineEmits(["close"])

const loader = ref()


const form = reactive({
  name: "",
  description: "",
  amount: 0,
  currency_unit: null,
})
const { dispatch, getters } = useStore();
const me = computed(() => getters["auth/me"])

const canSave = () => {
  return new Promise((resolve, reject) => {
    if (!form.name) {
      reject(t("Name is required"))
    }

    if (!form.amount) {
      reject(t("Product Unit Price must be different from zero"))
    }

    resolve()

  })
}
onMounted(()=>{
  form.currency_unit = me.value.active_company.currency_unit
})

watch(() => me.value.active_company.currency_unit, () => {
  form.currency_unit = me.value.active_company.currency_unit
  console.log( 'currency_unit', form.currency_unit)
})

const onSave = () => {
  // TODO Burada default TRY yazılmış değişebilir.
  loader.value.show();

  let body = {
    name: form.name,
    description: form.description,
    amount: form.amount,
    currency_unit: form.currency_unit,
  };

  const prepareData = (data) => {
    const filteredData = {};
    for (const key in data) {
      if (data[key] !== null && data[key] !== '') {
        filteredData[key] = data[key];
      }
    }
    return filteredData;
  };

  body = prepareData(body);

  api.post("customer/company-products", body)
    .then(() => emit('close', true))
    .finally(() => {
      loader.value.hide();
    });
};


const onSubmit = () => {
  canSave()
      .then(() => onSave())
      .catch((err) => toast.error(err))
}

</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item
                  :label="t('Product Name')"
              >
                <el-input
                    v-model="form.name"
                    :placeholder="t('Product Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Product Description')"
              >
                <el-input
                    v-model="form.description"
                    :placeholder="t('Product Description')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Currency Unit')"
              >
                <el-input
                    v-model="form.currency_unit"
                    :placeholder="t('Currency Unit')"
                    :disabled="true"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Product Unit Price')"
              >
                <el-input
                    v-model="form.amount"
                    type="number"
                    :placeholder="t('Product Unit Price')"
                >
                  <template #suffix>
                    <div class="center border-l border-slate-300 pl-2 font-bold text-slate-700">
                      {{ form.currency_unit }}
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <CustomButton
            :title="t('Add')"
            size="small"
            @click="onSubmit"
        >
          <div class="text-white text-sm">
            <FontAwesomeIcon icon="plus"/>
          </div>
        </CustomButton>
      </div>
    </div>
  </div>
</template>
