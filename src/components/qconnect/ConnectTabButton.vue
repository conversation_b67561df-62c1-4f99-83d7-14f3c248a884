<script>
export default {
  name: "ConnectTabButton",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tab: {
      type: Object,
      default: () => {
      },
    },
  },
  emits: ["click"],
  setup() {
  },
};
</script>

<template>
  <div
      @click="$emit('click')"
      class="h-9 w-9 md:w-12 md:h-12 flex items-center justify-center cursor-pointer border-slate-300 border-r"
      :class="[
              active
                 ?' text-slate-700'
                 : 'bg-slate-700 text-white'
            ]"
  >
    <FontAwesomeIcon :icon="tab.icon" fixed-width/>
  </div>
</template>
