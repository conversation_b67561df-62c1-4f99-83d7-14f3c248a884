<script setup>
import dayjs from "dayjs";
import CarrierInformation from "@/components/qconnect/orderDetail/components/CarrierInformation.vue";
import Actions from "@/components/qconnect/orderDetail/components/Actions.vue";
import Detail from "@/components/qconnect/orderDetail/components/Detail.vue";
import OrderPrintDialog from "@/components/qconnect/orderDetail/components/OrderPrintDialog.vue";
import GeneratorQRCode from "@/components/ui/GeneratorQRCode.vue";
import formatter from "@/class/formatter";
import {computed, inject, ref} from "vue";
import OrderStatus from "@/components/qconnect/orders/components/OrderStatus.vue";
import {useStore} from "vuex";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import PaymentBadge from "@/components/ui/PaymentBadge.vue";
import TaskDetailMap from "@/components/deliveries/TaskDetailMap.vue";


const emitter = inject("emitter");

const {getters} = useStore()

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  },
})
const mapVisible = ref(false)
const phoneNumber = computed(() => {
  const qconnect = props.order.meta?.qconnect;
  if (!qconnect) return null;

  const providerClientData = qconnect.providerClientData;
  return providerClientData?.clientPhoneNumber || qconnect.contactPhoneNumber || qconnect.callCenterPhone;
});


const _task = computed(() => {
  let location = {
    origin_lat: props.order.origin_lat,
    origin_lng: props.order.origin_lng,
    destination_lat: props.order.destination_lat,
    destination_lng: props.order.destination_lng,
    starts_at: props.order.starts_at,
    ends_at: props.order.ends_at,
    origin_address: props.order.origin_address,
    destination_address: props.order.destination_address,
    origin_name: props.order.origin_name,
    destination_name: props.order.destination_name,
    task_provider_courier: props.order.task_provider_courier,
    path: props.order.path,
  }
  return location
})



const totalQuantity = computed(() => {

  if (props.order.channel === "qconnect") {
    return props.order.contents.length > 1
        ? props.order.contents.map(a => a.quantity).reduce((a, b) => Math.round(Number(a)) + Math.round(Number(b)))
        : props.order.contents.length === 1
            ? Math.round(Number(props.order.contents[0].quantity))
            : 0
  } else {
    return props.order.items.length > 1
        ? props.order.items.map(a => a.quantity).reduce((a, b) => Math.round(Number(a)) + Math.round(Number(b)))
        : props.order.items.length === 1
            ? Math.round(Number(props.order.items[0].quantity))
            : 0
  }
})
const formattedAmount = computed(() => {
  return {
    amount: formatter.currency(props.order.amount),

    discount: props.order.discounted_amount ? formatter.currency(props.order.discounted_amount) : null

  }
})
const companyProductType = computed(() => getters["auth/me"].active_company.company_product_type);

const qrcodeDialogVisible = ref(false)
const printDialogVisible = ref(false)
const courierListDialogVisible = ref(false)
const integrationId = ref(null)

const openCourierListDialog = () => {
  courierListDialogVisible.value = true
}

const closeCourierListDialog = () => {
  courierListDialogVisible.value = false
}


const openPrintDialog = () => {
  printDialogVisible.value = true
}

const onSelectMergedTask = (id) => {
  emitter.emit("openSelectedMergedOrder", id)
}

const openMap = () => {
  mapVisible.value = true
}

</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <transition
        enter-active-class="ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="ease-in duration-200"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
    >
      <div
          v-if="order.loading"
          class="absolute left-0 top-0 z-30 w-full h-full flex items-center justify-center bg-white bg-opacity-75"
      >
        <img src="@/assets/images/loading.svg" class="h-3" alt/>
      </div>
    </transition>

    <div class="w-full flex items-center justify-between border-b border-slate-300 bg-slate-50 px-4">
      <div class=" flex  space-x-4 items-center text-slate-700 py-2">
        <div class="font-bold">
          {{ $t("Order Detail") }}
        </div>
        <FontAwesomeIcon icon="chevron-right"/>
        <div v-if="order.provider?.icon_url">
          <img :src="order.provider.icon_url" class="h-6 w-6">
        </div>
        <div v-if="order.provider?.icon_url">
          <FontAwesomeIcon icon="chevron-right"/>
        </div>
        <div class="font-bold text-lg">
          #{{ order.integration_id }}
        </div>

        <FontAwesomeIcon icon="chevron-right"/>

        <OrderStatus
            :preConfirmed="order.company_pre_approved_at"
            :confirmed="order.company_approved_at"
            :statusCategorySlug="order.status_category_slug"
        />

        <div
            v-if="order.merged_task_id && Number(order.integration_id) !== order.merged_task_id "
            class="py-1 px-1.5 text-white rounded text-xs bg-slate-700 cursor-pointer flex items-center"
            @click="onSelectMergedTask(order.merged_task_id)"
        >
          <FontAwesomeIcon icon="arrows-split-up-and-left"/>
          <div class="ml-1">{{ $t("Merged") }}</div>
        </div>


<!--          <div @click="openPrintDialog"-->
<!--               class="font-bold bg-white py-1 px-2 flex items-center justify-center border border-slate-300  cursor-pointer rounded">-->
<!--            <FontAwesomeIcon  icon="print"/> <span style="font-size: 11px ; margin-left: 4px; ">{{ $t('Order Receipt') }}</span>-->
<!--          </div>-->
        <el-button
          size="small"
          class="ml-2"
          @click="openPrintDialog"
        >
          <template #icon>
            <FontAwesomeIcon
              icon="print"

              fixed-width class="mr-0 sm:mr-2"
            />
          </template>
          {{ $t('Order Receipt') }}
        </el-button>

      </div>
      <div v-if="!['completed','cancelled','failed'].includes(order.status_category_slug)">
        <Actions :order="order"/>
      </div>
    </div>
    <div class="h-full w-full grid grid-cols-1 md:grid-cols-2 bg-white overflow-hidden">
      <div class="col-span-1 border-slate-300 border-r text-slate-700 flex flex-col overflow-auto select-text">
        <div class="px-4">

          <div class="pt-4 font-bold text-slate-700">
            {{ $t("Customer Information") }}
          </div>
          <div class="flex items-center pt-7 justify-between">
            <div class="flex items-center">
              <FontAwesomeIcon icon="user"/>
              <div class="ml-2 font-bold">
                {{ order?.destination_name }}
              </div>
            </div>
            <div class="flex items-center">
              <FontAwesomeIcon icon="phone-square-alt"/>
              <div v-if="order.channel ==='qconnect' " class="ml-2 font-bold">
                {{ phoneNumber }}
              </div>
              <div v-else class="ml-2 font-bold">
                {{ order.destination_phone }}
              </div>
            </div>
          </div>
          <div class="flex items-center pt-5">
            <FontAwesomeIcon icon="location-arrow"/>
            <div>
              <div class="ml-2 cursor-pointer" @click="openMap">
                {{ order?.destination_address }}
                <FontAwesomeIcon class="ml-1" icon="fa-map-location-dot"/>
                <br>
                <div v-if="order.distance">
                  <!--                  "İşletmeniz ile teslim noktası arasındaki mesafe 6 metredir."-->
                  <!--                  "The distance between your business and the delivery point is 6 meters."-->
                  <!--                  "Biznesinizlə çatdırılma məntəqəsi arasında məsafə 6 metrdir."-->
                  <!--                  "The distance between your business and the delivery point is x meters."-->
                  <!--                  "The distance between your business and the delivery point is x meters.": "The distance between your business and the delivery point is {x} meters."-->
                  <!--                  "The distance between your business and the delivery point is x meters.": "İşletmeniz ile teslim noktası arasındaki mesafe {x} metredir."-->
                  <!--                  "The distance between your business and the delivery point is x meters.": "Biznesinizlə çatdırılma məntəqəsi arasında məsafə {x} metrdir."-->


                  {{ $t("The distance between your business and the delivery point is x meters." , {x:order.distance.toLocaleString()}) }}
<!--                  <span class="text-red-600 font-bold">-->
<!--                    {{ order.distance.toLocaleString() }}-->
<!--                  </span> metredir.-->
                </div>
              </div>
            </div>

          </div>
          <div v-show="order?.destination_address_description" class="flex items-start  pt-7">

            <FontAwesomeIcon icon="note-sticky"/>
            <div class="ml-2">
              {{ order.destination_address_description }}
            </div>

          </div>

          <div
              v-if="order?.requires_dont_ring_the_bell"
              class="my-4 flex items-center"
          >
            <FontAwesomeIcon icon="bell-slash"/>
            <div class="ml-1">
              {{ $t("Please Do Not Ring The Bell") }}
            </div>
          </div>
          <div
              v-if="order?.requires_contactless"
              class="my-4 flex items-center"
          >
            <FontAwesomeIcon icon="handshake-slash"/>
            <div class="ml-1">
              {{ $t('Contactless Delivery') }}
            </div>
          </div>

          <div v-if="order?.recipient_notes">
            <div class="h-px bg-slate-300 my-7"/>
            <div class="font-bold text-slate-700">
              {{ $t("Order Note") }}
            </div>
            <div>
              {{ order?.recipient_notes }}
            </div>
          </div>
          <div>‚
            <div class="h-px bg-slate-300 my-6"/>
            <div class="flex font-bold text-slate-700 pb-4">
              {{ $t("Carrier Information") }}
            </div>
            <div class="">
              {{ $t("delivery of the relevant package") }}
              <span
                  class="font-bold "
                  :class="[order?.courier_type === 'kuik' ? 'text-green-400' : 'text-red-600']"
              >
                {{
                  order?.foreign_carrier ? order.foreign_carrier :
                      order?.courier_type === "kuik" ? $t('Pool Courier') : $t('Your Own Courier')
                }}
              </span>
              {{ $t("will be done by") }}.
            </div>

            <div v-if="order?.courier_type === 'company'
                                && companyProductType === 'qd-kuik'
                                && order?.company_approved_at
                                && !order?.courier
                                ">
              <div class="italic my-2 text-xxs ">
                {{ $t("*If you don't have your own courier, the top right") }}  <span class="font-bold text-xs text-green-400"
                                                                                    >{{ $t("Call a Driver") }} </span>
                {{ $t("you can assign it to the pool courier in your area with the button") }}
              </div>
              <el-button
                  @click="openCourierListDialog"
                  size="small"
                  type="primary"
                  plain
              >
                {{ $t("Assign to Your Driver") }}
              </el-button>

            </div>
          </div>
          <div v-if="!order?.company_approved_at" class="text-red-600 my-6">
            {{ $t("*You can adjust your carrier preferences after order confirmation processes.") }}
          </div>
        </div>
        <template v-if="order?.company_approved_at">
          <CarrierInformation :order="order"/>
        </template>
      </div>
      <div class="col-span-1 flex flex-col h-full overflow-hidden">
        <div class="px-7 pt-4 font-bold text-slate-700 flex items-center justify-between">
          <div>
            {{ $t("Order contents") }}
          </div>
          <div class="flex items-center">
            <div v-if="order?.is_scheduled" class="mr-2  px-2 text-slate-700 rounded bg-slate-300 font-bold text-xxs">
              Planlı {{ $t("Planned") }}
            </div>
            <div class="font-bold text-red-600">
              {{ dayjs(order?.starts_at).format('DD.MM.YYYY HH:mm') }}
            </div>
          </div>
        </div>
        <div class="h-full overflow-auto">
          <Detail :order="order"/>
        </div>
        <div
            class="px-7 border-t border-slate-300 pb-2 pt-4 bg-slate-50 grid grid-cols-6 text-slate-700 border-r border-slate-300">
          <div class="col-span-4 flex items-center">
            <div class="text-xs font-bold flex items-center">
              <div class="text-xxs font-bold">
                {{ $t("Payment Method") }}
              </div>
            </div>
          </div>
          <div class="col-span-1 text-center font-bold text-xxs">
            {{ $t('Quantity') }}
          </div>
          <div class="col-span-1 text-center font-bold text-xxs">
            T. {{ $t('Amount') }}
          </div>
        </div>
        <div v-if="Number(order?.discounted_amount) !== Number(order?.amount)"
             class="px-7 bg-slate-50 grid grid-cols-6 text-slate-700">
          <div class="col-span-4"></div>
          <div class="col-span-1"></div>
          <div class="col-span-1 text-center font-light text-xxs line-through">
            {{ formattedAmount.amount }}
          </div>
        </div>
        <div
            class="px-7 pb-2 bg-slate-50 grid grid-cols-6 text-slate-700 border-r border-slate-300">
          <div class="col-span-4 flex items-center">
            <div class="text-xs font-bold flex items-center">
              <div class="text-red-600 text-xs font-bold">
                <PaymentBadge :slug="order.payment_type">
                  {{
                    order.payment_type_description?.length > 24 ? order.payment_type_description?.substring(0, 24) + "..." : order.payment_type_description
                  }}
                </PaymentBadge>

              </div>
            </div>
          </div>
          <div class="col-span-1 text-center font-bold text-xs">
            {{ totalQuantity }}
          </div>
          <div class="col-span-1 text-center font-bold text-xs">
            {{ formattedAmount.discount ? formattedAmount.discount : formattedAmount.amount }}
          </div>
        </div>
      </div>
    </div>
    <el-dialog
        class="kuik-customized-dialog"
        v-model="courierListDialogVisible"
        title="Paketi Kuryeye Ata"
        destroy-on-close
    >
      <CourierList :order="order" @close="closeCourierListDialog"/>
    </el-dialog>
    <el-dialog
        v-model="qrcodeDialogVisible"
        :width="320"
        class="customized-header__dialog"
        title="Sipariş QR Kodu"
        destroy-on-close
    >
      <GeneratorQRCode :integrationId="integrationId"/>
    </el-dialog>
    <el-dialog
      width="400"
      class="el-repicent-dialog"
        v-model="printDialogVisible"
        title="Sipariş Fişi"
        destroy-on-close
    >
      <OrderPrintDialog :order="order"/>
    </el-dialog>
    <el-dialog class="map-customized-dialog" destroy-on-close title="Adres Bilgisi" :width="800" v-model="mapVisible">
      <TaskDetailMap :payload="_task" :statusVisible="false"/>
    </el-dialog>

  </div>
</template>
