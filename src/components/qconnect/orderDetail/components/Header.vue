<script setup>
import dayjs from "dayjs";
import {connectHelpers} from "./helper";
import {onMounted, watch, ref, computed} from "vue";
import {useStore} from "vuex";



let pendingColor = "red-600"

const {getters} = useStore()

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})



const headerColor = ref()


const activeCompanyName = computed(() => getters["auth/me"].active_company.name);

const controller = () => {
  if (props.order.company_approved_at) {
    headerColor.value = `bg-${getStatusColorClass(props.order.status_category_slug)}`
  } else {
    headerColor.value = `bg-${pendingColor}`
  }

}
const getStatusColorClass = (status_slug) => {
  switch (status_slug) {
    case "pool":
      return "slate-600";
    case "created":
      return "slate-700";
    case "assigned":
      return "indigo-600";
    case "in_progress":
      return "indigo-600";
    case "on_delivery":
      return "indigo-600";
    case "completed":
      return "lime-500";
    case "failed":
      return "slate-700";
    case "cancelled":
      return "red-200";
  }
}


onMounted(() => {
  controller()
})

watch(() => props.order, () => {
  controller()
},{deep:true})


</script>

<template>
  <div
      class="h-20 flex items-center justify-between px-7 py-6 text-white"
      :class="[headerColor]"
  >
    <div>
      <div class="text-xxxxs">
        {{ $t("Provider") }}
      </div>
      <div class="text-xs xl:text-sm font-bold flex items-center">
        <FontAwesomeIcon icon="cash-register" class="mr-1"/>
        {{ order?.provider?.name }}
      </div>
    </div>
    <div>
      <div class="text-xxxxs">
        {{ $t("Carrier") }}
      </div>
      <div class="text-xs xl:text-sm font-bold flex items-center">
        <FontAwesomeIcon icon="motorcycle" class="mr-1"/>
        {{ order.foreign_carrier ? order.foreign_carrier :
          order.courier_type === "kuik" ? "Kuik" : activeCompanyName
        }}
      </div>
    </div>
    <div>
      <div class="text-xxxxs">
        {{ $t('Arrival Time') }}
      </div>
      <div class="text-xs xl:text-sm font-bold flex items-center">
        {{ dayjs(order.created_at).format("HH:mm") }}
      </div>
    </div>
    <div>
      <div class="text-xxxxs">
        {{ $t("Order Status") }}
      </div>
      <div class="text-xs xl:text-sm font-bold">{{ connectHelpers.getStatusName(order.status_category_slug) }}</div>
    </div>
  </div>
</template>
