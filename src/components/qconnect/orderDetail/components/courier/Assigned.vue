<script setup>
import dayjs from "dayjs";
import Map from "@/components/qconnect/orderDetail/components/Map.vue";
import {computed} from "vue";

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  },
})

const courier = computed(() => {
  let courierType = props.order.courier_type
  let name = courierType === "provider" ? props.order.task_provider_courier.name : props.order.courier?.name
  let phone = courierType === "provider" ? null : props.order.courier?.phone

  return {
    name,
    phone
  }
})

const getStatusName = (status) => {


  if (status === "assigned" || status === "in_progress") {

    return "<PERSON>ürü<PERSON>ü Geliyor"

  } else if (status === "on_delivery") {

    return "Sürücü Yolda"

  } else if (status === "completed") {

    return "Teslim Edildi"

  } else if (status === "failed") {

    return "Başarısız"

  } else if (status === "cancelled") {

    return "İptal Edildi"

  }

}
const getStatusColorClass = () => {
  let status = props.order.status_category_slug
  switch (status) {
    case "pool":
      return "text-slate-600";
    case "created":
      return "text-slate-700";
    case "assigned":
      return "text-indigo-600";
    case "in_progress":
      return "text-indigo-600";
    case "on_delivery":
      return "text-indigo-600";
    case "completed":
      return "text-lime-500";
    case "failed":
      return "text-slate-700";
    case "cancelled":
      return "text-red-200";
  }
}


</script>

<template>

<!--    <Map :order="order"/>-->
</template>
