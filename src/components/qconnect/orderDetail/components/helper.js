export const connectHelpers = {
    getStatusName: (status_slug) => {
        switch (status_slug) {
            case "pool":
                return "Onay Bekliyor";
            case "created":
                return "Onay Bekliyor";
            case "assigned":
                return "Hazırlanıyor";
            case "in_progress":
                return "Hazırlanıyor";
            case "on_delivery":
                return "Yolda";
            case "completed":
                return "Tamamlandı";
            case "failed":
                return "Başarısız";
            case "cancelled":
                return "İptal";
        }
    },

    getStatusColorClass: (status_slug) => {
        switch (status_slug) {
            case "pool":
                return "red-600";
            case "created":
                return "red-600";
            case "assigned":
                return "indigo-600";
            case "in_progress":
                return "indigo-600";
            case "on_delivery":
                return "indigo-600";
            case "completed":
                return "lime-500";
            case "failed":
                return "slate-700";
            case "cancelled":
                return "slate-700";
        }
    }
}
