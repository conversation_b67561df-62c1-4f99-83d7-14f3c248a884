<script setup>
import ManuelAssign from "@/components/deliveries/components/ManuelAssign.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {onMounted, inject, computed, ref, watch} from "vue";
import {useI18n} from "vue-i18n";
import {useStore} from "vuex";
import debounce from "lodash.debounce";
import {useToast} from "vue-toastification";


const emitter = inject("emitter")
const api = inject("api")

const toast = useToast()
const {t} = useI18n()
const {getters} = useStore()

const emit = defineEmits(["close"])
const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  },
})

const selectedHub = computed(() => getters["operations/selectedHub"].id);
const dialogVisible = ref(false)
const loader = ref(null)
const search = ref(null)
const courier = ref(null)
const couriers = ref([])

const setSearch = (text) => {
  search.value = text;
};

const getFilteredCourierList = () => {
  loader.value.show()
  let latlng = props.order.origin_lat + "," + props.order.origin_lng;
  let body = {
    filter: {
      search: search.value ? search.value : null,
      online: "all",
      shift: "all",
      latlng: latlng ? latlng : null,
      duty: "all",
      hub_id: selectedHub.value
    },
  };
  api
      .post(
          "components/compact-couriers-table",
          body
      )
      .then((response) => {
        couriers.value = response.data;

      })
      .catch(() => {
      })
      .finally(() => {
        loader.value.hide()
      });
}

const onSelect = () => {
  dialogVisible.value = true
}
const onSubmit = () => {
  dialogVisible.value = false
  loader.value.show()
  api.patch(`customer/task-assignment/${courier.value.id}`, {task_ids: [props.order.id]})
      .then(() => {
        //TODO locale
        toast.success("Paket kuryeye atandı.")
        emit("close")
        emitter.emit("connect-refresh")
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide())

}

const debounceGetFilteredCourierList = debounce(getFilteredCourierList, 300)
onMounted(() => {
  getFilteredCourierList()
})

watch(search, () => {
  debounceGetFilteredCourierList()
})
</script>

<template>
  <div class=" w-full overflow-hidden flex flex-col p-2.5 text-slate-700">
    <LoadingBlock ref="loader"/>
    <div class="pt-5">
      İşletme Kuryeleri
    </div>
    <div class="py-4">Sürücü listesinden bir kurye seçerek görevi kendisine atayabilirsiniz.</div>
    <ManuelAssign
        v-model="courier"
        @set-search="setSearch"
        :couriers="couriers"
        :secondary="true"
    >
      <el-input
          v-model="search"
          :placeholder="t('Driver Search (Name or Phone Number)')"
      />
    </ManuelAssign>

    <div class="pt-4 text-right">
      <el-button
          size="small"
          @click="onSelect"
          type="primary"
          :disabled="!courier"
      >
        Görev Ata
      </el-button>

      <el-dialog
          v-model="dialogVisible"
          :width="300"
          center
          :title=" t('Are you sure?')"
      >
        <div class="text-center text-slate-700 ">
          Paketi seçmiş olduğunuz kuryeye atamak istediğinize emin misiniz?
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button
                @click="dialogVisible = false"
                size="small"
            >
              {{ t('Cancel') }}
            </el-button>
            <el-button
                size="small"
                @click="onSubmit" type="primary"
            >
              {{ t('Yes') }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
