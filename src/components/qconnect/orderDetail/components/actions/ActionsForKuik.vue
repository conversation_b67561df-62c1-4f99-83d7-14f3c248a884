<script setup>
import {computed, inject, ref} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import MergeableTaskDialog from "@/components/qconnect/orderDetail/components/mergeable/MergeableTaskDialog.vue";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const {t} = useI18n()
const {getters} = useStore()
const api = inject("api")
const emitter = inject("emitter")

const toast = useToast()

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  },
})

const dialogVisible = ref(false)
const disabledButton = ref(false)
const mergeableTaskDialogVisible = ref(false)
const mergeableTasks = ref([])
const selectedMergeTask = ref()
const updatePreparationTimeLoading = ref()
const preparationPopover = ref()

const cookingAlarmEnabled = computed(() => getters["connect/cookingAlarmEnabled"])

let cancelButtonDisabledStatuses = ["completed", "failed", "cancelled", "on_delivery"]

const cancelButtonDisableController = () => {
  return cancelButtonDisabledStatuses.includes(props.order.status_category_slug)
}


const callCourier = () => {
  dialogVisible.value = false
  disabledButton.value = true
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/tasks/${props.order.id}/use-courier-company-service`)
      .then(() => emitter.emit("connect-refresh"))
      .catch((err) => toast.error(err.data.message))
      .finally(() => {
        disabledButton.value = false
        emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: false})
      })

}

const mergeTasks = () => {
  mergeableTaskDialogVisible.value = false
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/merge-tasks/${props.order.id}`, {
    merger_task_id: selectedMergeTask.value.id
  })
      .then(() => emitter.emit("connect-refresh"))
      .catch((err) => toast.error(err.data.message))
      .finally(() => {
        disabledButton.value = false
        emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: false})
      })
}

const handleNewCourier = () => {
  mergeableTaskDialogVisible.value = false
  callCourier()
}

const openDialog = () => {

  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})

  api(`customer/mergeable-tasks/${props.order.id}?is_kuik=1`,)
      .then((response) => {
        if (response?.data.length > 0) {

          mergeableTasks.value = response.data.sort((a, b) => {
            return (Number(b.partner_plan_amount) - Number(b.partner_plan_discounted_amount)) - (Number(a.partner_plan_amount) - Number(a.partner_plan_discounted_amount))
          })

          mergeableTaskDialogVisible.value = true
        } else {
          dialogVisible.value = true
        }
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() => emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: false}))
}

const closeDialog = () => {
  dialogVisible.value = false

}

const setSelectedMergeTask = (params) => {
  selectedMergeTask.value = params
}


const updatePreparationTime = (min) => {

  preparationPopover.value.hide()

  let message = `Pişirme süreniz ${min} dakika olarak kaydedilmiştir. `

  if (cookingAlarmEnabled.value) {
    message += ` Eğer havuzdaki kurye taşırsa size ${min} dakika kala geliş mesafesinde alarm çalacaktır.`
  }

  updatePreparationTimeLoading.value = true
  api.put(`customer/tasks/${props.order.id}`, {preparation_time: min})
      .then(() => toast.info(message))
      .finally(() => updatePreparationTimeLoading.value = false)
}

</script>


<template>
  <div class="flex items-center justify-between">


    <div v-if="order.status_category_slug === 'created' || order.status_category_slug === 'pool'" class="custom-popover-preparation-time mr-2">
      <el-popover
          ref="preparationPopover"
          trigger="click"
          :teleported="false"
      >
        <template #default>
          <div class="inline-block space-y-1">
            <div
                v-for="min in 5"
                @click="updatePreparationTime(min * 5)"
                class="p-1 flex items-center justify-between rounded cursor-pointer"
                :class="[
                     order.preparation_time === (min * 5)
                     ? 'bg-green-400 text-white'
                     : 'border border-slate-300 hover:bg-slate-300'
                  ]"
            >
              <div class="mr-1">
                {{ min * 5 }}
              </div>
              <FontAwesomeIcon icon="clock-desk"/>
            </div>
          </div>
        </template>
        <template #reference>
          <el-button
              size="small"
              class="ml-2"
          >
            <template #icon>
              <FontAwesomeIcon
                  :icon="updatePreparationTimeLoading ? 'spinner' : 'clock-desk'"
                  :spin="updatePreparationTimeLoading"
                  fixed-width class="mr-0 sm:mr-2"
              />
            </template>
            {{ $t("Preparation Time") }}:   {{order.preparation_time}} {{ $t("Minute") }}
          </el-button>
        </template>
      </el-popover>

    </div>

    <el-button
        @click="$emit('onCancel')"
        :disabled="cancelButtonDisableController()"
        size="small"
        type="danger"
        plain
    >
      {{ $t("Cancel") }}
    </el-button>
    <div v-if="order.status_category_slug === 'created' || order.status_category_slug === 'pool'"
         class="flex items-center ml-2">
      <el-button
          @click="openDialog"
          size="small"
          type="success"
          class="font-bold bg-green-400 border-green-400"
      >
        {{ $t("Call a Driver") }}
      </el-button>

    </div>
  </div>
  <el-dialog
      :title="$t('Merge Order')"
      class="mergeable-task-dialog"
      style="padding: 0px !important;"
      v-model="mergeableTaskDialogVisible"
      :width="820"
      destroy-on-close
      append-to-body
  >
    <MergeableTaskDialog
        :currentTask="props.order"
        :mergeableTasks="mergeableTasks"
        :selectedMergeTask="selectedMergeTask"
        @setSelectedMergeTask="setSelectedMergeTask"
        @handleNewCourier="handleNewCourier"
        @mergeTasks="mergeTasks"
    />
    <template #footer>
      <div class="flex items-center">
        <div class="text-red-500">
          <FontAwesomeIcon icon="square-info" size="xl"/>
        </div>
        <div class="flex flex-col text-slate-700 text-xxxs text-left ml-2">
          <div>
            Paket birleştirme işlemlerinde teslimatların bir birine uzaklıkları
            <span class="font-bold">maksimum 2km</span>
            olmalıdır.
            Paketlerin eş zamanlı hazırlanması önemlidir.
          </div>
          <div>
            Paket birleştirme işlemleri sayesinde
            <span class="font-bold">%30 daha hızlı</span>
            teslimat
            yapmış olursunuz.
          </div>
        </div>
      </div>
    </template>
  </el-dialog>
  <el-dialog
      v-model="dialogVisible"
      :width="530"
      align-center
  >
    <span class="break-words">          {{ $t('The order will be assigned to the courier in the pool. This process cannot be undone. Are you sure you want to continue?') }} </span>
    <template #footer>
      <span class="dialog-footer">
        <el-button
            :disabled="disabledButton"
            @click="dialogVisible = false"
        >
          {{ $t('Cancel') }}
        </el-button>
        <el-button
            type="primary"
            :disabled="disabledButton"
            @click="callCourier"
        >
          {{ $t('Confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style>

</style>
