<script setup>
import {inject, onMounted, onUnmounted, ref, watch} from "vue";
import dayjs from "dayjs";

const emitter = inject("emitter")
const api = inject("api")

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})

const timeLeft = ref(false)
const loading = ref(false)

const onApprove = () => {
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/qconnect/${props.order.id}/accept`)
      .catch(() => {
        emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: false})
      })
}

let timer;

const calTimeLeft = () => {
  if (props.order.is_scheduled) {
    timer = setInterval(() => {
      timeLeft.value = dayjs(props.order.starts_at).diff(dayjs(), "minute")
      loading.value = true
    }, 1000)
  } else {
    loading.value = true
  }
}

onMounted(() => {
  calTimeLeft()
})
onUnmounted(() => {
  clearInterval(timer)
})
watch(() => props.order, () => {
  clearInterval(timer)
  calTimeLeft()
}, {deep: true})

</script>

<template>
  <div v-if="loading" class="flex items-center justify-between space-x-2">
    <div
        @click="$emit('onCancel')"
        class="flex items-center justify-between rounded bg-red-600 text-white cursor-pointer px-2 py-1">
      <div class="font-bold">
        <FontAwesomeIcon icon="times-square"/>
      </div>
      <div class="font-medium ml-2">
        {{ $t("Reject") }}
      </div>
    </div>
    <div
        v-if="order.is_scheduled ? timeLeft < 59  : true"
        @click="onApprove"
        class="flex items-center justify-between rounded bg-green-400 text-white cursor-pointer px-2 py-1">
      <div class="font-bold">
        <FontAwesomeIcon icon="check-square"/>
      </div>
      <div class="font-medium ml-2">
        {{ $t("Approve") }}
      </div>
    </div>
  </div>
</template>
