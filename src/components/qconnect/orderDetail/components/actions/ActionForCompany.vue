<script setup>
import {inject} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const {getters} = useStore()
const api = inject("api")


const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  },
})


// const customerProductType = computed(() => getters["auth/me"].active_company.company_product_type);


</script>


<template>
  <div class="flex items-center justify-between px-4 border-b border-slate-300 py-2 bg-slate-50">
    COMPANY

<!--    <el-button :disabled="cancelButtonDisableController()" size="small" class="font-bold text-red-600">-->
<!--      {{ $t("Cancel") }}-->
<!--    </el-button>-->


<!--    <div v-if="order.status_category_slug === 'created'" class="flex items-center">-->
<!--      <el-button v-if="customerProductType === 'qd-kuik' " @click="assignToCourier" size="small"-->
<!--                 class="font-bold text-indigo-600">-->
<!--        {{ $t("Assigned to Driver") }}-->
<!--      </el-button>-->
<!--      <el-button v-if="customerProductType !== 'qd'" @click="callCourier" size="small"-->
<!--                 class="ml-2 font-bold text-indigo-600">-->
<!--        {{ $t("Call a Driver") }}-->
<!--      </el-button>-->
<!--    </div>-->
<!--    <div v-else>-->
<!--      <el-button size="small" class="font-bold text-indigo-600">-->
<!--        {{ order.status.name }}-->
<!--      </el-button>-->
<!--    </div>-->
<!--    <el-dialog-->
<!--        class="kuik-customized-dialog"-->
<!--        v-model="dialogVisible"-->
<!--        title="Paketi Kuryeye Ata"-->
<!--        destroy-on-close-->
<!--    >-->
<!--&lt;!&ndash;      <CourierList :order="order" @close="closeDialog"/>&ndash;&gt;-->

<!--    </el-dialog>-->

  </div>
</template>
