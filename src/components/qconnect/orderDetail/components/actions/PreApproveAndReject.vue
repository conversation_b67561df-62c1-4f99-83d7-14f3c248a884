<script setup>

import {inject} from "vue";

const emitter = inject("emitter")
const api = inject("api")

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})

const onReject = () => {
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.delete(`customer/qconnect/${props.order.id}/reject`)
      .catch(() => {
        emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: false})
      })
      .finally(() => {

      })

}
const onApprove = () => {
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/qconnect/${props.order.id}/pre-accept`)
      .catch(() => {
        emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: false})
      })


}


</script>

<template>
  <div class="flex items-center justify-between space-x-2">
    <div
        @click="$emit('onCancel')"
        class="flex items-center justify-between rounded bg-red-600 text-white cursor-pointer px-2 py-1">
      <div class="font-bold">
        <FontAwesomeIcon icon="times-square"/>
      </div>
      <div class="font-medium ml-2">
        {{ $t("Reject") }}
      </div>
    </div>
    <div
        @click="onApprove"
        class="flex items-center justify-between rounded bg-green-400 text-white cursor-pointer px-2 py-1">
      <div class="font-bold">
        <FontAwesomeIcon icon="check-square"/>
      </div>
      <div class="font-medium ml-2">
        {{ $t("Pre Approve") }}
      </div>
    </div>
  </div>
</template>
