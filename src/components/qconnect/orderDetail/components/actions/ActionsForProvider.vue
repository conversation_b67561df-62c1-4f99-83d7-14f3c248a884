<script setup>
import {onMounted, watch, ref, inject} from "vue";
import CourierUpdate from "@/components/deliveries/CourierUpdate.vue";
import TaskEdit from "@/components/settings/drawers/edit/TaskEdit.vue";

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})

const api = inject("api")
const emitter = inject("emitter")
const step = ref()


const preparation = ["assigned", "in_progress"]
const delivery = ["on_delivery"]
const completed = ["completed", "failed", "cancelled"]

const onPrepared = () => {
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/qconnect/${props.order.id}/prepared`)
}

const onHandover = () => {
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/qconnect/${props.order.id}/handover`)
}



const controller = () => {
  const statusSlug = props.order.status_category_slug
  if (statusSlug === "created") {
    step.value = "assign"
  } else if (preparation.includes(statusSlug)) {
    step.value = "preparation"
  } else if (statusSlug === "on_delivery") {
    step.value = "delivery"
  } else if (completed.includes(statusSlug)) {
    step.value = "completed"
  }
}

onMounted(() => {
  controller()
})

watch(() => props.order, () => {
  controller()
}, {deep: true})
</script>

<template>
  <div
      class="flex items-center justify-between pl-3">

    <el-button
        @click="$emit('onCancel')"
        :disabled="step !== 'preparation'" size="small" class="font-bold text-red-600">
      {{ $t("Cancel") }}
    </el-button>

    <el-button
        @click="onPrepared"
        v-if="props.order.status_category_slug === 'created' || props.order.status_category_slug === 'assigned'"
        size="small"
        class="ml-2 font-bold text-indigo-600"
    >
      Sipariş Hazırlandı
    </el-button>
    <el-button
        @click="onHandover"
        v-if="props.order.provider.name === 'Getir Yemek' && props.order.status_category_slug === 'in_progress' "
        size="small"
        class="ml-2 font-bold text-indigo-600"
    >
      Kuryeye Teslim Et
    </el-button>

  </div>

</template>
