<script setup>
import dayjs from "dayjs";
import { computed, nextTick, onMounted, inject, ref } from "vue";
import { useStore } from "vuex";
import formatter from "@/class/formatter";
import QRCode from "qrcode";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { getters } = useStore();
const api = inject("api");

const props = defineProps({
  order: { type: Object }
});

const activeCompanyName = computed(() => getters["auth/me"].active_company.name);

const formattedAmount = computed(() => {
  return {
    amount: formatter.currency(props.order.amount),
    discount: props.order.discounted_amount ? formatter.currency(props.order.discounted_amount) : null
  };
});

const phoneNumber = computed(() => {
  const qconnect = props.order.meta?.qconnect;
  if (!qconnect) return null;

  const providerClientData = qconnect.providerClientData;
  return providerClientData?.clientPhoneNumber || qconnect.contactPhoneNumber || qconnect.callCenterPhone;
});

const products = computed(() => {
  if (props.order.channel === "qconnect") {
    return props.order.contents;
  } else {
    return props.order.items;
  }
});

const me = computed(() => getters["auth/me"]);

const filteredDetails = computed(() => {
  const detailsMap = {};
  props.order.contents.forEach(content => {
    detailsMap[content.id] = content.details.map(detail => detail.replace(/^PRODUCT:\s*/, ""));
  });
  return detailsMap;
});

const onPrint = () => {
  let printElement = document.getElementById("receipt");
  if (printElement) {
    let divContents = printElement.innerHTML;
    let a = window.open("", "", "height=500, width=500");
    a.document.write("<html>");
    a.document.write("<body>");
    a.document.write(divContents);
    a.document.write("</body></html>");
    a.document.close();  // Close the current document stream
    a.print();
    a.close();
  }
};

onMounted(() => {


  nextTick(() => {
    let opts = {
      errorCorrectionLevel: "H",
      type: "image/jpeg",
      quality: 0.3,
      margin: 1
    };

    QRCode.toDataURL(props.order.integration_id, opts, function(err, url) {
      if (err) throw err;

      let img = document.getElementById("image");
      img.src = url;
    });
  });
});
</script>

<template>
  <div id="receipt" style="padding: 1rem; border-radius: 0.5rem;">
    <h1 style="text-align: center; font-weight: bold; font-size: 1.25rem;">{{ me.active_company.short_name }}</h1>
    <div style="text-align: center; margin: 1rem 0;">
      <p><span style="font-weight: bold;">{{ t("Phone") }} : </span>{{props.order.channel ==='qconnect' ? phoneNumber : props.order.destination_phone }}</p>
      <p><span style="font-weight: bold;">{{ t("Order N") }} : </span>#{{ props.order.integration_id }}</p>
      <p v-if="props.order.channel === 'qconnect'"><span style="font-weight: bold;">{{ t("Channel Order") }} : </span>{{ props.order.meta?.qconnect.providerClientData.clientOrderNumber }}</p>
      <p><span style="font-weight: bold;">{{ t("Date")
        }} : </span>{{ dayjs(props.order.created_at).format("DD.MM.YYYY HH:mm") }}</p>
      <p><span style="font-weight: bold;">{{ t("Customer") }} : </span>{{ props.order.destination_name }}</p>
      <p ><span style="font-weight: bold;">{{ t('Channel') }} : </span>{{ props.order.foreign_carrier ? props.order.foreign_carrier :'Platform' }}</p>
    </div>
    <div style="border-top: 2px dashed ;">
      <div style="border-top: 2px dashed;">
        <div v-for="item in props.order.contents" :key="item.integration_id" style="margin: 0.5rem 0;">
          <p style="display: flex; justify-content: space-between;">
            <div>
              <span>{{ item.name }} ({{ item.quantity }})</span>

              <ul style="font-size: 11px">
                <li v-for="(detail, index) in item.details" :key="index">

                  {{ detail.replace("PRODUCT: ", "").replace("NONE:", "").replace("INGREDIENT:", "") }}
                </li>
              </ul>
            </div>
            <span>{{ formatter.currency(item.amount, item.currency_unit) }}</span>
          </p>
        </div>
      </div>

    </div>

    <div style="border-top: 2px dashed ; border-bottom: 2px dashed ; margin: 0.5rem 0; padding: 0.5rem 0;">
      <p style="display: flex; justify-content: space-between; font-weight: bold; font-size: 1.25rem;">
        <span>{{ t("Total")
          }}</span><span>{{ props.order.discounted_amount ? formatter.currency(props.order?.discounted_amount, props.order.items[0].currency_unit) : formatter.currency(props.order.amount, props.order.items[0].currency_unit)
        }}</span></p>
    </div>
    <div style="border-bottom: 2px dashed ; padding-bottom: 8px;">
      <p style="display: flex; justify-content: space-between;"><span style="font-weight: bold;">{{ t("Payment Type")
        }}:</span> {{ props.order.payment_type_description }}</p>
    </div>
    <div style="border-bottom: 2px dashed ; margin: 0.5rem 0;  padding-bottom: 8px;">
      <p style="display: flex; justify-content: space-between; padding-bottom: 3px;"><span
        style="font-weight: bold; width: 25%;">{{ t("Address") }}:</span><span
        style="width: 75%;">{{ props.order.destination_address }}</span></p>
      <p
        style="display: flex; justify-content: space-between; margin-top: 4px; margin-bottom: 4px ;border-top: 2px dashed; padding-top:6px; padding-bottom: 4px;">
        <div><span style="font-weight: bold">{{ t("Building Name")
          }} :</span><span>{{ props.order.destination_address_apartment }}</span></div>
        <div><span style="font-weight: bold">{{ t("Floor")
          }} :</span><span>{{ props.order.destination_address_floor }}</span></div>
        <div><span style="font-weight: bold">{{ t("Apartment")
          }} :</span><span>{{ props.order.destination_address_building }}</span></div>
      </p>
      <p
        style="display: flex; justify-content: space-between; border-top: 2px dashed ;padding-top:6px; padding-bottom: 2px;">
        <div style="font-weight: bold;">
          Adres Açıklaması :
        </div>
        <div>
          {{ props.order.destination_address_description }}
        </div>
      </p>
    </div>
    <div style="border-bottom: 2px dashed ; margin-top: 0.5rem; padding-bottom: 8px;">
      <p style="display: flex; justify-content: space-between;"><span style="font-weight: bold;">{{ t("Recipient Note")
        }}:</span> {{ props.order?.recipient_notes }}</p>
    </div>
    <p style="border-bottom: 2px dashed ; margin-top: 1px;"></p>
    <div style="text-align: center; margin-top: 1rem;">
      <p style="letter-spacing: 6px;">{{ t("THANK YOU") }}</p>
      <div style="display: flex; justify-content: center; padding-top: 0.875rem;">
        <img id="image">
      </div>

    </div>
  </div>
  <div style="display: flex; align-items: center;justify-content: center;padding-bottom:18px ">
    <el-button @click="onPrint" style="margin-top: 0.875rem;" size="small">{{ $t("Print") }}</el-button>
  </div>

</template>

<style scoped>
.receipt {
  font-family: Arial, sans-serif;
  line-height: 1.5;
}

.receipt-items p {
  margin: 0.5em 0;
}

.border-dotted {
  border-style: dotted;
}
</style>