<script setup>

import formatter from "@/class/formatter";
import { computed, onMounted, reactive, ref, watch, inject } from "vue";
import { useStore } from "vuex";
import CourierliveMap from "@/components/operations/components/CourierliveMap.vue";
import { useI18n } from "vue-i18n";


const api = inject("api");

const { getters } = useStore();
const { t } = useI18n();

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
});
const courierMapLiveVisible = ref(false);

const _task = computed(() => {
  let location = {
    origin_lat: props.order.origin_lat,
    origin_lng: props.order.origin_lng,
    destination_lat: props.order.destination_lat,
    destination_lng: props.order.destination_lng,
    starts_at: props.order.starts_at,
    ends_at: props.order.ends_at,
    origin_address: props.order.origin_address,
    destination_address: props.order.destination_address,
    origin_name: props.order.origin_name,
    destination_name: props.order.destination_name,
    task_provider_courier: props.order.courier_type === "company" ? props.order.courier : props.order.task_provider_courier
  };
  return location;
});

//TODO Servis İstemiyor kısmında eksik var

const transactions = computed(() => {
  let arr = [];
  const { transactions } = props.order;

  let deliveryFeeList = transactions.filter(transaction => transaction.type_slug === "delivery_fee");

  if (deliveryFeeList.length === 1) {
    let amount = deliveryFeeList[0].amount;
    let name = deliveryFeeList[0].type.name;
    let undiscounted_amount = null;
    let discounted_amount = null;

    if (props.order.merged_task_id && Number(props.order.integration_id) !== props.order.merged_task_id) {
      let ds = deliveryFeeList[0].partner_plan_fee;
      undiscounted_amount = Number(ds.customer_price);
      discounted_amount = Number(ds.customer_price) - amount;
    }

    arr.push({ amount, name, undiscounted_amount, discounted_amount });
  }

  if (deliveryFeeList.length > 1) {
    let amount = deliveryFeeList.reduce((a, b) => a.amount + b.amount);
    let name = deliveryFeeList[0].type.name;
    arr.push({ amount, name });
  }

  let deliveryCommissionList = transactions.filter(transaction => transaction.type_slug === "delivery_commission");

  if (deliveryCommissionList.length === 1) {
    let amount = deliveryCommissionList[0].amount;
    let name = deliveryCommissionList[0].type.name;
    arr.push({ amount, name });
  }

  if (deliveryCommissionList.length > 1) {
    let amount = deliveryCommissionList.reduce((a, b) => a.amount + b.amount);
    let name = deliveryCommissionList[0].type.name;
    arr.push({ amount, name });
  }

  return arr;
});

const courierId = ref(null);
const courier = reactive({
  name: null,
  phone: null,
  lat: null,
  lng: null,
  status: null
});

const visible = ref(true);
const orderType = ref(null);
const courierETA = ref(null);

const findOrderType = () => {

  if ((props.order.courier_type === "company" || props.order.original_task_id) && props.order.courier) {
    orderType.value = "company";
    visible.value = true;
    courier.name = props.order.courier.name;
    courier.phone = props.order.courier.phone;
    courier.lat = props.order.courier?.courier_location?.lat;
    courier.lng = props.order.courier?.courier_location?.lng;
    courierId.value = props.order.courier.id;
    listener(props.order.courier.id);
    //TODO location_update

    if (props.status_category_slug === "in_progress") {
      courier.status = "Restorana Geliyor";
    }
    if (props.status_category_slug === "on_delivery") {
      courier.status = "Teslimatta";
    }
    if (props.status_category_slug === "completed") {
      courier.status = "Tamamlandı";
    }
    if (props.status_category_slug === "failed") {
      courier.status = "Başarısız oldu";
    }
  }

  if (props.order.courier_type === "kuik" && props.order.kuik_task_id) {
    visible.value = true;
    orderType.value = "kuik";

    let foreignCourier = props.order.status_logs.filter((x) => x.foreign_courier !== null).sort((a, b) => a.id - b.id).slice(-1)[0]?.foreign_courier;

    if (foreignCourier) {

      courierId.value = foreignCourier.courier_location.courier_id;
      courier.name = foreignCourier.name;
      courier.phone = foreignCourier.phone;
      courier.lat = foreignCourier.courier_location.lat;
      courier.lng = foreignCourier.courier_location.lng;
      courier.status = foreignCourier.role_slug;
      listener(foreignCourier.courier_location.courier_id);
      getCourierETA(foreignCourier.courier_location.courier_id);

    }
    if (props.order.status_category_slug === "in_progress") {
      courier.status = "Restorana Geliyor";
    }
    if (props.order.status_category_slug === "on_delivery") {
      courier.status = "Teslimatta";
    }
    if (props.order.status_category_slug === "completed") {
      courier.status = "Tamamlandı";
    }
    if (props.order.status_category_slug === "failed") {
      courier.status = "Başarısız oldu";
    } else {
      courier.status = "Sürücü Aranıyor";
    }
  }
};

const resetFields = () => {
  return new Promise((resolve) => {
    stopListener();
    courier.name = null;
    courier.phone = null;
    courier.lat = null;
    courier.lng = null;
    courier.status = null;
    courier.status = null;
    courier.lng = null;
    courier.lat = null;
    visible.value = null;
    orderType.value = null;
    courierId.value = null;
    resolve();
  });
};

onMounted(() => {
  resetFields().then(() => {
    findOrderType();
  });
});

watch(() => props.order, () => {
  resetFields().then(() => {
    findOrderType();
  });
}, { deep: true });


const listener = (id) => {
  Echo.channel("courier.location").listen(".courier.location.updated", (data) => {
    if (data.courier.id === id) {
      courier.lat = data.courier.courier_location.lat;
      courier.lng = data.courier.courier_location.lng;
      getCourierETA(id);
    }
  });
};

const stopListener = () => {
  Echo.channel("courier.location").stopListening(".courier.location.updated");
};


const getCourierETA = (id) => {


  let body = null;

  if (props.order.courier_company_task?.status.name === "Teslim Almaya Gidiyorum") {
    body = {
      lat: props.order.origin_lat,
      lng: props.order.origin_lng
    };
  }

  if (props.order.status_category_slug === "on_delivery") {
    body = {
      lat: props.order.destination_lat,
      lng: props.order.destination_lng
    };
  }

  if (!body) return;

  api.post(`customer/couriers/${id}/eta`, body)
    .then((res) => {
      courierETA.value = res;
    });
};
const openCourierMap = () => {
  courierMapLiveVisible.value = true;
};


let visibleCourierETAStatuses = ["in_progress", "on_delivery"];

</script>

<template>

  <div v-if="visible" class="grid gap-6 grid-cols-2 text-slate-700 p-4 w-2/3">
    <div v-show="courier.name" class="col-span-1">
      <div class="font-bold mb-1.5">{{ t("Driver Name") }}</div>
      <div>{{ courier.name }}</div>
    </div>
    <div class="col-span-1">
      <div class="font-bold mb-1.5">{{ t("Driver Status") }}</div>
      <div>{{ orderType === "kuik" ? courier.status : props.order.status.name }}</div>
    </div>
    <div v-if="order.preparation_time && order.status_category_slug !== 'on_delivery'" class="col-span-1">
      <div class="font-bold mb-1.5">{{ t("Preparation Time") }}</div>
      <div>{{ order?.preparation_time }} {{ t("Minute") }}</div>
    </div>
    <div v-if="courierETA &&  visibleCourierETAStatuses.includes(order.status_category_slug)" class="col-span-1">
      <div class="font-bold mb-1.5"> {{
          order.status_category_slug !== "on_delivery"
            ? t("To the Restaurant")
            : t("To the Customer")
        }} {{ t("Estimated Time of Arrival") }}
      </div>
      <div class="flex">
        <div class="mr-4">{{ (courierETA?.duration / 60).toFixed(0) }} {{ t("Minute") }}</div>
        <div>
          <el-button @click="openCourierMap" size="small">{{ t("Live Location") }}</el-button>
        </div>
      </div>

    </div>

  </div>

  <div v-if="visible" class="px-4">
    <div class="h-px bg-slate-300 my-6" />
    <div class="font-bold text-slate-700 pb-4 ">
      {{ t("Fee Information") }}
    </div>
    <div class="grid gap-6 grid-cols-2 text-slate-700">
      <div v-if="orderType === 'kuik' " v-for="transaction in transactions" class="col-span-1">
        <div class="font-bold mb-1.5">{{ transaction.name }}</div>
        <div
          v-if="transaction?.undiscounted_amount"
          class="flex items-center"
        >
          <div class="line-through font-xxxs font-light text-red-300">
            {{ formatter.currency(transaction.undiscounted_amount) }}
          </div>
          <div class="ml-1">
            {{ formatter.currency(transaction.amount) }}
          </div>
        </div>
        <div
          v-else
        >
          {{ formatter.currency(transaction.amount) }}
        </div>
      </div>
    </div>
    <div
      v-if="transactions.find(x => x.undiscounted_amount )"
      class="italic mt-2 text-xxs">
      {{ t("Since your order was combined with another order carried by the pool courier, especially for you") }}
      <span class="font-bold">{{
          formatter.currency(transactions.find(x => x.undiscounted_amount).discounted_amount)
        }} </span>
      {{ t("a discount has been offered.") }}
    </div>
    <el-dialog class="map-customized-dialog" destroy-on-close :title="t('Live Tracking')" :width="800"
               v-model="courierMapLiveVisible">
      <CourierliveMap :payload="_task" />
    </el-dialog>
  </div>

</template>
