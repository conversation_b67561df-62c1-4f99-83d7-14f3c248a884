<script setup>
const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})
</script>

<template>
  <div class="grid gap-4 grid-cols-2 text-slate-700 text-xs px-7 py-6 border-b border-slate-300">
    <div class="col-span-1">
      <div class="">
        {{$t("Customer Name")}}
      </div>
      <div class="font-bold">{{ order.destination_name }}</div>
    </div>
    <div class="col-span-1">
      <div class="">
        {{$t("Phone")}}
      </div>
      <div class="font-bold">{{ order.destination_phone }}</div>
    </div>
    <div class="col-span-2">
      <div class="">
        {{$t("Address")}}
      </div>
      <p class="font-medium">
        {{ order.destination_address }}
      </p>
    </div>
  </div>
</template>
