<script setup>
/* eslint-disable no-undef */
import {ref, onMounted, reactive, watch, computed} from "vue";
import {Loader} from "@googlemaps/js-api-loader";
import DefaultMapStyle from "@/map-styles/default.json";
import CourierPin from "@/assets/images/map/marker/courier.png";
import OriginPin from "@/assets/images/map/marker/store.png";
import DestinationPin from "@/assets/images/map/marker/destinationpin.png";
import {useStore} from "vuex";

const {getters} = useStore()
const props = defineProps({
  courier: {type: Object},
  origin: {type: Object},
  destination: {type: Object},
});
const companyChannel = computed(
    () => "company." + getters["auth/activeCompany"].id
);
const trackingMapRef = ref(null);
const trackingMap = ref(null);
const markers = reactive({
  courier: null,
  destination: null,
  origin: null
});



const mapPromise = async () => {
  return new Promise((resolve) => {
    const map = new google.maps.Map(trackingMapRef.value, {
      center: {
        lat: 41.013028158816425,
        lng: 28.99737372063146,
      },
      zoom: 14,
      zoomControl: false,
      mapTypeControl: false,
      scaleControl: false,
      streetViewControl: false,
      rotateControl: false,
      fullscreenControl: false,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery"],
      },
    });
    map.mapTypes.set(
        "qdelivery",
        new google.maps.StyledMapType(DefaultMapStyle, {name: "Kuik"})
    );
    map.setMapTypeId("qdelivery");
    google.maps.event.addListenerOnce(map, "tilesloaded", function () {
      resolve(map);
    });
  });
};


watch(() => props.origin, (curr) => {
  markers && markers.origin && markers.origin.setPosition({
    lat: curr.lat,
    lng: curr.lng
  })
})

watch(() => props.destination, (curr) => {
  markers && markers.destination && markers.destination.setPosition({
    lat: curr.lat,
    lng: curr.lng
  })
})


watch(() => props.courier, (curr) => {

  if (curr) {
    markers && markers.courier && markers.courier.setPosition({
      lat: curr.lat,
      lng: curr.lng
    })
    // listener(curr.)
  }
  // let bounds = new google.maps.LatLngBounds();
  // bounds.extend(markers.courier.getPosition());
  // bounds.extend(markers.destination.getPosition());
  // trackingMap.value.fitBounds(bounds);
  // TODO burada pin zoomlaması yapılacak
  // let bounds = new google.maps.LatLngBounds();
  //
  // bounds.extend(markers.courier.getPosition());
  // bounds.extend(markers.destination.getPosition());
  // trackingMap.fitBounds(bounds);
}, {deep: true})

const loadMap = () => {
  try {
    mapPromise().then((map) => {

      let bounds = new google.maps.LatLngBounds();

      markers.origin = new google.maps.Marker({
        position: {
          lat: props.origin.lat,
          lng: props.origin.lng,
        },
        icon: {
          url: OriginPin,
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(14, 36),
        },
        map,
      });
      markers.destination = new google.maps.Marker({
        position: {
          lat: props.destination.lat,
          lng: props.destination.lng,
        },
        icon: {
          url: DestinationPin,
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(14, 36),
        },
        map,
      });
      if (props.courier) {
        markers.courier = new google.maps.Marker({
          position: props.courier && {
            lat: props.courier?.lat,
            lng: props.courier?.lng,
          },
          icon: {
            url: CourierPin,
            origin: new google.maps.Point(0, 0),
            anchor: new google.maps.Point(14, 36),
          },
          map,
        });
        bounds.extend(markers.courier.getPosition());
      }

      bounds.extend(markers.destination.getPosition());
      bounds.extend(markers.origin.getPosition());
      map.fitBounds(bounds);
      trackingMap.value = map
    });
  } catch (ex) {
  }
};

onMounted(() => {
  loadMap()
})


</script>

<template>
  <div id="map" ref="trackingMapRef" class="w-full h-full"/>
</template>
