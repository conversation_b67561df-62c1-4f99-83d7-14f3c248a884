<script setup>
import {inject, onMounted, ref} from "vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()

const api = inject("api")

const emit = defineEmits(['onSubmit'])
const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})

const loader = ref()
const categories = ref([])
const notes = ref()
const category = ref(null)
const status = ref(null)

const submitForm = () => {
  emit("onSubmit", {cancel_reason_id: status.value, cancel_note: notes.value})
}

onMounted(() => {
  loader.value.show()
  api(`customer/qconnect/${props.order.id}/cancel-reasons`)
      .then((res) =>{
        categories.value = res.data
      })
      .catch((err) => toast.error(err.message))
      .finally(() => loader.value.hide())
})

</script>

<template>
  <div class=" w-full overflow-hidden flex flex-col p-2.5 text-slate-700">
    <LoadingBlock ref="loader"/>
    <el-form label-position="top"
             class="grid grid-cols-12 gap-4">
      <div class="col-span-12">
        <el-form-item
            :label="$t('Reasons for Cancellation')"
        >
          <el-radio-group
              class="w-full h-full"
              v-model="status"
          >
            <div
                v-for="i in categories"
                :key="i.id"
            >
              <div class="flex items-center w-full">
                <el-radio :label="i.description"/>
                <span class="text-sm text-slate-700">
                {{ i.description }}
              </span>
              </div>
            </div>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="col-span-12">
        <el-form-item :label="$t('Notes')">
          <el-input
              v-model="notes"
              type="textarea"
          />
        </el-form-item>
      </div>
      <div class="col-span-12 pt-4 text-right">
        <el-button
            @click="submitForm"
            size="small"
            type="danger"
            :disabled="!status"
        >
          {{ $t('Approve') }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>
