<script setup>
import {computed,onMounted} from "vue";
import formatter from "@/class/formatter";

const props = defineProps({
  order: {
    type: Object, default: () => {
    }
  }
})

const products = computed(() => {
  if (props.order.channel === "qconnect") {
    return props.order.contents
  } else {
    return props.order.items
  }
})



</script>

<template>

  <div class="px-7 py-6 text-slate-700 select-text">
    <div class="grid grid-cols-6 text-slate-700 overflow-auto">
      <div class="col-span-4 flex items-center">
        <div class="text-xs font-bold">
          {{ $t("Product") }}
        </div>
      </div>
      <div class="col-span-1 text-center text-xs">
        {{ $t("Quantity") }}
      </div>
      <div class="col-span-1 text-center text-xs">
        {{ $t("Amount") }}
      </div>
      <template v-for="item in products">
        <div class="col-span-6 h-px bg-slate-300 my-2"/>
        <div class="col-span-4 items-center text-xxs">
          <div class="font-bold text-xs">
            {{ item.name }}
          </div>
          <div class="ml-3" v-if="item.notes">
            Not:{{ " " + item.notes }}
          </div>
          <div class="ml-5" v-for="note in item.details">
            - {{ note }}
          </div>
          <div class="ml-7" v-if="item.meta" v-for="meta in item.meta">
            <div>{{meta[0]}}</div>
            <div class="ml-6" v-for="i in meta.meta">{{i}}</div>
          </div>

        </div>
        <div class="col-span-1 text-center text-xs">{{ Number(item?.quantity) }}</div>
        <div class="col-span-1 text-center text-xs"> {{ formatter.currency(item?.amount) }}</div>
      </template>
    </div>
  </div>

</template>
