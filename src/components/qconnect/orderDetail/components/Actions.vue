<script setup>
import {inject, ref, onMounted, defineEmits} from 'vue';
import {useStore} from "vuex";
import ApproveAndReject from "@/components/qconnect/orderDetail/components/actions/ApproveAndReject.vue";
import ActionsForProvider from "@/components/qconnect/orderDetail/components/actions/ActionsForProvider.vue";
import ActionsForKuik from "@/components/qconnect/orderDetail/components/actions/ActionsForKuik.vue";
import UpdateStatus from "@/components/qconnect/orderDetail/components/UpdateStatus.vue";
import PreApproveAndReject from "@/components/qconnect/orderDetail/components/actions/PreApproveAndReject.vue";
import {conversionFormData} from "@/class/helpers";
import TaskEdit from "@/components/settings/drawers/edit/TaskEdit.vue";
import CopyTask from "@/components/settings/drawers/edit/CopyQuickTask.vue";

const {getters, dispatch} = useStore()

const emit = defineEmits(["openNewTaskDrawer"]);
const api = inject("api")
const emitter = inject("emitter")
const props = defineProps({
  order: {
    type: Array, default: () => {
    }
  }
})

const cancelOrderDialogVisibleForQConnect = ref(false)
const cancelOrderDialogVisibleForUI = ref(false)
const editDrawerVisible = ref(false)
const copyTaskDrawer = ref(false)


const openCancelOrderDialog = () => {
  if (props.order.channel === "qconnect") {
    cancelOrderDialogVisibleForQConnect.value = true
  } else {
    cancelOrderDialogVisibleForUI.value = true
  }
}

const onCancel = (data) => {
  cancelOrderDialogVisibleForQConnect.value = false
  let formData = conversionFormData(data)
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
  api.post(`customer/qconnect/${props.order.id}/reject`, formData)
}

const onUIOrderCancel = () => {
  cancelOrderDialogVisibleForUI.value = false
  emitter.emit("order.confirmation.proccess", {id: props.order.id, loading: true})
}

const openEditDrawer = () => {
  editDrawerVisible.value = true
}

const closeEditDrawer = () => {
  editDrawerVisible.value = false;
}
const openTaskCopy = () => {
  copyTaskDrawer.value = true;
}

const close = () => {
  copyTaskDrawer.value = false
}




</script>

<template>
  <div class="flex">
    <div>
      <el-button v-if="!props.order.courier_id && !props.order.task_provider_courier" @click="openEditDrawer"
                 size="small" class="ml-2 font-bold text-indigo-600">
        {{ $t("Edit") }}
      </el-button>
      <el-button v-if="props.order.courier_id || props.order.task_provider_courier" @click="openTaskCopy" size="small"
                 class="mr-2 font-bold text-indigo-600">
        {{ $t("Edit") }}
      </el-button>
    </div>
    <div v-if="!props.order.company_pre_approved_at">
      <PreApproveAndReject @onCancel="openCancelOrderDialog" :order="order"/>
    </div>
    <div v-else-if="!props.order.company_approved_at">
      <ApproveAndReject @onCancel="openCancelOrderDialog" :order="order"/>
    </div>
    <div v-else-if="props.order.courier_type === 'provider' ">
      <ActionsForProvider :order="order"/>
    </div>
    <div v-else-if="props.order.courier_type === 'kuik'">
      <div
          class="flex items-center justify-between ml-3">
        <el-button
            @click="openCancelOrderDialog"
            :disabled="!(order.status_category_slug === 'assigned' || order.status_category_slug === 'created'|| order.status_category_slug === 'in_progress')"
            size="small"
            type="danger"
            plain
        >
          {{ $t("Cancel") }}
        </el-button>
      </div>
    </div>
    <div v-else>
      <ActionsForKuik :order="order" @onCancel="openCancelOrderDialog"/>
    </div>
  </div>

  <el-dialog
      class="kuik-customized-dialog"
      v-model="cancelOrderDialogVisibleForQConnect"
      title="Sipariş İptali"
      destroy-on-close
  >
    <UpdateStatus @onSubmit="onCancel" :order="order"/>
  </el-dialog>
  <el-drawer
      v-model="cancelOrderDialogVisibleForUI"
      class="customized-drawer"
      title="Sipariş İptali"
      append-to-body
      destroy-on-close
  >
    <UpdateStatusDrawer
        :selectedTask="Array(order)"
        @close="onUIOrderCancel"
        selectedStatusCategory="cancelled"
    />
  </el-drawer>
  <el-drawer
      v-model="editDrawerVisible"
      class="customized-drawer "
      :title="$t('Task Edit')"
      append-to-body
      destroy-on-close
  >
    <TaskEdit @close="closeEditDrawer" :order="order"/>
  </el-drawer>
  <el-drawer
      v-model="copyTaskDrawer"
      class="customized-drawer "
      :title="$t('Task Edit')"
      append-to-body
      destroy-on-close
  >
    <CopyTask @close="close" :order="order"/>
  </el-drawer>


</template>
