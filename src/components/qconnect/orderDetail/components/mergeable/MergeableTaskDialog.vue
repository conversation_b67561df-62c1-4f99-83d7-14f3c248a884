<script setup>

import DefaultMapStyle from "@/map-styles/default.json";
import {computed, onMounted, onUnmounted, ref} from "vue";
import LimeMarker from "@/assets/images/map/marker/solid-lime-marker.svg"
import SladeMarker from "@/assets/images/map/marker/solid-slade-marker.svg"
import HubMarkerIcon from "@/assets/images/map/marker/store.png";
import {useStore} from "vuex";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import OrderItem from "@/components/qconnect/orders/OrderItem.vue";
import {getDirectionFromMapBoxWithLegs} from "@/class/helpers";
import formatter from "../../../../../class/formatter";


const {getters} = useStore();

const emits = defineEmits(["setSelectedMergeTask", "mergeTasks", "handleNewCourier"])
const props = defineProps({
  currentTask: {
    type: [Object, null],
    default: () => {
    }
  },
  mergeableTasks: {
    type: Array,
    default: () => []
  },
  selectedMergeTask: {
    type: [Object, null],
    default: () => {
    }
  },
})


const selectedHub = computed(() => getters["connect/selectedHubData"])

const markers = {
  couriers: [],
  hubs: [],
  origins: [],
  destinations: [],
  statusPoints: [],
  infowindow: null
}

const polylines = []

const operationMapRef = ref()
const operationMap = ref()
const wayPoint = ref()


onMounted(() => {

  Promise.all([getMap()])
      .then(() => {
        showOnMapTasks()
        onSelect(props.mergeableTasks[0])
      })

})

onUnmounted(() => {
  emits("setSelectedMergeTask", null)
})

const getMap = async () => {
  await mapPromise()
}

const mapPromise = () => {
  return new Promise((resolve) => {
    const map = new google.maps.Map(operationMapRef.value, {
      center: {
        lat: 41.013028158816425,
        lng: 28.99737372063146
      },
      minZoom: 5,
      zoom: 10,
      fullscreenControlOptions: {
        position: google.maps.ControlPosition.RIGHT_BOTTOM,
      },
      streetViewControl: false,
      rotateControl: false,
      fullscreenControl: false,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery"],
        position: google.maps.ControlPosition.TOP_RIGHT,
      },
    })
    map.mapTypes.set(
        "qdelivery",
        new google.maps.StyledMapType(DefaultMapStyle, {name: "QDelivery"})
    );
    map.setMapTypeId("qdelivery");
    markers.infowindow = new google.maps.InfoWindow({
      content: ""
    });
    operationMap.value = map;
    resolve();
  });
};

const createMarkers = (param, visible) => {
  const destinationContentString = `<div>#${param.id} - ${param.destination_name} </div>`

  const destination_marker = new google.maps.Marker({
    type: "destination",
    id: param.id,
    map: operationMap.value,
    visible,
    optimized: true,
    animation: google.maps.Animation.BOUNCE,
    zIndex: visible ? 10 : 0,
    position: {
      lat: param.destination_lat,
      lng: param.destination_lng,
    },
    icon: {
      url: visible ? SladeMarker : LimeMarker,
      origin: new google.maps.Point(0, 0),
      anchor: new google.maps.Point(13, 26),
      labelOrigin: new google.maps.Point(13, 9)
    },
  });
  google.maps.event.addListener(destination_marker, 'click', function () {
    markers.infowindow.setContent(destinationContentString)
    markers.infowindow.open(operationMap.value, destination_marker);

  });

  markers.destinations.push(destination_marker)

}


const showOnMapTasks = () => {

  createHubMarker()

  props.mergeableTasks.forEach((task) => {
    createMarkers(task, false)
  })
  createMarkers(props.currentTask, true)

setTimeout(()=>{
  onBoundAllMarkers()
},1000)

}

const createDirectionQuery = (param) => {

  if (!param) return

  polylines.forEach((polly) => {
    polly.setMap(null)
  })


  let locList = [
    `${props.currentTask.origin_lng + "," + props.currentTask.origin_lat}`,
    param,
    `${props.currentTask.destination_lng + "," + props.currentTask.destination_lat}`
  ]

  let query = locList.join(";")

  getDirectionFromMapBoxWithLegs(query)
      .then(({path, legs}) => {

        if (legs.length === 2) {
          let _wayPoint = legs[legs.length - 1]
          wayPoint.value = _wayPoint
        }

        const polyline = new google.maps.Polyline({
          icons: [
            {
              icon: {
                path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
              },
              offset: "100%",
              repeat: '200px',
            },
          ],
          strokeColor: "#94a3b8",
          strokeOpacity: 1,
          strokeWeight: 2,
          fillColor: "#4f46e5",
          fillOpacity: 1,
          map: operationMap.value,
          path
        });
        polylines.push(polyline)
        onBoundAllMarkers()
      })
}

const createHubMarker = () => {
  if (!selectedHub.value) {
    console.warn('selectedHub is null, skipping createHubMarker');
    return;
  }

  const contentString = `<div>${selectedHub.value.id} - ${selectedHub.value.name} </div>`
  const marker = new google.maps.Marker({
    type: "hub",
    id: selectedHub.value.id,
    map: operationMap.value,
    zIndex: -10,
    optimized: true,
    position: {
      lat: selectedHub.value.lat,
      lng: selectedHub.value.lng,
    },
    icon: {
      url: HubMarkerIcon,
      origin: new google.maps.Point(0, 0),
      anchor: new google.maps.Point(14, 36),
    },
  });
  google.maps.event.addListener(marker, 'click', function () {
    markers.infowindow.setContent(contentString)
    markers.infowindow.open(operationMap.value, marker);

  });
  markers.hubs.push(marker)
}

const onSelect = (task) => {

  emits("setSelectedMergeTask", task)

  markers.destinations.forEach((marker) => {

    if (marker.id === task.id) {
      marker.setVisible(true)
      google.maps.event.trigger(marker, 'click')
    } else if (marker.id !== props.currentTask.id) {
      marker.setVisible(false)
    }
  })

  let directionQueryStr = `${task.destination_lng + "," + task.destination_lat}`

  createDirectionQuery(directionQueryStr)
}

const onBoundAllMarkers = () => {

  const bounds = new google.maps.LatLngBounds()

  markers.origins.forEach((marker) => {

    bounds.extend(marker.getPosition())
  })

  markers.destinations.forEach((marker) => {
    bounds.extend(marker.getPosition())
  })

  markers.hubs.forEach((marker) => {
    bounds.extend(marker.getPosition())
  })

  operationMap.value.fitBounds(bounds, {top: 10, bottom: 60, left: 15, right: 15});
}


const handleNewCourier = () => {
  emits("handleNewCourier")
}

const mergeTasks = () => {
  emits("mergeTasks")
}


</script>

<template>

  <div class="border-b border-slate-300">
    <div class="flex flex-col md:flex-row items-center justify-between border-b border-slate-300 bg-slate-700">
      <div class="flex items-center grow p-4 ">
        <div class="text-lime-500">
          <FontAwesomeIcon icon="gifts" size="4x"/>
        </div>
        <div class="text-white flex flex-col ml-4 font-bold">
          <span class="text-l">İddia ediyoruz</span>
          <span class="text-xl">Bu teslimatı daha hızlı taşıyabiliriz!</span>
        </div>
      </div>
      <div
          v-if="selectedMergeTask"
          class="flex flex-col shrink-0 text-white px-4"
          style="width: 180px"
      >

        <template v-if="Number(selectedMergeTask.partner_plan_amount) !==
                  Number(selectedMergeTask.partner_plan_discounted_amount)">


              <span class="text-right text-3xl text-lime-500 font-bold">
               {{
                  formatter.currency(Number(selectedMergeTask.partner_plan_amount) - Number(selectedMergeTask.partner_plan_discounted_amount))
                }}
              </span>
          <span class="text-right text-xxs text-lime-500 font-bold">
                  İndirim

                  <span v-if="wayPoint">
                    ve {{ ((600 - (currentTask.duration - wayPoint.duration)) / 60).toFixed(0) }} Dakika daha hızlı teslimat
                  </span>

                  kazanın!
                </span>
        </template>
        <template v-else-if="wayPoint">
          <div class="text-right text-xs text-lime-500 font-bold">

            <div class="text-right text-3xl text-lime-500 font-bold">
              {{ ((600 - (currentTask.duration - wayPoint.duration)) / 60).toFixed(0) }}
              Dakika
            </div>
            <div>
              daha hızlı teslimat kazanın!
            </div>
          </div>
        </template>

      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-5">
      <div class="col-span-1 h-96 md:h-auto md:col-span-3 relative">
        <div
            id="operationMap"
            ref="operationMapRef"
            class="w-full h-full"
        >


        </div>
        <div
            v-if="wayPoint"
            class="absolute border border-slate-400  left-2  md:inset-x-20 bottom-9 rounded px-2 md:px-4 py-1 md:py-2 z-50"
            style="background-color: rgba(255,255,255,0.85)"
        >
          <div class="flex items-center justify-center space-x-0.5 md:space-x-1 font-bold text-xxs md:text-xs">

            <div>
              {{ currentTask.origin_name }}
            </div>
            <FontAwesomeIcon icon="arrow-right"/>
            <div>
              {{ selectedMergeTask.destination_name }}
            </div>
            <FontAwesomeIcon icon="arrow-right"/>
            <div>
              {{ currentTask.destination_name }}
            </div>
          </div>
          <div class="text-center mt-1 text-xxxs md:text-xxs">
            İlk teslimattan sonra, tahmini varış <span class="font-bold">+{{ (wayPoint.duration / 60).toFixed(0) }} Dakika </span>
          </div>
        </div>
      </div>
      <div class="col-span-1 md:col-span-2 flex flex-col grow border-l border-slate-300">
        <div class="text-xs font-bold px-4 py-2 border-b border-slate-300">
          Seçili Olan Teslimat
        </div>
        <div
            class="flex"
        >
          <div class="flex items-center justify-center bg-slate-300 text-white my-2 drop-shadow px-1">
            <FontAwesomeIcon icon="arrows-turn-right"/>
          </div>
          <div class="grow">
            <OrderItem
                :order="currentTask"
                :disabledSelect="true"
            />
          </div>
        </div>


        <div class=" text-xs font-bold px-4 py-2 border-b border-slate-300">
          Birleştirilebilir Teslimat Opsiyonlarınız
        </div>

        <div style="height: 340px" class="overflow-auto">

          <div
              class="flex "
              v-for="task in mergeableTasks"
          >
            <div v-if="selectedMergeTask?.id === task.id"
                 class="flex items-center justify-center bg-green-400 text-white my-2 drop-shadow px-1">
              <FontAwesomeIcon icon="arrows-turn-right" flip="horizontal"/>
            </div>
            <div class="grow">
              <OrderItem
                  :order="task"
                  :borderColorClass="selectedMergeTask?.id === task.id && 'border-lime-500' "
                  @onSelect="onSelect"

              />
            </div>
          </div>

        </div>
        <div class="flex items-center justify-between border-t border-slate-300 px-2 py-3">
          <el-button
              class="opacity-70"
              size="small"
              @click="handleNewCourier"
          >
            {{ $t("Call New Driver") }}
          </el-button>
          <el-button
              type="success"
              size="small"
              :disabled="!selectedMergeTask"
              @click="mergeTasks"
          >
            {{ $t("Merge Delivery") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
