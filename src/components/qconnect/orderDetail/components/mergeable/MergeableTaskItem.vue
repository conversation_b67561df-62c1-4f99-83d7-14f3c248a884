<script setup>

import <PERSON><PERSON><PERSON><PERSON> from "@/assets/images/kuik-logo-dark.svg"

const emits = defineEmits(["onSelect"])
const props = defineProps({
  task: {
    type: Object,
    default: () => {
    }
  },
  selectedMergeTaskId: {
    type: [Number, null],
    default: null
  }
})

const onSelect = (param) => {
  emits("onSelect", param)
}


</script>

<template>
  <div
      class="flex items-center justify-between border border-slate-300 mb-4 py-2 px-1 rounded"
      :class="[
          selectedMergeTaskId === task?.id
          ? 'bg-slate-300'
          : 'bg-white'
      ]"
  >
    <div class="flex items-center">
      <img v-if="task.provider?.icon_url" :src="task.provider?.icon_url" class="h-6 w-6">
      <img v-else :src="KuikLogo" class="h-6 w-6 rounded-full">
      <div class="font-bold text-lg ml-2">#{{ task?.integration_id }}</div>
      <div class="font-medium ml-2 ">{{ task?.destination_name }}</div>
    </div>
    <div>
      <el-button
          @click="onSelect(task)"
      >
        {{ selectedMergeTaskId === task?.id ? "Seçildi" : "Seç" }}
      </el-button>
    </div>
  </div>
</template>
