<script setup>

import {computed} from "vue";

const props = defineProps({
  color: {type: String, default: 'amber'},
  name: {type: String, default: null},
})

const borderColor = computed(() => {
  switch (props.color) {
    case "amber":
      return "border-amber-600"
    case "lime":
      return "border-lime-600";
  }
});

</script>

<template>
  <div class="flex items-center">
    <div class="h-3.5 w-3.5 rounded-full border-2 bg-black "
         :class="[borderColor]"
    >
    </div>
    <div class="ml-1 text-slate-700 text-xs">
      {{ name }}
    </div>
  </div>
</template>
