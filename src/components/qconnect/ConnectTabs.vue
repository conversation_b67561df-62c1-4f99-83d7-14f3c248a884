<script setup>
import {computed} from "vue";
import {useStore} from "vuex";
import ConnectTabButton from "./ConnectTabButton.vue";

const {dispatch, getters} = useStore();

const activeConnectTab = computed(() => getters["connecttabs/activeTab"]);
const connectTabs = computed(() => getters["connecttabs/currentlyOpenTabs"]);

function isTabActive(tab) {
  return activeConnectTab.value.title === tab.title;
}

function openConnectTab(tab) {
  dispatch("connecttabs/setActiveTab", tab);
}

</script>

<template>
  <div class="flex-grow flex border-b border-slate-300 md:border-none md:flex-col">
    <div
        @click="openConnectTab(connectTabs[0])"
        class="h-9 w-9 md:w-12 md:h-12 flex items-center justify-center cursor-pointer"
        :class="[
              isTabActive(connectTabs[0])
                ? 'text-slate-700'
                : 'text-slate-300',
            ]"
    >
      <FontAwesomeIcon :icon="connectTabs[0].icon" fixed-width/>
    </div>
  </div>
  <div
      v-for="tab in connectTabs"
      :key="tab.name"
  >
    <template v-if="tab.name !== 'ConnectHomeTab' ">
      <ConnectTabButton
          :active="isTabActive(tab)"
          :tab="tab"
          :disabled="!tab.name"
          @click="openConnectTab(tab)"
      />
    </template>
  </div>
</template>
