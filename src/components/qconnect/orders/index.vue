<script>
import { defineAsyncComponent } from "vue";

export default {
  components: {
    OrderList: defineAsyncComponent(() => import("@/components/qconnect/orders/tabs/OrderList.vue")),
    PlannedOrderList: defineAsyncComponent(() => import("@/components/qconnect/orders/tabs/PlannedOrderList.vue"))
  }
};
</script>

<script setup>
import TabButtons from "@/components/qconnect/orders/tabs/TabButtons.vue";
import { useI18n } from "vue-i18n";
import { computed, inject, onMounted, onUnmounted, ref, watch } from "vue";
import { useStore } from "vuex";
import { doneStatuses } from "@/class";
import { useToast } from "vue-toastification";
import CookingAlarmToaster from "@/components/toaster/CookingAlarmToaster.vue";

const toast = useToast();
const { t } = useI18n();
const { getters, dispatch } = useStore();

const audio = new Audio("notification.mp3");
const cookingAlarmAudio = new Audio("cookingAlarm.mp3");
const emitter = inject("emitter");
const api = inject("api");

const activeOrderTab = computed(() => getters["connect/activeOrderTab"]);
const selectedOrder = computed(() => getters["connect/selectedOrder"]);
const selectedHub = computed(() => getters["operations/selectedHub"].id);
const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);
const cookingAlarmEnabled = computed(() => getters["connect/cookingAlarmEnabled"]);
const cookingAlarmList = computed(() => getters["connect/cookingAlarmList"]);

const orders = ref([]);

watch(cookingAlarmEnabled, () => {

  if (!cookingAlarmEnabled.value) {
    cookingAlarmList.value.forEach(x => clearTimeout(x.timer));
    dispatch("connect/setCookingAlarmList", []);
  } else {
    cookingAlarmController(orders.value);
  }

});

const setSelectedOrder = (data) => {
  dispatch("connect/setSelectedOrder", data);
};

const promiseGetData = () => {
  return new Promise((resolve, reject) => {
    api(`customer/kuik-tasks?filter[hub_id]=${selectedHub.value}&filter[status_category_slug]=created,assigned,in_progress,on_delivery,pool`)
      .then((response) => {

        if (cookingAlarmEnabled.value) {
          cookingAlarmController(response.data);
        }

        orders.value = response.data.map(x => {
          return { ...x, loading: false };
        });

        if (selectedOrder.value) {

          let _selected = response.data.find(x => x.id === selectedOrder.value.id);
          setSelectedOrder({ ..._selected });
        } else {

          setSelectedOrder(orders.value[0]);
        }
        resolve();
      })
      .catch(() => reject());
  });
};

const cookingAlarmController = (data) => {

  let list = data.filter((order) => order.courier_company_task?.status.name === "Teslim Almaya Gidiyorum");
  list.forEach((task) => createCookingAlarm(task));

};

const createCookingAlarm = (task) => {

  if (cookingAlarmList.value.find(alarm => alarm.id === task.id)) return;

  if (!task.preparation_time) return;

  api.post(`customer/couriers/${task.task_provider_courier.courier_location.courier_id}/eta`, {
    lat: task.origin_lat,
    lng: task.origin_lng
  })
    .then((res) => {

      let duration = res.duration - (task.preparation_time * 60);

      if (duration <= 0) return;

      addCookingAlarm({
        id: task.id,
        duration: duration * 1000,
        preparation_time: task.preparation_time,
        name: task.destination_name
      });
    });


};

const setSelectedOrderById = (id) => {
  let _selected = orders.value.find(x => x.id === id);
  setSelectedOrder({ ..._selected });
};

onMounted(() => {

});

const addCookingAlarm = ({ id, duration, preparation_time, name }) => {

  let timer = null;

  timer = setTimeout(() => {

    cookingAlarmAudio.volume = 1;
    cookingAlarmAudio.play();

    let message = t("The estimated time for our courier to reach you for your name order number id is min minutes. You can now start preparing your meal.", {
      id,
      name,
      min: preparation_time
    });

    toast.info(
      {
        component: CookingAlarmToaster,
        props: {
          message
        },
        listeners: {
          onClick: () => setSelectedOrderById(id)
        }
      },
      {
        timeout: false,
        onClose: () => {
          cookingAlarmAudio.pause();
          cookingAlarmAudio.currentTime = 0;
        }
      });

    clearTimeout(timer);
    dispatch("connect/removeCookingAlarm", id);

  }, duration);

  dispatch("connect/addCookingAlarm", { id, timer });
};

const getData = () => {
  dispatch("connect/setHubSelectedLoading", true);
  promiseGetData()
    .finally(() => {
      dispatch("connect/setHubSelectedLoading", false);
    });
};

const getOrder = (id) => {
  return new Promise((resolve, reject) => {
    api(`customer/tasks/${id}`)
      .then((res) => {
        resolve(res);
      })
      .catch(() => reject());
  });
};

const updatedStatusOrder = (data) => {
  getOrder(data.task.id)
    .then((res) => {

      orders.value.filter((x) => {
        if (x.id === data.task.id) {
          if (doneStatuses.includes(data.status_category_slug)) {
            removeOrderFromOrders(data.task.id, data.status_category_slug);
          } else {
            x.status_category_slug = res.status_category_slug;
            x.status = res.status;
            x.status_logs = res.status_logs;
          }
        }
        return x;
      });

      if (selectedOrder.value?.id === data.task.id) {
        dispatch("connect/setSelectedOrder", { ...res });
      }

      if (!cookingAlarmEnabled.value) return;

      if (res.courier_company_task?.status.name === "Teslim Almaya Gidiyorum") {
        createCookingAlarm(res);
      } else if (cookingAlarmList.value.find(alarm => alarm.id === res.id)) {
        dispatch("connect/removeCookingAlarm", res.id);
      }
    });
};

const removeOrderFromOrders = (id, status) => {
  audio.volume = 1;
  audio.play();
  orders.value = orders.value.filter((order) => order.id !== id);
  if (selectedOrder.value.id === id) {
    dispatch("connect/setSelectedOrder", {
      ...selectedOrder.value,
      status_category_slug: status,
      loading: false
    });
  }

  if (status === "failed") {
    toast.error(t("1 order failed"));
  }
  if (status === "cancelled") {
    toast.error(t("1 order was canceled"));
  }
  if (status === "completed") {
    toast.success(t("1 order completed"));
  }
};

const taskCreated = (data) => {
  if (data.task.hub_id === selectedHub.value) {
    getOrder(data.task.id)
      .then((res) => {
        orders.value.unshift(res);
        orders.value.filter((value, index, self) => {
          self.indexOf(value) === index;
        });
        let count = 0;
        audio.onended = function() {
          count++;
          if (count < 3) {
            audio.play();
          }
        };
        audio.volume = 1;
        audio.play();


      });
  }
};

const taskStatusUpdated = (data) => {
  let orderIds = orders.value.map((order) => order.id);
  if (orderIds.includes(data.task.id)) {

    updatedStatusOrder(data);

  }
};

const taskUpdated = (data) => {
  let orderIds = orders.value.map(order => order.id);
  if (orderIds.includes(data.id)) {
    promiseGetData();
  }
};

const taskClaimedandAssigned = (data) => {

  let orderIds = orders.value.map((order) => order.id);

  if (data.task.original_task_id && orderIds.includes(data.task.original_task_id)) {

    getOrder(data.task.original_task_id)
      .then((res) => {

        if (selectedOrder.value?.id === data.task.original_task_id) {
          dispatch("connect/setSelectedOrder", res);
        }

        orders.value.filter((x) => {
          if (x.id === data.task.original_task_id) {
            Object.keys(res).forEach((key) => {
              x[key] = res[key];
            });
          }
          return x;
        });
      });
  }
};

onMounted(() => {
  getData();
  listener();
});

onUnmounted(() => {
  stopListener();
});

watch(selectedHub, () => {
  setSelectedOrder(null);
  getData();
});

watch(orders, () => {
  let notificationCount = orders.value.filter(order => order.is_scheduled && (!order.company_approved_at || !order.company_pre_approved_at)).length;
  dispatch("connect/setTabNotificationCount", { name: "PlannedOrderList", notificationCount });
}, { deep: true });

emitter.on("openSelectedMergedOrder", (id) => {

  let selectedOrder = orders.value.find((order) => Number(order.integration_id) === Number(id));

  if (!selectedOrder) return;

  dispatch("connect/setSelectedOrder", JSON.parse(JSON.stringify(selectedOrder)));
});


emitter.on("connect-refresh", () => {
  promiseGetData();
});

emitter.on("order.confirmation.proccess", ({ id, loading }) => {
  if (selectedOrder.value?.id === id) {
    dispatch("connect/setSelectedOrder", {
      ...selectedOrder.value,
      loading
    });
  }
  orders.value.filter((x) => {
    if (x.id === id) {
      x.loading = loading;
    }
    return x;
  });
});

const listener = () => {
  Echo.private(companyChannel.value).listen(".task.created", taskCreated);
  Echo.private(companyChannel.value).listen(".task.status.updated", taskStatusUpdated);
  Echo.private(companyChannel.value).listen(".task.updated", taskUpdated);
  Echo.private(companyChannel.value).listen(".task.claimed", taskClaimedandAssigned);
  Echo.private(companyChannel.value).listen(".task.assigned.to.courier", taskClaimedandAssigned);
};

const stopListener = () => {
  Echo.private(companyChannel.value).stopListening(".task.created");
  Echo.private(companyChannel.value).stopListening(".task.status.updated");
  Echo.private(companyChannel.value).stopListening(".task.updated");
  Echo.private(companyChannel.value).stopListening(".task.claimed");
  Echo.private(companyChannel.value).stopListening(".task.assigned.to.courier");
};


</script>

<template>
  <div class="w-full h-full flex flex-col overflow-hidden">
    <TabButtons />
    <div class="h-full flex-grow overflow-hidden">
      <keep-alive>
        <component :is="activeOrderTab.name" :orders="orders" />
      </keep-alive>
    </div>
  </div>

</template>
