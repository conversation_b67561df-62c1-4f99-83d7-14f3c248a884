<script setup>
import dayjs from "dayjs";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const emits = defineEmits(["onSelect"])

const props = defineProps({
  order: {
    type: [Object, null],
    default: () => {
    }
  },
  selectedOrder: {
    type: [Object, null],
    default: () => {
    }
  }
})

const onSelect = () => {
  emits("onSelect", props.order)
}

</script>

<template>

    <div @click="onSelect()"
         class="grow border-t border-b border-slate-300 drop-shadow px-3 py-4 text-slate-700 cursor-pointer h-32 my-2 relative"
         :class="[
           selectedOrder?.id === order.id
           ?  order?.company_approved_at
              ? 'bg-slate-100 border-r-2'
              : 'bg-red-200 border-r-2'
           : order?.company_approved_at
              ? 'bg-white'
              : order?.company_pre_approved_at
                ? 'waiting-approval-order'
                : 'bg-red-100'
    ]"
    >
      <transition
          enter-active-class="ease-out duration-300"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="ease-in duration-200"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
      >
        <div
            v-if="order?.loading"
            class="absolute left-0 top-0 z-30 w-full h-full flex items-center justify-center bg-white bg-opacity-75"
        >
          <img src="@/assets/images/loading.svg" class="h-3" alt/>
        </div>
      </transition>
      <div class="flex items-center justify-between pb-3">
        <div class="font-bold text-lg">#{{ order?.integration_id }}</div>
        <div class="flex items-center">
          <div
              v-if="order.merged_task_id && Number(order.integration_id) !== order.merged_task_id "
              class="py-0.5 px-1 text-white rounded text-xxs bg-slate-700 cursor-pointer mr-1"
          >
            <FontAwesomeIcon icon="arrows-split-up-and-left"/>
          </div>
          <OrderStatus
              :preConfirmed="order?.company_pre_approved_at"
              :confirmed="order?.company_approved_at"
              :statusCategorySlug="order?.status_category_slug"
          />
        </div>
      </div>
      <div class="flex items-center justify-between pb-1.5">
        <div class="flex items-center">
          <div class="mr-1">
            <FontAwesomeIcon icon="user"/>
          </div>
          <div class="font-light text-xs">{{ order?.destination_name }}</div>
        </div>
        <div class="flex items-center">
          <div v-if="order?.is_scheduled" class="mr-2  px-2 bg-slate-50 rounded text-slate-700 font-bold text-xxs">
            Planlı
          </div>
          <div class="font-bold text-xs">
            {{ dayjs(order?.starts_at).format('HH:mm') }}
          </div>
        </div>
      </div>
      <div class="border-b border-slate-300"/>
      <div class="flex items-center justify-between pt-3">
        <div class="grow flex items-center mr-2">
          <div class="mr-1">
            <FontAwesomeIcon icon="cash-register"/>
          </div>
          <div class="truncate">
            {{
              order.payment_type_description?.length > 24 ? order.payment_type_description?.substring(0, 24) + "..." : order.payment_type_description
            }}
          </div>
        </div>
        <div v-if="order.provider?.icon_url" class="flex-shrink">
          <img :src="order.provider?.icon_url" class="h-6 w-6">
        </div>
      </div>
    </div>


</template>
