<script setup>

import {useI18n} from "vue-i18n";

const {t} = useI18n()

const props = defineProps({
  preConfirmed: {type: String || Date},
  confirmed: {type: String || Date},
  statusCategorySlug: {type: String},
  size: {type: String, default: "medium"}
})


const getStatus = (preConfirmed, confirmed, statusCategorySlug) => {
  let status = null
  if (!preConfirmed) {
    status = t('Pending Pre-Approval')
  } else if (!confirmed && statusCategorySlug === 'created') {
    status = t('Waiting for approval')
  } else {
    if (statusCategorySlug === 'on_delivery') {
      status = t('On Delivery')
    } else if (statusCategorySlug === "completed") {
      status = t('Completed')
    } else if (statusCategorySlug === "failed") {
      status = t('Failed')
    } else if (statusCategorySlug === "cancelled") {
      status = t('Cancelled')
    } else {
      status = t('Getting Ready')
    }
  }

  return status;
}

const getBackgroundColor = (confirmed, statusCategorySlug) => {


  let backgroundColor = null

  let sizeClass = "py-1 px-1.5 text-xs"

  if (props.size === "small") {
    sizeClass = "py-0.5 px-1 text-xxs"
  }

  if (!confirmed) {
    backgroundColor = "bg-red-600"
  } else {

    if (statusCategorySlug === 'on_delivery') {
      backgroundColor = "bg-indigo-600"
    } else if (statusCategorySlug === "completed") {
      backgroundColor = "bg-lime-500"
    } else if (statusCategorySlug === "failed" || statusCategorySlug === "cancelled") {
      backgroundColor = "bg-slate-300"
    } else {
      backgroundColor = "bg-yellow-500"

    }
  }

  return backgroundColor + " " + sizeClass;
}

</script>

<template>
  <div
      class="text-white rounded"
      :class="[getBackgroundColor(confirmed,statusCategorySlug)]"
  >
    {{ getStatus(preConfirmed, confirmed, statusCategorySlug) }}
  </div>
</template>
