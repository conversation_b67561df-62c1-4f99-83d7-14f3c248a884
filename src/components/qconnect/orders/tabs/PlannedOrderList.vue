<script setup>
import OrderItem from "@/components/qconnect/orders/OrderItem.vue";
import {computed, onActivated} from "vue";
import {useStore} from "vuex";

const {dispatch} = useStore()

const props = defineProps({
  orders: {type: Array, default: () => []},
})

const scheduleOrders = computed(() => {
  return props.orders
      .filter(order => order.is_scheduled && (!order.company_approved_at || !order.company_pre_approved_at))
      .sort((a,b) =>(b.company_approved_at===null)-(a.company_approved_at===null) || -(a.id>b.id)||+(a.id<b.id))
})


onActivated(() => {
  scheduleOrders.value[0] && dispatch("connect/setSelectedOrder", JSON.parse(JSON.stringify(scheduleOrders.value[0])))
})

</script>

<template>
  <div class="w-full h-full overflow-auto">

    <OrderItem v-if="scheduleOrders.length > 0" v-for="order in scheduleOrders" :order="order" :key="order.id"/>
    <div v-else
         class="border-b border-white w-full h-full  flex items-center justify-center font-bold text-xxs px-2 text-slate-300">
      {{ $t('You Do Not Have a Planned Order') }}
    </div>
  </div>

</template>
