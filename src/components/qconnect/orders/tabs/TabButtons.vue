<script setup>
import {useI18n} from "vue-i18n";
import {useStore} from "vuex";
import {computed} from "vue";

const {t} = useI18n()
const {getters, dispatch} = useStore();

const orderTabs = computed(() => getters["connect/currentlyOpenOrderTabs"])
const activeOrderTab = computed(() => getters["connect/activeOrderTab"])

function openOrderTab(tab) {
    dispatch("connect/setActiveOrderTab", tab);
}

</script>

<template>
  <div class="flex items-center w-full border-b border-slate-300">
    <div
        v-for="(tab,index) in orderTabs"
        class="grow cursor-pointer"
        :class="index === 0 ? 'border-r border-slate-300' : null"
    >
      <div
          @click="openOrderTab(tab)"
          class="text-md font-bold px-2.5 py-3 flex "
          :class="activeOrderTab.title === tab.title ? 'bg-white text-slate-700' : 'text-slate-300'"
      >
        {{ $t(tab.title) }}

        <div
            v-if="tab.notificationCount"
            class="ml-1 w-4 h-4 rounded-full border border-red-600 text-red-600 bg-red-100 text-xxxs flex items-center justify-center">
          {{ tab.notificationCount }}
        </div>

      </div>
    </div>
  </div>
</template>
