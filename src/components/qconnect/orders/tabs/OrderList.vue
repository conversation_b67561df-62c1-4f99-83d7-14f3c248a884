<script setup>
import OrderItem from "@/components/qconnect/orders/OrderItem.vue";
import {computed, onActivated} from "vue";
import {useStore} from "vuex";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const {dispatch, getters} = useStore()

const props = defineProps({
  orders: {type: Array, default: () => []},
})

const selectedOrder = computed(() => getters["connect/selectedOrder"])

const data = computed(() => {
  let _data = JSON.parse(JSON.stringify(props.orders))
      .filter(order => {
        if (!order.is_scheduled) {
          return order;
        } else {
          if (order.company_pre_approved_at && order.company_approved_at) {
            return order;
          }
        }
      })

  const normalTasks = _data.filter(x => !x.merged_task_id)
  const _mergedTasks = makeGroup(_data.filter(x => x.merged_task_id), "merged_task_id")

  let arr = []

  Object.values(_mergedTasks).forEach(x => arr.push(x))

  let newData = arr.concat(normalTasks)

  onSort(newData)

  return newData
})

onActivated(() => {
  data.value.length > 0 && dispatch("connect/setSelectedOrder", JSON.parse(JSON.stringify(data.value[0])))
})

const onSort = (array) => {
  array.sort((a, b) => {

    if (Array.isArray(a)) {

      a.sort((x, y) => y.id - x.id)

      return b.id - a[a.length - 1].id
    }

    if (Array.isArray(b)) {

      b.sort((x, y) => y.id - x.id)

      return b[b.length - 1].id - a.id
    }

    return b.id - a.id
  })
}

const makeGroup = (array, _groupName) => {
  let newArray = array.reduce((group, item) => {
    const groupName = item[_groupName];
    group[groupName] = group[groupName] ?? [];
    group[groupName].push(item);
    return group;
  }, {});

  return newArray;
}


const onSelectOrder = (order) => {
  if (selectedOrder.value?.id !== order.id) {
    dispatch("connect/setSelectedOrder", JSON.parse(JSON.stringify(order)))
  }
}





</script>

<template>
  <div class="w-full h-full overflow-auto">
    <template v-if="data.length > 0" v-for="task in data">
      <OrderItem
          @onSelect="onSelectOrder"
          :selectedOrder="selectedOrder"
          v-if="!Array.isArray(task)" :order="task" :key="task.id"/>
      <div v-else-if="task.length === 1">
        <OrderItem
            @onSelect="onSelectOrder"
            :selectedOrder="selectedOrder"
            :order="task[0]"
        />
      </div>
      <div v-else class="flex -my-2">
        <div class="flex items-center justify-center bg-slate-700 text-white my-2 drop-shadow px-1">
          <FontAwesomeIcon icon="arrows-turn-right"/>
        </div>
        <div class="flex-col w-full">
          <OrderItem
              @onSelect="onSelectOrder"
              :selectedOrder="selectedOrder"
              v-for="order in task" :order="order" :key="task.id"
          />
        </div>
      </div>
    </template>
    <div v-else
         class="border-b border-white w-full h-full  flex items-center justify-center font-bold text-xxs px-2 text-slate-300">
      {{ $t('You Have No Active Orders') }}
    </div>
  </div>

</template>
