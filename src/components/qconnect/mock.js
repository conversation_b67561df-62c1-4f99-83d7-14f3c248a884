export const dummyData = {
    "data": [

    {
        "id": 18342,
        "integration_id": "18342",
        "tracking_code": "QD5-23172AKVTRI",
        "type_slug": "pool",
        "obligation_slug": "mandatory",
        "status_id": 35,
        "status_category_slug": "created",
        "plan_id": null,
        "company_id": 5,
        "owner_id": 621,
        "courier_id": null,
        "team_id": null,
        "hub_id": 491,
        "origin_hub_id": 491,
        "destination_hub_id": null,
        "origin_address": "Esentepe, Ecza Soka\u011f\u0131 No:1, 34394 \u015ei\u015fli\/\u0130stanbul, T\u00fcrkiye",
        "origin_address_building": null,
        "origin_address_floor": null,
        "origin_address_apartment": null,
        "origin_phone": null,
        "origin_phone_country": null,
        "origin_name": "Kanyon Avm",
        "origin_lat": 41.078517,
        "origin_lng": 29.01088,
        "destination_address": "<PERSON>, <PERSON><PERSON>\u00fcy\u00fckdere Cd. No:57, 34415 K\u00e2\u011f\u0131thane\/\u0130stanbul, T\u00fcrkiye",
        "destination_address_building": null,
        "destination_address_floor": null,
        "destination_address_apartment": null,
        "destination_phone": "+905431992231",
        "destination_phone_country": "TR",
        "destination_name": "Test 2",
        "destination_lat": 41.091906,
        "destination_lng": 29.004398,
        "is_origin_resolved": false,
        "is_destination_resolved": false,
        "slot": null,
        "starts_at": "2023-06-22T08:04:00.000000Z",
        "ends_at": "2023-06-22T08:49:00.000000Z",
        "quantity": 2,
        "service_time": 0,
        "response_time": 0,
        "requires_delivery_code": false,
        "requires_delivery_code_mandatory": false,
        "requires_pod_photo": false,
        "requires_pod_photo_mandatory": false,
        "requirements_delivery_code": null,
        "requirements_pod_photo": null,
        "notes": null,
        "recipient_notes": null,
        "channel": "ui",
        "status_at": "2023-06-22T08:04:28.000000Z",
        "seen_at": null,
        "claimed_at": null,
        "created_at": "2023-06-22T08:04:28.000000Z",
        "updated_at": "2023-06-22T08:04:28.000000Z",
        "route_order": 0,
        "priority": 0,
        "contents": [],
        "payment_type": "credit_card",
        "foreign_carrier": null,
        "handled_by_foreign_carrier": false,
        "rating": 0,
        "comment": null,
        "distance": 2112,
        "duration": 146,
        "was_finalized_duly": true,
        "was_delayed": false,
        "started_at": null,
        "ended_at": null,
        "starts_at_on_route": null,
        "ends_at_on_route": null,
        "action_slug": "drop_off",
        "meta": {
            "slot_close_data": null,
            "assignment_options": {
                "type": "pool",
                "queue": false,
                "team_id": null,
                "courier_id": null,
                "obligation": "mandatory",
                "distribution": "balanced",
                "min_capacity": null,
                "courier_duty_status": "all",
                "courier_shift_status": "all",
                "courier_online_status": "all"
            },
            "courier_finder_options": {
                "lat": 41.078517,
                "lng": 29.01088,
                "hub_id": 491,
                "search": null,
                "team_id": null,
                "duty_status": "all",
                "distribution": "balanced",
                "shift_status": "all",
                "online_status": "all",
                "for_acceptance": false,
                "consider_capacity": false,
                "excluded_courier_ids": []
            }
        },
        "slot_starts_at": "2023-06-22T08:04:00.000000Z",
        "slot_ends_at": "2023-06-22T08:49:00.000000Z",
        "requires_dont_ring_the_bell": false,
        "requires_contactless": false,
        "confirmed_at": "2023-06-22T08:04:28.000000Z",
        "unlocked_at": "2023-06-22T08:04:28.000000Z",
        "task_provider_id": null,
        "task_provider_courier": null,
        "courier_type": "company",
        "company_approved_at": "2023-06-22T08:04:28.000000Z",
        "amount": "378.00",
        "original_company_id": null,
        "original_task_id": null,
        "is_waiting_to_be_accepted": false,
        "origin_address_description": null,
        "destination_address_description": null,
        "discounted_amount": "378.00",
        "parent_id": null,
        "company_pre_approved_at": "2023-06-22T08:04:28.000000Z",
        "is_scheduled": false,
        "timezone": "Europe\/Istanbul",
        "kuik_task_id": null,
        "path": "edvyF}dapD]iHc@}@yW`OaMjLgDlBuWlGyS~BBtCdFD?l@",
        "preparation_time": 0,
        "merged_task_id": null,
        "original_hub_id": null,
        "payment_type_description": "Kap\u0131da Kredi Kart\u0131",
        "tags": [],
        "status": {
            "id": 35,
            "slug": "created",
            "name": "Olu\u015fturuldu"
        },
        "courier": null,
        "status_logs": [
            {
                "id": 40103,
                "task_id": 18342,
                "role_slug": "customer",
                "user_id": 621,
                "channel": "ui",
                "status_id": 35,
                "status_category_slug": "created",
                "notes": null,
                "lat": null,
                "lng": null,
                "geohash": null,
                "created_at": "2023-06-22T08:04:28.000000Z",
                "updated_at": "2023-06-22T08:04:28.000000Z",
                "courier_id": null,
                "foreign_courier": null,
                "context": "assignment_pool",
                "courier": {
                    "id": null,
                    "name": null,
                    "avatar_url": "https:\/\/api.dev.qdelivery.app\/images\/avatar.png"
                },
                "status": {
                    "id": 35,
                    "slug": "created",
                    "status_category_slug": "created",
                    "name": "Olu\u015fturuldu"
                }
            }
        ],
        "hub": {
            "id": 491,
            "integration_id": "Kanyon Avm",
            "name": "Kanyon Avm"
        },
        "team": null,
        "provider": null,
        "items": [
            {
                "id": 7546,
                "status_slug": "pending",
                "failure_slug": null,
                "company_id": 5,
                "task_id": 18342,
                "integration_id": "35",
                "name": "Burger3",
                "description": "iyi pi\u015fsin",
                "notes": null,
                "reason_for_failure": null,
                "quantity": 2,
                "amount": 378,
                "created_at": "2023-06-22T08:04:28.000000Z",
                "updated_at": "2023-06-22T08:04:28.000000Z",
                "currency": "TRY",
                "delivered_quantity": null
            }
        ],
        "transactions": [],
        "assignment_queue": null,
        "requirements_pod_photo_url": null,
        "action": {
            "slug": "drop_off",
            "name": "Teslim Etme"
        },
        "type": {
            "slug": "pool",
            "name": "Havuz"
        },
        "obligation": {
            "slug": "mandatory",
            "name": "Zorunlu"
        }
    },
    {
        "id": 18340,
        "integration_id": "18340",
        "tracking_code": "QD5-23172Z4QBRU",
        "type_slug": "pool",
        "obligation_slug": "mandatory",
        "status_id": 38,
        "status_category_slug": "in_progress",
        "plan_id": null,
        "company_id": 5,
        "owner_id": 621,
        "courier_id": null,
        "team_id": null,
        "hub_id": 491,
        "origin_hub_id": 491,
        "destination_hub_id": null,
        "origin_address": "Esentepe, Ecza Soka\u011f\u0131 No:1, 34394 \u015ei\u015fli\/\u0130stanbul, T\u00fcrkiye",
        "origin_address_building": null,
        "origin_address_floor": null,
        "origin_address_apartment": null,
        "origin_phone": null,
        "origin_phone_country": null,
        "origin_name": "Kanyon Avm",
        "origin_lat": 41.078517,
        "origin_lng": 29.01088,
        "destination_address": "Emniyetevleri, Eski B\u00fcy\u00fckdere Cd. No:1, 34415 K\u00e2\u011f\u0131thane\/\u0130stanbul, T\u00fcrkiye",
        "destination_address_building": null,
        "destination_address_floor": null,
        "destination_address_apartment": null,
        "destination_phone": "+905431000004",
        "destination_phone_country": "TR",
        "destination_name": "Test Test",
        "destination_lat": 41.034637,
        "destination_lng": 29.02687,
        "is_origin_resolved": false,
        "is_destination_resolved": false,
        "slot": null,
        "starts_at": "2023-06-22T08:03:00.000000Z",
        "ends_at": "2023-06-22T08:48:00.000000Z",
        "quantity": 2,
        "service_time": 0,
        "response_time": 0,
        "requires_delivery_code": false,
        "requires_delivery_code_mandatory": false,
        "requires_pod_photo": false,
        "requires_pod_photo_mandatory": false,
        "requirements_delivery_code": null,
        "requirements_pod_photo": null,
        "notes": null,
        "recipient_notes": null,
        "channel": "ui",
        "status_at": "2023-06-22T08:06:26.000000Z",
        "seen_at": null,
        "claimed_at": null,
        "created_at": "2023-06-22T08:03:37.000000Z",
        "updated_at": "2023-06-22T08:06:26.000000Z",
        "route_order": 0,
        "priority": 0,
        "contents": [],
        "payment_type": "credit_card",
        "foreign_carrier": null,
        "handled_by_foreign_carrier": false,
        "rating": 0,
        "comment": null,
        "distance": 2309,
        "duration": 159,
        "was_finalized_duly": true,
        "was_delayed": false,
        "started_at": null,
        "ended_at": null,
        "starts_at_on_route": null,
        "ends_at_on_route": null,
        "action_slug": "drop_off",
        "meta": {
            "slot_close_data": null,
            "assignment_options": {
                "type": "pool",
                "queue": false,
                "team_id": null,
                "courier_id": null,
                "obligation": "mandatory",
                "distribution": "balanced",
                "min_capacity": null,
                "courier_duty_status": "all",
                "courier_shift_status": "all",
                "courier_online_status": "all"
            },
            "courier_finder_options": {
                "lat": 41.078517,
                "lng": 29.01088,
                "hub_id": 491,
                "search": null,
                "team_id": null,
                "duty_status": "all",
                "distribution": "balanced",
                "shift_status": "all",
                "online_status": "all",
                "for_acceptance": false,
                "consider_capacity": false,
                "excluded_courier_ids": []
            }
        },
        "slot_starts_at": "2023-06-22T08:03:00.000000Z",
        "slot_ends_at": "2023-06-22T08:48:00.000000Z",
        "requires_dont_ring_the_bell": false,
        "requires_contactless": false,
        "confirmed_at": "2023-06-22T08:03:37.000000Z",
        "unlocked_at": "2023-06-22T08:03:37.000000Z",
        "task_provider_id": null,
        "task_provider_courier": {
            "name": "Test Driver",
            "email": null,
            "phone": "+905431000008",
            "avatar": "avatars\/4emY06DzQ1UzmKWNyILLvF50FaTiUEC8y93cas57.jpg",
            "locale": "en",
            "tag_ids": null,
            "password": null,
            "role_slug": "courier",
            "phone_country": "TR",
            "integration_id": null,
            "courier_location": {
                "id": 1856,
                "lat": 40.972092,
                "lng": 29.109986,
                "on_duty": false,
                "is_online": true,
                "courier_id": 614,
                "is_working": true,
                "last_seen_at": "2023-06-22T08:06:19.000000Z"
            },
            "terms_accepted_at": "2023-01-18T14:06:11.000000Z"
        },
        "courier_type": "kuik",
        "company_approved_at": "2023-06-22T08:03:37.000000Z",
        "amount": "300.00",
        "original_company_id": null,
        "original_task_id": null,
        "is_waiting_to_be_accepted": false,
        "origin_address_description": null,
        "destination_address_description": null,
        "discounted_amount": "300.00",
        "parent_id": null,
        "company_pre_approved_at": "2023-06-22T08:03:37.000000Z",
        "is_scheduled": false,
        "timezone": "Europe\/Istanbul",
        "kuik_task_id": 18341,
        "path": "edvyF}dapD]iHKm@WOuM`IcI~DyBdBgIdIgDlB{@Yk@yB[i@a@qBCuADaBN{@fCsCnCpHrBbHDp@Kb@e@f@sDfAOx@KdEPnAdAj@lEUJWU_F",
        "preparation_time": 0,
        "merged_task_id": null,
        "original_hub_id": null,
        "payment_type_description": "Kap\u0131da Kredi Kart\u0131",
        "tags": [],
        "status": {
            "id": 38,
            "slug": "in_progress",
            "name": "\u0130\u015fleniyor"
        },
        "courier": null,
        "status_logs": [
            {
                "id": 40100,
                "task_id": 18340,
                "role_slug": "customer",
                "user_id": 621,
                "channel": "ui",
                "status_id": 35,
                "status_category_slug": "created",
                "notes": null,
                "lat": null,
                "lng": null,
                "geohash": null,
                "created_at": "2023-06-22T08:03:37.000000Z",
                "updated_at": "2023-06-22T08:03:37.000000Z",
                "courier_id": null,
                "foreign_courier": null,
                "context": "assignment_pool",
                "courier": {
                    "id": null,
                    "name": null,
                    "avatar_url": "https:\/\/api.dev.qdelivery.app\/images\/avatar.png"
                },
                "status": {
                    "id": 35,
                    "slug": "created",
                    "status_category_slug": "created",
                    "name": "Olu\u015fturuldu"
                }
            },
            {
                "id": 40107,
                "task_id": 18340,
                "role_slug": "courier",
                "user_id": 614,
                "channel": "courier",
                "status_id": 38,
                "status_category_slug": "in_progress",
                "notes": null,
                "lat": null,
                "lng": null,
                "geohash": null,
                "created_at": "2023-06-22T08:06:26.000000Z",
                "updated_at": "2023-06-22T08:06:26.000000Z",
                "courier_id": null,
                "foreign_courier": {
                    "name": "Test Driver",
                    "email": null,
                    "phone": "+905431000008",
                    "avatar": "avatars\/4emY06DzQ1UzmKWNyILLvF50FaTiUEC8y93cas57.jpg",
                    "locale": "en",
                    "tag_ids": null,
                    "password": null,
                    "role_slug": "courier",
                    "phone_country": "TR",
                    "integration_id": null,
                    "courier_location": {
                        "id": 1856,
                        "lat": 40.972092,
                        "lng": 29.109986,
                        "on_duty": false,
                        "is_online": true,
                        "courier_id": 614,
                        "is_working": true,
                        "last_seen_at": "2023-06-22T08:06:19.000000Z"
                    },
                    "terms_accepted_at": "2023-01-18T14:06:11.000000Z"
                },
                "context": null,
                "courier": {
                    "id": null,
                    "name": null,
                    "avatar_url": "https:\/\/api.dev.qdelivery.app\/images\/avatar.png"
                },
                "status": {
                    "id": 38,
                    "slug": "in_progress",
                    "status_category_slug": "in_progress",
                    "name": "\u0130\u015fleniyor"
                }
            }
        ],
        "hub": {
            "id": 491,
            "integration_id": "Kanyon Avm",
            "name": "Kanyon Avm"
        },
        "team": null,
        "provider": null,
        "items": [
            {
                "id": 7544,
                "status_slug": "pending",
                "failure_slug": null,
                "company_id": 5,
                "task_id": 18340,
                "integration_id": "34",
                "name": "Burger2",
                "description": "az pi\u015fsin",
                "notes": null,
                "reason_for_failure": null,
                "quantity": 2,
                "amount": 300,
                "created_at": "2023-06-22T08:03:37.000000Z",
                "updated_at": "2023-06-22T08:03:37.000000Z",
                "currency": "TRY",
                "delivered_quantity": null
            }
        ],
        "transactions": [
            {
                "uuid": "99785518-611a-41c6-bb19-7c7551c4f219",
                "sender_wallet_id": 3,
                "sender": {
                    "uuid": "97692abb-69a5-4ecd-a388-d14b10aab535",
                    "name": "NewFranks - TRY",
                    "company_id": 5,
                    "company": {
                        "id": 5,
                        "integration_id": null,
                        "customer_id": 621,
                        "name": "NewFranks",
                        "short_name": "NF",
                        "email": "<EMAIL>",
                        "country_code": "TR",
                        "timezone": "Europe\/Istanbul",
                        "unit_slug": "metric",
                        "date_format_slug": "european",
                        "is_active": true,
                        "address": null,
                        "tax_id": null,
                        "tax_office": null,
                        "currency_unit": "TRY",
                        "brand_logo": null,
                        "primary_color": null,
                        "secondary_color": null,
                        "token": "108|NKe95FJA8zWF60rydGzMIlauY27xln1Ltlpk6lRm",
                        "sms_enabled": false,
                        "mail_enabled": false,
                        "created_at": "2022-05-17T07:01:07.000000Z",
                        "updated_at": "2023-01-31T07:55:26.000000Z",
                        "deleted_at": null,
                        "gdpr": true,
                        "is_maximum_delivery_distance": false,
                        "maximum_delivery_distance": null,
                        "auto_pool_slot": false,
                        "obey_status_order": false,
                        "call_center_enabled": false,
                        "call_center_options": null,
                        "lock_tasks": true,
                        "company_product_type": "qd-kuik",
                        "allow_batch_in_progress": false,
                        "allow_batch_on_delivery": false,
                        "calculate_accurate_distance": false,
                        "task_live_stream": false,
                        "call_couriers_for_delayed_action": false,
                        "voice_mail_ids": null,
                        "voice_mail_provider": null,
                        "courier_location_broadcast_limit": null,
                        "courier_location_broadcast_limit_onduty": null,
                        "show_destination": true,
                        "company_region_id": null,
                        "mobile_location_distance_filter": 200,
                        "mobile_location_stop_timeout": 5,
                        "country": {
                            "code": "TR",
                            "name": "Turkey",
                            "phone_code": 90
                        },
                        "measurement_unit": {
                            "slug": "metric",
                            "name": "Metrik"
                        },
                        "date_format": {
                            "slug": "european",
                            "format": "DD.MM.YYYY HH:mm",
                            "format2": "d.m.Y H:i",
                            "format3": "dd.MM.yyyy HH:mm"
                        },
                        "brand_logo_url": null
                    },
                    "currency_unit": "TRY",
                    "amount": "48800.00",
                    "amount_int": 7689734,
                    "iban": "TR24 0006 ************** 6252 56",
                    "created_at": "2022-10-03T06:44:02.000000Z",
                    "updated_at": "2023-06-22T08:03:47.000000Z"
                },
                "sender_company_id": 5,
                "receiver_wallet_id": 34,
                "receiver": {
                    "uuid": "97692abb-a757-4bef-b798-150d7dde2c4b",
                    "name": "QD F\u0130NANS - TRY",
                    "company_id": 79,
                    "company": {
                        "id": 79,
                        "integration_id": null,
                        "customer_id": 1829,
                        "name": "QD F\u0130NANS",
                        "short_name": "QD F\u0130NANS",
                        "email": "<EMAIL>",
                        "country_code": "TR",
                        "timezone": "Europe\/Istanbul",
                        "unit_slug": "metric",
                        "date_format_slug": "european",
                        "is_active": true,
                        "address": null,
                        "tax_id": null,
                        "tax_office": null,
                        "currency_unit": "TRY",
                        "brand_logo": null,
                        "primary_color": null,
                        "secondary_color": null,
                        "token": null,
                        "sms_enabled": false,
                        "mail_enabled": false,
                        "created_at": "2022-09-28T07:14:54.000000Z",
                        "updated_at": "2022-09-28T07:16:45.000000Z",
                        "deleted_at": null,
                        "gdpr": true,
                        "is_maximum_delivery_distance": false,
                        "maximum_delivery_distance": null,
                        "auto_pool_slot": false,
                        "obey_status_order": false,
                        "call_center_enabled": false,
                        "call_center_options": null,
                        "lock_tasks": false,
                        "company_product_type": "qd",
                        "allow_batch_in_progress": false,
                        "allow_batch_on_delivery": false,
                        "calculate_accurate_distance": false,
                        "task_live_stream": false,
                        "call_couriers_for_delayed_action": false,
                        "voice_mail_ids": null,
                        "voice_mail_provider": null,
                        "courier_location_broadcast_limit": null,
                        "courier_location_broadcast_limit_onduty": null,
                        "show_destination": true,
                        "company_region_id": null,
                        "mobile_location_distance_filter": 200,
                        "mobile_location_stop_timeout": 5,
                        "country": {
                            "code": "TR",
                            "name": "Turkey",
                            "phone_code": 90
                        },
                        "measurement_unit": {
                            "slug": "metric",
                            "name": "Metrik"
                        },
                        "date_format": {
                            "slug": "european",
                            "format": "DD.MM.YYYY HH:mm",
                            "format2": "d.m.Y H:i",
                            "format3": "dd.MM.yyyy HH:mm"
                        },
                        "brand_logo_url": null
                    },
                    "currency_unit": "TRY",
                    "amount": "394627.19",
                    "amount_int": 39462703,
                    "iban": "**************************",
                    "created_at": "2022-10-03T06:44:03.000000Z",
                    "updated_at": "2023-06-22T08:03:47.000000Z"
                },
                "receiver_company_id": 79,
                "type": {
                    "slug": "delivery_fee",
                    "name": "Teslimat \u00dccreti"
                },
                "type_slug": "delivery_fee",
                "currency": {
                    "unit": "TRY",
                    "description": "T\u00fcrk Liras\u0131"
                },
                "currency_unit": "TRY",
                "amount": 20,
                "amount_int": 2000,
                "hub_id": 491,
                "task_id": 18340,
                "task_amount": "300.00",
                "task_discounted_amount": "300.00",
                "task_archived_at": null,
                "user_id": 621,
                "notes": null,
                "is_receiver": false,
                "approved_at": "2023-06-22T08:03:47.000000Z",
                "approved_by": null,
                "cancelled_at": null,
                "created_at": "2023-06-22T08:03:47.000000Z",
                "updated_at": "2023-06-22T08:03:47.000000Z"
            },
            {
                "uuid": "99785518-6f8d-431a-ab4f-24ede08779c3",
                "sender_wallet_id": 3,
                "sender": {
                    "uuid": "97692abb-69a5-4ecd-a388-d14b10aab535",
                    "name": "NewFranks - TRY",
                    "company_id": 5,
                    "company": {
                        "id": 5,
                        "integration_id": null,
                        "customer_id": 621,
                        "name": "NewFranks",
                        "short_name": "NF",
                        "email": "<EMAIL>",
                        "country_code": "TR",
                        "timezone": "Europe\/Istanbul",
                        "unit_slug": "metric",
                        "date_format_slug": "european",
                        "is_active": true,
                        "address": null,
                        "tax_id": null,
                        "tax_office": null,
                        "currency_unit": "TRY",
                        "brand_logo": null,
                        "primary_color": null,
                        "secondary_color": null,
                        "token": "108|NKe95FJA8zWF60rydGzMIlauY27xln1Ltlpk6lRm",
                        "sms_enabled": false,
                        "mail_enabled": false,
                        "created_at": "2022-05-17T07:01:07.000000Z",
                        "updated_at": "2023-01-31T07:55:26.000000Z",
                        "deleted_at": null,
                        "gdpr": true,
                        "is_maximum_delivery_distance": false,
                        "maximum_delivery_distance": null,
                        "auto_pool_slot": false,
                        "obey_status_order": false,
                        "call_center_enabled": false,
                        "call_center_options": null,
                        "lock_tasks": true,
                        "company_product_type": "qd-kuik",
                        "allow_batch_in_progress": false,
                        "allow_batch_on_delivery": false,
                        "calculate_accurate_distance": false,
                        "task_live_stream": false,
                        "call_couriers_for_delayed_action": false,
                        "voice_mail_ids": null,
                        "voice_mail_provider": null,
                        "courier_location_broadcast_limit": null,
                        "courier_location_broadcast_limit_onduty": null,
                        "show_destination": true,
                        "company_region_id": null,
                        "mobile_location_distance_filter": 200,
                        "mobile_location_stop_timeout": 5,
                        "country": {
                            "code": "TR",
                            "name": "Turkey",
                            "phone_code": 90
                        },
                        "measurement_unit": {
                            "slug": "metric",
                            "name": "Metrik"
                        },
                        "date_format": {
                            "slug": "european",
                            "format": "DD.MM.YYYY HH:mm",
                            "format2": "d.m.Y H:i",
                            "format3": "dd.MM.yyyy HH:mm"
                        },
                        "brand_logo_url": null
                    },
                    "currency_unit": "TRY",
                    "amount": "48800.00",
                    "amount_int": 7689734,
                    "iban": "TR24 0006 ************** 6252 56",
                    "created_at": "2022-10-03T06:44:02.000000Z",
                    "updated_at": "2023-06-22T08:03:47.000000Z"
                },
                "sender_company_id": 5,
                "receiver_wallet_id": 34,
                "receiver": {
                    "uuid": "97692abb-a757-4bef-b798-150d7dde2c4b",
                    "name": "QD F\u0130NANS - TRY",
                    "company_id": 79,
                    "company": {
                        "id": 79,
                        "integration_id": null,
                        "customer_id": 1829,
                        "name": "QD F\u0130NANS",
                        "short_name": "QD F\u0130NANS",
                        "email": "<EMAIL>",
                        "country_code": "TR",
                        "timezone": "Europe\/Istanbul",
                        "unit_slug": "metric",
                        "date_format_slug": "european",
                        "is_active": true,
                        "address": null,
                        "tax_id": null,
                        "tax_office": null,
                        "currency_unit": "TRY",
                        "brand_logo": null,
                        "primary_color": null,
                        "secondary_color": null,
                        "token": null,
                        "sms_enabled": false,
                        "mail_enabled": false,
                        "created_at": "2022-09-28T07:14:54.000000Z",
                        "updated_at": "2022-09-28T07:16:45.000000Z",
                        "deleted_at": null,
                        "gdpr": true,
                        "is_maximum_delivery_distance": false,
                        "maximum_delivery_distance": null,
                        "auto_pool_slot": false,
                        "obey_status_order": false,
                        "call_center_enabled": false,
                        "call_center_options": null,
                        "lock_tasks": false,
                        "company_product_type": "qd",
                        "allow_batch_in_progress": false,
                        "allow_batch_on_delivery": false,
                        "calculate_accurate_distance": false,
                        "task_live_stream": false,
                        "call_couriers_for_delayed_action": false,
                        "voice_mail_ids": null,
                        "voice_mail_provider": null,
                        "courier_location_broadcast_limit": null,
                        "courier_location_broadcast_limit_onduty": null,
                        "show_destination": true,
                        "company_region_id": null,
                        "mobile_location_distance_filter": 200,
                        "mobile_location_stop_timeout": 5,
                        "country": {
                            "code": "TR",
                            "name": "Turkey",
                            "phone_code": 90
                        },
                        "measurement_unit": {
                            "slug": "metric",
                            "name": "Metrik"
                        },
                        "date_format": {
                            "slug": "european",
                            "format": "DD.MM.YYYY HH:mm",
                            "format2": "d.m.Y H:i",
                            "format3": "dd.MM.yyyy HH:mm"
                        },
                        "brand_logo_url": null
                    },
                    "currency_unit": "TRY",
                    "amount": "394627.19",
                    "amount_int": 39462703,
                    "iban": "**************************",
                    "created_at": "2022-10-03T06:44:03.000000Z",
                    "updated_at": "2023-06-22T08:03:47.000000Z"
                },
                "receiver_company_id": 79,
                "type": {
                    "slug": "delivery_commission",
                    "name": "Teslimat Komisyonu"
                },
                "type_slug": "delivery_commission",
                "currency": {
                    "unit": "TRY",
                    "description": "T\u00fcrk Liras\u0131"
                },
                "currency_unit": "TRY",
                "amount": 9,
                "amount_int": 900,
                "hub_id": 491,
                "task_id": 18340,
                "task_amount": "300.00",
                "task_discounted_amount": "300.00",
                "task_archived_at": null,
                "user_id": 621,
                "notes": null,
                "is_receiver": false,
                "approved_at": "2023-06-22T08:03:47.000000Z",
                "approved_by": null,
                "cancelled_at": null,
                "created_at": "2023-06-22T08:03:47.000000Z",
                "updated_at": "2023-06-22T08:03:47.000000Z"
            }
        ],
        "assignment_queue": null,
        "requirements_pod_photo_url": null,
        "action": {
            "slug": "drop_off",
            "name": "Teslim Etme"
        },
        "type": {
            "slug": "pool",
            "name": "Havuz"
        },
        "obligation": {
            "slug": "mandatory",
            "name": "Zorunlu"
        }
    }
]
}
