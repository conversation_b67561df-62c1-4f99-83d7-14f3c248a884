<script setup>
import Check from "@/components/ui/icons/Check.vue";
import ToggleClock from "@/components/qconnect/integrations/ToggleClock.vue";
import {computed, inject, ref} from "vue";
import {useStore} from "vuex";

const {getters} = useStore()
const api = inject("api")

const selectedHub = computed(() => getters["operations/selectedHub"].id);

const props = defineProps({
  item: {
    type: Object, default: () => {
    }
  },
  bulkUpdateStoreStatusLoading: {type: Boolean, default: false},
  bulkAutoAcceptLoading: {type: Boolean, default: false}
})

const storeStatusUpdateloading = ref(false)
const autoAcceptLoading = ref(false)
const minute = ref()
// const minutes = [5, 15, 30, 45, 60]

const onAutoAccept = (param) => {
  props.item.loading = true
  let body = {
    auto_accept: param,
    hub_id: selectedHub.value,
    integration_ids: [props.item.id]
  }
  api.patch(`customer/qconnect-integration/bulk-update`, body)
      .catch(() => props.item.is_store_open = !param)
      // .finally(() => props.item.loading = false)
}

const onStoreStatusUpdate = (param) => {
  props.item.loading = true
  let body = {
    is_store_open: param,
    hub_id: selectedHub.value,
    integration_ids: [props.item.id]
  }
  if (minute.value) {
    body.toggle_after_minutes = minute.value
  }
  api.patch(`customer/qconnect-integration/bulk-update`, body)
      .then(() => minute.value = null)
      .catch(() => props.item.is_store_open = !param)
      // .finally(() => props.item.loading = false)
}


</script>

<template>
  <div class="col-span-3 flex items-center">
    <img v-show="item.task_provider.icon_url" :src="item.task_provider.icon_url" width="24"/>
    <div class="text-sm font-bold text-left ml-1.5 truncate">{{ item.name ? item.name   : item.provider.name }}</div>
  </div>
  <div class="col-span-1 flex items-center">
    <el-switch
        @change="onAutoAccept"
        v-model="item.auto_accept"
        inline-prompt
        :active-icon="Check"
        :disabled="bulkAutoAcceptLoading || autoAcceptLoading"
        :loading="autoAcceptLoading"
    >
    </el-switch>
  </div>
  <div class="col-span-2 flex items-center justify-end">
    <ToggleClock v-model="minute"/>
    <el-switch
        @change="onStoreStatusUpdate"
        v-model="item.is_store_open"
        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
        :disabled="bulkUpdateStoreStatusLoading || item.loading"
        :loading="item.loading"
    >
    </el-switch>
  </div>
</template>
