<script setup>
import IntegrationItem from "@/components/qconnect/integrations/IntegrationItem.vue";
import ToggleClock from "@/components/qconnect/integrations/ToggleClock.vue";
import {computed, inject, onMounted, ref, watch} from "vue";
import {useI18n} from "vue-i18n";
import {useStore} from "vuex";

const {t} = useI18n()
const {getters} = useStore()
const api = inject("api")

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);
const selectedHub = computed(() => getters["operations/selectedHub"].id);

const autoAcceptanceCount = computed(() => {
  if (integrations.value.length > 0) {
    let on = integrations.value.filter(item => item.auto_accept)
    return `${on.length}/${integrations.value.length}`
  } else {
    return ""
  }
})

const openStoreCount = computed(() => {
  if (integrations.value.length > 0) {
    let on = integrations.value.filter(item => item.is_store_open)
    return `${on.length}/${integrations.value.length}`
  } else {
    return ""
  }
})

const integrations = ref([])
const bulkAutoAcceptance = ref(false)
const bulkUpdateStoreStatus = ref(false)
const bulkAutoAcceptLoading = ref(false)
const bulkUpdateStoreStatusLoading = ref(false)
const minute = ref(null)

const onBulkAutoAcceptance = (param) => {
  bulkAutoAcceptLoading.value = true
  let body = {
    auto_accept: param,
    hub_id: selectedHub.value
  }
  api.patch(`customer/qconnect-integration/bulk-update`, body)
      .then((res) => {
        integrations.value.forEach(item => {
          if (res.success_list.includes(item.provider.name)) {
            item.auto_accept = param
          } else {
            item.auto_accept = !param
          }
        })
      })
      .catch(() => bulkAutoAcceptance.value = !param)
      .finally(() => bulkAutoAcceptLoading.value = false)

}

const onBulkUpdateStoreStatus = (param) => {
  bulkUpdateStoreStatusLoading.value = true
  let body = {
    is_store_open: param,
    hub_id: selectedHub.value,
  }
  if (minute.value) {
    body.toggle_after_minutes = minute.value
  }
  api.patch(`customer/qconnect-integration/bulk-update`, body)
      .then((res) => {
        integrations.value.forEach(item => {
          if (res.success_list.includes(item.provider.name)) {
            item.is_store_open = param
          } else {
            item.is_store_open = !param
          }
        })
        minute.value = null
      })
      .catch(() => bulkUpdateStoreStatus.value = !param)
      .finally(() => bulkUpdateStoreStatusLoading.value = false)
}

const getIntegrations = () => {
  api(`customer/qconnect-integration?filter[hub_id]=${selectedHub.value}`)
      .then((res) => {
        integrations.value = res.data.map((x) => {
          return {
            ...x,
            loading: false,
          }
        })
        bulkAutoAcceptance.value = !!res.data.find(x => x.auto_accept);
        bulkUpdateStoreStatus.value = !!res.data.find(x => x.is_store_open);
      })
}

onMounted(() => {
  listener()
  selectedHub.value && getIntegrations()
})

watch(selectedHub, () => {
  getIntegrations()
})

watch(integrations, () => {

  bulkUpdateStoreStatus.value = !!integrations.value.find(x => x.is_store_open)
  bulkAutoAcceptance.value = !!integrations.value.find(x => x.auto_accept)

}, {deep: true})


const listener = () => {
  Echo.private(companyChannel.value).listen(".task.provider.integration.updated", (data) => {
    if (getters["operations/selectedHub"].id === data.task_provider_integration.hub_id) {
      getIntegrations()
    }
  })
  Echo.private(companyChannel.value).listen(".task.provider.integration.store.status.updated", (data) => {
    if (getters["operations/selectedHub"].id === data.task_provider_integration.hub_id) {
      getIntegrations()
    }
  })

}

</script>
<template>
  <el-popover
      trigger="click"
      :width="400"
  >
    <template #default>
      <div class="w-full grid gap-2 grid-cols-6 text-slate-700 px-2.5">
        <div class="col-span-3 text-xxs font-bold text-left">{{ $t('Integrations') }}</div>
        <div class="col-span-1 text-xxs font-bold text-center">{{ $t('Auto Approval') }}</div>
        <div class="col-span-2 text-xxs font-bold text-right">{{$t('Status')}}</div>
        <template v-for="item in integrations">
          <IntegrationItem
              :item="item"
              :bulkAutoAcceptLoading="bulkAutoAcceptLoading"
              :bulkUpdateStoreStatusLoading="bulkUpdateStoreStatusLoading"
          />
        </template>
        <div class="col-span-6 border-t border-slate-300"/>
        <div class="col-span-3 flex items-center">
          <div class="text-sm font-bold flex items-center ml-1.5">{{ $t('Batch') }}</div>
        </div>

        <div class="col-span-1 flex items-center">
          <el-switch
              v-model="bulkAutoAcceptance"
              @change="onBulkAutoAcceptance"
              :loading="bulkAutoAcceptLoading"
          >
          </el-switch>
        </div>
        <div class="col-span-2 flex items-center justify-end">
          <ToggleClock v-model="minute"/>
          <el-switch
              v-model="bulkUpdateStoreStatus"
              @change="onBulkUpdateStoreStatus"
              :loading="bulkUpdateStoreStatusLoading"
          >
          </el-switch>
        </div>
      </div>

    </template>
    <template #reference>
      <div class="text-white bg-slate-700 py-1 px-2 border rounded hover:opacity-90 text-md cursor-pointer">
        <FontAwesomeIcon icon="bars"/>
      </div>
    </template>

  </el-popover>
</template>
