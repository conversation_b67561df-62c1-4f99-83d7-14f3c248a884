<script setup>
import {computed, ref} from "vue";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {type: Number},
});

const minute = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});
const changeToggleAfterMinutes = () => {
  if (minute.value === 5) {
    minute.value = 15
  } else if (minute.value === 15) {
    minute.value = 30
  } else if (minute.value === 30) {
    minute.value = 45
  } else if (minute.value === 45) {
    minute.value = 60
  } else if (minute.value === 60) {
    minute.value = null
  } else {
    minute.value = 5
  }
}
</script>
<template>
  <div v-if="minute" class="text-slate-700 text-sm font-bold mr-2">
    {{ minute }}dk
  </div>
  <div
      @click="changeToggleAfterMinutes"
      class="mr-2 cursor-pointer text-lg"
      :class="[
            minute === 5 ? 'text-yellow-400' :
              minute === 15 ? 'text-yellow-500' :
                 minute === 30 ? 'text-yellow-600' :
                   minute === 45 ? 'text-yellow-700' :
                     minute === 60 ? 'text-yellow-800' :
                      'text-slate-300'
        ]"
  >
    <FontAwesomeIcon icon="clock"/>
  </div>
</template>
