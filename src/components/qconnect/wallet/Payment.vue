<script setup>
import AddFunds from "@/components/qconnect/wallet/addFunds/AddFunds.vue";
import Withdraw from "@/components/qconnect/wallet/Withdraw.vue";
import OperationsManager from "@/components/admin/OperationsManager.vue";

import {useI18n} from "vue-i18n";

const {t} = useI18n()

</script>

<template>
  <div class="flex flex-col w-full h-full border-slate-300 border-l text-slate-700 overflow-hidden">
    <div class="h-full overflow-auto">
      <el-tabs type="border-card" style="padding: 0px !important;">
        <el-tab-pane  :label="t('Add Funds')" class="h-full w-full">
          <AddFunds/>
        </el-tab-pane>
        <el-tab-pane lazy :label="t('Withdraw')" class="h-full w-full">
          <Withdraw/>
        </el-tab-pane>
      </el-tabs>
      </div>
    <OperationsManager/>
  </div>
</template>
<style >
.el-tabs__content {
  padding: 0!important;
}
</style>
