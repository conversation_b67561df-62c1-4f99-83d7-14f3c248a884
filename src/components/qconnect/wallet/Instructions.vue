<script setup>
import {inject, onMounted, reactive, ref} from "vue";
import {useI18n} from "vue-i18n";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import formatter from "@/class/formatter";
import {useToast} from "vue-toastification";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";

const toast = useToast()
const {t} = useI18n()

const api = inject("api")

const loader = ref()
const savedCardList = ref()
const currency = ref("TRY")
const autoPaymentInstructions = ref([])

const form = reactive({
  amount: 0,
  threshold_amount: 0,
  selectedCard: null,
  receiverWallet: null
})

onMounted(() => {
  getData()
})

const getData = () => {
  loader.value.show()
  Promise.all([getSavedCreditCard(), getWallets(), getAutoPaymentInstructions()])
      .finally(() => loader.value.hide())
}

const getSavedCreditCard = () => {
  return api("customer/payment-cards")
      .then((res) => {
        let newCard = [{
          alias: t("New Credit Card"),
          uuid: null
        }]

        savedCardList.value = res.data.concat(newCard)
        form.selectedCard = savedCardList.value[0].uuid
      })
}

const getWallets = () => {
  return api("customer/wallets")
      .then((res) => {
        form.receiverWallet = res.data.find(x => x.currency_unit === 'TRY')
      })
}

const getAutoPaymentInstructions = () => {
  return api('customer/auto-payment-instructions')
      .then((res) => {
        autoPaymentInstructions.value = res.data.filter(x => !x.terminated_by)
      })
}

const createAutoPaymentInstructions = () => {
  loader.value.show()
  let body = {
    "wallet_uuid": form.receiverWallet?.uuid,
    "payment_card_uuid": form.selectedCard,
    "threshold_amount": form.threshold_amount,
    "amount": form.amount
  }

  api.post('customer/auto-payment-instructions', body)
      .then(() => getData())
      .catch((err) => {
        toast.error(err.data.message)
        loader.value.hide()
      })
}

const terminateAutoPaymentInstructions = (id) => {
  loader.value.show()
  api.delete(`customer/auto-payment-instructions/${id}`)
      .then(() => getData())
      .catch((err) => {
        toast.error(err.data.message)
        loader.value.hide()
      })
}

</script>

<template>
  <div class="h-full w-full flex flex-col text-slate-700 font-bold">
    <LoadingBlock ref="loader"/>
    <el-form v-if="autoPaymentInstructions.length === 0" label-position="top"
             class="grid grid-cols-12 mt-7 gap-4 text-slate-700">
      <div class="col-span-12">
        {{ $t('Automatic Payment Instruction') }}
      </div>
      <div class="col-span-12">
        <el-select
            class="w-full-important"
            v-model="form.selectedCard"
        >
          <el-option
              v-for="item in savedCardList"
              :key="item.uuid"
              :label="item.alias + `${item.bank_name ? ' - ' + item.bank_name : '' }` + `${item.last_four_digits ? ' - ' + item.last_four_digits : ''}`"
              :value="item.uuid"
          />
        </el-select>
      </div>
      <div class="col-span-12">
        <MoneyInput
            v-model="form.threshold_amount"
            :options="{currency}"
            :min="0"
            :label="t('Wallet Amount')"
            :placeholder="t('Wallet Amount')"
        />
      </div>
      <div class="col-span-12">
        <MoneyInput
            v-model="form.amount"
            :options="{currency}"
            :label="t('Amount to be completed')"
            :placeholder="t('Amount to be completed')"
        />
      </div>
      <div class="col-span-12 flex items-center justify-end">
        <el-button
            @click="createAutoPaymentInstructions"
            size="small"
            type="primary"
        >
          {{ $t("Create") }}
        </el-button>
      </div>
    </el-form>
    <div v-else class="grid grid-cols-12 mt-7 gap-4 text-slate-700">
      <div
          v-for="instructions in autoPaymentInstructions"
          class="flex border border-slate-300 rounded col-span-12 p-2"
      >
        <div class="w-full flex flex-col space-y-1.5">
          <div class="flex items-center">
            <div class="text-xs text-slate-700">
              <div class="text-xxs text-slate-500 font-bold">
                {{t('Lower Limit')}}
              </div>
              {{ formatter.currency(instructions.threshold_amount) }}
            </div>
            <div class="text-xs text-slate-700 ml-4">
              <div class="text-xxs text-slate-500 font-bold">
                {{t('Target Amount')}}
              </div>
              {{ formatter.currency(instructions.amount) }}
            </div>
          </div>
          <div class="text-xxs text-slate-700">
            <div class="text-xxs text-slate-500 font-bold">
              {{ t('Credit Card') }}
            </div>
            {{
              instructions.payment_card.bank_name
                  ? instructions.payment_card.alias + ' - ' + instructions.payment_card.bank_name : '' + instructions.payment_card.last_four_digits ? ' - ' + instructions.payment_card.last_four_digits
                      : ''
            }}
          </div>

        </div>
        <div class="mx-2">
          <div
              @click="terminateAutoPaymentInstructions(instructions.id)"
              class="text-red-500 cursor-pointer"
          >
            <FontAwesomeIcon icon="times"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
