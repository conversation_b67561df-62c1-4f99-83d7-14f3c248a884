<script setup>
import ApprovedTransactions from "@/components/qconnect/wallet/tabTransactions/ApprovedTransactions.vue";
import AwaitingTransactions from "@/components/qconnect/wallet/tabTransactions/AwaitingTransactions.vue";

</script>
<template>
  <div class="w-full h-full flex flex-col text-slate-700 overflow-hidden">
    <div class="h-12 border-b border-slate-300 bg-slate-50 flex items-center px-2">
      <div class="text-lg font-bold">
        {{ $t('Wallet Transactions') }}
      </div>
    </div>
      <el-tabs  type="border-card" class="h-full w-full">
        <el-tab-pane :label="$t('Approved Transactions')" class="h-full w-full">
          <ApprovedTransactions/>
        </el-tab-pane>
        <el-tab-pane :label="$t('Awaiting Approval')" lazy class="h-full w-full">
          <AwaitingTransactions/>
        </el-tab-pane>
      </el-tabs>

  </div>
</template>
