<script setup>

import { ref} from 'vue';
import PreliminaryInformationForm
  from "@/components/qconnect/wallet/addFunds/components/PreliminaryInformationForm.vue";


const props = defineProps({
  money:{type:Number , default:0},
  currency:{type:String, default: "TRY"}
})



const preliminaryInformationFormVisible = ref(false)
const distanceSalesAgreement = ref(false)

const openPreliminaryInformationForm = () => {
  preliminaryInformationFormVisible.value = true
}

const openDistanceSalesAgreement = () => {
  distanceSalesAgreement.value = true
}

//TODO dil template'i var öğretici nitelikte

</script>


<template>
  <div class="text-xxxs font-bold text-slate-700 mx-2">
    <i18n-t keypath="I approve the Preliminary Information Form and the Distance Sales Agreement" tag="p">
      <template #preliminaryInformationForm>
                  <span @click="openPreliminaryInformationForm" class="text-indigo-500 cursor-pointer">
                   {{ $t('Preliminary Information Form') }}
                  </span>
      </template>
      <template #distanceSalesAgreement>
                  <span @click="openDistanceSalesAgreement" class="text-indigo-500 cursor-pointer">
                   {{ $t('Distance Sales Agreement') }}
                  </span>
      </template>
    </i18n-t>

    <el-drawer
        v-model="preliminaryInformationFormVisible"
        class="customized-drawer"
        :title="$t('Preliminary Information Form')"
        append-to-body
        destroy-on-close
    >
      <PreliminaryInformationForm/>
    </el-drawer>
    <el-drawer
        v-model="distanceSalesAgreement"
        class="customized-drawer"
        :title="$t('Distance Sales Agreement')"
        append-to-body
        destroy-on-close
    >
      <DistanceSalesAgreement :amount="money" :currency="currency"/>
    </el-drawer>


  </div>
</template>
