<script setup>

const emit = defineEmits(["retry", "close"])
const props = defineProps({
  result: {
    type: Object, default: () => {
    }
  },

})

// {
//   "user_id": 621,
//     "status": true,
//     "message": "Transaction successful.",
//     "payment_id": "160279",
//     "transaction_uuid": "97613b5d-8a28-41ab-ace3-8fec163d1234",
//     "company_id": 5,
//     "receiver_wallet": {
//   "uuid": "97152842-85fe-46dc-adf4-c23a3b19075b",
//       "name": "TRY Wallet Test - Company 5",
//       "currency": "TRY",
//       "amount": 15463
// }
// }


const onClose = () => {
  emit("close")
}

const onRetry = () => {
  emit("retry")
}

</script>

<template>
  <div class="w-full h-full flex flex-col items-center justify-center">
    <div class="text-red-500" style="font-size: 100px">
      <FontAwesomeIcon icon="times"/>
    </div>
    <div class="pb-3">{{ result.message }}</div>

    <div class="flex items-center justify-between">
      <el-button

          @click="onClose"
      >
        Kapat
      </el-button>
      <el-button
          type="primary"
          @click="onRetry"
      >
        Tekrar Dene
      </el-button>
    </div>
  </div>

</template>

