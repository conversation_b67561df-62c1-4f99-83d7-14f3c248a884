<script>
import {defineAsyncComponent} from "vue";

export default {
  components: {
    CreditCard: defineAsyncComponent(() =>
        import("@/components/qconnect/wallet/addFunds/CreditCard.vue")
    ),
    BankTransfer: defineAsyncComponent(() =>
        import("@/components/qconnect/wallet/addFunds/BankTransfer.vue")
    ),
    Instructions: defineAsyncComponent(() =>
        import("@/components/qconnect/wallet/Instructions.vue")
    ),
  }
}

</script>

<script setup>

import {useI18n} from "vue-i18n";
import {ref} from "vue";

const {t} = useI18n()

const tabs = [
  "CreditCard",
  "BankTransfer",
  "Instructions"
]

const activeTab = ref(tabs[0])

const changeActiveTab = (tabName) => {
  activeTab.value = tabName
}

</script>

<template>
  <div class="w-full h-full flex flex-col px-7 py-4 bg-slate-50 overflow-auto">
    <div class="flex items-center text-sm font-bold">
      <div
          class="cursor-pointer"
          :class="[activeTab === 'CreditCard' ? 'text-slate-700' : 'text-slate-300']"
          @click="changeActiveTab('CreditCard')"
      >
        {{ t("Credit Card") }}
      </div>
      <div class="mx-4 w-px bg-slate-300 h-full">

      </div>
      <div
          class="cursor-pointer"
          :class="[activeTab === 'BankTransfer' ? 'text-slate-700' : 'text-slate-300']"
          @click="changeActiveTab('BankTransfer')"
      >
        {{ t("Bank Transfer") }}
      </div>
      <div class="mx-4 w-px bg-slate-300 h-full">

      </div>
      <div
          class="cursor-pointer"
          :class="[activeTab === 'Instructions' ? 'text-slate-700' : 'text-slate-300']"
          @click="changeActiveTab('Instructions')"
      >
        {{ t("Instructions") }}
      </div>
    </div>
    <keep-alive>
      <component :is="activeTab"/>
    </keep-alive>
  </div>
</template>
