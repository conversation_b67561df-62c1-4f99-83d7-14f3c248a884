<script setup>

import {computed, inject, onActivated, onDeactivated, onMounted, reactive, ref} from "vue";
import CreditCardExpireInput from "@/components/ui/CreditCardExpireInput.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useStore} from "vuex";
import Success from "@/components/qconnect/wallet/addFunds/components/Success.vue";
import Error from "@/components/qconnect/wallet/addFunds/components/Error.vue";
import formatter from "@/class/formatter";
import ApproveFormMessage from "@/components/qconnect/wallet/addFunds/components/ApproveFormMessage.vue";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()
const {getters} = useStore();

const api = inject("api")
const emitter = inject("emitter")

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);
const saveCardVisible = computed(() => !selectedCard.value)
const giveCardNameVisible = computed(() => saveCard.value)
const creditCardExpire = computed(() => {
  const arr = expire.value.split("/")
  return {month: arr[0], year: arr[1]}
})

const currency = ref("TRY")
const receiverWallet = ref()
const view = ref()
const result = ref()
const loader = ref()
const selectedCard = ref()
const money = ref(0)
const saveCard = ref(true)
const confirmed = ref(false)
const savedCardList = ref([]);
const wallets = ref([]);
const expire = ref()
const removeCardDialogVisible = ref(false)

const creditCard = reactive({
  holder_name: null,
  number: null,
  cvc: null,
  alias: null
})

const removeCardDialog=()=>{
  removeCardDialogVisible.value=false
}

onMounted(() => {
  getSavedCreditCard()
  getWallets()
})

onActivated(() => {
  listener()
})

onDeactivated(() => {
  listener()
})

const listener = () => {
  Echo.private(companyChannel.value).listen(".payment.result", (data) => {
    if (data.receiver_wallet.uuid === receiverWallet.value) {
      result.value = data
    }
  })
}

const onPay = () => {
  try {
    if (!confirmed.value) {
      toast.error(t('You must approve the preliminary information and the Distance Sales Agreement'));
      return;
    }
    if (money.value < 500) {
      toast.error(t("The given amount must be at least", {amount: formatter.currency(500, currency)}));
      return;
    }

    loader.value.show();

    let body;
    if (selectedCard.value) {
      body = {
        currency_unit: currency.value,
        amount: money.value,
        receiver_wallet: receiverWallet.value,
        stored_card: selectedCard.value,
      };
    } else {
      body = {
        currency_unit: currency.value,
        amount: money.value,
        receiver_wallet: receiverWallet.value,
        card: {
          holder_name: creditCard.holder_name,
          number: creditCard.number,
          expire_year: Number("20" + creditCardExpire.value.year),
          expire_month: Number(creditCardExpire.value.month),
          cvc: creditCard.cvc,
          is_store: Number(saveCard.value),
        },
      };
      if (saveCard.value) {
        body.card.alias = creditCard.alias;
      }
    }

    // prepareData fonksiyonunu ekleyelim ve body nesnesini filtreleyelim
    const prepareData = (data) => {
      const filteredData = {};
      for (const key in data) {
        if (data[key] !== null && data[key] !== '') {
          filteredData[key] = data[key];
        }
      }
      return filteredData;
    };

    // body.card varsa, onu da filtreleyelim
    if (body.card) {
      body.card = prepareData(body.card);
    }

    body = prepareData(body);

    api.post("customer/payments/add-funds/credit-card", body)
      .then((res) => {
        view.value = res.html;
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide());

  } catch (err) {
    loader.value.hide();
  }
};


const onClear = () => {
  result.value = null
  view.value = null
  money.value = 0
  if (selectedCard.value) {
    selectedCard.value = savedCardList.value[0].uuid
  } else {
    expire.value = null
    creditCard.cvc = null
    creditCard.number = null
    creditCard.alias = null
    creditCard.holder_name = null
  }
}

const onRetry = () => {
  result.value = null
  view.value = null
}


const getSavedCreditCard = () => {
  api("customer/payment-cards")
      .then((res) => {
        let newCard = [{
          alias: t("New Credit Card"),
          uuid: null
        }]

        savedCardList.value = res.data.concat(newCard)
        selectedCard.value = savedCardList.value[0].uuid
      })
}

const getWallets = () => {
  loader.value.show()
  api("customer/wallets")
      .then((res) => {
        wallets.value = res.data
        if (res.data.length === 1) {
          receiverWallet.value = res.data[0].uuid
        }
      })
      .finally(() => loader.value.hide())
}

const onClose = () => {
  view.value = null
}

const openRemoveCardDialogVisible = () => {
  removeCardDialogVisible.value = true
}

const closeRemoveCadDialogVisible = () => {
  removeCardDialogVisible.value = false
}

const onRemove = () => {
  closeRemoveCadDialogVisible()
  loader.value.show()
  api.delete(`customer/payment-cards/${selectedCard.value}`)
      .then(() => {
        selectedCard.value = null
        getSavedCreditCard()
      })
      .finally(() => loader.value.hide())
}

</script>

<template>
  <div class="h-full w-full flex flex-col text-slate-700 font-bold">
    <LoadingBlock ref="loader"/>
    <el-form v-if="!view" label-position="top" class="grid grid-cols-12 mt-7 gap-4">
      <div v-show="wallets.length > 1" class="col-span-12">
        <el-form-item>
          <el-select
              class="w-full-important"
              v-model="receiverWallet"
          >
            <el-option
                v-for="item in wallets"
                :key="item.uuid"
                :label="item.name"
                :value="item.uuid"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="col-span-12 mb-4">
        <div class="flex flex-col">
          <div class="w-full flex items-center justify-end">
            <small v-if="selectedCard" @click="openRemoveCardDialogVisible()"
                   class="text-red-400 cursor-pointer underline "
                   style="font-size: 9px">
              {{ $t('Delete Selected Card') }}
            </small>
          </div>
          <el-select
              class="w-full-important"
              v-model="selectedCard"
              :placeholder="t('New Credit Card')"
          >
            <el-option
                v-for="item in savedCardList"
                :key="item.uuid"
                :label="item.alias + `${item.bank_name ? ' - ' + item.bank_name : '' }` + `${item.last_four_digits ? ' - ' + item.last_four_digits : ''}`"
                :value="item.uuid"
            />
          </el-select>
        </div>
      </div>
      <div v-show="saveCardVisible && giveCardNameVisible" class="col-span-12">
        <el-form-item>
          <el-input v-model="creditCard.alias" :placeholder="$t('Give this card a name')"/>
        </el-form-item>
      </div>
      <div v-show="saveCardVisible" class="col-span-12">
        <el-form-item>
          <el-input v-model="creditCard.holder_name" :placeholder="$t('Name on the Card')"/>
        </el-form-item>
      </div>
      <div v-show="saveCardVisible" class="col-span-12">
        <el-form-item>
          <el-input v-model="creditCard.number" :placeholder="$t('Card Number')"/>
        </el-form-item>
      </div>
      <div v-show="saveCardVisible" class="col-span-6">
        <CreditCardExpireInput v-model="expire"/>
      </div>
      <div v-show="saveCardVisible" class="col-span-6">
        <el-form-item>
          <el-input v-model="creditCard.cvc" :placeholder="$t('CVC/CVV')" maxlength="4"/>
        </el-form-item>
      </div>
      <div v-show="saveCardVisible" class="col-span-12">
        <el-checkbox v-model="saveCard">
              <span class="text-xxs font-semibold text-slate-700">
               {{ $t("Save Credit Card") }}
              </span>
        </el-checkbox>
      </div>
      <div class="col-span-12">
        <MoneyInput
            v-model="money"
            :options="{currency}"
            :label="t('Funding Amount')"
            :placeholder="t('Funding Amount')"
        />

      </div>
      <div class="col-span-12">
        <div class="flex items-center w-full mt-4">
          <div class="flex items-center grow">
            <el-checkbox
                v-model="confirmed"
                size="small">
            </el-checkbox>
            <ApproveFormMessage :money="money" :currency="currency"/>
          </div>

          <el-button

              :disabled="!confirmed"
              @click="onPay" size="small" type="primary">
            {{ $t("Add Funds") }}
          </el-button>
        </div>
      </div>
    </el-form>
    <div v-else class="w-full h-full">
      <div v-if="!result" class=" h-full w-full flex flex-col">
        <div class="text-right p-2.5">
          <div @click="onClose" class="cursor-pointer text-xl font-bold text-slate-700">
            <FontAwesomeIcon icon="times"/>
          </div>
        </div>
        <iframe :src="`data:text/html,${view}`"
                sandbox="allow-same-origin allow-top-navigation allow-forms allow-scripts allow-modals" class="h-full w-full"/>
      </div>
      <div v-else class="h-full w-full">
        <Success v-if="result.status" :result="result" @close="onClear"/>
        <Error v-else :result="result" @retry="onRetry" @close="onClear"/>
      </div>
    </div>
    <el-dialog
        class="kuik-customized-dialog"
        v-model="removeCardDialogVisible"
        title="Kayıtlı Kredi Kartı Silme"
        destroy-on-close
    >
      <div class="text-center text-lg text-slate-700 font-bold pt-7">
        {{ t('Are you sure?') }}
      </div>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="removeCardDialog">{{ t('Cancel') }}</el-button>
        <el-button @click="onRemove" type="danger">
          {{ t('Approve') }}
        </el-button>
      </span>
      </template>
    </el-dialog>
  </div>
</template>
