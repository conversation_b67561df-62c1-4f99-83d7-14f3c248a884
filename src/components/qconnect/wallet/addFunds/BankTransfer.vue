<script setup>
import IsBank from "@/assets/images/bank/is.png"
import MoneyInput from "@/components/ui/MoneyInput.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ApproveFormMessage from "@/components/qconnect/wallet/addFunds/components/ApproveFormMessage.vue";
import {inject, onMounted, ref} from 'vue';
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()

const is_iban = "TR24 0006 4000 0011 0254 6252 56"

const copyText = () => {
  navigator.clipboard.writeText(is_iban);
  toast.success(t("Copied to clipboard"))
};


const api = inject("api")
const loader = ref()
const amount = ref(0)
const confirmed = ref(false)
const receiverWallet = ref(null)
const wallets = ref([])
const currency = ref("TRY")

const onSubmit = () => {
  try {
    if (!confirmed.value) {
      toast.error(t('You must approve the preliminary information and the Distance Sales Agreement'));
      return;
    }

    loader.value.show();

    let body = {
      currency_unit: currency.value,
      amount: amount.value,
      receiver_wallet: receiverWallet.value,
    };

    // prepareData fonksiyonunu ekleyelim ve body nesnesini filtreleyelim
    const prepareData = (data) => {
      const filteredData = {};
      for (const key in data) {
        if (data[key] !== null && data[key] !== '') {
          filteredData[key] = data[key];
        }
      }
      return filteredData;
    };

    body = prepareData(body);

    api.post("customer/payments/add-funds/bank-transfer", body)
      .then(() => {
        //TODO locale
        toast.success(t("Your deposit request has been processed"));
      })
      .finally(() => loader.value.hide());

  } catch (x) {
    loader.value.hide();
  }
};


const getWallets = () => {
  loader.value.show()
  api("customer/wallets")
      .then((res) => {
        wallets.value = res.data
        if (res.data.length === 1) {
          receiverWallet.value = res.data[0].uuid
        }
      })
      .finally(() => loader.value.hide())
}

onMounted(() => {
  getWallets()
})

</script>

<template>

  <div class="h-full w-full">
    <LoadingBlock ref="loader"/>
    <div class="flex flex-col py-4 text-slate-700 font-bold">
      <img :src="IsBank" class="w-40"/>
      <div class=" text-xs py-2">
        QDelivery Teknoloji Anonim Şirketi
      </div>

      <div class="flex items-center text-xxs">
        <div class="grow">
          {{ is_iban }}
        </div>
        <div @click="copyText" class="p-1">
          <FontAwesomeIcon icon="copy"/>
        </div>
      </div>

      <el-form label-position="top" class="mt-7 space-y-6">

        <el-form-item v-show="wallets.length > 1" class="pb-1">
          <el-select
              class="w-full-important"
              v-model="receiverWallet"
          >
            <el-option
                v-for="item in wallets"
                :key="item.uuid"
                :label="item.name"
                :value="item.uuid"
            />
          </el-select>
        </el-form-item>

        <MoneyInput
            v-model="amount"
            :options="{currency}"
            :label="t('Funding Amount')"
            :placeholder="t('Funding Amount')"
            :min="50"
        />

      </el-form>
      <div class="flex items-center w-full mt-4">
        <div class="flex items-center grow">
          <el-checkbox
              v-model="confirmed"
              size="small">
          </el-checkbox>
          <ApproveFormMessage/>
        </div>

        <el-button @click="onSubmit" size="small" type="primary">
          {{ $t('Report') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
