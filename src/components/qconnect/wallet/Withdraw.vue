<script setup>

import {computed, inject, onMounted, reactive, ref} from 'vue'
import {useStore} from "vuex";
import {useToast} from "vue-toastification";
import {useI18n} from "vue-i18n";


const api = inject("api")

const {t} = useI18n()
const toast = useToast()
const {getters} = useStore()

const activeCompany = computed(() => getters["auth/activeCompany"]);

const loader = ref()
const wallet = ref()
const money = ref(0)
const currency = ref("TRY")
const form = reactive({
  name: null,
  iban: null
})


onMounted(() => {
  getWallets()
})

const getWallets = () => {
  loader.value.show()
  api("customer/wallets")
      .then((res) => {
        wallet.value = res.data.find(x => x.currency_unit === "TRY")
        form.iban = wallet.value.iban
      })
      .finally(() => loader.value.hide())
}

const onWithdraw = () => {
  loader.value.show()
  let body = {
    "currency_unit": "TRY",
    "amount": money.value,
    "sender_wallet_id": wallet.value.uuid,
    "notes": ""
  }

  api.post("customer/payments/withdrawal", body)
      .then(() => {
        toast.success(t('Your request has been processed'))
        getWallets()
      })
      .catch((err) => {
        toast.error(err.data.message)
        loader.value.hide()
      })
}

</script>

<template>
  <div
      class="w-full h-full flex flex-col px-7 py-4 bg-slate-50 overflow-auto"
      style="background-color: white"
  >
    <LoadingBlock ref="loader"/>
    <el-form label-position="top" class="grid grid-cols-12 mt-7 gap-4">
      <div class="col-span-12">
        <el-form-item
            :label="$t('Company Name')">
          <el-input
              v-model="activeCompany.name"
              :placeholder="$t('Company Name')"
              disabled
          />
        </el-form-item>
      </div>
      <div class="col-span-12">
        <el-form-item
            label="IBAN">
          <el-input
              v-model="form.iban"
              placeholder="IBAN"
              disabled
          />
        </el-form-item>
      </div>
      <div class="col-span-12">
        <MoneyInput
            v-model="money"
            :options="{currency}"
            :min="0"
            :label="$t('Withdraw Amount')"
            :placeholder="$t('Withdraw Amount')"
        />
      </div>
      <div class="col-span-12 flex items-center justify-end">
        <el-button
            @click="onWithdraw"
            size="small"
            type="primary"
        >
          {{ $t("Withdraw") }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>
