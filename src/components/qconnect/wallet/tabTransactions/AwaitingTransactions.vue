<script>

import TransactionAmount from "@/renderers/TransactionAmount";

export default {
  components: {
    TransactionAmount,
  }
}
</script>

<script setup>
import {computed, onMounted, onUnmounted, ref} from "vue";
import {useStore} from "vuex";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import formatter from "@/class/formatter";
import {useI18n} from "vue-i18n";
import {transactionTypeSlug} from "@/class";

const {t} = useI18n()
const {getters} = useStore();

const filter = computed(()=> {
  return {
    "filter[is_approved]": 0
  }
})

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);

const grid = ref()

const columns = [
  {
    field: "task.integration_id",
    headerName: t("Order No"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "type_slug",
    headerName: t("Operation Type"),
    filter: 'agSetColumnFilter',
    valueFormatter: (params) => t(transactionTypeSlug[params.value]),
    sortable: true,
    export: true,
    cellClass: 'stringType',
    filterParams: {
      values: function (params) {
        params.success(Object.keys(transactionTypeSlug))
      },
      valueFormatter: (params) => t(transactionTypeSlug[params.value])
    }
  },
  {
    field: "task.provider.name",
    headerName: t("Order Source"),
    filter: 'agSetColumnFilter',
  },
  {
    field: "task.origin_name",
    headerName: t("Hub"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "approved_at",
    headerName: t("Approval Date"),
    filter: 'agDateColumnFilter',
    sortable: true,
    export: true,
    cellClass: 'dateType',
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "amount",
    headerName: t("Amount"),
    pinned: "right",
    cellStyle: {textAlign: "center"},
    filter: 'agTextColumnFilter',
    cellRenderer: 'TransactionAmount'
  },
]

onMounted(()=>{
  listener()
})

onUnmounted(()=>{
  stopListening()
})

const refresh = () => {
  grid.value && grid.value.refresh()
}

const listener = () => {
  Echo.private(companyChannel.value).listen(".transaction.created",refresh)
  Echo.private(companyChannel.value).listen(".transaction.updated", refresh)
}

const stopListening = () => {
  Echo.private(companyChannel.value).stopListening(".transaction.created")
  Echo.private(companyChannel.value).stopListening(".transaction.updated")
}

</script>
<template>
  <div class="h-full w-full">
    <SSDataGrid
        ref="grid"
        :columns="columns"
        url="customer/transactions"
        rowKey="uuid"
        :filter="filter"
        :auto-size-column="false"
    />
  </div>
</template>
