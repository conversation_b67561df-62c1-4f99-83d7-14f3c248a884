<script>

import TransactionAmount from "@/renderers/TransactionAmount";

export default {
  components: {
    TransactionAmount,
  }
}
</script>

<script setup>
import {computed, inject, onMounted, onUnmounted, ref} from "vue"
import {useStore} from "vuex";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import formatter from "@/class/formatter";
import {useI18n} from "vue-i18n";
import {transactionTypeSlug} from "@/class";

const {t} = useI18n()
const {getters} = useStore();

const filter = computed(() => {
  return {
    "filter[is_approved]": 1
  }
})

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);

const selectedTypesForNotes = [
  "commitment_penalty",
  "add_funds_from_credit_card",
  "add_funds_from_bank_transfer"
]

const grid = ref()

const columns = [
  {
    field: "task.integration_id",
    headerName: t("Order No"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "approved_at",
    headerName: t("Transaction Date"),
    filter: 'agDateColumnFilter',
    sortable: true,
    export: true,
    cellClass: 'dateType',
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: 'task_amount',
    headerName: t("Order Amount"),
    filter: 'agTextColumnFilter',
    valueFormatter:(params)=>params.data.task_amount? formatter.currency(params.data.task_amount) : null
    // filterParams: {
    //   // values: function (params) {
    //   //    formatter.currency(params.data.task.amount)
    //   // },
    //   valueFormatter: (params) => {
    //    return   formatter.currency(params.data.task.amount)
    //   },
    // },

  },
  {
  //   field: "task.task_created_at",
  //   headerName: t("Order Date"),
  //   filter: 'agDateColumnFilter',
  //   cellClass: 'dateType',
  //   sortable: true,
  //   filterParams: {
  //     inRangeInclusive: true,
  //     filterOptions: ["inRange"],
  //     suppressAndOrCondition: true
  //   },
  //   valueFormatter: (params) => params.data.task ? formatter.date(params.data.task.task_created_at) : formatter.date(params.data.created_at)
  // },
  // {
    field: 'task_discounted_amount',
    headerName: t("Discounted Order Amount"),
    filter: 'agTextColumnFilter',
    valueFormatter:(params)=>params.data.task_discounted_amount? formatter.currency(params.data.task_discounted_amount) : null
  },

  {
    field: "type_slug",
    headerName: t("Process Type"),
    filter: 'agSetColumnFilter',
    valueFormatter: (params) => t(transactionTypeSlug[params.value]),
    sortable: true,
    export: true,
    cellClass: 'stringType',
    filterParams: {
      values: function (params) {
        params.success(Object.keys(transactionTypeSlug))
      },
      valueFormatter: (params) => t(transactionTypeSlug[params.value])
    }
  },
  {
    field: "task.provider.name",
    headerName: t("Order Source"),
    filter: 'agSetColumnFilter',
  },
  {
    field: "task.origin_name",
    headerName: t("Hub"),
    filter: 'agTextColumnFilter',
  },

  {
    field: "notes",
    headerName: t("Not"),
    filter: "agTextColumnFilter",
    valueFormatter: (params) => {
      if (selectedTypesForNotes.includes(params.data.type_slug)) {
        return params.value
      } else {
        return params.value = null
      }
    }
  },
  {
    field: "amount",
    headerName: t("Amount"),
    pinned: "right",
    cellStyle: {textAlign: "center"},
    filter: 'agTextColumnFilter',
    cellRenderer: 'TransactionAmount'
  },
]


onMounted(() => {
  listener()
})

onUnmounted(() => {
  stopListening()
})

const refresh = () => {
  grid.value && grid.value.refresh()
}

const listener = () => {
  Echo.private(companyChannel.value).listen(".transaction.created", refresh)
  Echo.private(companyChannel.value).listen(".transaction.updated", refresh)
}

const stopListening = () => {
  Echo.private(companyChannel.value).stopListening(".transaction.created")
  Echo.private(companyChannel.value).stopListening(".transaction.updated")
}

</script>
<template>
  <div class="h-full w-full relative">
    <SSDataGrid
        ref="grid"
        :columns="columns"
        url="customer/transactions"
        rowKey="uuid"
        :filter="filter"
        :auto-size-column="true"
    />
  </div>
</template>
