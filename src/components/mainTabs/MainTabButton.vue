<script>
import {useStore} from "vuex";
import {computed} from "vue";

export default {
  name: "MainTabButton",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tab: {
      type: Object,
      default: () => {
      },
    },
  },
  emits: ["click"],
  setup() {
    const {dispatch, getters} = useStore();
    const primaryColor = computed(() => getters["ui/primaryColor"]);

    function closeMainTab(tab) {
      dispatch("maintabs/closeTab", tab);
    }

    return {closeMainTab, primaryColor};
  },
};
</script>
<template>
  <div
      :class="[
      'rounded-t-sm cursor-pointer flex items-center overflow-hidden flex-shrink-0',
      active
        ? 'bg-white text-slate-700'
        : disabled
        ? 'text-indigo-525 cursor-not-allowed'
        : 'text-slate-50',
    ]"
  >
    <div
        class="flex p-2 group"
        @click="$emit('click')"
    >
      <span class="font-sans font-semibold">
        {{ $t(tab.name) }}
      </span>
    </div>

  </div>
</template>


