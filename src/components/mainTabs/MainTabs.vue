<script setup>
import {computed} from "vue";
import router from "@/router";

import {useStore} from "vuex";

const {dispatch, getters} = useStore();


const routes = computed(() => {

  const {role_slug, active_company} = getters["auth/me"]

  return router.getRoutes()
      .find(x => x.name === "Home").children
      .filter((route) => {
            if (route.meta.type.includes(active_company.company_product_type) && route.meta.role.includes(role_slug) && !route.hidden) {
              return route
            }

          }
      )
})

const goToRoute = (param) => {
  router.push({path: param.path})
}

</script>

<template>
  <nav class="flex space-x-0.5 overflow-y-auto px-2" aria-label="Tabs">
    <template v-for="route in routes" :key="route.name">
      <div
          class="rounded-t-sm cursor-pointer flex items-center overflow-hidden flex-shrink-0"
          :class="[ $route.path.includes(route.path)
                    ? 'bg-white text-slate-700'
                    : 'text-slate-50',
          ]"
      >
        <div
            class="flex py-2 px-1 group"
            @click="goToRoute(route)"
        >
           <span class="font-sans font-semibold">
             {{ $t(route.name) }}
           </span>
        </div>
      </div>
    </template>
  </nav>
</template>
