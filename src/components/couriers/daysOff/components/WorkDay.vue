<script setup>
import {reactive, onMounted} from 'vue';
import dayjs from "dayjs"
import {useI18n} from "vue-i18n";


const {t} = useI18n()

const emits = defineEmits(['click'])

const props = defineProps({
  data: {type: Object},
  currentDate: {type: [Object, Date]},
})

const forms = reactive({
  label: null,
  hours: null,
  delay: null,
  color: null,
})

onMounted(() => {
  setData()
})

const setData = () => {

  let courierStartedAt = props.data.courier_started_at ? dayjs(props.data.courier_started_at).format("HH:mm") : ""
  let courierEndedAt = props.data.courier_ended_at ? dayjs(props.data.courier_ended_at).format("HH:mm") : ""
  let timeline = props.currentDate.diff(dayjs(), 'day')

  forms.delay = props.data.shift_start_diff
  forms.color = props.data.shift_start_diff ? '#FCC419' : '#6BB000'
  forms.hours = courierEndedAt ? `${courierStartedAt} - ${courierEndedAt}` : courierStartedAt

  if (timeline < 0) forms.label = forms.delay ? t('Incompletely') : t('Worked')


  if (timeline === 0) {


    if (!props.data.courier_ended_at) {

      if (dayjs(props.data.shift_starts_at).diff(dayjs(), "minute") > 0) {

        forms.label = t('Will Work')
        forms.color= '#A6A6A6'

      } else {

        if (!courierStartedAt) {
          forms.label = t("Didn't Start Working")
          forms.color= '#A6A6A6'
        } else {

          if (!props.data.shift_start_diff) {
            forms.label = t("Working")
            forms.color= '#7BAE34'
          } else {
            forms.label = t("It's Working But It's Late")
            forms.color= '#F3C648'
          }

        }

      }


    } else {

      if (dayjs(props.data.courier_ended_at).diff(dayjs(props.data.shift_ends_at), "minute") < 0) {


        forms.label = t("Tried but left early")
        forms.color='#F3C648'

      } else {

        forms.label = t("Worked")
        forms.color= '#7BAE34'
      }


    }


  }

}

</script>


<template>
  <div
      @click="$emit('click')"
      class="w-full h-full border-b border-r border-slate-300 cursor-pointer relative px-2 pt-1"
  >
    <div

        class="flex items-center justify-between">

      <div
          class="text-xs flex items-center "
          :style="{color:forms.color}"
      >
        <div
            class="h-2 w-2 rounded-full mr-1"
            :style="{backgroundColor:forms.color}"
        >

        </div>

        {{ forms.label }}
      </div>

      <div
          class="text-xxxs font-light text-slate-300"
      >
        {{ dayjs(data.shift_starts_at).format("HH:mm") }}
        :
        {{ dayjs(data.shift_ends_at).format("HH:mm") }}
      </div>

    </div>
    <div
        class="text-xxxs font-light text-slate-700"
    >
      {{ forms.hours }}
    </div>
    <div
        v-if="forms.delay"
        class="text-xxxxs font-light text-slate-700"
    >
      {{ forms.delay }} {{ $t('Minute') }}
    </div>
  </div>
</template>


