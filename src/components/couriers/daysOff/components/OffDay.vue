<script setup>

import dayjs from "dayjs";
import {onMounted, ref} from "vue";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const emits = defineEmits(['click'])
const props = defineProps({
  data: {type: Object},
  shiftData: {type: Object},
})

const types = {
  "absence": {
    text: 'absence__text',
    bg: 'absence__bg',
    dot: 'absence-dot__bg',
    label: "Absence",
    color: '#CB4747',
  },
  "sick-leave": {
    text: 'sick-leave__text',
    bg: 'sick-leave__bg',
    dot: 'sick-leave-dot__bg',
    label: "Sick-leave",
    color: '#A6A6A6',
  },
  "time-off": {
    text: 'time-off__text',
    bg: 'time-off__bg',
    dot: 'time-off-dot__bg',
    label: "Time-off",
    color: '#47ABCB'
  },

}

const selectedType = ref()

onMounted(() => {
  if (props.data.type) selectedType.value = types[props.data.type]
})

</script>


<template>
  <div
      @click="$emit('click')"
      class="w-full h-full border-b border-r border-slate-300 cursor-pointer px-2 pt-1 relative"
      :class="[selectedType?.bg]"
  >
    <div
        v-if="selectedType"
        class="flex item-center justify-between"
    >
      <div
          v-if="selectedType"
          :class="selectedType?.text"
          class="text-xs flex items-center"
      >
        <div
            class="h-2 w-2 rounded-full mr-1"
            :class="[selectedType.dot]"
        >
        </div>
        {{ $t(selectedType.label) }}
      </div>
      <div
          class="text-xxxs font-light text-slate-300"
      >
        {{ dayjs(data.starts_at).format("HH:mm") }}
        :
        {{ dayjs(data.ends_at).format("HH:mm") }}
      </div>
    </div>
    <div
        v-if="data?.notes && selectedType"
        :class="[selectedType?.text]"
        class="absolute bottom-1 right-1 cursor-pointer"
    >
      <FontAwesomeIcon icon="note"/>
    </div>
  </div>
</template>

<style>

.absence__text {
  color: #CB4747;
}

.absence__bg {
  background-color: rgba(203, 71, 71, 10%);
}

.absence-dot__bg {
  background-color: #CB4747;
}

.sick-leave__text {
  color: #A6A6A6;
}

.sick-leave__bg {
  background-color: rgba(166, 166, 166, 10%);
}

.sick-leave-dot__bg {
  background-color: #A6A6A6;
}

.time-off__text {
  color: #47ABCB;
}

.time-off__bg {
  background-color: rgba(71, 171, 203, 10%);
}

.time-off-dot__bg {
  background-color: #47ABCB;
}

</style>

