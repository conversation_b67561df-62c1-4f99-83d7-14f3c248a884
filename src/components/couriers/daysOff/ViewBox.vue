<script setup>
import dayjs from "dayjs";
import {useI18n} from "vue-i18n";
import {computed, inject} from "vue";
import OffDay from "@/components/couriers/daysOff/components/OffDay.vue";
import WorkDay from "@/components/couriers/daysOff/components/WorkDay.vue";

const emit = defineEmits(["refresh", "openDrawerVisible"])
const props = defineProps({
  day: {
    type: Object, default: () => {
    }
  },
  courier: {
    type: Object, default: () => {
    }
  },
  types: {
    type: Array, default: () => []
  }
})

const {t} = useI18n()
const api = inject("api")


const dayOffData = computed(() => props.courier?.days_off ? props.courier.days_off.find((x) => Object.keys(x.dates).includes(dayjs(props.day.date).format('YYYY-MM-DD'))) : null)
const shiftData = computed(() => props.courier?.courier_shifts ? props.courier.courier_shifts.find((x) => dayjs(x.shift_date).format('YYYY-MM-DD') === dayjs(props.day.date).format('YYYY-MM-DD')) : null)


const openDrawerVisible = () => {
  let params = {
    day: JSON.parse(JSON.stringify(props.day)),
    courier: JSON.parse(JSON.stringify(props.courier)),
    dayOffPop: dayOffData.value,
  }

  emit("openDrawerVisible", params)
}


</script>
<template>

  <template v-if="dayOffData">
    <OffDay @click="openDrawerVisible" :data="dayOffData"/>
  </template>

  <template v-else-if="shiftData">
    <WorkDay @click="openDrawerVisible" :data="shiftData" :currentDate="props.day.date"/>
  </template>

  <template v-else>
    <div
        @click="openDrawerVisible"
        class="w-full h-full border-b border-r border-slate-300 cursor-pointer relative"
    >
    </div>

  </template>


</template>


