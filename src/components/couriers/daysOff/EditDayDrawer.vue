<script setup>
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {inject, onMounted, reactive, ref, computed} from "vue";
import {useI18n} from "vue-i18n";
import dayjs from "dayjs";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import {useToast} from "vue-toastification";

const {t} = useI18n()
const toast = useToast()

const api = inject("api")

const emit = defineEmits(["close"])
const props = defineProps({
  data: {
    type: Object, default: () => {
    }
  },
  types: {
    type: Array, default: () => []
  },
  day: {
    type: Object, default: () => {
    }
  },
})

const data = ref()
const loader = ref()
const dayOffTime = ref([])

const form = reactive({
  type: "time-off",
  notes: null,
  all_day: false
})

const shiftDayStartAt = computed(() => {
  return props.data.courier.courier_shifts.find((x) => dayjs(x.shift_date).format('YYYY-MM-DD') === dayjs(props.data.day.date).format('YYYY-MM-DD'))
})
const typeList = computed(()=>{
    return props.types.filter((x)=>x.slug !=='not-working')
})



onMounted(() => {
  setPermissionFields()

})

const setPermissionFields = () => {
  loader.value.show()

  let _data = props.data.dayOffPop


  if (_data) {
    data.value = _data
    dayOffTime.value[0] = dayjs(_data.starts_at).format("YYYY-MM-DD HH:mm")
    dayOffTime.value[1] = dayjs(_data.ends_at).format("YYYY-MM-DD HH:mm")
    form.type = _data.type
    form.notes = _data.notes
    form.all_day = _data.all_day
  } else {
    dayOffTime.value[0] = dayjs(props.data.day.date).startOf("day")
    dayOffTime.value[1] = dayjs(props.data.day.date).endOf("day")

  }

  loader.value.hide()
}


const createCurrentDateWithHourAndMinute = (date) => {

  const _time = dayjs(date)
  const _minute = _time.minute()
  let _second = _minute === 59 ? 59 : 0

  return dayjs(date).second(_second).toISOString()
}

const onSubmit = () => {

  loader.value.show()

  let body = {
    type: form.type,
    user_id: props.data.courier.id,
    notes: form.notes,
    all_day: form.all_day
  }

  body.starts_at = createCurrentDateWithHourAndMinute(dayOffTime.value[0])
  body.ends_at = createCurrentDateWithHourAndMinute(dayOffTime.value[1])

  data.value ? onUpdate(body) : onSave(body)
}

const onSave = (body) => {
  loader.value.show()
  api.post("customer/days-off", body)
      .then(() => emit("close", true))
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide())
}

const onUpdate = (body) => {
  loader.value.show()
  api.put(`customer/days-off/${props.data.dayOffPop.id}`, body)
      .then(() => emit("close", true))
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide())
}

const onRemove = () => {
  loader.value.show()
  api.delete(`customer/days-off/${props.data.dayOffPop.id}`)
      .then(() => emit("close", true))
      .finally(() => loader.value.hide())
}


</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex mt-2">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto p-6 pb-16">
        <div class="text-xl text-slate-700 font-bold mb-5">
          {{ t('Shift Information') }}
        </div>
        <div class="flex items-start space-x-2">
          <div class="flex-shrink-0">
            <FontAwesomeIcon icon="calendar" size="sm" class="pb-1 text-indigo-700"/>
          </div>
          <div class="text-slate-700 font-light text-xxs">
            <h3 class="font-medium text-xs">
              <span>
                {{ dayjs(shiftDayStartAt?.shift_starts_at).format('DD.MMM.YYYY') }}
                -
                {{ dayjs(shiftDayStartAt?.shift_ends_at).format('DD.MMM.YYYY') }}
              </span>
            </h3>
            <div>
              {{ t('Planned Shift') }} : {{ dayjs(shiftDayStartAt?.shift_starts_at).format('HH.mm') }}
              -
              {{ dayjs(shiftDayStartAt?.shift_ends_at).format('HH.mm') }}
            </div>
            <div>
              {{ t('Realized Work') }} : {{ dayjs(shiftDayStartAt?.courier_started_at).format('HH.mm') }}
              {{
                shiftDayStartAt?.courier_ended_at ? '-' + ' ' + dayjs(shiftDayStartAt?.courier_ended_at).format('HH.mm') : null
              }}
            </div>
          </div>
        </div>
        <el-form label-position="top" class="grid grid-cols-12 gap-4 mt-4">
          <div class="col-span-12">
            <el-form-item :label="t('Type')" required>
              <el-select
                  v-model="form.type"
                  class="w-full"
                  filterable
                  :placeholder="t('Type')"
              >
                <el-option
                    v-for="item in typeList"
                    :key="item.slug"
                    :label="item.name"
                    :value="item.slug"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('Date Range')" required>
              <el-date-picker
                  v-model="dayOffTime"
                  type="datetimerange"
                  format="YYYY-MM-DD HH:mm"
                  range-separator="İle"
                  start-placeholder="Başlangış Tarihi"
                  end-placeholder="Bitiş Tarihi"
              />
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('Notes')">
              <el-input v-model="form.notes" :placeholder="t('Notes')"/>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div
        class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
    >
      <div>
        <el-button v-if="data" @click="onRemove" type="danger">
          <template #icon>
            <FontAwesomeIcon icon="trash"/>
          </template>
          {{ t('Delete') }}
        </el-button>
      </div>
      <div>
        <el-button @click="onSubmit" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="edit"/>
          </template>
          {{ data ? t('Edit') : t('Save') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
