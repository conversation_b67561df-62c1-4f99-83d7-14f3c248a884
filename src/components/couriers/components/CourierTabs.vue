<script setup>
import {computed} from "vue";
import {useStore} from "vuex";
import router from "@/router";
import couriers from "@/router/couriers";

const {dispatch, getters} = useStore();

const currentlyOpenMainTabs = computed(() => couriers.children)

const goToRoute = (param) => {
  router.push({path: param.path})
}

</script>

<template>
  <div class="flex items-center border-slate-300 border-b md:border-none"
       style="min-width: 383px"
  >
    <div
        v-for="route in currentlyOpenMainTabs"
        :key="route.name"
    >
      <CourierTabButtons
          :active="$route.name === route.name"
          :tab="route"
          @click="goToRoute(route)"
      />
    </div>
  </div>
</template>
