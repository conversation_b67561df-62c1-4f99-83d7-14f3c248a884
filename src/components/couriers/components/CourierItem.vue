<script setup>
import AvatarImg from "@/assets/images/avatar.png";
import {VehicleTypeIcon} from "@/class";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import {ref,defineEmits} from "vue"
import CourierUpdate from "@/components/deliveries/CourierUpdate.vue";

const emit = defineEmits(["getData"]);

const props = defineProps({
  courier: {type: Object},
  day: {
    type: Object, default: () => {
    }
  },
})

const visible = ref(false)

const drawerVisible = () => {
  visible.value = true
}

const getDataa = ()=>{
  emit('getData',true)
}
const close =()=>{
  visible.value = false
}

</script>

<template>
  <td>
    <div class="group flex items-center h-14 bg-white border-b border-r border-slate-300">
      <div
          class="group flex justify-between w-full items-center space-x-2 px-2 py-2"
      >
        <div class="flex items-center space-x-2">
          <div class="flex relative items-center">
            <slot></slot>
            <div
                v-if="courier?.courier_location?.is_working"
                class="w-4 h-4 absolute z-10 -right-1 bottom-0 border-2 border-white rounded-full bg-white overflow-hidden"
            >
              <div class="w-4 h-4"
                   :class="[courier?.courier_location?.is_online ? 'bg-green-500': 'bg-amber-500']"/>
            </div>
            <img
                class="rounded-full z-0 h-10 w-10 ml-1"
                :src="courier.avatar_url ? courier.avatar_url : AvatarImg "
            />
          </div>
          <div class="flex-1 min-w-0">
            <p
                class="truncate flex items-center space-x-1 text-sm font-bold text-black select-text"
            >
              <span>{{ courier.name }}</span>
            </p>
            <p
                class="text-xxxs font-medium text-gray-500 select-text"
            >
              <span>{{ courier.phone }}</span>
            </p>
          </div>
        </div>
        <div class="flex space-x-2 items-center">
          <div
              class="flex flex-col items-end font-medium text-xxxs"
          >
            {{ courier.courier_detail?.vehicle_license_plate }}
          </div>
          <div>
            <img
                class="w-7.5"
                :src="VehicleTypeIcon[courier?.courier_detail?.vehicle_type_slug]"
                alt="vehice-type"
            />
          </div>

        </div>
      </div>
<!--      <div class="hidden group-hover:flex flex-col cursor-pointer divide-y border-l border-slate-300 h-full">-->
<!--        <div @click="drawerVisible" class=" w-full h-full flex items-center justify-center text-white bg-slate-500 hover:bg-slate-700 text-xs">-->
<!--          <ButtonToolTip class="h-full w-full flex items-center px-2" :tooltipText="$t('Edit')" position="left">-->
<!--            <FontAwesomeIcon icon="pen"/>-->
<!--          </ButtonToolTip>-->
<!--        </div>-->

<!--      </div>-->
    </div>
<!--    <el-drawer-->
<!--        v-model="visible"-->
<!--        class="customized-drawer "-->
<!--        :title="$t('Driver Edit')"-->
<!--        append-to-body-->
<!--        destroy-on-close-->
<!--    >-->
<!--        <CourierUpdate  @getData="getDataa" @close="close" :courierId="$props.courier.id"/>-->
<!--    </el-drawer>-->
  </td>
</template>
