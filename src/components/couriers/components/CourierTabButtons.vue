<script>
export default {
  name: "CourierTabButtons",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tab: {
      type: Object,
      default: () => {},
    },
  },
  emits: ["click"],
  setup() {
  },
};
</script>

<template>
  <div
      @click="$emit('click')"
      style="font-size: 12px;"
      :style=" { background: active ? '#d9d9d9' : '#FFF' }"
      class="px-4 h-7 cursor-pointer flex items-center overflow-hidden flex-shrink-0 text-slate-700 rounded-md mx-2 font-bold"

  >
    {{$t(tab.name)}}
  </div>

</template>
