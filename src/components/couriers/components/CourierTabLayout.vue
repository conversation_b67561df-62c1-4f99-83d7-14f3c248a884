<script setup>
import CourierTabs from "@/components/couriers/components/CourierTabs.vue";
</script>

<template>
  <div class="w-full h-full flex flex-col">
    <div class=" block md:flex items-center justify-between border-b border-slate-300">
      <CourierTabs/>
      <div class="h-10 grow">
        <slot name="courierAction"/>
      </div>
    </div>
    <div class="w-full h-full">
      <slot name="courierContent"></slot>
    </div>
    <slot name="courierDrawers"></slot>
  </div>
</template>
