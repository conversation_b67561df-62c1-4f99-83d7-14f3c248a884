<script setup>
import {computed, inject, onMounted, ref, watch,defineEmits} from "vue";
import dayjs from "dayjs";
import {useI18n} from "vue-i18n";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const {t} = useI18n();
const api = inject("api")

const emits = defineEmits(["thisWeek", "nextWeek", "lastWeek","firsPage"])
const props = defineProps({
  filters: {type: Object},
  date: {type: [Date, String, Object]}
})

const teamDisable = computed(() => !props.filters.hub_id)

const hubs = ref([])
const teams = ref([])

onMounted(() => {
  getHubs()
})

watch(() => props.filters.hub_id, () => {
  emits("firsPage",true)
  getTeams()
})

const getHubs = () => {
  return api("customer/hubs")
      .then((res) =>{
        hubs.value = res.data
      }
      )
}

const getTeams = () => {
  if (props.filters.hub_id) {
    api(`customer/teams?filter[hub_id]=${props.filters.hub_id}`)
        .then((res) => {
          teams.value = res.data
        })
  } else {
    props.filters.team_id = null
    teams.value = []
  }
}


const clearFilter =()=>{
  props.filters.hub_id = null
  props.filters.team_id = null
  props.filters.search = null
}

</script>

<template>
  <div class=" h-full flex items-center justify-between pr-7">
    <div class="flex items-center justify-end space-x-4">
      <el-select
          v-model="filters.hub_id"
          :placeholder="$t('Hub')"
          filterable
          size="small"

          style="width:200px"
          clearable
      >
        <el-option
            v-for="item in hubs"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        >
        </el-option>
      </el-select>
      <el-select
          v-model="filters.team_id"
          :disabled="teamDisable"
          :placeholder="$t('Team')"
          filterable
          size="small"
          style="width:200px"
          clearable
      >
        <el-option
            v-for="item in teams"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        >
        </el-option>
      </el-select>
      <ButtonToolTip v-if="filters.team_id || filters.hub_id || filters.search" :tooltipText="$t('Clear Filters')" position="left">
        <el-button
            size="small"
            @click="clearFilter"
        >
          <FontAwesomeIcon icon="filter-circle-xmark"/>
        </el-button>
      </ButtonToolTip>
    </div>

    <div class="flex items-center">
      <div
          @click="$emit('lastWeek')"
          class="text-slate-500 mr-4 py-1 px-2 border border-slate-300 rounded h-6 flex items-center justify-center"
      >
        <FontAwesomeIcon icon="chevron-left"/>
      </div>
      <div
          @click="$emit('thisWeek')"
          class="text-md text-slate-700 font-semibold "
      >
        {{ dayjs(date).format("MMMM") + " " + dayjs(date).format("YYYY") }}
      </div>
      <div
          @click="$emit('nextWeek')"
          class="text-slate-500 ml-4 py-1 px-2 border border-slate-300 rounded h-6 flex items-center justify-center"
      >
        <FontAwesomeIcon icon="chevron-right"/>
      </div>
    </div>
  </div>
</template>
