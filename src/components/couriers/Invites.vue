<script setup>
import CourierTabLayout from "@/components/couriers/components/CourierTabLayout.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {inject, onActivated, onMounted, ref} from "vue";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const api = inject("api")
const invitations = ref([]);
const invitationsGridApi = ref(null);
const loader = ref()
const inviteColumnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: 'agTextColumnFilter',
  },
  {
    field: "status_slug",
    headerName: t("Status"),
    filter: 'agSetColumnFilter',
    valueFormatter: (param) => formatter.inviteTypeSlug(param),
    keyCreator:(param) => formatter.inviteTypeSlug(param),

  },
  {
    field: "courier.name",
    headerName: t("Name"),
    filter: 'agTextColumnFilter',
  },
  {field: "courier.phone", headerName: t("Phone"), filter: 'agTextColumnFilter',},
  {field: "courier.id", headerName: t("Driver Id"), filter: 'agTextColumnFilter',},
  {
    field: "created_at",
    headerName: t("Created Date"),
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => formatter.date(params),
  },
]);

const promiseGetInvitations = () => {
  return new Promise((resolve, reject) => {
    api("customer/courier-invitations")
        .then((res) => {
          invitations.value = res.data
          resolve()
        })
        .catch(() => reject())
  })
}

onActivated(() => {
  loader.value.show(
      promiseGetInvitations()
          .finally(() => loader.value.hide())
  )
})

</script>

<template>
  <CourierTabLayout>
    <template #courierAction>
    </template>
    <template #courierContent>
      <LoadingBlock ref="loader"/>
      <DataGrid
          v-model="invitationsGridApi"
          :dataSource="invitations"
          :columns="inviteColumnDefs"
      />
    </template>

  </CourierTabLayout>
</template>
