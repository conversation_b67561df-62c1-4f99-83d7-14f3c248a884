<script setup>
import dayjs from "dayjs";
import {inject, onActivated, onMounted, ref, watch, reactive} from 'vue';
import {useI18n} from "vue-i18n";
import debounce from "lodash.debounce";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ViewBox from "@/components/couriers/daysOff/ViewBox.vue";
import CourierTabLayout from "@/components/couriers/components/CourierTabLayout.vue";
import CourierItem from "@/components/couriers/components/CourierItem.vue";
import ControllerViews from "@/components/couriers/components/ControllerViews.vue";
import EditDayDrawer from "@/components/couriers/daysOff/EditDayDrawer.vue";


const {t} = useI18n();
const api = inject("api")


const pagination = reactive({
  nextEnable: true,
  prevEnable: false,
})

const filters = reactive({
  starts_at: null,
  ends_at: null, perPage: 15,
  hub_id: null,
  team_id: null,
  search: null
})

const loader = ref()
const search = ref()
const date = ref()
const days = ref([])
const drawerVisible = ref(false)
const types = ref([])
const filteredCourierList = ref([])
const editDrawerData = ref()
const page =ref(0)

onMounted(() => {
  getTypes()
})

onActivated(() => {
  thisWeek()
})

watch(date, () => {
  fillDays()
})

watch(page, () => {
  debounceGetData()
}, {
  deep: true
})

watch(filters, () => {
 if (page.value ===1){
   debounceGetData()
 }
 page.value = 1


}, {
  deep: true
})


const fillDays = () => {
  let _days = []
  for (let i = 0; i <= 6; i++) {
    let _date = date.value.add(i, 'day')
    _days.push({
      day: i,
      date: _date,
    })
    if (i === 0) {
      filters.starts_at = _date.startOf('day').toISOString()
    }
    if (i === 6) {
      filters.ends_at = _date.endOf('day').toISOString()
    }

  }
  days.value = _days
}

const thisWeek = () => {
  return date.value = dayjs(date.value).startOf('week')
}

const nextWeek = () => {
  date.value = dayjs(date.value).weekday(7)
}

const lastWeek = () => {
  date.value = dayjs(date.value).weekday(-7)
}

const getTypes = () => {
  return api.post("components/day-off-types-select")
      .then((res) => types.value = res)
}


const getData = () => {

  loader.value.show()

  let queryFilter = {
    "filter[date]": filters.starts_at + "," + filters.ends_at,
    page: page.value,
    per_page: 15,
  }

  let formatterFilter = {}

  if (filters.hub_id) {
    formatterFilter.hub_id = filters.hub_id
  }

  if (filters.team_id) {
    formatterFilter.team_id = filters.team_id
  }

  if (filters.search) {
    formatterFilter.search = filters.search


  }


  let _filters = Object.fromEntries(
      Object.entries(formatterFilter).map(([key, value]) =>
          [`filter[${key}]`, value]
      )
  )

  api("customer/shift-calendar", {params: {...queryFilter, ..._filters}})
      .then((res) => {
        filteredCourierList.value = res.data
        pagination.prevEnable = !!res.links.prev
        pagination.nextEnable = !!res.links.next

      })
      .finally(() => loader.value.hide())

}

const debounceGetData = debounce(getData, 300)

const nextPage = () => {
  if (pagination.nextEnable) {
    page.value += 1
  }
}

const prevPage = () => {
  if (pagination.prevEnable) {
    page.value -= 1
  }
}

const openDrawerVisible = (data) => {
  editDrawerData.value = data
  drawerVisible.value = true
}

const closeDrawerVisible = (param) => {
  drawerVisible.value = false
  if (param) {
    getData()
  }
}


</script>

<template>
  <CourierTabLayout>
    <template #courierAction>
      <ControllerViews
          :filters="filters"
          :date="date"
          @thisWeek="thisWeek"
          @nextWeek="nextWeek"
          @lastWeek="lastWeek"

      />
    </template>
    <template #courierContent>
      <div class="h-full w-full flex flex-col">
        <LoadingBlock ref="loader"/>
        <div class="h-full w-full relative">
          <div class="absolute inset-0">
            <div class="courier-shift-table w-full h-full">
              <table>
                <thead>
                <tr>
                  <th>
                    <div class="w-full h-full border-r border-b border-slate-300 flex items-center px-4">
                      <el-input v-model="filters.search" :placeholder="t('Search')" class="rounded"/>
                    </div>
                  </th>
                  <th v-for="day in days">
                    <div
                        class="w-full h-full border-r border-b border-slate-300 flex flex-col items-center justify-center"
                        :class="[
                        dayjs().isSame(day.date, 'day') ? 'bg-indigo-600 text-white' : 'bg-slate-50 text-slate-700'
                    ]"
                    >
                      <div class="text-sm">
                        {{ dayjs(day.date).format("ddd").toLocaleUpperCase() }}
                      </div>
                      <div class="text-md font-bold">
                        {{ dayjs(day.date).format("DD") }}
                      </div>
                    </div>
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="courier in filteredCourierList" :key="courier.id">
                  <CourierItem  @getData="getData" :courier="courier"/>

                  <th v-for="day in days" :key="day.day">

                    <ViewBox
                        :day="day"
                        :courier="courier"
                        :types="types"
                        @openDrawerVisible="openDrawerVisible"
                        @refresh="getData"
                    />

                  </th>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div
            class="group flex items-center justify-start h-10 bg-slate-50 border border-slate-300 px-2.5 text-slate-500 font-bold">

          {{ page + " - " + (((page- 1) * 15) + filteredCourierList.length) }}

          <div @click="prevPage"
               class="text-xs py-1 px-2 cursor-pointer bg-white border border-slate-400 rounded mx-2"
               :class="[pagination.prevEnable ? null : 'opacity-30']"
          >
            <FontAwesomeIcon icon="chevron-left"/>
          </div>
          <div @click="nextPage"
               class="text-xs py-1 px-2 cursor-pointer bg-white border border-slate-400 rounded"
               :class="[pagination.nextEnable ? null : 'opacity-30']"
          >
            <FontAwesomeIcon icon="chevron-right"/>
          </div>
        </div>
      </div>
      <el-drawer
          v-model="drawerVisible"
          class="customized-drawer"
          append-to-body
          destroy-on-close
      >
        <template #header>
          <div class="text-white">
            <div>
              {{ editDrawerData?.courier?.name }}
            </div>
            <span class="leading-tight block text-xxs ">
          {{ dayjs(editDrawerData?.day?.date).format('D MMMM dddd') }}
        </span>
          </div>
        </template>
        <EditDayDrawer :day="day" :data="editDrawerData" :types="types" @close="closeDrawerVisible"/>
      </el-drawer>
    </template>
  </CourierTabLayout>
</template>

<style>


</style>
