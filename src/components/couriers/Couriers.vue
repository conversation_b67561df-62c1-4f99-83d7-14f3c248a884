<script>
import ActiveOrPassiveRenderer from "@/renderers/ActiveOrPassiveRenderer.vue";

export default {
  components: {
    ActiveOrPassiveRenderer,
  }
}

</script>

<script setup>
import CourierTabLayout from "@/components/couriers/components/CourierTabLayout.vue";
import NewCourier from "@/components/deliveries/NewCourier.vue";
import CourierActions from "@/components/deliveries/CourierActions.vue";
import {inject, ref} from 'vue';
import formatter from "@/class/formatter";
import {useI18n} from "vue-i18n";
import dayjs from "dayjs";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const {t} = useI18n()
const api = inject("api")
const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const courierDetailDrawerVisible = ref(false);
const newCourierDrawerVisible = ref(false);
const gridApi = ref(null);
const selectedCourier = ref();
const vehicleTypes = ref([])
const grid = ref()

const columnDefs = [
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "courier_detail.code",
    headerName: t("Code"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "teams",
    headerName: t("Teams"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "courier_detail.is_active",
    headerName: t("Status"),
    filter: 'agSettColumnFilter',
    cellStyle: {textAlign: "center", display: 'flex', alignItems: 'center', justifyContent: 'center'},
    cellRenderer: "ActiveOrPassiveRenderer",
    filterParams: {
      values: function (params) {
        params.success([true, false])
      },
      valueFormatter: (params) => {
        return JSON.parse(params.value) ? t("Active") : t("Passive")
      },
    },
  },
  {
    field: "courier_location.is_working",
    headerName: t("Working Status"),
    filter: 'agSettColumnFilter',
    width: 120,
    cellStyle: {textAlign: "center", display: 'flex', alignItems: 'center', justifyContent: 'center'},
    cellRenderer: "ActiveOrPassiveRenderer",
    filterParams: {
      values: function (params) {
        params.success([true, false])
      },
      valueFormatter: (params) => {
        return JSON.parse(params.value) ? t("Working") : t("Not Working")
      },
    },
  },
  {
    field: "courier_location.on_duty",
    headerName: t("On Duty Status"),
    filter: 'agSettColumnFilter',
    cellRenderer: "ActiveOrPassiveRenderer",
    cellStyle: {textAlign: "center", display: 'flex', alignItems: 'center', justifyContent: 'center'},
    filterParams: {
      values: function (params) {
        params.success([true, false])
      },
      valueFormatter: (params) => {
        return JSON.parse(params.value) ? t("On Duty") : t("Idle")
      },
    },
  },
  {
    field: "name",
    headerName: t("Name"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "phone",
    headerName: t("Phone"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "hubs",
    headerName: t("Hubs"),
    filter: 'agTextColumnFilter',
    sortable: false,
    valueGetter: params => params.data.hubs.map(hub => hub.name),
    valueFormatter: params => params.value.join(", ")
  },
  {
    field: "courier_detail.vehicle_type_slug",
    headerName: t("Vehicle Type"),
    filter: 'agSetColumnFilter',
    valueFormatter: (params) => {
      return params.data.courier_detail.vehicle_type
    },
    filterParams: {
      values: function (params) {
        api.post("components/vehicle-type-select")
          .then((res) => {
            vehicleTypes.value = res.data
            params.success(res.data.map(x => x.slug))
          })
      },
      valueFormatter: (params) => {
        return vehicleTypes.value.find(x => x.slug === params.value).name
      },
    },
  },
  {
    field: "courier_detail.vehicle_license_plate",
    headerName: t('License Plate'),
    filter: 'agTextColumnFilter',
  },
  {
    field: "courier_detail.capacity",
    headerName: t("Capacity"),
    filter: 'agNumberColumnFilter',
  },
  {
    field: "unit_capacity",
    headerName: t("Unit Capacity"),
    filter: 'agNumberColumnFilter',
  },
  {
    field: "current_task_capacity_status",
    headerName: t("Current Task"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "courier_location.last_seen_at",
    headerName: t("Last Seen At"),
    filter: 'agDateColumnFilter',
    filterParams: {
      comparator: function (filterLocalDateAtMidnight, cellValue) {
        if (cellValue === null) return -1;
        let cellDate = new Date(cellValue);
        if (dayjs(filterLocalDateAtMidnight).isSame(dayjs(cellDate), "time")) {
          return 0;
        }
        if (cellDate < filterLocalDateAtMidnight) {
          return -1;
        }
        if (cellDate > filterLocalDateAtMidnight) {
          return 1;
        }
      },
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "last_seen_minute",
    headerName: `${t("Last Seen At")} (${t('Min')})`,
    filter: 'agNumberColumnFilter',
  },
  {
    field: "courier_location.is_online",
    headerName: t("Online"),
    filter: 'agSettColumnFilter',
    cellStyle: {textAlign: "center", display: 'flex', alignItems: 'center', justifyContent: 'center'},
    cellRenderer: "ActiveOrPassiveRenderer",
    filterParams: {
      values: function (params) {
        params.success([true, false])
      },
      valueFormatter: (params) => {
        return JSON.parse(params.value) ? t("Online") : t("Offline")
      },
    },
  },
  {
    field: "tags",
    headerName: t("Tags"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "logged_in_once",
    headerName: t("Logged in once"),
    filter: 'agSetColumnFilter',
    sortable: true,
    valueFormatter: (params) => {
      return params.value ? t("Yes") : t("No")
    },
    filterParams: {
      values: function (params) {
        params.success([true, false])
      },
      valueFormatter: (params) => {
        return JSON.parse(params.value) ? t("Yes") : t("No")
      },
    },
  },
  {
    field: "employment_date",
    headerName: t("Join Date"),
    filter: 'agDateColumnFilter',
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "id",
    headerName: null,
    pinned: "right",
    width: 120,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openCourierDetail(params);
      },
    },
  },
]

const optionalFilter = [
  {
    old: "teams",
    new: "courierTeams.name",
  },
  {
    old: "tags",
    new: "tags.name"
  },
  {
    old: "hubs",
    new: "courierHubs.name",
  },
  {
    old: "last_seen_minute",
    new: "courierLocation.last_seen_at",
    newValue: [
      {time_type: "minutes"},
      {diff_type: "before"},
      {manipulation_type: "date"}
    ],
  }
]

const openCourierDetail = (id) => {
  selectedCourier.value = gridApi.value.getRowNode(id)?.data
  courierDetailDrawerVisible.value = true;
};

const rowClicked = () => {
  emit("taskClicked");
}

const rowDoubleClicked = (e) => {
  emit("taskDoubleClicked", e.data);
}

const exportExcel = () => {
  grid.value.openExportExcelDrawer()
}

const openNewCourierDrawer = () => {
  newCourierDrawerVisible.value = true;
};

const closeNewCourierDrawer = (param) => {
  newCourierDrawerVisible.value = false;
  if (param) {
    refresh()
    // promiseGetCouriers()
  }
};

const refresh = () => {
  grid.value.refresh()
}

const setDataSource = (response) => {
  gridApi.value.setRowData(response.map((x) => {
    x.last_seen_minute = Number(dayjs().diff(dayjs(x.courier_location.last_seen_at), "minute"))
    x.teams = x.teams.map(x => x.name).join(",")
    return x
  }))
}


const onRefresh = () => {
  grid.value.refresh()
}
const sideBar = {
  toolPanels: [
    {
      id: "columns",
      labelDefault: "Columns",
      labelKey: "columns",
      iconKey: "columns",
      toolPanel: "agColumnsToolPanel",
      toolPanelParams: {
        suppressRowGroups: true,
        suppressSyncLayoutWithGrid: true,
        suppressValues: true,
        suppressPivots: true,
        suppressPivotMode: true,
        suppressColumnFilter: true,
        suppressColumnSelectAll: true,
        suppressColumnExpandAll: true,
      },
    },
  ],
  defaultToolPanel: "columns",
};


</script>

<template>
  <CourierTabLayout>
    <template #courierAction>
      <div class="h-full flex items-center justify-end space-x-4 pr-7">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
          <el-button
            size="small"
            @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('Export')" position="bottom">
          <el-button
            size="small"
            @click="exportExcel"
          >
            <FontAwesomeIcon icon="file-excel"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Driver')" position="bottom">
          <el-button
            size="small"
            @click="openNewCourierDrawer"
          >
            <FontAwesomeIcon icon="plus"/>
          </el-button>
        </ButtonToolTip>
      </div>
    </template>
    <template #courierContent>
      <SSDataGrid
        ref="grid"
        v-model="gridApi"
        url="customer/couriers"
        columnStateSlug="couriers"
        excelExportType="couriers"
        :columns="columnDefs"
        :sideBar="sideBar"
        :setDataSourceMiddleware="true"
        :restore-column-state-enabled="true"
        :optionalFilter="optionalFilter"
        @setDataSource="setDataSource"
      />
    </template>
    <template #courierDrawers>
      <el-drawer
        v-model="newCourierDrawerVisible"
        class="customized-drawer"
        :title="t('New Driver')"
        append-to-body
        destroy-on-close
      >
        <NewCourier @close="closeNewCourierDrawer"/>
      </el-drawer>
      <CourierActions v-model="courierDetailDrawerVisible" :selectedCourier="selectedCourier"
                      @refresh="refresh"/>

    </template>
  </CourierTabLayout>
</template>
