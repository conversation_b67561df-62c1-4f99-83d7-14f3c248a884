<script setup>
import { computed } from "vue";
import { useStore } from "vuex";
import AnalyzeTabButton from "@/components/analyzes/AnalyzeTabButton.vue";

const { dispatch, getters } = useStore();

const activeAnalyzeTab = computed(() => getters["analyze/activeAnalyzeTab"]);
const analyzeTabs = computed(() => getters["analyze/analyzeTabs"]);

function isTabActive(tab) {
  return activeAnalyzeTab.value.title === tab.title;
}

function openAnalyzeTab(tab) {
  dispatch("analyze/setActiveAnalyzeTab", tab);
}

function closeAnalyzeTab(tab) {
  dispatch("analyze/closeAnalyzeTab", tab);
}


</script>

<template>

  <div class="flex-grow flex md:flex-col">
    <div
        v-for="tab in analyzeTabs"
        :key="tab.name"
    >
        <AnalyzeTabButton
            :active="isTabActive(tab)"
            :tab="tab"
            :disabled="!tab.name"
            @click="openAnalyzeTab(tab)"
        />
    </div>
  </div>

<!--  <AnalyzeTabButton-->
<!--      :active="isTabActive(oldConnectTabs[3])"-->
<!--      :tab="oldConnectTabs[3]"-->
<!--      :disabled="!oldConnectTabs[3].name"-->
<!--      @click="openAnalyzeTab(oldConnectTabs[3])"-->
<!--  />-->
</template>
