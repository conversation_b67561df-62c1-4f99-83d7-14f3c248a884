<template>
  <div class="flex justify-end ">
    <span
        :class="[
        'border border-r-0  px-3 py-1 text-base font-medium cursor-pointer',
        active
          ? 'pl-8 border-slate-300 bg-white'
          : ' border-transparent text-gray-900 hover:text-gray-900 hover:bg-gray-50',
      ]"
    >
      <slot></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: "AnalyzeSubTabItem",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: "",
    },
  },
};
</script>
