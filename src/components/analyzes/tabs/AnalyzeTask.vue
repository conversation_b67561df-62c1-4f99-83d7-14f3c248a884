<script>
 import {defineAsyncComponent} from "vue";

 export default {
   components:{
     General:defineAsyncComponent(()=>import("./subTabs/task/General.vue")),
     DeliveryPerformance:defineAsyncComponent(()=>import("./subTabs/task/DeliveryPerformance.vue")),
     DelayedPackage:defineAsyncComponent(()=>import("./subTabs/task/DelayedPackage.vue")),
     CourierBasedAverage:defineAsyncComponent(()=>import("./subTabs/task/CourierBasedAverage.vue")),
     HubBasedAverage:defineAsyncComponent(()=>import("./subTabs/task/HubBasedAverage.vue")),
     AverageDeliveryTime:defineAsyncComponent(()=>import("./subTabs/task/AverageDeliveryTime.vue")),
   }
 }
</script>
<script setup>
import AnalyzeSubTabItem from "@/components/analyzes/tabs/subTabs/AnalyzeSubTabItem.vue";
import { computed } from "vue";
import { useStore } from "vuex";

const { dispatch, getters } = useStore();

const activeTaskAnalyzeTab = computed(() => getters["analyze/activeTaskAnalyzeTab"]);
const taskAnalyzeTabs = computed(() => getters["analyze/taskAnalyzeTabs"]);

function isTabActive(tab) {
  return activeTaskAnalyzeTab.value.title === tab.title;
}

function openTaskAnalyzeTab(tab) {
  dispatch("analyze/setActiveTaskAnalyzeTab", tab);
}



</script>
<template>
  <div class="w-full h-full flex relative bg-slate-50">
    <div class="w-64 h-full border-r border-slate-300">
      <nav class=" h-full justify-end overflow-auto">
        <div class="flex flex-col sticky top-6 py-4">
          <template v-for="tab in taskAnalyzeTabs" :key="tab.component">
            <AnalyzeSubTabItem
                :active="activeTaskAnalyzeTab.name === tab.name"
                :icon="tab.icon"
                @click="openTaskAnalyzeTab(tab)"
            >{{ tab.title }}
            </AnalyzeSubTabItem>
          </template>
        </div>
      </nav>
    </div>
    <div class="grow flex-col">
      <keep-alive>
        <component
            :is="activeTaskAnalyzeTab.name"
            :title="activeTaskAnalyzeTab.title"
        ></component>
      </keep-alive>
    </div>
  </div>
</template>
