<script>
import { useStore } from "vuex";
export default {
  name: "AnalyzeTabButton",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tab: {
      type: Object,
      default: () => {},
    },
  },
  emits: ["click"],
  setup() {
    const { dispatch } = useStore();

    function closeAnalyzeTab(tab) {
      dispatch("analyze/closeAnalyzeTab", tab);
    }

    return { closeAnalyzeTab };
  },
};
</script>

<template>
  <div
      @click="$emit('click')"
      class="w-12 h-12 md:w-20 md:h-20 flex items-center justify-center cursor-pointer"
      :class="[
              active
                ? 'bg-white text-slate-700 border border-slate-300'
                : 'text-white',
            ]"
  >
    <FontAwesomeIcon  :icon="tab.icon" fixed-width />
  </div>

</template>
