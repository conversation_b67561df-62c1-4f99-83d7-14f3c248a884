<script>
import {ref, computed} from "vue";
import {useStore} from "vuex";
import MobileMenuLink from "@/components/header/components/MobileMenuLink.vue";
import MainTabButton from "@/components/mainTabs/MainTabButton.vue";
import LogoSvg from "@/assets/images/logo-wr.png";
import router from "@/router";
import Logo from "@/components/header/newHeader/Logo.vue";
import AddedActionPopover from "@/components/header/newHeader/AddedActionPopover.vue";
import GearMenuPopover from "@/components/header/newHeader/GearMenuPopover.vue";
import settings from "@/router/settings";
import {groupBy} from "@/class/helpers";

export default {
  name: "MainMenu",
  components: {
    GearMenuPopover,
    AddedActionPopover,
    Logo,
    MobileMenuLink,
    MainTabButton,
  },
  setup() {

    const isMenuVisible = ref(false);
    const isMoreMenuVisible = ref(false);
    const {getters} = useStore();
    const primaryColor = computed(() => getters["ui/primaryColor"]);

    const routes = computed(() => {

      const {role_slug, active_company} = getters["auth/me"]

      return router.getRoutes()
          .find(x => x.name === "Home").children
          .filter((route) => route.meta.type.includes(active_company.company_product_type)
              && route.meta.role.includes(role_slug)
              && !route.hidden)
    })

    const moreMenuList = computed(() => {
      const {role_slug, active_company} = getters["auth/me"]

      return settings.children
          .filter((route) => route.meta.type.includes(active_company.company_product_type)
              && route.meta.role.includes(role_slug)
              && !route.hidden)

    })

    function openMainTab(param) {
      router.push({path: param.path})
      hideMenu();
    }


    const toggleMenu = () => {
      isMenuVisible.value = !isMenuVisible.value;
    };

    const toggleMoreMenu = () => {
      isMoreMenuVisible.value = !isMoreMenuVisible.value;
    };

    const hideMenu = () => {
      isMenuVisible.value = false;
    };

    const hideMoreMenu = () => {
      isMoreMenuVisible.value = false
    }

    const goToDocs = () => {
      hideMenu()
      window.open("https://doc.qdelivery.app/docs", "_blank")
    }

    const goToRoute = (param) => {
      hideMenu()
      router.push({path: param.path})
    }


    return {
      moreMenuList,
      toggleMoreMenu,
      hideMoreMenu,
      isMoreMenuVisible,
      goToRoute,
      goToDocs,
      primaryColor,
      isMenuVisible,
      hideMenu,
      toggleMenu,
      LogoSvg,
      openMainTab,
      routes,
    };
  },
};
</script>

<template>
  <div>

    <div class="flex items-center justify-end">

      <div class="mr-2">
        <AddedActionPopover @callback="hideMenu"/>
      </div>
      <div
          class="text-white hover:text-white cursor-pointer focus:outline-none"
          @click="toggleMenu"
      >
        <FontAwesomeIcon icon="bars" fixed-width/>
      </div>
    </div>

    <transition
        enter-active-class="ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="ease-in duration-200"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
    >
      <div
          v-if="isMenuVisible"
          class="fixed inset-0 z-50  min-h-screen"
          :style="{ background: primaryColor }"
      >
        <div class="h-full flex flex-col pb-4">
          <div
              class="h-11 flex items-center justify-between px-2.5 text-white"
              :style="{ background: primaryColor }"
          >
            <Logo/>
            <div class="flex items-center justify-end">
              <div
                  class="text-white cursor-pointer focus:outline-none"
                  @click="hideMenu"
              >
                <FontAwesomeIcon icon="times"/>
              </div>
            </div>

          </div>
          <div class="h-full overflow-auto">
            <template v-for="route in routes" :key="route.name">
              <div
                  class="cursor-pointer flex items-center overflow-hidden flex-shrink-0 text-white border-b"
                  style="border-color:rgba(248,250,252,0.28)"
              >
                <div
                    class="flex px-2 py-2 group"
                    @click="goToRoute(route)"
                >
                   <span class="text-lg font-sans font-semibold">
                     {{ $t(route.name) }}
                   </span>
                </div>
              </div>
            </template>
            <template v-for="route in moreMenuList" :key="route.name">
              <div
                  class="cursor-pointer flex items-center overflow-hidden flex-shrink-0 text-white border-b"
                  style="border-color:rgba(248,250,252,0.28)"
              >
                <div
                    class="flex px-2 py-2 group"
                    @click="goToRoute(route)"
                >
                   <span class="text-lg font-sans font-semibold">
                     {{ $t(route.name) }}
                   </span>
                </div>
              </div>
            </template>

          </div>
          <div class="flex items-center justify-center pt-3">
            <div
                @click="goToDocs()"
                class="cursor-pointer flex items-center hover:text-slate-200 mr-3"
            >
              <FontAwesomeIcon icon="circle-question" size="lg"/>
            </div>
            <GearMenuPopover @callback="hideMenu"/>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>


