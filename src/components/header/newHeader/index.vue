<script setup>
import {computed} from "vue";
import {useStore} from "vuex";
import MenuController from "@/components/header/newHeader/MenuController.vue";
import CustomDateInput from "@/components/header/newHeader/CustomDateInput.vue";
import CustomHubSelectInput from "@/components/header/newHeader/CustomHubSelectInput.vue";
import Breadcrumb from "@/components/header/newHeader/Breadcrumb.vue";
import Logo from "@/components/header/newHeader/Logo.vue";
import HeaderActions from "@/components/header/newHeader/HeaderActions.vue";

const {getters} = useStore()

const me = computed(() => getters["auth/me"]);
const activeCompany = computed(() => getters["auth/activeCompany"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);


</script>

<template>
  <div>
    <div
        class="h-11 flex items-center justify-between px-2.5 text-white"
        :style="{ background: primaryColor }"
    >
      <Logo/>
      <HeaderActions/>
    </div>
    <div
        class="h-11 flex items-center justify-between pl-0.5 pr-2.5 border-b border-slate-200"
        style="background: #F8FAFC;"
    >
      <div class="hidden md:block">
        <MenuController/>
      </div>
      <div class="flex justify-end items-center">
        <template v-if="activeCompany.task_live_stream">
          <RealtimeStatus/>
        </template>

        <CustomHubSelectInput/>
        <div class="ml-2">
          <CustomDateInput/>
        </div>
      </div>
    </div>
<!--    <div class="px-3 py-1.5 bg-white border-b border-slate-200">-->
<!--      <Breadcrumb/>-->
<!--    </div>-->

  </div>

</template>
