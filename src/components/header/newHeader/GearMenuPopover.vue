<script setup>
import {capitalizeFirstLetter} from "@/class/helpers";
import {useStore} from "vuex";
import {computed, ref} from "vue";
import router from "@/router";


const {getters} = useStore()

const emits = defineEmits(["callback"])

const me = computed(() => getters["auth/me"]);
const activeCompany = computed(() => getters["auth/activeCompany"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);

const gearMenu = ref()

const logout = () => {
  gearMenu.value.hide()
  emits("callback")
  localStorage.removeItem("authToken")
  window.location.reload()
};

const goToSettings = () => {
  gearMenu.value.hide()
  emits("callback")
  router.push({name: "Profile"});
}


</script>

<template>
  <el-popover
      trigger="hover"
      :width="180"
      ref="gearMenu"
  >
    <template #default>
      <div class="flex items-center mb-4">
        <div
            :style="{ color: primaryColor }"
        >
          <FontAwesomeIcon icon="circle-user" size="2xl"/>
        </div>
        <div class="flex flex-col ml-2">
          <div
              class="text-xs font-semibold"
              style="line-height: 0.7rem!important;"
          >
            {{ me.name }}
          </div>
          <div class="text-xxs font-medium text-slate-300">
            {{ $t(capitalizeFirstLetter(me.role_slug)) }}
          </div>
        </div>
      </div>
      <div
          @click="goToSettings()"
          class="text-xs py-1 px-2 rounded font-semibold mb-1 cursor-pointer hover:bg-indigo-100 hover:text-slate-800"
      >
        {{ $t('Profile') }}
      </div>
      <div
          @click="logout"
          class="text-xs py-1 px-2 rounded font-semibold mb-1 cursor-pointer hover:bg-indigo-100 hover:text-slate-800"
      >
        {{ $t('Logout') }}
      </div>
    </template>
    <template #reference>
      <div
          class="cursor-pointer flex items-center hover:text-slate-200"

      >
        <FontAwesomeIcon icon="gear" size="lg"/>
      </div>
    </template>
  </el-popover>
</template>
