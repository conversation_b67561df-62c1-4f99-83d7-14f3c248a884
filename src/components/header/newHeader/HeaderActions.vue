<script setup>
import {capitalizeFirstLetter} from "@/class/helpers";
import {useStore} from "vuex";
import {computed, ref} from "vue";
import NewMobileMenu from "@/components/header/newHeader/NewMobileMenu.vue";
import router from "@/router";
import GearMenuPopover from "@/components/header/newHeader/GearMenuPopover.vue";

const {getters} = useStore()

const me = computed(() => getters["auth/me"]);
const activeCompany = computed(() => getters["auth/activeCompany"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);

const gearMenu = ref()

const logout = () => {
  gearMenu.value.hide()
  localStorage.removeItem("authToken")
  window.location.reload()
};

const goToSettings = () => {
  gearMenu.value.hide()
  router.push({name: "Profile"});
}

const goToDocs = () => {
  window.open("https://doc.qdelivery.app/docs", "_blank")
}

</script>

<template>
  <div class="hidden md:flex items-end">
    <div
        @click="goToDocs()"
        class="cursor-pointer flex items-center hover:text-slate-200 mr-3"
    >
      <FontAwesomeIcon icon="circle-question" size="lg"/>
    </div>
    <GearMenuPopover/>
  </div>
  <div class="md:hidden">
    <NewMobileMenu/>
  </div>
</template>
