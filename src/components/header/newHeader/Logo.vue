<script setup>
import LogoSvg from "../../../../public/logo.png";
import {computed, inject, onMounted, ref, shallowRef} from "vue";
import {useStore} from "vuex";

const api = inject("api");

const {getters, dispatch} = useStore()


const activeCompany = computed(() => getters["auth/activeCompany"]);
const me = computed(() => getters["auth/me"]);


const companies = ref([])
const selectedCompany = ref(activeCompany.value.id)
const loading = ref(true)

onMounted(() => {
  getData()
});

const getData = () => {
  loading.value = true
  api("customer/active-companies")
      .then((r) => companies.value = r.data.filter(x => x.is_active))
      .finally(() => loading.value = false)
}

const customSuffix = shallowRef({
      template: `
        <div
            class="text-sm text-white font-bold"
        >
          <font-awesome-icon icon="chevron-down"/>
        </div>
      `,
    }
)


const selectActiveCompany = (id) => {
  dispatch("ui/setLoading", true);
  api.put("customer/active-companies/" + id).then(() => {
    window.location.reload()
  });
};


</script>

<template>
  <div class="flex items-center ">

    <div
        v-if="activeCompany?.brand_logo_url"
        class="h-10 w-10 p-1.5"
    >

      <img
          :src="activeCompany?.brand_logo_url"
          alt="QDelivery Logo"
      />

    </div>
    <div
        v-else
        class="h-10 w-10 p-1.75"
    >
      <img
          :src="LogoSvg"
          alt="QDelivery Logo"
      />
    </div>
    <div v-if="!loading" class="px-3 flex items-center">
      <FontAwesomeIcon icon="buildings"/>
      <div v-if="companies.length === 1" class="ml-2 font-semibold">
        {{ activeCompany?.short_name ? activeCompany?.short_name : activeCompany?.name }}
      </div>
      <el-select
          v-else
          v-model="selectedCompany"
          class="custom-company-select ml-3"
          placeholder="Enabled"
          style="width:200px !important;  color: white !important;   font-weight: 600 !important; font-size: 14px !important;"
          @change="selectActiveCompany"
          :suffix-icon="customSuffix"
      >
        <el-option
            v-for="item in companies"
            :key="item.id"
            :label="item.short_name"
            :value="item.id"
        >
        </el-option>
      </el-select>
    </div>

  </div>
</template>
