<script setup>
import {computed, inject, ref} from "vue";
import router from "@/router";

import {useStore} from "vuex";
import settings from "@/router/settings";
import {groupBy} from "@/class/helpers";
import AddedActionPopover from "@/components/header/newHeader/AddedActionPopover.vue";

const {dispatch, getters} = useStore();

const emitter = inject("emitter")


const me = computed(() => getters["auth/me"]);
const secondaryColor = computed(() => getters["ui/secondaryColor"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);


const routes = computed(() => {

  const {role_slug, active_company} = getters["auth/me"]

  return router.getRoutes()
      .find(x => x.name === "Home").children
      .filter((route) => route.meta.type.includes(active_company.company_product_type)
          && route.meta.role.includes(role_slug)
          && !route.hidden)
})

const moreMenuList = computed(() => {
  const {role_slug, active_company} = getters["auth/me"]

  let data = settings.children
      .filter((route) => route.meta.type.includes(active_company.company_product_type)
          && route.meta.role.includes(role_slug)
          && !route.hidden)

  return groupBy(data, "subMenuTitle")
})

const isMoreMenuActive = computed(() => settings.children
    .filter(route => !route.hidden)
    .map(route => route.path)
    .includes(router.currentRoute.value.fullPath))

const goToRoute = (param) => {
  router.push({path: param.path})
}





</script>

<template>
  <nav class="flex space-x-0.5 overflow-y-auto px-2" aria-label="Tabs">
    <template v-for="route in routes" :key="route.name">
      <div
          class="cursor-pointer flex items-center overflow-hidden flex-shrink-0 text-slate-700 rounded-md"
          :style="{ background: $route.path.includes(route.path) ? '#d9d9d9' : '#F8FAFC' }"
      >
        <div
            class="flex px-2 py-1 group"
            @click="goToRoute(route)"
        >
           <span class=" font-sans font-semibold">
             {{ $t(route.name) }}
           </span>
        </div>
      </div>
    </template>
<!--    <el-popover-->
<!--        placement="bottom-start"-->
<!--        trigger="hover"-->
<!--        :width="288"-->
<!--    >-->
<!--      <template #reference>-->
<!--        <div-->
<!--            class="cursor-pointer flex items-center overflow-hidden flex-shrink-0 text-slate-700 rounded-md"-->
<!--            :style="{ background: isMoreMenuActive ? '#d9d9d9' : '#F8FAFC' }"-->
<!--        >-->
<!--          <div-->
<!--              class="flex px-2 py-1 group"-->
<!--          >-->
<!--           <span class=" font-sans font-semibold">-->
<!--                {{ $t('+More') }}-->
<!--           </span>-->
<!--          </div>-->
<!--        </div>-->
<!--      </template>-->

<!--      <template #default>-->
<!--        <div class="grid grid-cols-2 gap-4">-->
<!--          <div-->
<!--              v-for="(moreRoutes,key) in moreMenuList"-->
<!--              :key="key"-->
<!--              class="col-span-1"-->
<!--          >-->

<!--            <div class="font-semibold">-->
<!--              {{ $t(key) }}-->
<!--            </div>-->

<!--            <div-->
<!--                v-for="route in moreRoutes"-->
<!--                @click="goToRoute(route)"-->
<!--                :key="route.name"-->
<!--                class="text-xs font-light cursor-pointer hover:underline m-1 pl-1"-->
<!--                :class="[-->
<!--                  $route.path.includes(route.path)-->
<!--                    ? 'underline text-indigo-600'-->
<!--                    : ''-->
<!--                ]"-->

<!--            >-->
<!--              {{ $t(route.name) }}-->
<!--            </div>-->

<!--          </div>-->
<!--        </div>-->
<!--      </template>-->
<!--    </el-popover>-->


    <AddedActionPopover/>
  </nav>
</template>
