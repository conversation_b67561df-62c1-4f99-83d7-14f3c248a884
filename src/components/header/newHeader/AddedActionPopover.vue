<script setup>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { computed, ref, inject } from "vue";
import { useStore } from "vuex";
import {useI18n} from "vue-i18n";


const emitter = inject("emitter")

const { dispatch, getters } = useStore();

const emits = defineEmits(["callback"])

const actionPopover = ref()

const me = computed(() => getters["auth/me"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);


const close = () => {
  emits("callback")
  actionPopover.value.hide()
}
// TODO
const actionList = [
  {
    title: "New Delivery",
    description: "",
    icon: "cart-plus",
    action: () => {
      close()
      emitter.emit("open_new_task")
    },
    role: ["admin", "customer", "manager", "dispatcher"],
    type: ["qd"],
  },
  {
    title: "New Quick Delivery",
    description: "",
    icon: "cart-plus",
    action: () => {
      close()
      emitter.emit("open_new_quick_task")
    },
    role: ["admin", "customer", "manager", "dispatcher"],
    type: ["kuik", "qd-kuik"],
  },
  {
    title: "New Driver",
    description: "",
    icon: "id-card",
    action: () => {
      close()
      emitter.emit("open_new_driver")
    },
    role: ["admin", "customer", "manager", "dispatcher"],
    type: ["qd", "qd-kuik"],
  },
  {
    title: "Import",
    description: "",
    icon: "file-excel",
    action: () => {
      close()
      emitter.emit("open_import_excel")
    },
    role: ["admin", "customer", "manager", "dispatcher"],
    type: ["qd", "qd-kuik"],
  }
]

const actions = computed(() => {

  const { role_slug, active_company } = getters["auth/me"]
  return actionList.filter((item) => item.type.includes(active_company.company_product_type)
    && item.role.includes(role_slug))
})

</script>


<template>
  <el-popover
    ref="actionPopover"
    placement="bottom-start"
    trigger="click"
    :width="200"
  >
    <template #reference>
      <div
        id="action_octagon_plus"
        class="cursor-pointer px-2 py-1 text-white md:text-slate-700"
      >
        <FontAwesomeIcon
          icon="octagon-plus"
          size="lg"
        />
      </div>
    </template>

    <template #default>
      <div
        v-for="item in actions"
        :key="item.title"
        @click="item.action()"
        class="mb-2 py-1 pr-1 pl-1 rounded hover:bg-indigo-100 cursor-pointer"
      >

        <div class="flex items-center">
          <div
            class="mr-2"
            :style="{ color: primaryColor, width: '18px', display: 'flex', alignItems: 'center', justifyContent: 'center' }"
          >
            <FontAwesomeIcon :icon="item.icon" />
          </div>
          <div class="font-semibold ">
            {{ $t(item.title) }}
          </div>
        </div>
        <div
          v-if="item.description"
          class="text-xxxs font-light"
        >
          {{ $t(item.description) }}
        </div>
      </div>
    </template>
  </el-popover>
</template>
