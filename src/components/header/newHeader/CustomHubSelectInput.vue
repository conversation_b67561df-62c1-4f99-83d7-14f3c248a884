<script setup>
import {computed, inject, onMounted, ref, shallowRef, watch} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import {useRoute} from "vue-router";

const route = useRoute()
const {t} = useI18n()
const {getters, dispatch} = useStore()

const api = inject("api")
const emitter = inject("emitter")

const me = computed(() => getters["auth/me"])
const selectedHub = computed(() => getters["operations/selectedHub"])
const primaryColor = computed(() => getters["ui/primaryColor"]);

const hubs = ref([])
const hub = ref()
const loading = ref(false)

onMounted(() => {
  getHubs()
  if (selectedHub.value) {
    hub.value = selectedHub.value.id
  } else {
    hub.value = 0
  }
})

watch(selectedHub, () => {
  if (selectedHub.value) {
    hub.value = selectedHub.value.id
  } else {
    hub.value = 0
  }
})

watch(hub, () => {
  if (selectedHub.value) {
    selectedHub.value.id = hub.value
  } else {
    hub.value = 0
  }
})

emitter.on('hubUpdated', () => {
  getHubs()
})

const getHubs = () => {
  loading.value = true
  api("customer/hubs?filter[is_active]=true")
      .then((response) => {

        if (me.value?.active_company.company_product_type === "qd") {
          response.data.unshift({id: 0, name: t("All")})
          dispatch("operations/setSelectedHub", {id: 0, name: t("All")})
          setActiveHub(0) // All seçeneğini aktif hub olarak ayarla
        }

        dispatch("operations/setHubs", response.data)
        hubs.value = response.data
      })
      .finally(() => loading.value = false)
}

const setActiveHub = (id) => {
  if (id) {
    api.put(`customer/active-hub/${id}`)
        .then((response) => {
          dispatch("operations/setSelectedHub", response)
          emitter.emit("active.hub.changed", response)
        })
  } else {
    api.delete(`customer/active-hub`)
        .then(() => {
          dispatch("operations/setSelectedHub", null)
          emitter.emit("active.hub.changed", null)
        })
  }
}

const customPrefix = shallowRef({

      setup() {
        const {getters} = useStore()

        const primaryColor = computed(() => getters["ui/primaryColor"]);

        return {
          primaryColor
        }
      },
      template: `
        <div
            class="text-sm"
            :style="{ color: primaryColor }"
        >
          <font-awesome-icon icon="store"/>
        </div>
      `,
    }
)

</script>

<template>
  <el-select
      v-if="route.name === 'Operation' || route.name === 'Connect'"
      class="custom-hub-select-input"
      style="width: 240px;"
      v-model="hub"
      @change="setActiveHub"
      :placeholder="$t('Hub')"
      size="small"
      filterable
      :loading="loading"
      :prefix-icon="customPrefix"
  >
    <template #prefix>
      <div
          class="text-base"
          :style="{ color: primaryColor }"
      >
        <FontAwesomeIcon icon="store"/>
      </div>
    </template>
    <el-option
        v-for="item in hubs"
        :key="item.id"
        :label="`${item.integration_id ? item.integration_id + ' - ' : ''}${item.name}`"
        :value="item.id"
    />
  </el-select>
</template>
