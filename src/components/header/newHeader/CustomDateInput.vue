<script setup>
import {computed, ref, shallowRef, onMounted, watch} from 'vue';
import {useRoute} from "vue-router";
import {useStore} from "vuex";

const {dispatch} = useStore()

const route = useRoute()


const date = ref(new Date())

const customPrefix = shallowRef({

      setup() {
        const {getters} = useStore()

        const primaryColor = computed(() => getters["ui/primaryColor"]);

        return {
          primaryColor
        }
      },
      template: `
        <div
            class="text-sm"
            :style="{ color: primaryColor }"
        >
          <font-awesome-icon icon="calendar-days"/>
        </div>
      `,
    }
)

onMounted(() => {
  date.value = new Date()
})

watch(date, () => {
  dispatch("operations/setSelectedDate", date.value)
})

</script>

<template>

  <el-date-picker
      v-if="route.name === 'Operation'"
      class="custom-main-datepicker"
      v-model="date"
      size="small"
      type="date"
      placeholder="Pick a day"
      format="D MMMM"
      :editable="false"
      :clearable="false"
      :prefix-icon="customPrefix"
  />



</template>
