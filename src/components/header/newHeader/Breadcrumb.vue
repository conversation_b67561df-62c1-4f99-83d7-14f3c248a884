<script setup>
import {ArrowRight} from '@element-plus/icons-vue'
import {computed, onMounted, ref, shallowRef, watch} from "vue";
import {useRouter} from "vue-router";
import {useStore} from "vuex";

const router = useRouter()

const crumbs = ref([])

onMounted(() => {
  crumbs.value = router.currentRoute.value.matched.map(x => x.name)
})

watch(() => router, () => {
  crumbs.value = router.currentRoute.value.matched.map(x => x.name)
}, {deep: true})


const customPrefix = shallowRef({

      setup() {
        const {getters} = useStore()

        const primaryColor = computed(() => getters["ui/primaryColor"]);

        return {
          primaryColor
        }
      },
      template: `
        <div
            class="text-base"
            :style="{ color: primaryColor }"
        >
          <font-awesome-icon icon="arrow-right"/>
        </div>
      `,
    }
)

</script>

<template>
  <el-breadcrumb :separator-icon="ArrowRight">
    <el-breadcrumb-item
        v-for="(item,index) in crumbs"
        class="text-xxs"
    >
      <div
          :class="[
              index === crumbs.length - 1
                  ? ' font-semibold text-indigo-700'
                  : 'font-light text-slate-500'
          ]"
      >
        {{ $t(item) }}
      </div>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>
