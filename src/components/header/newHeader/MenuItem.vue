<script setup>

import {computed, ref} from "vue";
import {useStore} from "vuex";

const {dispatch, getters} = useStore();

const props = defineProps({
  route: {
    type: Object, default: () => {
    }
  }
})

const secondaryColor = computed(() => getters["ui/secondaryColor"]);

const isHovered = ref(false)

</script>

<template>
  <div
      class="cursor-pointer flex items-center overflow-hidden flex-shrink-0 text-slate-700 rounded-md"
      :style="{ background: isHovered ?  secondaryColor : 'white' }"
  >
    <div
        class="flex px-2 py-1 group"
        @mouseover="isHovered = true"
        @mouseout="isHovered = false"
    >
           <span class=" font-sans font-semibold">
             {{ $t(route.name) }}
           </span>
    </div>
  </div>
</template>
