<script setup>
import LogoSvg from "@/assets/images/logo-wr.png";
import KuikLogoSvg from "@/assets/images/kuik-logo-dark.svg";
import MainTabs from "@/components/mainTabs/MainTabs.vue";
import ActiveCompanySelect from "@/components/header/components/ActiveCompanySelect.vue";
import UserMenu from "@/components/header/components/UserMenu.vue";
import MobileMenu from "@/components/header/components/MobileMenu.vue";
import {computed, inject, onMounted, nextTick, ref} from "vue";
import {useStore} from "vuex";


const {getters} = useStore();
const emitter = inject("emitter");
const openNewTask = () => emitter.emit("open_new_task");
const me = computed(() => getters["auth/me"]);
const activeCompany = computed(() => getters["auth/activeCompany"]);
const secondaryColor = computed(() => getters["ui/secondaryColor"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);
const infosetEnable = ref(JSON.parse(import.meta.env.VITE_INFOSET_ENABLE))


</script>

<template>
  <header class="relative z-20">
    <div
        class="px-4 items-center flex justify-between h-11"
        :style="{ background: primaryColor }"
    >
      <div class="flex-shrink-0">
        <img
            :src="me.active_company.company_product_type !== 'qd' ? KuikLogoSvg :activeCompany?.brand_logo_url ? activeCompany.brand_logo_url : LogoSvg "
            class="w-10 h-10" alt="QDelivery Logo"/>
      </div>
      <div class="place-self-end mt-auto flex-grow px-4 flex sm:hidden">
        <MobileMenu/>
      </div>
      <div
          class="hidden sm:flex flex-grow overflow-x-auto px-2 place-self-end mt-auto"
      >
        <MainTabs/>
      </div>
      <div class="text-white flex items-center space-x-1 sm:space-x-4">
        <div
            v-if="activeCompany.company_product_type=== 'qd'"
            @click="openNewTask"
            class="h-7 flex items-center px-3 py-2 text-black font-bold text-xs focus:outline-none cursor-pointer rounded-md"
            :style="{ background: secondaryColor }"
        >
          <FontAwesomeIcon icon="plus" fixed-width class="mr-0 sm:mr-2"/>
          <div
              class="hidden sm:flex truncate uppercase"
              style="max-width: 120px"
          >
            {{ $t('New Delivery') }}
          </div>
        </div>
        <div>

            <ActiveCompanySelect/>


        </div>
        <div>
          <UserMenu/>
        </div>
      </div>
    </div>
    <div class="bg-white h-1.5 border-b border-slate-300"></div>
  </header>

</template>
<style scoped>
/*#element_id {*/
/*  display: flex;*/

/*  position: fixed;*/
/*  z-index: 999;*/
/*  left: 0;*/
/*  bottom: 0;*/
/*  */
/*}*/
</style>
