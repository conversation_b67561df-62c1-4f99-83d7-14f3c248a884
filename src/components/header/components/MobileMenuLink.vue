<template>
  <div
    class="block px-4 py-2 cursor-pointer"
    :class="[
      active
        ? 'bg-white text-slate-700'
        : disabled
        ? 'bg-indigo-575 text-indigo-525 cursor-not-allowed'
        : 'bg-indigo-550 text-slate-50 hover:bg-indigo-700',
    ]"
  >
    <div class="flex items-start">
      <FontAwesomeIcon
        v-if="icon"
        :icon="icon"
        class="mr-2 mt-1 flex-shrink-0 text-white"
        :class="[
          active
            ? 'text-slate-700'
            : disabled
            ? 'text-indigo-525 cursor-not-allowed'
            : 'text-slate-50',
        ]"
        fixed-width
        size="lg"
      />
      <div :class="[icon ? 'ml-4' : '']">
        <p class="text-sm font-bold">{{ $t(title) }}</p>
        <p
          v-if="description"
          class="text-xs"
          :class="[active ? 'text-slate-500' : '']"
        >
          {{ description }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  props: {
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    description: {
      type: String,
      default: "",
    },
    active: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
});
</script>
