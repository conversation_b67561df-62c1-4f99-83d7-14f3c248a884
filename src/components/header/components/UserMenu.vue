<script setup>
import DropdownMenuItem from "./DropdownMenuItem.vue";
import router from "@/router";
import {useStore} from "vuex";

const {dispatch, getters} = useStore();

const goTo = () => {
  localStorage.removeItem("authToken")
  window.location.reload()
};

const goToSelectCompany = () => {
  router.push({name: "SelectActiveCompany"});
}

const goToSettings = () => {
  router.push({name: "Settings"});
}


</script>

<template>
  <el-dropdown size="medium" trigger="click">
    <div
        class="p-1 text-white cursor-pointer focus:outline-none"
    >
      <FontAwesomeIcon icon="user-circle" size="lg" fixed-width/>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item  @click="goToSettings()"
        >
          <DropdownMenuItem icon="gear">
            {{ $t("Settings") }}
          </DropdownMenuItem>
        </el-dropdown-item>
      </el-dropdown-menu>
      <el-dropdown-menu>
        <el-dropdown-item @click="goToSelectCompany()"
        >
          <DropdownMenuItem icon="building">
            {{ $t("Companies") }}
          </DropdownMenuItem>
        </el-dropdown-item>
      </el-dropdown-menu>
      <el-dropdown-menu>
        <el-dropdown-item @click="goTo()"
        >
          <DropdownMenuItem icon="sign-out-alt">
            {{ $t("Logout") }}
          </DropdownMenuItem>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

