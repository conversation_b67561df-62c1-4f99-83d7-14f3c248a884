<template>
  <div>
    <div class="flex items-center">
      <div>
        <template
            v-for="(tab, index) in routes"
            :key="tab.name"
        >
          <MainTabButton
              v-if="index === 0"
              :active="$route.path.includes(tab.path)"
              :tab="tab"
              :disabled="!tab.name"
              @click="openMainTab(tab)"
          />
        </template>
      </div>
      <div
          class="p-2 text-white hover:text-white cursor-pointer focus:outline-none"
          @click="toggleMenu"
      >
        <FontAwesomeIcon icon="bars" fixed-width/>
      </div>
    </div>

    <transition
        enter-active-class="ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="ease-in duration-200"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
    >
      <div
          v-if="isMenuVisible"
          class="fixed inset-0 z-50 min-w-max min-h-screen"
      >
        <div
            class="fixed inset-0 w-full h-full bg-black opacity-30 z-0"
            @click="hideMenu"
        ></div>
        <div class="relative max-w-xs h-full z-10">
          <div
              class="relative bg-indigo-600 w-full h-full overflow-y-scroll"
              style="box-shadow: 10px 0 25px 0 rgba(0, 0, 0, 0.65)"
          >
            <div class="sticky inset-0 bg-indigo-600 pb-1">
              <div class="flex justify-between p-2 items-center">
                <div>
                  <img class="h-8 w-8" :src="LogoSvg" alt="QDelivery"/>
                </div>
                <div
                    class="cursor-pointer p-1 hover:bg-indigo-500"
                    @click="hideMenu"
                >
                  <FontAwesomeIcon
                      icon="times"
                      size="lg"
                      fixed-width
                      class="text-white"
                  />
                </div>
              </div>
            </div>
            <nav>
              <template v-for="tab in routes" :key="tab.name">
                <MobileMenuLink
                    :active="$route.path.includes(tab.path)"
                    :disabled="!tab.name"
                    :title="tab.name"
                    @click="openMainTab(tab)"
                />
              </template>
            </nav>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import {ref, computed} from "vue";
import {useStore} from "vuex";
import MobileMenuLink from "@/components/header/components/MobileMenuLink.vue";
import MainTabButton from "@/components/mainTabs/MainTabButton.vue";
import LogoSvg from "@/assets/images/logo-wr.png";
import router from "@/router";

export default {
  name: "MainMenu",
  components: {
    MobileMenuLink,
    MainTabButton,
  },
  setup() {
    const isMenuVisible = ref(false);
    const {getters} = useStore();

    const routes = computed(() => {

      const {role_slug, active_company} = getters["auth/me"]

      return router.getRoutes()
          .find(x => x.name === "Home").children
          .filter((route) => {
                if (route.meta.type.includes(active_company.company_product_type) && route.meta.role.includes(role_slug)) {
                  return route
                }
              }
          )
    })

    function openMainTab(param) {
      router.push({path: param.path})
      hideMenu();
    }


    const toggleMenu = () => {
      isMenuVisible.value = !isMenuVisible.value;
    };

    const hideMenu = () => {
      isMenuVisible.value = false;
    };

    return {
      isMenuVisible,
      hideMenu,
      toggleMenu,
      LogoSvg,
      openMainTab,
      routes,
    };
  },
};
</script>
