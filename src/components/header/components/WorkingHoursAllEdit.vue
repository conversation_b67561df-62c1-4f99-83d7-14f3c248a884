<!--<script setup>-->
<!--import {useI18n} from "vue-i18n";-->
<!--import {reactive, ref, inject, onMounted, watch, computed} from "vue";-->
<!--import LoadingBlock from "@/components/ui/LoadingBlock.vue";-->
<!--import formatter from "@/class/formatter";-->
<!--import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";-->
<!--import {useToast} from "vue-toastification";-->
<!--import {useStore} from "vuex";-->

<!--const {t} = useI18n()-->
<!--const api = inject('api')-->
<!--const loader = ref()-->
<!--const emit = defineEmits(["close"])-->
<!--const toast = useToast()-->
<!--const {dispatch, getters} = useStore();-->

<!--const me = computed(() => getters["auth/me"])-->


<!--const workingTemplates = ref()-->
<!--const workingTemplatesForHub = ref()-->
<!--const workingTemplatesForCourier = ref()-->
<!--const selectedWorkingHourPlan = ref()-->
<!--const planList = ref([])-->

<!--const formWorkingHour = ref()-->

<!--const defaultList = [-->
<!--  {-->
<!--    day_name: "monday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  },-->
<!--  {-->
<!--    day_name: "tuesday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  },-->
<!--  {-->
<!--    day_name: "wednesday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  },-->
<!--  {-->
<!--    day_name: "thursday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  },-->
<!--  {-->
<!--    day_name: "friday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  },-->
<!--  {-->
<!--    day_name: "saturday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  },-->
<!--  {-->
<!--    day_name: "sunday",-->
<!--    starts_at: "09:00",-->
<!--    ends_at: "22:00",-->
<!--    is_close: true,-->
<!--    break_time: 0,-->
<!--    auto_working_on: false,-->
<!--    auto_working_off: false-->
<!--  }-->
<!--]-->
<!--const defaultPlanForm = reactive(defaultList)-->

<!--const form = reactive({-->
<!--  type: null,-->
<!--  name: null,-->
<!--  is_default: false,-->
<!--})-->


<!--  const typelist = me.value.active_company_id === 52 ? ref([-->
<!--    {-->
<!--      name: t("Hub"),-->
<!--      type: "hub"-->
<!--    },-->

<!--  ]) : ref([-->
<!--    {-->
<!--      name: t("Hub"),-->
<!--      type: "hub"-->
<!--    },-->
<!--    {-->
<!--      name: t("Driver"),-->
<!--      type: "courier"-->
<!--    }-->
<!--  ])-->





<!--const getWorkingHourTemplates = () => {-->
<!--  loader.value.show()-->
<!--  api("customer/working-hour-templates")-->
<!--      .then((res) => {-->
<!--        workingTemplates.value = res.data-->

<!--        workingTemplatesForHub.value = res.data.filter(x => x.type === 'hub')-->
<!--        workingTemplatesForCourier.value = res.data.filter(x => x.type === 'courier')-->
<!--      })-->
<!--      .finally(() => loader.value.hide())-->
<!--}-->


<!--const getData = () => {-->
<!--  if (formWorkingHour.value) {-->
<!--    loader.value.show()-->
<!--    api(`customer/working-hour-templates/${formWorkingHour.value}`)-->
<!--        .then((res) => {-->
<!--          selectedWorkingHourPlan.value = res-->
<!--          form.is_default = res.is_default-->
<!--          let arr = []-->
<!--          let _list = res.working_hours.map(x => {-->
<!--            return {-->
<!--              ...x,-->
<!--              starts_at: x.starts_at ? x.starts_at.split(":").slice(0, 2).join(":") : null,-->
<!--              ends_at: x.ends_at ? x.ends_at.split(":").slice(0, 2).join(":") : null,-->
<!--              is_closed: x.is_open,-->
<!--              break_time: res.type === 'courier' ? x.break_time : null,-->
<!--              auto_working_on: res.type === 'courier' ? x.auto_working_on : null,-->
<!--              auto_working_off: res.type === 'courier' ? x.auto_working_off : null-->
<!--            }-->
<!--          })-->
<!--          arr.push(_list.find(x => x.day_name === "monday"))-->
<!--          arr.push(_list.find(x => x.day_name === "tuesday"))-->
<!--          arr.push(_list.find(x => x.day_name === "wednesday"))-->
<!--          arr.push(_list.find(x => x.day_name === "thursday"))-->
<!--          arr.push(_list.find(x => x.day_name === "friday"))-->
<!--          arr.push(_list.find(x => x.day_name === "saturday"))-->
<!--          arr.push(_list.find(x => x.day_name === "sunday"))-->

<!--          planList.value = [...arr]-->

<!--        })-->
<!--        .finally(() => {-->
<!--          loader.value.hide()-->
<!--        })-->
<!--  }-->


<!--}-->

<!--const createWorkingHourTemplate = () => {-->
<!--  loader.value.show()-->
<!--  emit("close", true)-->
<!--  api.patch(`customer/working-hour-templates/${formWorkingHour.value}`, {-->
<!--    name: selectedWorkingHourPlan.value.name,-->
<!--    is_default: form.is_default,-->
<!--    working_hours: addWorkingHoursToCreatedTemplate(),-->
<!--    type: selectedWorkingHourPlan.value.type-->
<!--  })-->
<!--      .then(() => {-->
<!--        toast.success('Çalışma planınız güncellendi.')-->

<!--      })-->
<!--      .catch((err) => toast.error(err.message))-->
<!--      .finally(() => {-->
<!--        loader.value.hide()-->
<!--      })-->
<!--}-->

<!--const addWorkingHoursToCreatedTemplate = () => {-->
<!--  let arr = []-->
<!--  planList.value.forEach((x) => {-->
<!--    let body = {-->
<!--      day_name: x.day_name,-->
<!--      starts_at: x.starts_at,-->
<!--      ends_at: x.ends_at,-->
<!--      is_open: x.is_open,-->
<!--      break_time: selectedWorkingHourPlan.value.type === 'courier' ? x.break_time : 0,-->
<!--      auto_working_on: selectedWorkingHourPlan.value.type === 'courier' ? x.auto_working_on : false,-->
<!--      auto_working_off: selectedWorkingHourPlan.value.type === 'courier' ? x.auto_working_off : false-->
<!--    }-->
<!--    arr.push(body)-->
<!--  })-->

<!--  return arr-->

<!--}-->



<!--onMounted(() => {-->

<!--  getWorkingHourTemplates()-->

<!--  if (me.value.active_company_id === 5) {-->
<!--    const newTypeList = typelist.value.filter(x=>x.type !== 'courier')-->
<!--    return newTypeList-->
<!--  }-->
<!--})-->
<!--watch(formWorkingHour, () => {-->
<!--  if (formWorkingHour.value) {-->
<!--    getData()-->
<!--  }-->
<!--}, {deep: true})-->

<!--watch(() => form.type, () => {-->
<!--  planList.value = []-->
<!--  formWorkingHour.value = null-->
<!--})-->




<!--</script>-->
<!--<template>-->

<!--  <div-->
<!--      class="w-full text-slate-700 text-sm working-hour-plan"-->
<!--  >-->
<!--    <LoadingBlock ref="loader"/>-->
<!--    <div class="grid gap-4 grid-cols-5 border-b border-slate-300 p-7">-->
<!--      <div class="col-span-1 flex items-center font-semibold">-->
<!--        {{ $t("Type") }}-->
<!--      </div>-->

<!--      <el-select-->
<!--          class="col-span-3"-->
<!--          placeholder="Tip"-->
<!--          filterable-->
<!--          v-model="form.type"-->
<!--      >-->
<!--        <el-option-->
<!--            v-for="item in typelist"-->
<!--            :key="item.type"-->
<!--            :label="item.name"-->
<!--            :value="item.type"-->
<!--        >-->
<!--        </el-option>-->
<!--      </el-select>-->
<!--    </div>-->
<!--    <div v-if="form.type" class="grid gap-4 grid-cols-5 border-b border-slate-300 p-7">-->
<!--      <div class="col-span-1 flex items-center font-semibold">-->
<!--        {{ $t("Plan Name") }}-->
<!--      </div>-->
<!--      <el-select-->
<!--          v-if="form.type === 'courier'"-->
<!--          class="col-span-3"-->
<!--          placeholder="Tip"-->

<!--          filterable-->
<!--          v-model="formWorkingHour"-->
<!--      >-->

<!--        <el-option-->
<!--            v-for="item in workingTemplatesForCourier"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id"-->
<!--        >-->
<!--          <span style="float: left">{{ item.name }}</span>-->
<!--          <span v-if="item.is_default"-->
<!--                style="-->
<!--                        float: right;-->
<!--                        color: var(&#45;&#45;el-text-color-secondary);-->
<!--                        font-size: 13px;-->
<!--        "-->
<!--          > <FontAwesomeIcon icon="check" class="text-md "/></span-->
<!--          >-->

<!--        </el-option>-->
<!--      </el-select>-->
<!--      <el-select-->
<!--          v-if="form.type === 'hub'"-->
<!--          class="col-span-3"-->
<!--          placeholder="Tip"-->
<!--          filterable-->
<!--          v-model="formWorkingHour"-->
<!--      >-->
<!--        <el-option-->
<!--            v-for="item in workingTemplatesForHub"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id"-->
<!--        >-->
<!--          <span style="float: left">{{ item.name }}</span>-->
<!--          <span v-if="item.is_default"-->
<!--                style="-->
<!--                        float: right;-->
<!--                        color: var(&#45;&#45;el-text-color-secondary);-->
<!--                        font-size: 13px;-->
<!--        "-->
<!--          > <FontAwesomeIcon icon="check" class="text-md "/></span-->
<!--          >-->

<!--        </el-option>-->
<!--      </el-select>-->
<!--      <div class="col-span-1 text-center">-->
<!--        &lt;!&ndash;        <el-checkbox&ndash;&gt;-->
<!--        &lt;!&ndash;            v-model="form.is_default"&ndash;&gt;-->
<!--        &lt;!&ndash;        >&ndash;&gt;-->
<!--        &lt;!&ndash;          {{ $t('Default') }}&ndash;&gt;-->
<!--        &lt;!&ndash;        </el-checkbox>&ndash;&gt;-->
<!--      </div>-->
<!--    </div>-->
<!--    <div-->

<!--        v-for="item in planList" class="grid grid-cols-5  gap-4 border-b border-slate-300 px-4 py-2.5">-->

<!--      <div class="col-span-1 flex items-center justify-end font-medium">-->
<!--        {{ $t(formatter.capitalizeFirstLetter(item.day_name)) }}-->
<!--      </div>-->
<!--      <div class="col-span-3 grid-cols-4 flex items-center justify-evenly">-->
<!--        <div class="w-32">-->
<!--          <el-time-select-->
<!--              v-model="item.starts_at"-->
<!--              :disabled="!item.is_open"-->
<!--              :clearable="false"-->
<!--              :editable="false"-->
<!--              start="00:00"-->
<!--              end="23:30"-->
<!--          >-->

<!--          </el-time-select>-->
<!--        </div>-->
<!--        <div class="grow flex items-center justify-center">-->
<!--          <FontAwesomeIcon icon="right-left"/>-->
<!--        </div>-->
<!--        <div class="w-32">-->
<!--          <el-time-select-->
<!--              v-model="item.ends_at"-->
<!--              :disabled="!item.is_open"-->
<!--              :clearable="false"-->
<!--              :editable="false"-->
<!--              start="00:00"-->
<!--              end="23:30"-->
<!--          >-->

<!--          </el-time-select>-->
<!--        </div>-->

<!--      </div>-->
<!--      <div v-if="form.type === 'hub'" class="col-span-1 flex items-center justify-center mr-3">-->
<!--        <el-checkbox size="small" v-model="item.is_open" :label="$t('Open')"/>-->
<!--      </div>-->
<!--      <div v-if="form.type === 'courier'" class="col-span-1 flex items-center justify-center mr-3">-->
<!--        <el-checkbox size="small" v-model="item.is_open" :label="$t('Working')"/>-->
<!--      </div>-->
<!--      <div v-if="form.type === 'courier'" class="col-span-1 flex items-center justify-end font-medium">-->
<!--        {{ $t('Break time') }}-->
<!--      </div>-->
<!--      <div v-if="form.type === 'courier'" class="col-span-4 flex items-center ">-->
<!--        <div class="w-32">-->
<!--          <el-input :disabled="!item.is_open" v-model="item.break_time" type="number" size="small">-->
<!--            <template #suffix>DK</template>-->
<!--          </el-input>-->
<!--        </div>-->
<!--        <div class="mx-3">-->
<!--          <el-checkbox :disabled="!item.is_open" size="small" v-model="item.auto_working_on"-->
<!--                       :label="$t('Automatic Online')"/>-->
<!--        </div>-->
<!--        <div class="ml-1">-->
<!--          <el-checkbox :disabled="!item.is_open" size="small" v-model="item.auto_working_off"-->
<!--                       :label="$t('Automatic Offline')"/>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

<!--    <div class="bg-slate-50 text-right p-4 flex justify-between" v-if="formWorkingHour">-->
<!--      <div class="col-span-1 text-center">-->
<!--        <ButtonToolTip v-if="formWorkingHour" tooltipText="Varsayılanı seçtiğiniz zaman otomatik bu plana uyulacaktır."-->
<!--                       position="bottom">-->

<!--          <el-checkbox-->
<!--              :disabled="form.is_default"-->
<!--              v-model="form.is_default"-->
<!--          >-->
<!--            {{ $t('Default') }}-->
<!--          </el-checkbox>-->
<!--        </ButtonToolTip>-->

<!--      </div>-->
<!--      <el-button @click="createWorkingHourTemplate" type="primary">-->
<!--        {{ $t('Save') }}-->
<!--      </el-button>-->
<!--    </div>-->
<!--  </div>-->
<!--</template>-->
