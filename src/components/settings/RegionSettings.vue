<script>
import DetailButton from "@/renderers/RegionEditButton.vue";

export default {
  components: {

    DetailButton

  }
};
</script>
<script setup>
import { ref, onMounted, watch, inject } from "vue";
import { useI18n } from "vue-i18n";
import maplibregl from "maplibre-gl";
import { MaplibreTerradrawControl } from "@watergis/maplibre-gl-terradraw";
import { Search } from "@element-plus/icons-vue";
import "maplibre-gl/dist/maplibre-gl.css";
import "@watergis/maplibre-gl-terradraw/dist/maplibre-gl-terradraw.css";
import { ElMessage } from "element-plus";
import NewRegionDrawer from "./drawers/new/NewRegionDrawer.vue";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import formatter from "@/class/formatter";
import SearchRegionPolygon from "@/components/ui/SearchRegionPolygon.vue";
import RegionEdit from "@/components/settings/drawers/edit/RegionEdit.vue";
import { useToast } from "vue-toastification";
import * as terraDraw from "terra-draw";

const { t } = useI18n();
const activeTab = ref("map");
const mapContainer = ref(null);
const map = ref(null);
const draw = ref(null);
const searchQuery = ref("");
const searchPolygonData = ref(null);
const searchRegionPolygonRef = ref(null);
const deleteDialogVisible = ref(false);
const deleteFromMap = ref(false);
const deleteRegionId = ref(null);
const grid = ref();
const gridApi = ref(null);
const regionCoordinates = ref([]);
const api = inject("api");
const editRegionDrawerVisible = ref(false);
const region = ref();
const toast = useToast();



// State to store the GeoJSON data
const geoJsonData = ref({
  type: "FeatureCollection",
  features: []
});

const showNewRegionDrawer = ref(false);
const popup = ref(null);

const drawnPolygonData = ref(null);


onMounted(() => {
  initializeMap();
  setTimeout(() => {
    getRegions();
  },1000)

});

watch(activeTab, (newTab) => {
  if (newTab === "map" && map.value) {
    setTimeout(() => {
      map.value.resize();
    }, 200);
  }
});

const initializeMap = () => {
  if (!mapContainer.value) return;

  map.value = new maplibregl.Map({
    container: mapContainer.value,
    style: {
      version: 8,
      sources: {
        osm: {
          type: 'raster',
          tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
          tileSize: 256,
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }
      },
      layers: [
        {
          id: 'osm',
          type: 'raster',
          source: 'osm',
          minzoom: 0,
          maxzoom: 19
        }
      ]
    },
    center: [28.9784, 41.0082], // Istanbul coordinates
    zoom: 10
  });

  map.value.addControl(new maplibregl.NavigationControl(), "top-right");

  draw.value = new MaplibreTerradrawControl({
    modes: [
      "polygon",
      "rectangle",
      "circle",
      "freehand",
      "angled-rectangle",
      "sensor",
      "sector",
      "select",
      "download"
    ],
    open: true,
    modeOptions: {
      select: new terraDraw.TerraDrawSelectMode({
        flags: {
          polygon: {
            feature: {
              draggable: false,
              rotateable: true,
              scaleable: true,
              coordinates: {
                midpoints: false,
                draggable: true,
                deletable: false
              }
            }
          }
        }
      })
    }
  });


  map.value.addControl(draw.value, "top-left");

  // Get the Terra Draw instance
  const terraDrawInstance = draw.value.getTerraDrawInstance();


  // Check if terraDrawInstance is properly initialized
  if (terraDrawInstance && terraDrawInstance.on) {
    try {
      // Listen for the 'finish' event which is fired when a drawing is completed
      terraDrawInstance.on("finish", (id, context) => {
        try {
          if (context.action === "draw" && (context.mode === "polygon" || context.mode === "rectangle" || context.mode === "circle")) {
            // Get the features from the Terra Draw instance
            const features = draw.value.getFeatures();

            // Find the feature that was just drawn
            const drawnFeature = features.features.find(feature => feature.id === id);

            if (drawnFeature) {
              // Set the drawn polygon data
              drawnPolygonData.value = drawnFeature;

              // Show the popup on the map
              showMapPopup(drawnFeature);
            }
          } else if (context.action === "dragFeature" || context.action === "dragCoordinate") {
            // Handle editing of existing features


            // Get the updated feature
            const features = draw.value.getFeatures();
            const editedFeature = features.features.find(feature => feature.id === id);

            if (editedFeature) {
              // Update drawnPolygonData with the edited feature
              drawnPolygonData.value = editedFeature;

              // Check if this is an existing region from our database
              const regionId = editedFeature.properties?.id;
              if (regionId) {
                // This is an existing region that was edited
                const regionData = regionCoordinates.value.find(r => r.id === regionId);
                if (regionData) {
                  // Update the region's polygon data
                  regionData.polygon_geojson = editedFeature.geometry;

                  // Set the region for editing
                  region.value = regionData;

                  // Show a toast notification
                  toast.info(t("Region successfully edited."));
                }
              }
            }
          }
        } catch (error) {
          console.warn("Error in finish event handler:", error);
        }
      });

      // Listen for the 'select' event which is fired when a feature is selected
      terraDrawInstance.on("select", (id) => {
        try {
          // Get the features from the Terra Draw instance
          const features = draw.value.getFeatures();

          // Find the selected feature
          const selectedFeature = features.features.find(feature => feature.id === id);

          if (selectedFeature) {
            // Set the selected polygon data
            drawnPolygonData.value = selectedFeature;

            // Check if this is an existing region from our database
            const regionId = selectedFeature.properties?.id;
            if (regionId) {
              // This is an existing region
              const regionData = regionCoordinates.value.find(r => r.id === regionId);
              if (regionData) {
                // Show a different popup for existing regions
                new maplibregl.Popup()
                  .setLngLat(getFeatureCenter(selectedFeature))
                  .setHTML(`
                    <div class="region-popup">
                      <h3>${regionData.name}</h3>
                      ${regionData.description ? `<p>${regionData.description}</p>` : ""}
                      <button id="edit-region-btn" class="popup-button">Düzenle</button>
                    </div>
                  `)
                  .addTo(map.value)
                  .on("open", () => {
                    setTimeout(() => {
                      const editBtn = document.getElementById("edit-region-btn");
                      if (editBtn) {
                        editBtn.addEventListener("click", () => {
                          region.value = regionData;
                          editRegionDrawerVisible.value = true;
                        });
                      }
                    }, 100);
                  });
                return;
              }
            }

            // For new drawings, show the standard popup
            showMapPopup(selectedFeature);
          }
        } catch (error) {
          console.warn("Error in select event handler:", error);
        }
      });
    } catch (error) {
      console.warn("Error setting up Terra Draw event listeners:", error);
    }
  } else {
    console.warn("Terra Draw instance is not properly initialized, event listeners not set up");
  }
};

// Helper function to calculate the center of a feature
const getFeatureCenter = (feature) => {
  let center;
  if (feature.geometry.type === "Polygon") {
    // For polygons, calculate the center of the first ring
    const coordinates = feature.geometry.coordinates[0];
    const sumLng = coordinates.reduce((sum, coord) => sum + coord[0], 0);
    const sumLat = coordinates.reduce((sum, coord) => sum + coord[1], 0);
    center = [sumLng / coordinates.length, sumLat / coordinates.length];
  } else if (feature.geometry.type === "MultiPolygon") {
    // For MultiPolygons, calculate the center of the first polygon's first ring
    const coordinates = feature.geometry.coordinates[0][0];
    const sumLng = coordinates.reduce((sum, coord) => sum + coord[0], 0);
    const sumLat = coordinates.reduce((sum, coord) => sum + coord[1], 0);
    center = [sumLng / coordinates.length, sumLat / coordinates.length];
  } else {
    // For other geometry types, use the first coordinate
    center = feature.geometry.coordinates[0];
  }
  return center;
};

// Function to show a popup on the map
const showMapPopup = (feature) => {
  // Calculate the center of the polygon to position the popup
  const center = getFeatureCenter(feature);

  // Check if the feature has a name property
  const hasName = feature.properties && feature.properties.name;


  // Prepare translated strings
  const regionNameLabel = t("Region Name") + ":";
  const descriptionLabel = t("Description") + ":";
  const editButtonLabel = t("Edit");
  const deleteButtonLabel = t("Delete");
  const newPolygonLabel = t("New Polygon");
  const polygonDrawnMessage = t("You have drawn a polygon. Click the button below to create a new region.");
  const createNewRegionLabel = t("Create New Region");

  // Create HTML content for the popup based on whether the feature has a name
  const popupContent = hasName 
    ? `
    <div class="popup-content">
      <div class="popup-header">
<!--        <div class="popup-icon">-->
<!--          <i class="el-icon-map-location"></i>-->
<!--        </div>-->
        <div class="popup-message">
         <h3>${regionNameLabel} ${feature.properties.name}</h3>
          ${feature.properties.description ? `<p>${descriptionLabel} ${feature.properties.description}</p>` : ""}
        </div>
      </div>
      <div class="popup-button-container">
        <button id="edit-region-button" class="popup-button popup-button-primary">
         </i> ${editButtonLabel}
        </button>
        <button id="delete-region-button" class="popup-button popup-button-danger">
        </i> ${deleteButtonLabel}
        </button>
      </div>
    </div>
    `
    : `
    <div class="popup-content">
      <div class="popup-header">
<!--        <div class="popup-icon">-->
<!--          <i class="el-icon-location"></i>-->
<!--        </div>-->
        <div class="popup-message">
          <h3>${newPolygonLabel}</h3>
          <p>${polygonDrawnMessage}</p>
        </div>
      </div>
      <div class="popup-button-container">
        <button id="create-region-button" class="popup-button popup-button-primary">
         </i> ${createNewRegionLabel}
        </button>
        <button id="delete-selection-button" class="popup-button popup-button-danger">
       </i> ${deleteButtonLabel}
        </button>
      </div>
    </div>
  `;

  // If there's already a popup, remove it
  if (popup.value) {
    popup.value.remove();
  }

  // Create a new popup
  popup.value = new maplibregl.Popup({
    closeOnClick: false,
    closeButton: true,
    maxWidth: "300px"
  })
    .setLngLat(center)
    .setHTML(popupContent)
    .addTo(map.value);

  // Add event listener to the button after the popup is added to the DOM
  setTimeout(() => {
    if (hasName) {
      // For features with a name, add event listener to the edit button
      const editButton = document.getElementById("edit-region-button");
      if (editButton) {
        editButton.addEventListener("click", () => {
          // Close the popup
          popup.value.remove();
          popup.value = null;

          // Set the region data and open the edit drawer
          region.value = {
            ...feature.properties,
            polygon_geojson: feature.geometry
          };
          editRegionDrawerVisible.value = true;
        });
      }

      // Add event listener to the delete button
      const deleteButton = document.getElementById("delete-region-button");
      if (deleteButton) {
        deleteButton.addEventListener("click", () => {
          // Set the region ID to delete and show confirmation dialog
          deleteRegionId.value = feature.properties.id;
          deleteFromMap.value = true;
          deleteDialogVisible.value = true;
        });
      }
    } else {
      // For new features, add event listener to the create button
      const createButton = document.getElementById("create-region-button");
      if (createButton) {
        createButton.addEventListener("click", () => {
          // Close the popup
          popup.value.remove();
          popup.value = null;

          // Open the drawer
          showNewRegionDrawer.value = true;
        });
      }

      // Add event listener to the delete selection button
      const deleteSelectionButton = document.getElementById("delete-selection-button");
      if (deleteSelectionButton) {
        deleteSelectionButton.addEventListener("click", () => {
          // Close the popup
          popup.value.remove();
          popup.value = null;

          // Get the Terra Draw instance
          const terraDrawInstance = draw.value.getTerraDrawInstance();

          // Delete the selected feature
          if (terraDrawInstance && drawnPolygonData.value) {
            // Check if this is a search polygon (has place_id) and remove it from SearchRegionPolygon
            if (drawnPolygonData.value.properties && drawnPolygonData.value.properties.place_id) {
              const placeId = drawnPolygonData.value.properties.place_id;

              // Remove the specific polygon from SearchRegionPolygon component
              if (searchRegionPolygonRef.value && searchRegionPolygonRef.value.removePolygon) {
                searchRegionPolygonRef.value.removePolygon(placeId);
              }
            }

            // Remove the feature from the map
            terraDrawInstance.removeFeatures([drawnPolygonData.value.id]);
            drawnPolygonData.value = null;
          }
        });
      }
    }
  }, 100);
};

const exportGeoJSON = () => {
  if (!draw.value) {
    ElMessage.error(t("The drawing tool could not be loaded."));
    return;
  }

  try {
    // Get all features from the Terra Draw instance
    const features = draw.value.getFeatures();

    if (!features || features.features.length === 0) {
      ElMessage.warning(t("No drawn polygon found to download. Please draw a polygon on the map first."));
      return;
    }

    // Convert the features to a JSON string
    const geoJSONString = JSON.stringify(features, null, 2);

    // Create a Blob with the GeoJSON data
    const blob = new Blob([geoJSONString], { type: "application/json" });

    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);

    // Create a temporary link element
    const link = document.createElement("a");
    link.href = url;
    link.download = "poligonlar.geojson";

    // Append the link to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Release the URL object
    URL.revokeObjectURL(url);

    ElMessage.success(t("GeoJSON file successfully downloaded."));
  } catch (error) {
    console.error(t("Error exporting GeoJSON:"), error);
    ElMessage.error(t("An error occurred during GeoJSON export."));
  }
};


watch(drawnPolygonData, () => {
  if (drawnPolygonData.value) {
    showMapPopup(drawnPolygonData.value);
  }
});



const columnDefs = [
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "name",
    headerName: t("Region Name"),
    width: 150,
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "description",
    headerName: t("Description"),
    width: 200,
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "is_active",
    headerName: t("Status"),
    width: 100,
    filter: "agSetColumnFilter",
    sortable: true,
    cellRenderer: (params) => {
      return params.value ? t("Active") : t("Passive");
    }
  },
  {
    field: "created_at",
    headerName: t("Created Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "updated_at",
    headerName: t("Updated Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 60,
    maxWidth: 60,
    cellStyle: { textAlign: "center" },
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clickedEdit: (params) => {
        editRegionDrawerVisible.value = true;
        region.value = params;
      },
      clickedDelete: (params) => {
        region.value = params;
        deleteDialogVisible.value = true;
      }
    }
  }
];

const closeDrawer = (result) => {
  showNewRegionDrawer.value = false;
  editRegionDrawerVisible.value = false;

  // Close the popup if it's open
  if (popup.value) {
    popup.value.remove();
    popup.value = null;
  }

  // Check if we received an updated region from the edit drawer
  if (result && typeof result === 'object' && result.success && result.updatedRegion) {
    // Update the region with the new data
    region.value = result.updatedRegion;

    // Find the feature in Terra Draw that corresponds to this region
    const terraDrawInstance = draw.value?.getTerraDrawInstance();
    if (terraDrawInstance) {
      const features = terraDrawInstance.getSnapshot();
      const featureToUpdate = features.find(feature => 
        feature.properties && feature.properties.id === region.value.id
      );

      if (featureToUpdate) {
        // Create an updated feature with the new geometry
        const updatedFeature = {
          ...featureToUpdate,
          geometry: region.value.polygon_geojson
        };

        // Update drawnPolygonData with the updated feature
        drawnPolygonData.value = updatedFeature;
      }
    }
  } else {
    // If no updated region was provided, clear the selection
    drawnPolygonData.value = null;
  }

  grid.value.refresh();

  // Reload the map to update polygons
  setTimeout(() => {
    getRegions();
  },1000)

};


const handleSearchPolygonUpdate = (value) => {
  searchPolygonData.value = value;
};

const drawSearchedPolygon = () => {
  if (!searchPolygonData.value || !draw.value) {
    return;
  }

  try {
    // Parse the GeoJSON data
    const geoJsonData = JSON.parse(searchPolygonData.value);

    // Check if there are any features
    if (!geoJsonData.features || geoJsonData.features.length === 0) {
      ElMessage.warning("Geçerli bir poligon bulunamadı.");
      return;
    }

    // Get the Terra Draw instance
    const terraDrawInstance = draw.value.getTerraDrawInstance();

    // Check if Terra Draw instance is properly initialized and enabled
    if (!terraDrawInstance || !terraDrawInstance.updateModeOptions) {
      console.warn("Terra Draw is not fully initialized or enabled yet");
      throw new Error("Terra Draw is not fully initialized or enabled yet");
    }

    // Clear any existing search polygons (polygons without database ID)
    // This ensures only one search polygon is displayed at a time
    const existingFeatures = terraDrawInstance.getSnapshot();
    if (existingFeatures && existingFeatures.length > 0) {
      // Filter out search polygons (features without a database ID)
      // Keep region polygons (features with properties.id from database)
      const searchPolygonIds = existingFeatures
        .filter(feature => !feature.properties || !feature.properties.id)
        .map(feature => feature.id);

      if (searchPolygonIds.length > 0) {
        terraDrawInstance.removeFeatures(searchPolygonIds);
      }
    }

    // Add each new search feature to the map
    geoJsonData.features.forEach(feature => {
      // Make sure the feature has a mode property
      if (!feature.properties) {
        feature.properties = {};
      }
      // Set the mode to polygon
      feature.properties.mode = "polygon";

      // Preserve place_id and display_name for later identification
      if (feature.place_id) {
        feature.properties.place_id = feature.place_id;
      }
      if (feature.display_name) {
        feature.properties.display_name = feature.display_name;
      }

      // Add the feature to the map
      terraDrawInstance.addFeatures([feature]);
    });

    // Check if Terra Draw has the setMode method and is enabled
    if (terraDrawInstance.setMode) {
      // Set the active mode to "select" to enable the buttons
      terraDrawInstance.setMode("select");
    } else {
      console.warn("Terra Draw setMode method is not available");
    }

    ElMessage.success("Poligon başarıyla çizildi.");

    // Fit map to the drawn polygons
    fitMapToSearchPolygons(geoJsonData.features);

    // Clear the searchPolygonData to reset the search input
    searchPolygonData.value = null;

    // Clear the polygon v-model in the SearchRegionPolygon component
    if (searchRegionPolygonRef.value && searchRegionPolygonRef.value.clearPolygon) {
      searchRegionPolygonRef.value.clearPolygon();
    }
  } catch (error) {
    console.error("Poligon çizme hatası:", error);
    ElMessage.error("Poligon çizilirken bir hata oluştu.");
  }
};



const getRegions = () => {
  // Fetch regions from the API
  api("customer/regions")
    .then((r) => {
      regionCoordinates.value = r.data;

      // API'den veriler geldikten sonra poligonları haritaya çiz
      if (map.value && map.value.loaded()) {
        displayRegionsOnMap();
      } else if (map.value) {
        map.value.on("load", displayRegionsOnMap);
      }
    })
    .catch(err => {
      console.error(t("Error retrieving regions:"), err);
      toast.error(t("There was a problem loading the regions."));
    });
};

// Bölgeleri harita üzerinde göster
const displayRegionsOnMap = () => {
  if (!map.value || !regionCoordinates.value || regionCoordinates.value.length === 0) return;

  // Mevcut tüm bölgeleri temizle (kaynak varsa)
  if (map.value.getSource("regions-source")) {
    map.value.removeLayer("regions-fill");
    map.value.removeLayer("regions-outline");
    map.value.removeSource("regions-source");
  }

  // GeoJSON veri yapısını oluştur
  const geojsonData = {
    type: "FeatureCollection",
    features: regionCoordinates.value.map(region => {
      return {
        type: "Feature",
        properties: {
          id: region.id,
          name: region.name,
          description: region.description,
          is_active: region.is_active
        },
        geometry: region.polygon_geojson
      };
    })
  };

  // Get the Terra Draw instance
  const terraDrawInstance = draw.value?.getTerraDrawInstance();

  // Check if Terra Draw instance is properly initialized
  if (terraDrawInstance && terraDrawInstance.addFeatures) {
    try {
      // First, clear any existing features in TerraDraw
      const existingFeatures = terraDrawInstance.getSnapshot();
      if (existingFeatures && existingFeatures.length > 0) {
        const featureIds = existingFeatures.map(feature => feature.id);
        terraDrawInstance.removeFeatures(featureIds);
      }

      // Add each region to TerraDraw for editing
      geojsonData.features.forEach(feature => {
        // MultiPolygon'u Polygon'a çevir
        if (feature.geometry.type === "MultiPolygon" && feature.geometry.coordinates.length === 1) {
          feature.geometry = {
            type: "Polygon",
            coordinates: feature.geometry.coordinates[0]
          };
        }

        if (!feature.properties) {
          feature.properties = {};
        }

        feature.properties.mode = "polygon";

        const result = terraDrawInstance.addFeatures([feature]);
      });




      // Set the mode to select to enable editing
      if (terraDrawInstance.setMode) {
        terraDrawInstance.setMode("select");
      }
    } catch (error) {
      console.error("Error adding regions to TerraDraw:", error);
    }
  }

  map.value.on("click", "regions-fill", (e) => {
    if (e.features && e.features.length > 0) {
      const feature = e.features[0];
      const { id, name, description } = feature.properties;

      // Bölge verisini bul
      const regionData = regionCoordinates.value.find(r => r.id === id);
      if (regionData) {
        // Popup göster
        new maplibregl.Popup()
          .setLngLat(e.lngLat)
          .setHTML(`
            <div class="region-popup">
              <h3>${name}</h3>
              ${description ? `<p>${description}</p>` : ""}
              <button id="edit-region-btn" class="popup-button">Düzenle</button>
            </div>
          `)
          .addTo(map.value)
          .on("open", () => {
            setTimeout(() => {
              const editBtn = document.getElementById("edit-region-btn");
              if (editBtn) {
                editBtn.addEventListener("click", () => {
                  region.value = regionData;
                  editRegionDrawerVisible.value = true;
                });
              }
            }, 100);
          });
      }
    }
  });

  // No need for mouseenter/mouseleave events as Terra Draw handles interactions

  // Tüm bölgeleri içerecek şekilde harita görünümünü ayarla
  if (regionCoordinates.value.length > 0) {
    fitMapToRegions();

    // Enable select, delete-selection, and download buttons if there are regions
    if (draw.value) {
      try {
        const terraDrawInstance = draw.value.getTerraDrawInstance();

        // Check if Terra Draw instance is properly initialized and enabled
        if (!terraDrawInstance || !terraDrawInstance.updateModeOptions) {
          console.warn("Terra Draw is not fully initialized or enabled yet");
          return;
        }

        // Check if Terra Draw has the setMode method and is enabled
        if (terraDrawInstance.setMode) {
          // Set the active mode to "select" to enable the buttons
          terraDrawInstance.setMode("select");
        } else {
          console.warn("Terra Draw setMode method is not available");
        }
      } catch (error) {
        console.warn("Error setting Terra Draw mode:", error);
      }
    }
  }
};

const fitMapToRegions = () => {
  if (!map.value || !regionCoordinates.value || regionCoordinates.value.length === 0) return;

  // Get the Terra Draw instance
  const terraDrawInstance = draw.value?.getTerraDrawInstance();

  if (terraDrawInstance) {
    try {
      // Get all features from Terra Draw
      const features = terraDrawInstance.getSnapshot();

      if (features && features.length > 0) {
        let allCoordinates = [];

        // Extract coordinates from all features
        features.forEach(feature => {
          if (feature.geometry && feature.geometry.coordinates) {
            if (feature.geometry.type === "Polygon") {
              // For Polygon, add all coordinates from the first ring
              allCoordinates = [...allCoordinates, ...feature.geometry.coordinates[0]];
            } else if (feature.geometry.type === "MultiPolygon") {
              // For MultiPolygon, add all coordinates from all rings
              feature.geometry.coordinates.forEach(polygonCoords => {
                polygonCoords.forEach(ringCoords => {
                  allCoordinates = [...allCoordinates, ...ringCoords];
                });
              });
            }
          }
        });

        if (allCoordinates.length > 0) {
          // Calculate bounds
          const bounds = new maplibregl.LngLatBounds();
          allCoordinates.forEach(coord => {
            bounds.extend(coord);
          });

          // Fit map to bounds
          map.value.fitBounds(bounds, {
            padding: 50,
            maxZoom: 15
          });

          return;
        }
      }
    } catch (error) {
      console.error("Error fitting map to Terra Draw features:", error);
    }
  }

  // Fallback to original method if Terra Draw approach fails
  let allCoordinates = [];
  regionCoordinates.value.forEach(region => {
    if (region.polygon_geojson && region.polygon_geojson.coordinates) {
      if (region.polygon_geojson.type === "Polygon") {
        allCoordinates = [...allCoordinates, ...region.polygon_geojson.coordinates[0]];
      } else if (region.polygon_geojson.type === "MultiPolygon") {
        region.polygon_geojson.coordinates.forEach(polygonCoords => {
          polygonCoords.forEach(ringCoords => {
            allCoordinates = [...allCoordinates, ...ringCoords];
          });
        });
      }
    }
  });

  if (allCoordinates.length === 0) return;

  // Sınırları hesapla
  const bounds = new maplibregl.LngLatBounds();
  allCoordinates.forEach(coord => {
    bounds.extend(coord);
  });

  // Haritayı bu sınırlara göre ayarla
  map.value.fitBounds(bounds, {
    padding: 50,
    maxZoom: 15
  });
};

// Function to fit map to search polygons
const fitMapToSearchPolygons = (features) => {
  if (!map.value || !features || features.length === 0) return;

  let allCoordinates = [];

  // Extract coordinates from all search polygon features
  features.forEach(feature => {
    if (feature.geometry && feature.geometry.coordinates) {
      if (feature.geometry.type === "Polygon") {
        // For Polygon, add all coordinates from the first ring
        allCoordinates = [...allCoordinates, ...feature.geometry.coordinates[0]];
      } else if (feature.geometry.type === "MultiPolygon") {
        // For MultiPolygon, add all coordinates from all rings
        feature.geometry.coordinates.forEach(polygonCoords => {
          polygonCoords.forEach(ringCoords => {
            allCoordinates = [...allCoordinates, ...ringCoords];
          });
        });
      }
    }
  });

  if (allCoordinates.length === 0) return;

  // Calculate bounds
  const bounds = new maplibregl.LngLatBounds();
  allCoordinates.forEach(coord => {
    bounds.extend(coord);
  });

  // Fit map to bounds with padding
  map.value.fitBounds(bounds, {
    padding: 50,
    maxZoom: 15
  });
};

const deleteRegion = () => {
  // Determine which ID to use based on whether we're deleting from map or grid
  const id = deleteFromMap.value ? deleteRegionId.value : region.value.id;

  api.delete(`customer/regions/${id}`)
    .then(() => {
      toast.success(t("Region successfully deleted."));
      deleteDialogVisible.value = false;

      // If deleting from map, also clean up the map
      if (deleteFromMap.value) {
        // Close the popup if it's open
        if (popup.value) {
          popup.value.remove();
          popup.value = null;
        }

        // Remove the feature from Terra Draw
        const terraDrawInstance = draw.value?.getTerraDrawInstance();
        if (terraDrawInstance) {
          // Get all features
          const features = terraDrawInstance.getSnapshot();

          // Find the feature with the matching region ID
          const featureToRemove = features.find(feature => 
            feature.properties && feature.properties.id === id
          );

          // Remove the feature if found
          if (featureToRemove) {
            terraDrawInstance.removeFeatures([featureToRemove.id]);
          }
        }

        // Reset the deleteFromMap flag
        deleteFromMap.value = false;
        deleteRegionId.value = null;
      }

      // Always refresh the grid
      grid.value.refresh();

      // Always refresh the map by reloading the regions
      setTimeout(() => {
        getRegions();
      },1000)

    })
    .catch((error) => {
      toast.error(error.data?.message);
    });
};

// deleteRegionFromMap function has been integrated into the deleteRegion function

</script>

<template>
  <div class="region-settings">
    <div class="main-content">
      <div class="sidebar-container">
        <div class="sidebar-card custom-card">
          <div class="custom-card-header">
            <div class="sidebar-header">
              <h2 class="sidebar-title">{{ t('All Polygons') }}</h2>
              <p class="sidebar-subtitle">
                {{ t('Use the tools on the map or drag and drop your GeoJSON file.') }}
              </p>
            </div>
          </div>
          <div class="custom-card-body">
            <!-- GeoJSON Export Button -->

            <!-- Regions List -->
            <div class="regions-list">
              <SSDataGrid
                ref="grid"
                v-model="gridApi"
                :columns="columnDefs"
                :restore-column-state-enabled="true"
                url="customer/regions"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="map-section">
        <el-card class="map-card" shadow="never">
          <div class="map-container">
            <div ref="mapContainer" class="map"></div>
            <div class="search-overlay">
              <div class="search-input">
                <div class="flex items-center">
                  <div class="flex-grow mr-2">
                    <SearchRegionPolygon
                      ref="searchRegionPolygonRef"
                      v-model="searchPolygonData"
                      @update:modelValue="handleSearchPolygonUpdate"
                    />
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Search"
                    @click="drawSearchedPolygon"
                  >
                  </el-button>
                </div>
              </div>
            </div>

          </div>
        </el-card>
      </div>
    </div>
  </div>

  <!-- New Region Drawer -->
  <el-drawer
    v-model="showNewRegionDrawer"
    class="customized-drawer"
    :title="t('New Region')"
    direction="rtl"
    size="30%"
  >
    <NewRegionDrawer @close="closeDrawer" :geoJsonData="drawnPolygonData" />
  </el-drawer>
  <el-drawer
    v-model="editRegionDrawerVisible"
    class="customized-drawer"
    :title="t('Region Edit')"
    direction="rtl"
    size="30%"
    append-to-body
    destroy-on-close
  >
    <RegionEdit @close="closeDrawer" :region="region" />
  </el-drawer>
  <el-dialog
    v-model="deleteDialogVisible"
    :width="400"
    center
    :title="t('Are you sure you want to delete the region?')"
    @closed="deleteFromMap = false; deleteRegionId = null;"
  >
    <div class="text-center text-slate-700">
      <p>{{ t('This action cannot be undone.') }}</p>
    </div>
    <template #footer>
          <span class="dialog-footer">
            <el-button
              @click="deleteDialogVisible = false"
              size="small"
            >
              {{ t("Cancel") }}
            </el-button>
            <el-button
              size="small"
              @click="deleteRegion" 
              type="danger"
            >
              {{ t("Delete") }}
            </el-button>
          </span>
    </template>
  </el-dialog>

</template>


<style scoped>
.region-settings {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.header-tabs {
  background: white;
  margin: 0;
  border-bottom: 1px solid #e4e7ed;
}

.header-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 24px;
}

.main-content {
  display: flex;
  flex: 1;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
  height: 100%;
}


.sidebar-container {
  width: 350px;
  height: 100%;
  margin-bottom: 16px; /* Alt margin eklendi */
}

.sidebar-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.custom-card {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  overflow: hidden;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
  transition: .3s;
}

.custom-card-header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
}

.custom-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  text-align: left;
  margin-bottom: 16px;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.sidebar-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
  line-height: 1.5;
}

.team-selection {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.team-selection :deep(.el-form-item) {
  margin-bottom: 0;
}

.team-selection :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.regions-list {
  flex: 1;
  overflow: hidden;
}

.regions-scrollbar {
  height: 100%;
}

.region-item {
  margin-bottom: 8px;
}

.region-card {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 12px;
  background: white;
  transition: all 0.3s;
  cursor: pointer;
  min-height: 50px;
}

.region-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.region-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.region-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.region-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  margin-right: 10px;
  flex-shrink: 0;
}

.region-details {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.region-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.region-area {
  margin-left: 8px;
}

.region-actions {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.map-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  margin-bottom: 16px; /* Alt margin eklendi */
}

.search-card {
  flex-shrink: 0;
}

.search-card :deep(.el-card__body) {
  padding: 16px;
}

.search-overlay {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 50%;
  display: flex;
  justify-content: center;
}

.search-input {
  max-width: 350px;
  width: 100%;
}

.map-card {
  flex: 1;
  overflow: hidden;
}

.map-card :deep(.el-card__body) {
  padding: 0;
  height: 100%;
}

.map-container {
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .sidebar-container {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }

  .sidebar-container {
    width: 100%;
    height: 300px;
  }

  .map-section {
    flex: 1;
  }
}

/* Popup styles */
:deep(.maplibregl-popup-content) {
  padding: 10px !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  max-width: 240px !important;
  width: auto !important;
  box-sizing: border-box !important;
}

:deep(.popup-content) {
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

:deep(.popup-header) {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

:deep(.popup-icon) {
  background-color: #f0f7ff;
  color: #409eff;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

:deep(.popup-message) {
  flex: 1;
}

:deep(.popup-message h3) {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

:deep(.popup-message p) {
  font-size: 11px;
  color: #606266;
  line-height: 1.2;
  margin: 0;
}

:deep(.popup-button-container) {
  display: flex !important;
  flex-direction: row !important;
  gap: 4px !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.popup-button) {
  flex: 1;
  border: none !important;
  border-radius: 3px !important;
  padding: 5px 8px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  cursor: pointer;
  transition: all 0.3s;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
  line-height: 1 !important;
  height: auto !important;
  min-height: 0 !important;
}

:deep(.popup-button i) {
  font-size: 12px !important;
  width: 12px !important;
  height: 12px !important;
  line-height: 1 !important;
  margin: 0 !important;
}

:deep(.popup-button-primary) {
  background-color: #409eff !important;
  color: white !important;
}

:deep(.popup-button-primary:hover) {
  background-color: #66b1ff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
}

:deep(.popup-button-danger) {
  background-color: #f56c6c !important;
  color: white !important;
}

:deep(.popup-button-danger:hover) {
  background-color: #f78989 !important;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3) !important;
}

</style>
