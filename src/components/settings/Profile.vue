<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import SettingsInputRow from "@/components/settings/components/SettingsInputRow.vue";
import {Options} from "@/class";
import {inject, reactive, onMounted, ref, computed} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n({useScope: 'global'})
const {getters, dispatch} = useStore();
const api = inject("api")

const profile = computed(() => getters["auth/me"])

const mode = ref(false)
const password_mode = ref(false)

const form = reactive({
  name: null,
  email: null,
  phone_country: null,
  phone: null,
  locale: null,
  password: null,
  password_confirmation: null,
});

const onResetPhone = () => {
  setFormValues()
  mode.value = false
}

const onResetPassword = () => {
  setFormValues()
  password_mode.value = false
}

const toggle = () => {
  if (mode.value) {
    updatePhone()
  }
  mode.value = !mode.value;
};

const password_modeToggle = () => {
  if (password_mode.value) {
    if (form.password === form.password_confirmation) {
      updatePassword()
    } else {
      // TODO locale
      toast.error("Paralolar uyuşmuyor")
      form.password = null
      form.password_confirmation = null
    }
  }
  password_mode.value = !password_mode.value;
};

const setFormValues = () => {
  form.name = profile.value.name;
  form.email = profile.value.email;
  form.locale = profile.value.locale;
  form.phone = profile.value.phone;
  form.phone_country = profile.value.phone_country;
}

const updatePhone = () => {
  api.put(`customer/profile`, {
    phone_country: form.phone_country,
    phone: form.phone
  })
      .then((res) => dispatch("auth/setMe", res))
      .catch((err) => {
        toast.error(err.message);
        setFormValues()
      });
}

const updatePassword = () => {
  api.put(`customer/profile`, {
    password_confirmation: form.password_confirmation,
    password: form.password
  })
      .then((res) => {
        dispatch("auth/setMe", res)
      })
      .catch((err) => {
        toast.error(err.message);
        setFormValues()
      });
}

const saveRow = (param) => {
  api.put(`customer/profile`, {[param.name]: param.value})
      .then((res) => {
        dispatch("auth/setMe", res)
        if (param.name === "locale") {
          window.location.reload()
        }
      })
      .catch((err) => {
        toast.error(err.message);
        setFormValues()
      });
}

onMounted(() => {
  setFormValues()
});


</script>

<template>
  <SettingsPageLayout>
    <template #header>

        <span class="text-lg font-bold text-slate-700 m-3"> {{ t('Profile') }} </span>

    </template>
    <template #content>
      <SettingsInputRow
          v-model="form.name"
          :label="t('Name')"
          form-data-name="name"
          @save="saveRow"
          @cancel="setFormValues"
      />
      <SettingsInputRow
          v-model="form.email"
          :label="t('Email')"
          @save="saveRow"
          form-data-name="email"
          @cancel="setFormValues"
      />
      <div
          class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
      >
        <dt class="col-span-1 text-sm font-medium text-gray-500">{{ t('Phone') }}</dt>
        <dd
            class="mt-1 flex text-sm items-center text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
        >
          <div v-if="mode" class="flex flex-grow col-span-3 h-10 items-center">

            <el-select
                v-model="form.phone_country"
                :placeholder="$t('Country')"
                class=""
            >
              <el-option
                  v-for="item in Options.countries"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
            </el-select>
            <el-input
                v-model="form.phone"
                :placeholder="$t('Phone')"
            />
          </div>
          <div v-else class="flex flex-grow col-span-3 ml-4 h-10 items-center">
            {{ form.phone }}
          </div>
          <div class="ml-4 col-span-3 flex-shrink-0">
            <el-button :text="true" @click="toggle">
              {{ !mode ? t("Edit") : t("Update") }}
            </el-button>
            <div @click="onResetPhone" v-if="mode" class="ml-2 text-slate-300 cursor-pointer">
              <FontAwesomeIcon icon="ban"/>
            </div>
          </div>
        </dd>
      </div>
      <div
          class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
      >
        <dt class="col-span-1 text-sm font-medium text-gray-500">{{ t('Password') }}</dt>
        <dd
            class="mt-1 flex text-sm items-center text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
        >
          <div v-if="password_mode" class="flex flex-grow col-span-3  h-10 items-center">
            <el-input
                class="mr-1"
                v-model="form.password"
                type="password"
                :placeholder="t('Password')"
            />

            <el-input
                class="ml-1"
                v-model="form.password_confirmation"
                type="password"
                :placeholder="t('Password Confirmation')"
            />
          </div>
          <div v-else class="flex flex-grow col-span-3 ml-4  h-10 items-center">
          </div>
          <div class="ml-4 col-span-3 flex-shrink-0 flex items-center">
            <el-button :text="true" @click="password_modeToggle">
              {{ !password_mode ? t("Edit") : t("Update") }}
            </el-button>
            <div @click="onResetPassword" v-if="password_mode" class="ml-2 text-slate-300 cursor-pointer">
              <FontAwesomeIcon icon="ban"/>
            </div>
          </div>
        </dd>
      </div>
      <SettingsInputRow
          v-model="form.locale"
          :label="t('Language')"
          type="select"
          :options="Options.lang_list"
          options_key="key"
          options_label="label"
          @save="saveRow"
          form-data-name="locale"
          @cancel="setFormValues"
      />
    </template>
  </SettingsPageLayout>
</template>
