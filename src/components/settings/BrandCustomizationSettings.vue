<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import SettingsInputRow from "@/components/settings/components/SettingsInputRow.vue";
import DefaultLogo from "@/assets/images/logo-wr.png"
import {reactive, computed, inject, ref, onActivated,onMounted} from "vue";
import {useStore} from "vuex";
import PickImage from "@/components/ui/PickImage.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()
const {getters, dispatch} = useStore();

const api = inject("api");

const activeCompany = computed(() => getters["auth/activeCompany"]);
const primaryColor = computed(() => getters["ui/primaryColor"]);
const secondaryColor = computed(() => getters["ui/secondaryColor"]);

const dialogImageUrl = ref(null);
const upload = ref();
const mode = ref(false);
const form = reactive({
  primary_color: primaryColor.value,
  secondary_color: secondaryColor.value,
});

let defaultData = {
  primary_color: "#1450FF",
  secondary_color: "#26EB73",
  brand_logo: DefaultLogo
}

const setFormValues = () => {
  form.primaryColor = activeCompany.value.primary_color ? activeCompany.value.primary_color : defaultData.primary_color
  form.secondaryColor = activeCompany.value.secondary_color ? activeCompany.value.secondary_color : defaultData.secondary_color;
  dialogImageUrl.value = activeCompany.value.brand_logo_url ? activeCompany.value.brand_logo_url : defaultData.brand_logo;
}

const saveRow = (param) => {
  let formData = new FormData();
  formData.append(param.name, param.value);
  formData.append("_method", "PUT");
  api.post(`customer/companies`, formData).then((res) => {
    dispatch("auth/setActiveCompany", res);
    dialogImageUrl.value = res.brand_logo_url
    window.location.reload();
  }).catch(() => {
    toast.error(t("An error occurred"));
    setFormValues()
  });
};

const toggle = () => {
  if (mode.value) {
    saveRow({name: "brand_logo", value: upload.value});
  }
  mode.value = !mode.value;
};

const toggleUpload = (e) => {
  upload.value = e.target.files[0];
};

function getImageFormUrl(url, callback) {
  let img = new Image();
  img.setAttribute('crossOrigin', 'anonymous');
  img.onload = function () {
    let canvas = document.createElement("canvas");
    canvas.width = this.width;
    canvas.height = this.height;
    let ctx = canvas.getContext("2d");
    ctx.drawImage(this, 0, 0);
    let dataURI = canvas.toDataURL("image/jpg");
    let byteString;
    if (dataURI.split(',')[0].indexOf('base64') >= 0)
      byteString = atob(dataURI.split(',')[1]);
    else
      byteString = unescape(dataURI.split(',')[1]);

    let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    let ia = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    return callback(new Blob([ia], {type: mimeString}));
  }
  img.src = url;
}

const setDefaultLogo = () => {
  mode.value = false;
  getImageFormUrl(defaultData.brand_logo, (value) => {
    saveRow({name: "brand_logo", value});
  })
}

const setDefaultPrimaryColor = () => {
  form.primaryColor = defaultData.primary_color
  saveRow({name: "primary_color", value: defaultData.primary_color});
}

const setDefaultSecondaryColor = () => {
  saveRow({name: "secondary_color", value: defaultData.secondary_color});
  form.secondaryColor = defaultData.secondary_color;
}

const onResetLogo = () => {
  setFormValues()
  mode.value = false
}

onActivated(() => {
  setFormValues()
});
onMounted(() => {
  setFormValues()
});

</script>

<template>
  <SettingsPageLayout>
    <template #header>

        <span class="text-lg font-bold text-slate-700 m-3">
         {{ t('Brand Customization') }}
        </span>

    </template>
    <template #content>
      <div
          class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row bg-slate-50"
      >
        <dt class="col-span-1 text-sm font-medium text-gray-500">{{ t('Brand Logo') }}</dt>
        <dd
            class="mt-1 flex text-sm items-center justify-between text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
        >
          <div class="flex flex-grow col-span-3 items-center">
            <div v-if="mode" class="flex-grow ml-4">
              <PickImage v-model="upload"/>
            </div>
            <div v-else class="flex-grow ml-4">
              <img class="h-20 w-20" :src="dialogImageUrl" alt=""/>
            </div>
            <el-button v-if="mode" @click="setDefaultLogo">{{ t('Default') }}</el-button>
            <el-button :text="true" @click="toggle">
              {{ !mode ? t("Edit") : t("Update") }}
            </el-button>
            <div @click="onResetLogo" v-if="mode" class="ml-2 text-slate-300 cursor-pointer">
              <FontAwesomeIcon icon="ban"/>
            </div>
          </div>
        </dd>
      </div>
      <SettingsInputRow
          v-model="form.primaryColor"
          :label="t('Primary Color')"
          type="color"
          @save="saveRow"
          @reset="setFormValues"
          form-data-name="primary_color"
      >
        <template #buttons>
          <el-button @click="setDefaultPrimaryColor">{{ t('Default') }}</el-button>
        </template>
      </SettingsInputRow>
      <SettingsInputRow
          v-model="form.secondaryColor"
          :label="t('Secondary Color')"
          type="color"
          @save="saveRow"
          @reset="setFormValues"
          form-data-name="secondary_color"
      >
        <template #buttons>
          <el-button @click="setDefaultSecondaryColor">{{ t('Default') }}</el-button>
        </template>
      </SettingsInputRow>
    </template>
  </SettingsPageLayout>
</template>

<style>
.hideUpload > div {
  display: none;
}
</style>
