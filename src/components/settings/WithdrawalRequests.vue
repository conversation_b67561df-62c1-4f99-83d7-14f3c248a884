
<script>
import ConfirmButtonRenderer from "@/renderers/ConfirmButtonRenderer.vue";

export default {
  components: {
    ConfirmButtonRenderer,
  },
};
</script>
<script setup>

import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import WalletDetail from "@/components/settings/drawers/detail/WalletDetail.vue";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { inject, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import Formatter from "@/class/formatter";
import formatter from "@/class/formatter";
import { useToast } from "vue-toastification";
import { transactionTypeSlug } from "@/class";

const api = inject("api");
const { t } = useI18n();
const loader = ref();
const gridApi = ref();
const transactions = ref();

const grid = ref();
const confirmVisible = ref(false);
const uuidTransaction = ref();
const toast = useToast();


const refresh = () => {
  grid.value.refresh();
};

const filter = {
  "filter[is_approved]":0
}

const sideBar = {
  toolPanels: [
    {
      id: "columns",
      labelDefault: "Columns",
      labelKey: "columns",
      iconKey: "columns",
      toolPanel: "agColumnsToolPanel",
      toolPanelParams: {
        suppressRowGroups: true,
        suppressSyncLayoutWithGrid: true,
        suppressValues: true,
        suppressPivots: true,
        suppressPivotMode: true,
        suppressColumnFilter: true,
        suppressColumnSelectAll: true,
        suppressColumnExpandAll: true,
      },
    },
  ],
  defaultToolPanel: "columns",
};

const columnDefs = [

  {
    field: "uuid",
    headerName: "UUID",
    width: 90,
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "receiver_company_id",
    headerName: t("Receiver Company Id"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "receiver_wallet_id",
    headerName: t("Receiver Wallet Id"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "sender_company_id",
    headerName: t("Sender Company Id"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "sender_wallet_id",
    headerName: t("Sender Wallet Id"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "task_id",
    headerName: t("Task Id"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "task_amount",
    headerName: t("Task Amount"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "amount",
    headerName: t("Amount"),
    filter: "agTextColumnFilter",
    sortable: true,
    valueFormatter: (props) => Formatter.currency(Number(props.value), props.data.currency_unit)
  },
  {
    field: "notes",
    headerName: t("Note"),
    filter: "agTextColumnFilter",
    sortable: true,

  },
  {
    field: "task_discounted_amount",
    headerName: t("Task Discounted Amount"),
    filter: "agTextColumnFilter",
    sortable: true,
    valueFormatter: (props) => Formatter.currency(Number(props.value), props.data.currency_unit)
  },
  {
    field: "type_slug",
    headerName: t("Type"),
    filter: 'agSetColumnFilter',
    valueFormatter: (params) => t(transactionTypeSlug[params.value]),
    sortable: true,
    export: true,
    cellClass: 'stringType',
    filterParams: {
      values: function(params) {
        params.success(Object.keys(transactionTypeSlug))
      },
      valueFormatter: (params) => t(transactionTypeSlug[params.value])
    }
  },
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    export: true,
    sortable: true,
    cellClass: 'dateType',
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },

  {
    field: "uuid",
    headerName: "",
    pinned: "right",
    width: 150,
    cellStyle: {textAlign: "center"},
    cellRenderer: "ConfirmButtonRenderer",
    cellRendererParams: {
      clicked: (params) => {
        confirmVisible.value = true;
        uuidTransaction.value = params.uuid

      },
    },
  },

];

const close = () => {
  confirmVisible.value = false;
};


const approveTransaction =()=>{
  loader.value.show()
  api(`customer/courier-finance/transactions/${uuidTransaction.value}/approve`)
    .then(()=>{

grid.value.refresh()
    toast.success('Your confirmation process was successful.')
    })
    .catch((err) => {
      toast.error(err.data.message);
    })
    .finally(()=>{

      confirmVisible.value = false;
      loader.value.hide()

    })
}


</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>

    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3">{{ t("Withdrawal Requests") }} </span>
      <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
        <el-button
          class="m-3 items-center"
          size="small"
          @click="refresh"
        >
          <FontAwesomeIcon icon="refresh" size="lg" />
        </el-button>
      </ButtonToolTip>

    </template>
    <template #content>
      <div class="h-full w-full">
        <SSDataGrid
          ref="grid"
          :columns="columnDefs"
          url="customer/courier-finance/transactions"
          :auto-size-column="false"
          :filter="filter"
          :restore-column-state-enabled="true"
          :sideBar="sideBar"
          columnStateSlug="withdrawal_requests"
        />
      </div>
    </template>
  </SettingsPageLayout>
  <el-dialog v-model="confirmVisible" width="480px" center>
    <div class="flex flex-col justify-center items-center static">
        <span slot="title" class="pb-20 text-lg absolute absolute">{{ t("Information") }} <FontAwesomeIcon icon="circle-info"
                                                                                                  class="pl-0.5 text-slate-700"></FontAwesomeIcon></span>
      <span class="pt-3">{{t("Are you sure you want to confirm the withdrawal request?")}}</span>
    </div>
    <div class="flex items-center justify-center pt-7">
      <el-button @click="close">{{ t("Cancel") }}</el-button>
      <el-button @click="approveTransaction" type="primary">
        {{ t('Approve') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<style scoped>

</style>