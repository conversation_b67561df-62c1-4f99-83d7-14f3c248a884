<script>
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import DetailButton from "@/renderers/DetailButton.vue";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    StatusRenderer,
    DetailButton
  },
};
</script>
<script setup>

import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import {inject, ref, onActivated,onMounted} from "vue";
import NewTeam from "@/components/settings/drawers/new/NewTeam.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import TeamActions from "@/components/settings/drawers/actions/TeamActions.vue";
import {useI18n} from "vue-i18n";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const {t} = useI18n()
const api = inject("api")

const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const gridApi = ref(null);
const loader = ref(null);
const teams = ref([]);
const selectedTeam = ref(null);
const newTeamDrawerVisible = ref(false)
const teamDetailDrawerVisible = ref(false)

const columnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    width: 90,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
    filter: 'agTextColumnFilter',
  },
  {
    field: "name",
    headerName: t('Team Name'),
    filter: 'agTextColumnFilter',
  },
  {field: "couriers_count", headerName: t('Couriers Count'), filter: 'agTextColumnFilter',},
  {
    field: "hub.name",
    headerName: t('Hub Name'),
    filter: 'agTextColumnFilter',
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openTeamDetail(params)
      },
    },
  },
]);

function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

const openNewTeamDrawer = () => {
  newTeamDrawerVisible.value = true;
};

const closeNewTeamDrawer = (params) => {
  newTeamDrawerVisible.value = false;
  if (params) {
    getTeams()
  }
}

const openTeamDetail = (params) => {
  selectedTeam.value = teams.value.find(i => i.id === params);
  teamDetailDrawerVisible.value = true;
};

const getTeams = () => {
  loader.value.show()
  api("customer/teams")
      .then((res) => {
        teams.value = res.data
      })
      .finally(() => loader.value.hide())
}

onActivated(() => {
  getTeams()
})


onMounted(() => {
  getTeams()
})

// <ButtonToolTip :tooltipText="$t('New Hub')" position="bottom" >
//     <el-button
// class="mr-4 items-center"
// size="small"
// @click="openNewHubDrawer"
//     >
//     <FontAwesomeIcon icon="plus" size="lg"/>
//     </el-button>
// </ButtonToolTip>

const onRefresh = ()=>{
  getTeams()
}

</script>


<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>

    <template #header>

      <span class="text-lg font-bold text-slate-700 m-3"> {{ t('Teams') }} </span>

      <div class="flex items-center ">

          <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom" >
            <el-button
                class="mr-3 items-center"
                size="small"
                @click="onRefresh"
            >
              <FontAwesomeIcon icon="refresh" size="lg"/>
            </el-button>
          </ButtonToolTip>
          <ButtonToolTip :tooltipText="$t('New Team')" position="bottom" >
            <el-button
                class="mr-3 items-center"
                size="small"
                @click="openNewTeamDrawer"
            >
              <FontAwesomeIcon icon="plus" size="lg"/>
            </el-button>
          </ButtonToolTip>
      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxs border-b border-slate-300"
        >
          <div class="flex items-center justify-between flex-grow">
            <div class="flex px-1">
              <div class="px-1 py-2">
                {{ t('Total Team') }}:
                <span class="font-bold">{{ teams.length }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow">
          <DataGrid
              v-model="gridApi"
              :dataSource="teams"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              :autoSizeColumn="false"
          />
        </div>
      </div>
    </template>
    <template #drawers>
      <el-drawer
          v-model="newTeamDrawerVisible"
          class="customized-drawer"
          :title="t('New Team')"
          append-to-body
          destroy-on-close
      >
        <NewTeam
            @close="closeNewTeamDrawer"
        />
      </el-drawer>
      <TeamActions v-model="teamDetailDrawerVisible" :selectedTeam="selectedTeam" @refresh="getTeams"/>
    </template>
  </SettingsPageLayout>
</template>
