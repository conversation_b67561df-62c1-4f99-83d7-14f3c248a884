<script setup>
import { onMounted, ref, computed } from "vue";
import settings from "@/router/settings";
import { groupBy } from "@/class/helpers";
import router from "@/router";
import { useStore } from "vuex";
import DispatcherActions from "@/components/settings/drawers/actions/DispatcherActions.vue";

const { getters } = useStore();

const isMenuPanelOpen = true;

const me = computed(() => getters["auth/me"])

const test = ref([]);


const goToRoute = (param) => {
  router.push({ path: param.path });
};


const moreMenuList = computed(() => {

  const { role_slug, active_company } = getters["auth/me"];

  let data = settings.children
    .filter((route) => route.meta.type.includes(active_company.company_product_type)
      && route.meta.role.includes(role_slug)
      && !route.hidden);


  if (!me.value.active_company.is_courier_financial_transactions) {
    data = data.filter(route => route.subMenuTitle !== "Financial Management");
  }
  if (!me.value.active_subscription) {
    data = data.filter(route => route.subMenuTitle !== "Subscription & Billing");
  }

  // Group the data by subMenuTitle
  return groupBy(data, "subMenuTitle");
});
</script>
<template>
  <div class="absolute inset-0 ">
    <div class="w-full h-full flex">
      <div class="flex flex-col">
        <div class="text-xl flex items-center"
             style="height: 53px; background: #FFFF; border-bottom: 1px solid; border-bottom-color: #EAEAEA; border-right: 1px solid; border-right-color: #dadbe1" >
          <div style="padding-left: 14px; font: normal normal 600 19px/18px Inter; color: #3E4B5E;">
            {{$t('Settings')}}
          </div>

        </div>
        <el-menu
          :unique-opened="true"
          class="el-menu-vertical-demo grow overflow-y-auto el-custom-menu"
          :collapse="!isMenuPanelOpen"
          :router="true"
          :default-active="$route.path"
          style="background: white; "
        >
          <template v-for="(routes,key) in test" :key="key">

            <el-sub-menu :index="routes[0].path">
              <template #title>
                <span>  {{ $t(key) }}</span>
              </template>
              <template v-for="(x,index) in routes" :key="index">
                <el-menu-item @click="goToRoute(x)" :index="x.path" class="bg-white cursor-pointer">

                  {{$t(x.name)}}

                </el-menu-item>
              </template>
            </el-sub-menu>
          </template>
          <template v-for="(moreRoutes,key) in moreMenuList" :key="key">
            <el-sub-menu :index="moreRoutes[0].path">
              <template #title>
                <span>  {{ $t(key) }}</span>
              </template>
              <template v-for="(x,index) in moreRoutes" :key="index">
                <el-menu-item :index="x.path" class="bg-white cursor-pointer"  @click="goToRoute(x)">
                 <span > {{ $t(x.name) }}</span>
                </el-menu-item>
              </template>
            </el-sub-menu>
          </template>
        </el-menu>
      </div>
      <div class="grow h-full w-full overflow">
        <router-view :key="$route.fullPath" />
      </div>
    </div>
  </div>
</template>
<style scoped>
.text-button {
  .text-button {
    background: none;
    border: none;
    padding: 0;
    /*optional:*/
    font: inherit;

    cursor: pointer;
  }
}
</style>