<template>
  <div>
    <el-form label-position="top">
      <FormCard
        ><template #title>Personal Information</template
        ><template #subTitle>
          Use a permanent address where you can recieve mail. </template
        ><template #actions
          ><el-button type="primary">Save</el-button></template
        >

        <div class="col-span-6 sm:col-span-3">
          <el-form-item label="First Name" class="text-sm">
            <el-input v-model="formValues.name"></el-input>
          </el-form-item>
        </div>

        <div class="col-span-6 sm:col-span-3">
          <el-form-item label="Last Name" class="text-sm">
            <el-input v-model="formValues.lastName"></el-input>
          </el-form-item>
        </div>

        <div class="col-span-6">
          <el-form-item label="Adres" class="text-sm">
            <el-input v-model="formValues.address"></el-input>
          </el-form-item>
        </div>

        <div class="col-span-6 sm:col-span-6 lg:col-span-2">
          <el-form-item label="City" class="text-sm">
            <el-input v-model="formValues.city"></el-input>
          </el-form-item>
        </div>

        <div class="col-span-6 sm:col-span-3 lg:col-span-2">
          <el-form-item label="State" class="text-sm">
            <el-input v-model="formValues.state"></el-input>
          </el-form-item>
        </div>

        <div class="col-span-6 sm:col-span-3 lg:col-span-2">
          <el-form-item label="ZIP" class="text-sm">
            <el-input v-model="formValues.zip"></el-input>
          </el-form-item>
        </div>
      </FormCard>
    </el-form>
  </div>
</template>

<script>
import { reactive } from "vue";
export default {
  name: "TabSettings",
  setup() {
    const formValues = reactive({
      name: "",
      lastName: "",
      address: "",
      city: "",
      state: "",
      zip: "",
    });

    return { formValues };
  },
};
</script>
