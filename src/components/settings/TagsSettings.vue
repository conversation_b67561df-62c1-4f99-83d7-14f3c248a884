<script>
import EditButton from "@/renderers/EditButton.vue";

export default {
  components: {
    EditButton,
  },
};
</script>
<script setup>

import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import {inject, onActivated, ref,onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import NewTag from "@/components/settings/drawers/new/NewTag.vue";
import TagEdit from "@/components/settings/drawers/edit/TagEdit.vue";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import {useI18n} from "vue-i18n";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const {t} = useI18n()

const api = inject("api")

const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const gridApi = ref(null);
const loader = ref(null);
const tags = ref([]);
const selectedTag = ref(null);
const newTagDrawerVisible = ref(false)
const tagEditDrawerVisible = ref(false)

const columnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: 'agTextColumnFilter',
  },
  {
    field: "name",
    headerName: t('Tag'),
    filter: 'agTextColumnFilter',
  },
  {
    field: "status",
    headerName: t('Status'),
    filter: 'agTextColumnFilter',
    valueFormatter: (params) => params ? t("Active"):t("Passive"),
  },
  {
    field: "created_at",
    headerName: t("Created Date"),
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "EditButton",
    cellRendererParams: {
      clicked: (params) => {
        openTagEdit(params);
      },
    },
  },
]);

const openTagEdit = (params) => {
  selectedTag.value = tags.value.find(i=>i.id===params);
  tagEditDrawerVisible.value = true;
}

function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

const openNewTagDrawer = () => {
  newTagDrawerVisible.value = true;
};

const closeNewTagDrawer = (params) => {
  newTagDrawerVisible.value = false;
  if(params){
    getTags()
  }
}

const closeTagsDrawer = (param) => {
  newTagDrawerVisible.value = false;
  tagEditDrawerVisible.value = false
  if (param) {
    getTags();
  }
};

const getTags = () => {
  loader.value.show()
  api("customer/tags")
      .then((res)=> tags.value = res.data)
      .finally(()=>loader.value.hide())
}

onActivated(()=>{
  getTags()
})

onMounted(()=>{
  getTags()
})

const onRefresh = () => {
  getTags()
}

</script>


<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{t('Tags')}} </span>
      <div class="flex items-center">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom" >
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" size="lg"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Tag')" position="bottom">
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="openNewTagDrawer"
          >
            <FontAwesomeIcon icon="plus" size="lg"/>
          </el-button>
        </ButtonToolTip>
      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxs border-b border-slate-300"
        >
          <div class="flex items-center justify-between flex-grow">
            <div class="flex px-1">
              <div class="px-1 py-2">
                {{t('Total Tag')}}:
                <span class="font-bold">{{tags.length}}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow">
          <DataGrid
              v-model="gridApi"
              :dataSource="tags"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              :autoSizeColumn="false"
          />
        </div>
      </div>
    </template>
    <template #drawers>
      <el-drawer
          v-model="newTagDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('New Tag')"
          append-to-body
          destroy-on-close
      >
        <NewTag
            @close="closeNewTagDrawer"
        />
      </el-drawer>
      <el-drawer
          v-model="tagEditDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('Tag Edit')"
          append-to-body
          destroy-on-close
      >
        <TagEdit @close="closeTagsDrawer" :tag="selectedTag"/>
      </el-drawer>
    </template>
  </SettingsPageLayout>
</template>
