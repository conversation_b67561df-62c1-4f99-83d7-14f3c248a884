<script>
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import DetailButton from "@/renderers/DetailButton.vue";
import ActiveOrPassiveRenderer from "@/renderers/ActiveOrPassiveRenderer.vue";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    StatusRenderer,
    DetailButton,
    ActiveOrPassiveRenderer
  },
};
</script>

<script setup>
import {inject, ref, onActivated,onMounted} from "vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import NewDispatcher from "@/components/settings/drawers/new/NewDispatcher.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import DispatcherActions from "@/components/settings/drawers/actions/DispatcherActions.vue";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const toast = useToast()
const {t} = useI18n()
const api = inject("api");

const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const loader = ref();
const selectedDispatcher = ref(null);
const dispatchers = ref([]);
const newDispatcherDrawerVisible = ref(false);
const dispatcherDetailDrawerVisible = ref(false);
const selectedRows = ref();
const gridApi = ref(null);

const columnDefs = ref([
  {
    field: "suspended_at",
    headerName: "",
    cellRenderer: "ActiveOrPassiveRenderer",
    cellRendererParams: {
      reverted: true
    },
    keyCreator: (params) => params.value ? t("Suspend") : t("Active"),
    width: 60,
  },
  {
    field: "id",
    headerName: "Id",
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
    filter: 'agTextColumnFilter',
  },
  {
    field: "name",
    headerName: t("Dispatcher Name"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "phone",
    headerName: t("Phone"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "email",
    headerName: t("Email"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: 'hubs',
    headerName: t('Hubs'),
    filter: 'agTextColumnFilter',
    valueFormatter: (params) => params.data.hubs.map((hub) => hub.name).join(', '),
    filterParams: {
      valueGetter: (params) => params.data.hubs.map((hub) => hub.name).join(', ')
    }
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 120,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openDispatcherDetail(params);
      },
    },
  },
]);




function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

function handleSelectionChanged() {
  selectedRows.value = gridApi.value.getSelectedRows();
}

const openNewDispatcherDrawer = () => {
  newDispatcherDrawerVisible.value = true;
};

const closeNewDispatcherDrawer = (params) => {
  newDispatcherDrawerVisible.value = false;
  if (params) {
    getDispatchers()
  }
};

const openDispatcherDetail = (params) => {
  selectedDispatcher.value = dispatchers.value.find(i => i.id === params);
  dispatcherDetailDrawerVisible.value = true;
};

const getDispatchers = () => {
  loader.value.show();
  api("customer/dispatchers")
      .then((res) => {
        dispatchers.value = res.data;
      })
      .catch(() => {
        toast.error(t('An error occurred'))
      })
      .finally(() => loader.value.hide());
};

onActivated(() => {
  getDispatchers();
});

onMounted(() => {
  getDispatchers();
});
const onRefresh =()=>{
  getDispatchers()
}




</script>

<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{ t('Dispatchers') }} </span>
      <div class="flex items-center">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom" >
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" size="lg"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Dispatcher')" position="bottom" >
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="openNewDispatcherDrawer"
          >
            <FontAwesomeIcon icon="plus" size="lg"/>
          </el-button>
        </ButtonToolTip>


      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxs border-b border-slate-300"
        >
          <div class="flex items-center justify-between flex-grow">
            <div class="flex px-1">
              <div class="px-1 py-2">
                {{ t('Total Dispatcher') }}:
                <span class="font-bold">{{ dispatchers.length }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow">
          <DataGrid
              v-model="gridApi"
              :dataSource="dispatchers"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              @handleSelectionChanged="handleSelectionChanged"
              :autoSizeColumn="false"
          />
        </div>
      </div>
    </template>
    <template #drawers>
      <el-drawer
          v-model="newDispatcherDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('New Dispatcher')"
          append-to-body
          destroy-on-close
      >
        <NewDispatcher @close="closeNewDispatcherDrawer"/>
      </el-drawer>
      <DispatcherActions v-model="dispatcherDetailDrawerVisible" :selectedDispatcher="selectedDispatcher"
                         @refresh="getDispatchers"/>

    </template>
  </SettingsPageLayout>
</template>
