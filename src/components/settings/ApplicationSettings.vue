<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import SettingsInputRow from "@/components/settings/components/SettingsInputRow.vue";
import CallCenter from "@/components/settings/components/application/CallCenter.vue";
import {inject, reactive, ref, computed, onActivated,onMounted} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import TaskConfirmAction from "@/components/settings/components/application/TaskConfirmAction.vue";

const toast = useToast()
const {t} = useI18n()
const {getters, dispatch} = useStore();

const api = inject("api")


const activeCompany = computed(() => getters["auth/activeCompany"])

const timeZones = ref([])
const measurements = ref([]);
const dateFormatList = ref([])
const me = computed(() => getters["auth/me"])
const mode = ref(false)
const form = reactive({
  timeZone: null,
  hourFormat: null,
  timeFormat: null,
  distanceFormat: null,
  currencyUnit: null,
  gdpr: null,
  is_maximum_delivery_distance: null,
  maximum_delivery_distance: null,
  auto_pool_slot: null,
  obey_status_order: null,
  sms_enabled: null,
  mail_enabled: null,
  lock_tasks: null,
  allow_batch_in_progress: null,
  allow_batch_on_delivery: null,
  task_live_stream: null,
  show_destination: null,
  date_format_slug: null

});

const onResetTaskCompletedRules = () => {
  setFormValues()
  mode.value = false
}

const toggle = () => {
  if (mode.value) {
    updateDeliveryDistance()
  }
  mode.value = !mode.value;
};
const getDateFormat = () => {

  api.post("components/date-format-select", { lang: me.value.locale })
    .then((r) => {
      dateFormatList.value = r.data;
    })
};

const updateDeliveryDistance = () => {
  let formData = new FormData();
  formData.append("is_maximum_delivery_distance", Number(form.is_maximum_delivery_distance));
  formData.append("maximum_delivery_distance", form.maximum_delivery_distance);
  formData.append("_method", "PUT");
  api
      .post(`customer/companies`, formData)
      .then((res) => dispatch("auth/setActiveCompany", res))
      .catch(() => {
        toast.error(t('An error occurred'));
        setFormValues()
      });
}

const setFormValues = () => {


  api.post("components/timezone-select")
      .then((r) => (timeZones.value = r.data));

  api.post("components/measurement-select")
      .then((r) => (measurements.value = r.data));
  api.post("components/date-format-select", { lang: me.value.locale })
    .then((r) => {
      dateFormatList.value = r.data;
    })

  form.timeZone = activeCompany.value.timezone;
  form.distanceFormat = activeCompany.value.unit_slug;
  form.gdpr = activeCompany.value.gdpr;
  form.is_maximum_delivery_distance = activeCompany.value.is_maximum_delivery_distance;
  form.maximum_delivery_distance = activeCompany.value.maximum_delivery_distance;
  form.auto_pool_slot = activeCompany.value.auto_pool_slot;
  form.obey_status_order = activeCompany.value.obey_status_order;
  form.sms_enabled = activeCompany.value.sms_enabled;
  form.mail_enabled = activeCompany.value.mail_enabled;
  form.lock_tasks = activeCompany.value.lock_tasks;
  form.allow_batch_in_progress = activeCompany.value.allow_batch_in_progress;
  form.allow_batch_on_delivery = activeCompany.value.allow_batch_on_delivery;
  form.task_live_stream = activeCompany.value.task_live_stream;
  form.show_destination = activeCompany.value.show_destination
  form.currencyUnit = activeCompany.value.currency_unit
  form.date_format_slug = activeCompany.value.date_format_slug
}

const saveRow = (param) => {
  let formData = new FormData();
  let value = param.value
  if (typeof param.value === "boolean") {
    value = Number(param.value)
  }
  formData.append(param.name, value);
  formData.append("_method", "PUT")
  api.post(`customer/companies`, formData)
      .then((res) => dispatch("auth/setActiveCompany", res))
      .catch(() => {
        toast.error(t('An error occurred'));
        setFormValues()
      });
}

onActivated(() => {
  setFormValues()
  getDateFormat()
});
onMounted(() => {
  setFormValues()
  getDateFormat()
})


let activePassive = [
  {
    key: true,
    label: t("Active"),
  },
  {
    key: false,
    label: t("Passive")
  }
]
let showHide = [
  {
    key: true,
    label: t('Show')
  },
  {
    key: false,
    label: t('Hide')
  }
]


</script>

<template>
  <SettingsPageLayout>
    <template #header>
        <span class="text-lg font-bold text-slate-700 m-3"> {{ t('Application Settings') }} </span>
    </template>
    <template #content>
      <div class="border-b border-slate-300 text-base px-4 py-7 text-slate-700  bg-slate-50">
        {{ t('Default System Settings') }}
      </div>
      <SettingsInputRow
          v-model="form.timeZone"
          :label="t('Time Zone')"
          @cancel="setFormValues"
          type="select"
          :options="timeZones"
          options_key="slug"
          options_label="name"
          @save="saveRow"
          form-data-name="timezone"
      />
      <SettingsInputRow
        v-model="form.date_format_slug"
        :label="t('Date Format')"
        @cancel="setFormValues"
        type="select"
        :options="dateFormatList"
        options_key="slug"
        options_label="format"
        @save="saveRow"
        form-data-name="date_format_slug"
      />
      <SettingsInputRow
          v-model="form.distanceFormat"
          :label="t('Distance Unit')"
          @cancel="setFormValues"
          type="select"
          :options="measurements"
          options_key="slug"
          options_label="name"
          @save="saveRow"
          form-data-name="unit_slug"
      />
      <SettingsInputRow
          v-model="form.currencyUnit"
          :label="t('Currency Unit')"
          @cancel="setFormValues"
          :disabled="true"
      />
      <SettingsInputRow
          v-model="form.gdpr"
          :label="t('GDPR')"
          @cancel="setFormValues"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          form-data-name="gdpr"
      />
      <SettingsInputRow
          :label="t('SMS')"
          :options="activePassive"
          type="select"
          options_key="key"
          options_label="label"
          form-data-name="sms_enabled"
          @save="saveRow"
          v-model="form.sms_enabled"

      />
      <SettingsInputRow
          :label="t('Email')"
          :options="activePassive"
          type="select"
          options_key="key"
          options_label="label"
          form-data-name="mail_enabled"
          @save="saveRow"
          v-model="form.mail_enabled"
      />

      <CallCenter/>

      <SettingsInputRow
          v-model="form.task_live_stream"
          :label="t('Task Live Stream')"
          @cancel="setFormValues"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          form-data-name="task_live_stream"
      />
      <SettingsInputRow
          v-model="form.show_destination"
          :label="t('Show Destination')"
          @cancel="setFormValues"
          type="select"
          :options="showHide"
          options_key="key"
          options_label="label"
          @save="saveRow"
          form-data-name="show_destination"
      />

      <TaskConfirmAction/>

      <div class="border-b border-slate-300 text-base px-4 py-7 text-slate-700  bg-slate-50">
        {{ t('Task Completed Rules') }}
      </div>


      <div
          class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
      >
        <dt class="col-span-1 text-sm font-medium text-gray-500">{{ t('Delivery Location Check') }}</dt>
        <dd
            class="mt-1 flex text-sm items-center justify-between text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
        >

          <div class="flex flex-grow col-span-3 items-center">
            <div class="flex-grow grid grid-cols-4 h-10 items-center">
              <div class="col-span-2">
                <el-select
                    v-if="mode"
                    v-model="form.is_maximum_delivery_distance"
                    class="w-full-important"
                    placeholder="Select"
                    filterable
                >
                  <el-option
                      v-for="item in activePassive"
                      :key="item.key"
                      :label="item.label"
                      :value="item.key"
                  >
                  </el-option>
                </el-select>
                <div v-else class="ml-4">
                  {{ form.is_maximum_delivery_distance ? t("Active") : t("Passive") }}
                </div>
              </div>
              <div class="col-span-2 ml-4 ">
                <el-input
                    v-if="mode"
                    v-model="form.maximum_delivery_distance"
                    placeholder="Delivery Distance"
                    type="number"
                    class=""
                >
                  <template #append>m</template>
                </el-input>
                <div v-else class="ml-4">
                  {{ form.maximum_delivery_distance ? form.maximum_delivery_distance : "" }}m
                </div>
              </div>
            </div>
            <el-button class="ml-2" :text="true" @click="toggle">
              {{ !mode ? t("Edit") : t("Update") }}
            </el-button>
            <div @click="onResetTaskCompletedRules" v-if="mode" class="ml-2 text-slate-300 cursor-pointer">
              <FontAwesomeIcon icon="ban"/>
            </div>
          </div>
        </dd>
      </div>
      <div class="border-b border-slate-300 text-base px-4 py-7 text-slate-700  bg-slate-50">
        {{ t('Slot Rules') }}
      </div>
      <SettingsInputRow
          v-model="form.auto_pool_slot"
          :label="t('Auto Pool Slot')"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="auto_pool_slot"
      />
      <SettingsInputRow
          v-model="form.allow_batch_in_progress"
          :label="t('Allow Batch In Progress')"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="allow_batch_in_progress"
      />
      <SettingsInputRow
          v-model="form.allow_batch_on_delivery"
          :label="t('Allow Batch On Delivery')"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="allow_batch_on_delivery"
      />
      <SettingsInputRow
          v-model="form.obey_status_order"
          :label="t('Obey Status Order')"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="obey_status_order"
      />
      <SettingsInputRow
          v-model="form.lock_tasks"
          :label="t('Lock Tasks')"
          type="select"
          :options="activePassive"
          options_key="key"
          options_label="label"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="lock_tasks"
      />
      <div class="pb-20"/>
    </template>
  </SettingsPageLayout>
</template>
