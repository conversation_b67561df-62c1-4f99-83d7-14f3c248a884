<script setup>
// TODO abonelik bilgileri: plan_data içindeki plan içindeki plan_features
// TODO yenileme tarihi:
// TODO aylık plan ücreti: planın içeindeki price alanı
// TODO faturalar EP:subscription-invoices alanının içinde

import { ref, onMounted, inject, computed } from "vue";
import { useI18n } from "vue-i18n";
import { Chart, BarController, LinearScale, CategoryScale, BarElement } from "chart.js";
import { useStore } from "vuex";
import formatter from "@/class/formatter";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import dayjs from "dayjs";
import AddCartModal from "@/components/settings/AddCartModal.vue";
import { useToast } from "vue-toastification";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { faCcVisa } from "@fortawesome/free-brands-svg-icons";
import { faCcMastercard } from "@fortawesome/free-brands-svg-icons";
import masterCard from "/src/assets/images/masterCard.png";
import visa from "/src/assets/images/visa.png";


const api = inject("api");
const { t } = useI18n();
const loader = ref();
const { dispatch, getters } = useStore();
const me = computed(() => getters["auth/me"]);
const activeCompany = ref(null);
const tasks = ref();
const activeTab = ref("first");
const tasksArchive = ref();
const subscription = ref();
const invoices = ref();
const dialogVisible = ref(false);
const isActivePayment = ref();
const paymentMethods = ref();
const paymentUpdateDialogVisible = ref(false);
const paymentType = ref();
const toast = useToast();
const paymentMethodList = ref();
const paymentList = ref();

const serviceUsage = ref();
const totalCount = computed(() => {
  if (!serviceUsage.value) {
    return 0;
  }
  return serviceUsage.value.filter((r) => r.type_slug === "delivery").reduce((total, item) => {
    return total + Number(item.total_count);
  }, 0);
});
const totalCountCourier = computed(() => {
  if (!serviceUsage.value) {
    return 0;
  }
  return serviceUsage.value.filter((r) => r.type_slug === "courier").reduce((total, item) => {
    return total + Number(item.total_count);
  }, 0);
});

const deliveryFeatures = computed(() => {
  return subscription.value?.plan_data?.plan.plan_features.filter(feature => feature.service_usage_type.slug === "delivery");
});

const courierFeatures = computed(() => {
  return subscription.value?.plan_data?.plan.plan_features.filter(feature => feature.service_usage_type.slug === "courier");
});


const percentageDelivery = computed(() => {
  if (!deliveryFeatures.value || deliveryFeatures.value.length === 0) {
    return 0;
  }
  let rawPercentage = (totalCount.value / deliveryFeatures.value[0].limit) * 100;
  return Number(rawPercentage.toFixed(2));
});


const percentageDeliveryCourier = computed(() => {
  if (!courierFeatures.value || courierFeatures.value.length === 0) {
    return 0;
  }
  let rawPercentage = (totalCountCourier.value / courierFeatures.value[0].limit) * 100;
  return Number(rawPercentage.toFixed(2));
});

let labels = [];
let today = new Date();

for (let i = 14; i >= 0; i--) {
  let pastDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - i);
  labels.push(pastDate.getDate() + " " + pastDate.toLocaleString("tr-TR", { month: "long" }));
}

Chart.register(BarController, LinearScale, CategoryScale, BarElement);
const chartRef = ref(null);

onMounted(async () => {
  await Promise.all([
    getInvoices(),
    getTasksArchive(),
    getSubscription(),
    getTasks(),
    getServiceUsage(),
    getPaymentMethods()
  ]);
  setTimeout(() => {
    if (serviceUsage.value) {
      const ctx = chartRef.value.getContext("2d");
      let fifteenDaysAgo = new Date();
      fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);
      let recentTasks = serviceUsage.value.filter(task => new Date(task.usage_date) >= fifteenDaysAgo);

      let taskCounts = recentTasks.reduce((counts, task) => {
        let date = new Date(task.usage_date);
        let key = date.toLocaleString("tr-TR", { day: "numeric", month: "long" });
        counts[key] = (counts[key] || 0) + task.total_count;
        return counts;
      }, {});

      let chartData = Object.values(taskCounts);
      new Chart(ctx, {
        type: "bar",
        data: {
          labels: Object.keys(taskCounts),
          datasets: [{
            label: "# of Tasks",
            data: chartData,
            backgroundColor: [
              "#334155"
            ],
            borderColor: [
              "#334155"
            ],
            borderWidth: 1
          }]
        },
        options: {
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                display: false
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          },
          maintainAspectRatio: false,
          responsive: true,
          layout: {
            height: 180
          }
        }
      });
    }
  }, 10000);

});


const getTasks = () => {
  api("customer/tasks")
    .then((r) => {
      tasks.value = r.data;
    });
};

const getTasksArchive = () => {
  api("customer/task-archive")
    .then((r) => {
      tasksArchive.value = r.data;
    });
};


const getSubscription = () => {
  loader.value.show();
  api("customer/subscriptions/active-subscription")
    .then((r) => {
      subscription.value = r.data;

    }).finally(() => loader.value.hide());
};

const getInvoices = () => {
  api("customer/subscriptions/invoices")
    .then((r) => {

      invoices.value = r.data;
      paymentList.value = invoices.value.reduce((acc, invoice) => {
        if (Array.isArray(invoice.payments_data)) {
          acc.push(...invoice.payments_data);
        }
        return acc;
      }, []);



    });
};


const paymetMethods = () => {
  api("customer/subscriptions/payment-methods")
    .then((r) => {

      paymentMethodList.value = r.data;
    });
};


const getServiceUsage = () => {
  let today = new Date();
  let pastDate = new Date();
  pastDate.setDate(today.getDate() - 14);
  let todayISO = today.toISOString().split("T")[0];
  let pastDateISO = pastDate.toISOString().split("T")[0];

  let body = {
    filter: {
      used_at: pastDateISO + "," + todayISO
    }
  };
  api.post("customer/subscriptions/service-usages", body)
    .then((r) => {
      serviceUsage.value = r.data;
    });
};


const getPaymentMethods = () => {
  api("customer/subscriptions/payment-methods")
    .then((r) => {
      isActivePayment.value = r.data.find((x) => x.is_default === true);
      paymentType.value = isActivePayment.value.id;
      paymentMethods.value = r.data;


    });
};


const updatePaymentMethod = () => {
  loader.value.show();
  api.put("customer/subscriptions/payment-methods/update-default-payment-method", { payment_method: paymentType.value })
    .then((r) => {
      getInvoices();
      getTasksArchive();
      getSubscription();
      getTasks();
      getServiceUsage();
      getPaymentMethods();
      paymentUpdateDialogVisible.value = false;
      toast.success("Kart güncellendi");
    }).catch((e) => toast.error(e.message))
    .finally(() => loader.value.hide());
};

const openModal = () => {
  dialogVisible.value = true;
};

const openPaymentUpdateModal = () => {
  paymentUpdateDialogVisible.value = true;
};


const closeDialog = () => {
  paymentUpdateDialogVisible.value = false;
};

const getData = () => {
  paymentUpdateDialogVisible.value = false;
  getInvoices();
  getTasksArchive();
  getSubscription();
  getTasks();
  getServiceUsage();
  getPaymentMethods();
  paymetMethods();

};


</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full bg-white flex-grow overflow-auto">
    <div class="w-full flex flex-col bg-white">
      <div class="flex  w-full  border-b" style="background: #FFFFFF;height: 53px;">
      </div>
      <div class="flex flex-col w-full  border-b" style="background:#F8FAFC;height: 120px;">
        <div style="font-size: 22px;" class="text-slate-600 p-4 ml-4">
          {{ $t("Subscription Information") }}
        </div>
        <div>
          <div class="text-slate-600 pt-3-4 ml-8 flex justify-between">
            <div class="flex">
              <span class="font-bold">{{ subscription?.plan_data?.plan?.name }} </span>
              <div class="mr-2" v-for="item in subscription?.plan_data?.plan?.plan_features"><span
                class="mx-2">|</span>{{ item.limit }} {{ item.service_usage_type.name }}
              </div>
            </div>
            <div class="mr-8">
              <!--              <el-button type="primary" size="small">Abonelik Düzenle</el-button>-->
            </div>
          </div>
        </div>
      </div>
      <!--      <div class=" h-24 border rounded-md mt-10 mx-6">-->
      <!--        <div class="h-full flex items-center justify-between mx-2">-->
      <!--          <div>-->
      <!--            <div class="text-slate-700" style="font-size: 17px;">-->
      <!--              Paketi Enterprise'a Yükselt-->
      <!--            </div>-->
      <!--            <div class="text-slate-700">-->
      <!--              Marka kimliği oluşturma ,kapsamlı analizler son kullanıcı-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          <div>-->
      <!--            <el-button class="bg-slate-700 text-white" size="small">Paketi Yükselt</el-button>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <div class="flex items-center justify-between mt-6 mx-6">
        <div class="w-1/3 border rounded-md h-36 flex flex-col items-start justify-between">
          <div class="flex flex-col ml-2 mt-1 text-slate-700">
            {{ $t("Updated Date") }}
          </div>
          <div class="text-2xl ml-2 text-slate-700 mt-4">
            {{ dayjs(subscription?.updated_at).format('MMMM DD ,YYYY') }}
          </div>
          <div @click="activeTab = 'second'" class="ml-2 text-slate-700 mt-4 mb-2 cursor-pointer"
               style="color: #0B46F4;">
            {{ $t("Show Invoices") }}
          </div>

        </div>
        <div class="w-1/3 border rounded-md h-36 mx-6 flex flex-col items-start justify-between">
          <div class="flex flex-col ml-2 mt-1 text-slate-700">
            {{ $t("Monthly Plan Fee") }}
          </div>
          <div class="text-2xl ml-2 text-slate-700 mt-4">
            {{ subscription && subscription.plan_data ? formatter.currency(subscription.plan_data?.plan?.price, subscription.plan_data?.plan?.currency_unit) : ""
            }}
          </div>
          <div @click="activeTab = 'third'" class="ml-2 text-slate-700 mt-4 mb-2 cursor-pointer"
               style="color: #0B46F4;">
            {{ $t("Payment History") }}
          </div>
        </div>
        <div
          class="w-1/3 border rounded-md h-36  bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white opacity-75">
          <div class="flex  ml-2 mt-1 justify-between items-start">
            <div>{{ $t("Defined Payment Method") }}</div>
            <img src="/public/logo.png" class="h-8 w-7 mt-2 mr-3" alt />
          </div>
          <div v-if="isActivePayment && isActivePayment.card" class="text-2xl ml-2  mt-2">
            **** **** **** {{ isActivePayment.card.last4 }}
          </div>
          <div v-if="!isActivePayment" class="text-2xl ml-2  mt-2">

          </div>

          <div v-if="isActivePayment" class="flex items-center justify-between ml-2 mt-7 mb-8 text-white ">
            <div @click="openPaymentUpdateModal" :class="isActivePayment.card.brand === 'visa'?'mb-0.5':''"
                 class="cursor-pointer">{{ $t("Update") }}
            </div>
            <!--            <FontAwesomeIcon v-if="paymentMethods && paymentMethods.length > 0" class="mr-2 text-2xl" :icon="faCcMastercard"/>-->
            <img

              :src="isActivePayment.card.brand === 'visa' ? visa : masterCard"
              class="h-4 mr-2"
              :class="isActivePayment.card.brand === 'visa' ? 'h-5': 'h-5'"
              alt
            />
          </div>
          <div v-if="!isActivePayment" @click="openPaymentUpdateModal" class="cursor-pointer ml-2 pt-2">
            {{ $t("Add New Card") }}
          </div>


        </div>
      </div>
      <div class="mx-6 mt-8">
        <el-tabs v-model="activeTab" class="demo-tabs">
          <el-tab-pane :label="t('Package Usage')" name="first">
            <div class="border rounded-lg p-4 ">
              <canvas style="height: 240px;" ref="chartRef"></canvas>
            </div>
            <div class="my-4  h-24  border rounded-lg ">
              <div class="my-3 px-4 flex justify-between">
               <span style="font-size: 15px;" class="text-slate-700">
                {{ $t("Task Capacity") }}
               </span>
                <div>
                  <span class="text-slate-700" style="font-size: 15px;"> {{ totalCount }}</span>
                  <span class="mx-1.5 text-slate-700" style="font-size: 15px; color:#a6a1a1; ">/</span>
                  <span class="text-slate-700"
                        style="font-size: 15px;">{{ deliveryFeatures && deliveryFeatures.length > 0 ? deliveryFeatures[0].limit : 0
                    }}</span>
                </div>
              </div>
              <el-progress class="px-4 custom-el-progress-bar" :text-inside="true" :stroke-width="26"
                           :percentage="percentageDelivery" />

              <!--              <div class="mt-4 px-4 flex justify-between">-->
              <!--                <span style="font-size: 15px;" class="text-slate-700">-->
              <!--               {{totalCount}}-->
              <!--               </span>-->
              <!--                <div>-->
              <!--                  <span class="text-slate-700" style="font-size: 15px;">100</span>-->
              <!--                </div>-->
              <!--              </div>-->
            </div>
            <div class="my-4  h-24  border rounded-lg ">
              <div class="my-3 px-4 flex justify-between">
               <span style="font-size: 15px;" class="text-slate-700">
               {{ $t("Employee Right") }}
               </span>
                <div>
                  <span class="text-slate-700" style="font-size: 15px;"> {{ totalCountCourier }}</span>
                  <span class="mx-1.5 text-slate-700" style="font-size: 15px; color:#a6a1a1; ">/</span>
                  <span class="text-slate-700"
                        style="font-size: 15px;">{{ courierFeatures && courierFeatures.length > 0 ? courierFeatures[0].limit : 0
                    }}</span>
                </div>
              </div>
              <el-progress class="custom-el-progress-bar px-4" :text-inside="true" :stroke-width="26"
                           :percentage="percentageDeliveryCourier" />

              <!--              <div class="mt-4 px-4 flex justify-between">-->
              <!--                <span style="font-size: 15px;" class="text-slate-700">-->
              <!--               17-->
              <!--               </span>-->
              <!--                <div>-->
              <!--                  <span class="text-slate-700" style="font-size: 15px;">100</span>-->
              <!--                </div>-->
              <!--              </div>-->
            </div>

          </el-tab-pane>
          <el-tab-pane :label="t('Invoices')" name="second">

            <table class="table-auto w-full ">
              <thead>
              <tr style="background: #F8FAFC" class="border-y border-x">
                <th class="px-3 py-2 font-bold text-left" style="color:#334155;">{{ t("Invoice Period") }}</th>
                <th class="px-4 py-2 font-bold text-center" style="color:#334155; min-width: 150px;">
                  {{ t("Invoice Description") }}
                </th>
                <th class="px-4 py-2 font-bold text-center" style="color:#334155; min-width: 150px;">{{ t("Amount") }}
                </th>
                <th class="px-1 pr-20 py-2 font-bold text-right" style="color:#334155;">{{ t("Status") }}</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="item in invoices" class="bg-white border-b border-x ">
                <td class="px-3 py-2 text-inter-regular" style="font-size: 13px;">
                  {{ dayjs(item.created_at).format("DD.MM.YYYY HH:mm") }}
                </td>
                <td class="px-4 py-2 text-center text-inter-regular" style="font-size: 13px;">
                  {{ item.provider.description }}
                </td>
                <td class="px-4 py-2 text-center text-inter-regular" style="font-size: 13px;">
                  {{ formatter.currency(item.amount_due, item.currency_unit) }}
                </td>
<!--                <td class="px-3 py-2 text-right text-inter-regular" style="font-size: 13px;">-->
<!--                  <div class=" flex items-center justify-end">-->


<!--                    <el-button size="small">{{ t("Pending") }}</el-button>-->
<!--                    <el-button size="small">{{ t("Make a Payment") }}</el-button>-->

<!--                  </div>-->
<!--                </td>-->
              </tr>
              </tbody>
            </table>

          </el-tab-pane>
          <el-tab-pane :label="t('Payments')" name="third">
            <table class="table-auto w-full ">
              <thead>
              <tr style="background: #F8FAFC" class="border-y border-x">
                <th class="px-3 py-2 font-bold text-left" style="color:#334155;">{{ t("Payment Date") }}</th>
                <th class="px-4 py-2 font-bold text-center" style="color:#334155; min-width: 150px;">
                  {{ t("Payment Description") }}
                </th>
                <th class="px-4 py-2 font-bold text-center" style="color:#334155; min-width: 150px;">{{ t("Amount") }}
                </th>
                <th class="px-1  py-2 font-bold text-right" style="color:#334155;">{{ t("Payment Channel") }}</th>
              </tr>
              </thead>
              <tbody>
              <tr class="bg-white border-b border-x" v-for="item in paymentList">
                <td class="px-3 py-2 text-inter-regular" style="font-size: 13px;">{{ item.paid_at }}</td>
                <td class="px-4 py-2 text-center text-inter-regular" style="font-size: 13px;">
                 {{item.billing_reason.name}}
                </td>
                <td class="px-4 py-2 text-center text-inter-regular" style="font-size: 13px;">
                  {{ formatter.currency(item.amount_paid, item.currency) }}
                </td>
                <td class="px-3 py-2 text-right text-inter-regular" style="font-size: 13px;">
                  Stripe
                </td>
              </tr>

              </tbody>
            </table>
          </el-tab-pane>
          <el-tab-pane :label="t('Billing Information')" name="fourth">
            <div style="margin-top: 0 !important;">
              <div class="h-11 border-y border-x flex items-center justify-end pr-3" style="background: #F8FAFC;">
<!--                <el-button size="small">-->
<!--                  {{ t("Edit") }}-->
<!--                </el-button>-->
              </div>
              <div class="h-16 border-b border-x flex justify-start items-center px-3">
                <div class="w-1/3 text-slate-700" style="font-size: 15px;">
                  {{ t("Company Title") }}
                </div>
                <div class="w-1/3 text-slate-700" style="font-size: 15px;">
                  {{ me.active_company.name }}
                </div>

              </div>
              <div class="h-16 border-b border-x flex justify-start items-center px-3">
                <div class="w-1/3  text-slate-700" style="font-size: 15px;">
                  {{ t("Tax Id") }}
                </div>
                <div class="w-1/3 text-slate-700" style="font-size: 15px;">
                  {{ me.active_company.tax_id }}
                </div>

              </div>
              <div class="h-16 border-b border-x flex justify-start items-center px-3">
                <div class="w-1/3  text-slate-700" style="font-size: 15px;">
                  {{ t("Tax Office") }}
                </div>
                <div class="w-1/3 text-slate-700" style="font-size: 15px;">
                  {{ me.active_company.tax_office }}
                </div>

              </div>
              <div class="h-16 border-b border-x flex justify-start items-center px-3">
                <div class="w-1/3  text-slate-700" style="font-size: 15px;">
                  {{ t("Address") }}
                </div>
                <div class="w-1/3 text-slate-700" style="font-size: 15px;">
                  {{ me.active_company.address }}
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <el-dialog width="480" v-model="paymentUpdateDialogVisible" class="el-custom-dialog full_screen">
        <el-tabs class="el-custom-tabs">
          <el-tab-pane :disabled="!isActivePayment" :label="t('Saved Cards')">
            <template #header>
              <div class="font-semibold text-left mr-auto border-b pb-2 h-"
                   style="font-size: 18px;color: #3E4B5E">
                {{ $t("Update Card") }}
              </div>
            </template>

            <div class="w-full flex flex-row mb-3 justify-between items-center px-4">
              <div class="flex flex-col  w-full">
               <span>
                  {{ $t("Payment Methods") }}
               </span>
                <el-select v-model="paymentType">
                  <el-option
                    v-for="item in paymentMethods"
                    :key="item.id"
                    :label="item.billing_details.name"
                    :value="item.id"
                  >
                    <span style="float: left">{{ item.billing_details.name }}</span>
                    <span
                      class="text-lg"
                      style="
                        float: right;
                        color: var(--el-text-color-secondary);
                        font-size: 14px;
                        "
                    >  **** **** **** {{ item.card.last4 }} <FontAwesomeIcon v-if="item.card.brand ==='visa'"
                                                                             :icon="faCcVisa" /> <FontAwesomeIcon
                      v-if="item.card.brand ==='mastercard'" :icon="faCcMastercard" /></span>

                  </el-option>
                </el-select>

              </div>
            </div>
            <div class="px-4 pb-3" style="color: #3E4B5E; font-size: 14px">
              {{ $t("Your default payment method will be changed.") }}
            </div>
            <div class="flex flex-row justify-start px-4 mb-4 ">
              <el-button @click="closeDialog">{{ $t("Cancel") }}</el-button>
              <el-button type="primary" @click="updatePaymentMethod">
                {{ $t("Update") }}
              </el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="t('Add New Card')">
            <AddCartModal @getData="getData"></AddCartModal>
          </el-tab-pane>

        </el-tabs>
        <!--        <AddCartModal  ></AddCartModal>-->
      </el-dialog>

    </div>
  </div>

</template>

<style scoped>
.el-tabs__item is-top {
  font-size: 33px !important;
}

.el-tabs__header is-top {
  margin: 0 !important;
}

</style>





