<script setup>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { defineProps, watch, inject, ref } from "vue";
import OptionsItem from "@/components/settings/statusFlow/OptionsItem.vue";
import OptionsSwitchItem from "@/components/settings/statusFlow/OptionsSwitchItem.vue";
import { useRoute } from "vue-router";
import { useToast } from "vue-toastification";
import { useI18n } from "vue-i18n";

const route = useRoute();
const api = inject("api");
const emit = defineEmits(["refreshList", "disableSelectClick"]);

const toast = useToast();
const { t } = useI18n();
const inputData = ref([]);
const loading = ref(true);

const buttonDisabled = ref(false);
const elementLoading = ref(false);
const entryDeleteDialog = ref(false);
const props = defineProps({
  step: {
    type: Object,
    required: true
  },
  entry: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  inputData: {
    type: Array,
    required: true
  },
  flowData: {
    type: Object,
    required: true
  },
  sortedEntries: {
    type: Array,
    required: true
  }
});

const data = {
  // steps -> data
};

const addNewFlow = () => {
};
const openDeleteDialog = () => {
};

const createEnryOption = (step) => {


  // let options = props.entry.options.map((i) => {
  //   let option = {
  //     option_slug: i.option_slug,
  //     option_values: i.option_values,
  //     is_active: i.is_active
  //   }
  //
  //   if(!Array.isArray(inputData.find(x => x.slug === option.option_slug).fields) ){
  //     _d= {
  //       option_slug: i.option_slug,
  //       option_values: i.option_values,
  //       is_active: i.is_active
  //     }
  //   }else {
  //
  //   }
  //
  //   return option
  // });


  if (!props.entry.id) {
    elementLoading.value = true;
    let body = {
      step_id: props.step.id,
      status_id: props.entry.status_id,
      is_required: true,
      is_active: props.entry.is_active ? props.entry.is_active : false,
      options: props.entry.options
    };


    api.post(`customer/status-maps/${route.params.id}/entries`, body)
      .then((r) => {
        emit("refreshList", true);
        toast.success(t("Successfully added."));
        emit("disableSelectClick", true);
        buttonDisabled.value=false
        elementLoading.value = false;

      }).catch((err) => {
      toast.error(err.message);
      elementLoading.value = false;
    });
  }

  if (props.entry.id) {
    elementLoading.value = true;
    let body = {
      step_id: props.step.id,
      status_id: props.entry.status_id,
      is_required: true,
      is_active: props.entry.is_active ? props.entry.is_active : false,
      options: props.entry.options
    };


    api.patch(`customer/status-maps/${route.params.id}/entries/${props.entry.id}`, body)
      .then((r) => {
        emit("refreshList", true);
        toast.success(t("The editing was successful."));
        buttonDisabled.value=false
        elementLoading.value = false;
      }).catch((err) => {
      toast.error(err.message);
      elementLoading.value = false;
    });
  }

};


const moveUp = () => {
  api(`customer/status-maps/${route.params.id}/entries/${props.entry.id}/order:up`)
    .then((r) => {
      emit("refreshList", true);
    });
};
const moveDown = () => {
  api(`customer/status-maps/${route.params.id}/entries/${props.entry.id}/order:down`)
    .then((r) => {
      emit("refreshList", true);
    });
};

const removeEntry = () => {
  const index = props.step.entries.findIndex(e => e === props.entry);
  if (index !== -1) {
    props.step.entries.splice(index, 1);
    emit("disableSelectClick", true);
  }
};

const click = () => {
  buttonDisabled.value = true;
};



const deleteEntry = () => {
  api.delete(`customer/status-maps/${route.params.id}/entries/${props.entry.id}`)
    .then((r) => {
      entryDeleteDialog.value = false;
      emit("refreshList", true);
      emit("disableSelectClick", true);

    });
};
// watch(props.entry.options, () => {
//
//   buttonDisabled.value = true;
//
// }, { deep: true });


const openDelete = () => {
  entryDeleteDialog.value = true;
};

const close = () => {
  entryDeleteDialog.value = false;
};

</script>

<template>

  <div @click="click">
    <div class="flex items-center justify-between" >
      <div class="flex items-center justify-start">
        <FontAwesomeIcon v-if="props.entry.id" @click="moveDown" style="color: #334155" size="xl"
                         icon="square-arrow-down" />
        <FontAwesomeIcon @click="moveUp" class="ml-1.5" v-if="props.index !== 0 && props.entry.id"
                         style="color: #334155" size="xl" icon="square-arrow-up" />
        <div class="ml-2 " style="font-size: 14px;">{{ props.entry?.entry_order }}</div>
        <div class="ml-12 " style="color:#334155;">
          {{ props.entry.status?.name }}
        </div>
      </div>
      <div class="flex items-center justify-start">
        <div class="bold" style="color:#334155;">

        </div>
        <div class="mx-4">
          <!--          <el-switch v-model="form2.başlatma_adımı" size="small" />-->
        </div>

      </div>
    </div>
    <div class="border-b w-full mt-1">
    </div>


    <div class="py-3 pl-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <div v-for="option in props.entry?.options" :key="option.id">
        <template v-if="Array.isArray(props.inputData.find(x => x.slug === option.option_slug).fields)">
          <OptionsSwitchItem class="pt-3"  :flowData="props.flowData" :option="option"
                             :data="props.inputData.find(x => x.slug === option.option_slug)" />
        </template>
        <template v-else>
          <div class="pt-8 flex justify-start items-center">
            <OptionsItem :option="option" :data="props.inputData.find(x => x.slug === option.option_slug)"
                         :entry="props.entry" />
          </div>
        </template>
      </div>
    </div>
    <div class="border-t w-full mb-3">
    </div>
    <div class="flex items-center justify-between">
      <div class="flex items-center justify-start">
        <el-switch v-model="props.entry.is_active" />
        <span class="ml-2 font-bold">{{ t("Active") }}</span>
      </div>
      <div class="flex items-center justify-start">
        <el-button :loading="elementLoading" v-if="!props.entry.id" @click="createEnryOption" size="small">{{ t("Save")
          }}
        </el-button>
        <el-button :loading="elementLoading" v-if="props.entry.id && buttonDisabled" @click="createEnryOption"
                   size="small">

          {{ t("Update") }}
        </el-button>
        <FontAwesomeIcon @click.stop v-if="props.entry.id" class="text-red-600 mx-2" @click="openDelete" icon="trash" />
        <FontAwesomeIcon v-if="!props.entry.id" class="text-red-600 mx-2" @click="removeEntry" icon="xmark" />
      </div>
    </div>
  </div>
  <el-dialog v-model="entryDeleteDialog"
             width="520px"
             center
             destroy-on-close
             top="250px">

    <template #header>
      <div class="font-semibold text-left mr-auto  pb-2"
           style="font-size: 18px;color: #3E4B5E">
        {{ t("Are you sure you want to delete this task?") }}
      </div>
    </template>


    <div class="flex flex-row justify-start mt-2">
      <el-button @click="close">{{ t("Cancel") }}</el-button>
      <el-button @click="deleteEntry" type="danger">
        {{ t("Delete") }}
      </el-button>
    </div>

  </el-dialog>
</template>
<style scoped>
div.border-b {
  box-sizing: border-box;
}
</style>