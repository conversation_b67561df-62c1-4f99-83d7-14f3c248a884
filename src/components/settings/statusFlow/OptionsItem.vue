<script setup>
import { defineProps, onMounted, watch } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const props = defineProps({
  option: {
    type: Object
  },
  data: {
    type: Object
  },
  entry: {
    type: Object
  },

});


const onChange = (event) => {

  if(props.option.option_slug === "check_destination_distance" && event){

    const item = props.entry.options.find(x => x.option_slug === "check_origin_distance");

    item.is_active = false

  }

  if(props.option.option_slug === "check_origin_distance" && event){

    const item = props.entry.options.find(x => x.option_slug === "check_destination_distance");

    item.is_active = false

  }

};


</script>


<template>
  <div class=" justify-center items-between ">
    <div class="mr-3">
      <el-switch class="mr-2" size="small" v-model="props.option.is_active" @change="onChange" />
      <el-popover
        ref="actionPopover"
        placement="top-start"
        trigger="click"
        :width="310"
      >
        <div v-if="props.data.fields.distance">
          {{ props.data.fields.distance.description }}
        </div>
        <div v-if="props.data.fields.template_id">
          {{ props.data.fields.template_id.description }}
        </div>
        <template #reference>
          <FontAwesomeIcon :class="props.option.option_slug === 'send_sms' ? 'mr-3 pl-1':null" style="color: #D7DAE2" class="mr-2" icon="circle-info" />
        </template>
      </el-popover>

      <span style="font-size: 12px;">{{ $t(props.data.name) }}</span>
    </div>
    <div  class="flex items-center ">
      <span style="font-size: 12px;" v-if="props.data.fields.distance" class="mr-2 w-1/3">{{ props.data.fields.distance.name }}</span>
      <span class="w-1/2" style="font-size: 12px;" v-if="props.data.fields.template_id">{{ props.data.fields.template_id.name }}</span>
      <el-input size="small" v-if="'distance' in props.option.option_values" placeholder=".metre"
                v-model="props.option.option_values.distance" />
      <el-input  size="small" v-if="'template_id' in props.option.option_values"  placeholder="Şablon ID"
                v-model="props.option.option_values.template_id" />
    </div>

  </div>


</template>