<script setup>
import { defineProps, watch, computed, inject } from "vue";
import EntryItem from "@/components/settings/statusFlow/EntryItem.vue";
import { useRoute } from "vue-router";

const emit = defineEmits(["refresh","disableSelectClick"]);
const props = defineProps({
  step: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  inputData: {
    type: Array,
    required: true
  },
  flowData: {
    type: Object,
    required: true
  }
});

const api = inject("api");
const route = useRoute();

const sortedEntries = computed(() => {
  if (props.step && props.step.entries) {
    return [...props.step.entries].sort((a, b) => a.entry_order - b.entry_order);
  } else {
    return [];
  }
});
const fetchEntries = () => {
  emit("refresh", true);
};
const disableSelectClick = () => {
  emit("disableSelectClick",true);
};


</script>

<template>


  <div v-for="(entry, index) in sortedEntries" :key="index"
       :class="entry.id?'px-5 py-2 border border-gray-200 rounded-lg my-2 hover:bg-gray-50' :'px-5 py-2 border border-gray-200 rounded-lg my-2 hover:bg-gray-50' ">
    <EntryItem  @disableSelectClick="disableSelectClick" :disabled="props.disabled" :flowData="props.flowData" :inputData="props.inputData" :index="index" :entry="entry" :step="props.step"
               @refreshList="fetchEntries" />
  </div>

</template>