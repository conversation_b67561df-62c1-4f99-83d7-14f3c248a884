<script setup>
import { defineProps } from 'vue'
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const props = defineProps({

  option:{
    type: Object,
  },
  data:{
    type: Object,
  },
  flowData:{
    type: Object,
  },

})


const onChange = (event) => {
  if(props.option.option_slug === "start_entry" && event === true){
    props.flowData.steps.forEach(step => {
      step.entries.forEach(entry => {
        entry.options.forEach(option => {
          if (option.option_slug === "start_entry") {
            option.is_active = false;
          }
        });
      });
    });
    props.option.is_active = true;
  }
};



</script>


<template>
  <div class="flex w-full items-center justify-start">
    <div class="pr-2">
      <el-switch @change="onChange"  size="small" v-model="props.option.is_active" />
    </div>
<div>
  <el-popover
    ref="actionPopover"
    placement="top-start"
    trigger="click"
    :width="310"
    :height="500"
  >
    <div >
      {{props.data.description}}
    </div>
    <template #reference>
      <FontAwesomeIcon style="color: #D7DAE2" class="mr-2" icon="circle-info"/>
    </template>
  </el-popover>
</div>
    <div class="bold" style="color:#334155; font-size: 12px;">
      {{ $t(props.data.name) }}
    </div>

  </div>
</template>