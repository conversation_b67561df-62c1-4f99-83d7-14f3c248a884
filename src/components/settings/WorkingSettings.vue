<script setup>
import { ref, inject, watch, onActivated, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import NewWorkingHourPlan from "@/components/settings/drawers/new/NewWorkingHourPlan.vue";
import WorkingHourPlanEdit from "@/components/settings/drawers/edit/WorkingHourPlanEdit.vue";
import WorkingTemplateInput from "@/components/settings/components/WorkingTemplateInput.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { t } = useI18n();
const api = inject("api");

const loader = ref();
const workingTemplates = ref([]);
const workingTemplatesForHub = ref([]);
const workingTemplatesForCourier = ref([]);
const newPlanDialogVisible = ref(false);
const planEditDialogVisible = ref(false);
const selectedWorkingHourTemplate = ref();

const openNewPlanDialog = () => {
  newPlanDialogVisible.value = true;
};

const closeNewPlanDialog = (param) => {
  newPlanDialogVisible.value = false;
  if (param) {
    getWorkingHourTemplates();
  }
};

const openEditPlanDialog = () => {
  if (selectedWorkingHourTemplate.value) {
    planEditDialogVisible.value = true;
  }
};

const closeEditPlanDialog = (param) => {
  planEditDialogVisible.value = false;

  getWorkingHourTemplates();

};

const onUpdate = (id) => {
  loader.value.show();
  api.patch(`customer/working-hour-templates/${id}`, {
    ...workingTemplates.value.find(x => x.id === id),
    is_default: true
  })
    .then(() => getWorkingHourTemplates())
    .finally(() => loader.value.hide());
};

const onSelectToEdit = (param) => {
  selectedWorkingHourTemplate.value = param;
  openEditPlanDialog();
};


const getWorkingHourTemplates = () => {
  loader.value.show();
  api("customer/working-hour-templates")
    .then((res) => {
      workingTemplates.value = res.data;

      workingTemplatesForHub.value = res.data.filter(x => x.type === "hub");
      workingTemplatesForCourier.value = res.data.filter(x => x.type === "courier");

    })
    .finally(() => loader.value.hide());
};


onActivated(() => {
  getWorkingHourTemplates();
});

onMounted(() => {
  getWorkingHourTemplates();
});

watch(planEditDialogVisible, () => {
  if (!planEditDialogVisible.value) {
    selectedWorkingHourTemplate.value = null;
  }
});

const onRefresh = () => {
  getWorkingHourTemplates();
};

</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{ t("Working Plan") }} </span>
      <div class="flex items-center">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
          <el-button
            class="mr-3 items-center"
            size="small"
            @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" size="lg" />
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Plan')" position="bottom">
          <el-button
            @click="openNewPlanDialog"
            class="mr-3 items-center"
            size="small"
          >
            <FontAwesomeIcon icon="plus" size="lg" />
          </el-button>
        </ButtonToolTip>

      </div>
    </template>
    <template #content>
      <WorkingTemplateInput
        :title="$t('Default Hub Work Plan')"
        :workingTemplatesData="workingTemplatesForHub"
        @onSelectToEdit="onSelectToEdit"
        @update="onUpdate"
      />
      <WorkingTemplateInput
        :title="$t('Default Driver Work Plan')"
        :workingTemplatesData="workingTemplatesForCourier"
        @onSelectToEdit="onSelectToEdit"
        @update="onUpdate"
      />
    </template>
    <template #drawers>

      <el-dialog
        v-model="newPlanDialogVisible"
        class="customized-dialog customized-dialog--big"
        :title="t('New Working Plan')"
        destroy-on-close
      >

        <NewWorkingHourPlan @close="closeNewPlanDialog" />

      </el-dialog>

      <el-dialog
        v-model="planEditDialogVisible"
        class="customized-dialog customized-dialog--big"
        :title="t('Edit Working Plan')"
        destroy-on-close
      >

        <WorkingHourPlanEdit @close="closeEditPlanDialog" :planId="selectedWorkingHourTemplate" />

      </el-dialog>

    </template>

  </SettingsPageLayout>
</template>
