<script setup>
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import { useI18n } from "vue-i18n";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { ref, reactive, watch, onMounted, inject, computed } from "vue";
import router from "@/router";
import { useRoute } from "vue-router";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import StepsItem from "@/components/settings/statusFlow/StepsItem.vue";
import { useToast } from "vue-toastification";

const route = useRoute();
const { t } = useI18n();
const api = inject("api");
const flow = ref();
const loader = ref();
const defaultFlow = ref(false);
const flowName = ref();
const updateFlowName = ref(false);
const flowDeleteDialog = ref(false);
const flowId = ref();
const toast = useToast();
const inputData = ref([]);

const entrysItems = ref([]);
const statuses = ref([]);
const statuses2 = ref([]);
const disabledStatusSelect = ref(false);

const seitchAllowPrev = ref(false);
const seitchAllowNext = ref(false);
const stepId = ref();

// // 
const mainActiveNames = ref([]);
const staticActiveNames = ref([]);

const groupedStatuses = computed(() => {
  if (Array.isArray(statuses.value)) {
    return statuses.value.reduce((grouped, status) => {
      (grouped[status.category_slug] = grouped[status.category_slug] || []).push(status);
      return grouped;
    }, {});
  } else {
    return {};
  }
});

const flows = ref([]);

const navigateToNewFlowSettings = () => {
  router.push({ name: "Status Flows" });
};

const getInputData = () => {
  return api.post("components/status-map-entry-option-item-select")
    .then((r) => {
      inputData.value = r.data;
    })
};

onMounted(() => {
  getInputData();
  setTimeout(() => {
    getFlowDetail();
    getFlows();
    getStatuses();
    getStatusEntryItem();
  }, 720)

});

const getStatuses = () => {
  api("customer/statuses")
    .then((r) => {
      statuses.value = r.data.filter((item) => item.category_slug === "on_delivery" || item.category_slug === "in_progress").filter((x) => x.is_active === true);
      statuses2.value = r.data.filter((item) => item.category_slug === "completed" || item.category_slug === "cancelled" || item.category_slug === "failed").filter((x) => x.is_active === true);
    });
};

const getFlowDetail = () => {

  loader.value.show();
  api("customer/status-maps/" + route.params.id)
    .then((r) => {
      flow.value = r;
      flowName.value = r.name;
      defaultFlow.value = r.is_default;
    })
    .finally(() => {
      loader.value.hide();
    });
};

const openFlowNameDialog = () => {
  updateFlowName.value = true;
};
const updateflowName = () => {
  updateFlowName.value = false;
  loader.value.show();
  api.put(`customer/status-maps/${route.params.id}`, { name: flowName.value })
    .then((r) => {
      flow.value = r;
      getFlowDetail();
      toast.success(t("Your operation is successful."))
    })
    .finally(() => {

      loader.value.hide();
    });
};

const getFlows = () => {
  loader.value.show();
  api("customer/status-maps/" + route.params.id + "/steps")
    .then((r) => {
      flows.value = r.data;
    }).finally(() => {

      loader.value.hide();
    });
};

const newStep = () => {
  let body = {
    is_required: true,
    is_active: true,
    allow_prev: false,
    allow_next: false
  };
  api.post("customer/status-maps/" + route.params.id + "/steps", body)
    .then((r) => {

      getFlowDetail();
      getFlows();
      getStatuses();
      toast.success(t("New step added."))
    });
};

const openDeleteDialog = (id) => {
  flowDeleteDialog.value = true;
  flowId.value = id;
};

const deleteStep = () => {
  loader.value.show();
  api.delete("customer/status-maps/" + route.params.id + "/steps/" + flowId.value)
    .then((r) => {
      getFlowDetail();
      getFlows();
      getStatuses();
    })
    .finally(() => {
      flowDeleteDialog.value = false;
      loader.value.hide();
    });
};

const addNewFlow = (step, _index, _type) => {
  disabledStatusSelect.value = true

  let selectedStatus;
  if (step.type_slug === "completed" || step.type_slug === "failed" || step.type_slug === "cancelled") {
    selectedStatus = statuses2.value.find(x => x.id === step.selectedStatus);
  } else {
    selectedStatus = statuses.value.find(x => x.id === step.selectedStatus);
  }
  const statusIndex = statuses.value.findIndex(status => status.id === step.selectedStatus);
  if (statusIndex === -1) {
    statuses.value.push(selectedStatus);
  }

  // - // - // - // - //
  if (_type === 'main') {
    const mainIsOpened = mainActiveNames.value.indexOf(_index) !== -1;
    !mainIsOpened && mainActiveNames.value.push(_index);
  } else {
    const staticIsOpened = staticActiveNames.value.indexOf(_index) !== -1;
    !staticIsOpened && staticActiveNames.value.push(_index);
  }
  // - // 

  step.entries.push({
    entry_order: step.entries.length + 1,
    status: {
      name: selectedStatus.name
    },
    status_id: selectedStatus.id,
    options: [
      {
        option_slug: "mandatory_note",
        option_values: [],
        is_active: false
      },
      {
        option_slug: "set_after_confirm",
        option_values: [],
        is_active: false
      },
      {
        option_slug: "proceed_to_next_step",
        option_values: [],
        is_active: false
      },
      {
        option_slug: "start_entry",
        option_values: [],
        is_active: false
      },
      {
        option_slug: "check_origin_distance",
        option_values: {
          distance: 0
        },
        is_active: false
      },
      {
        option_slug: "check_destination_distance",
        option_values: {
          distance: 0
        },
        is_active: false
      },
      {
        option_slug: "send_sms",
        option_values: {
          template_id: 0
        },
        is_active: false
      }
    ]
  });

  step.selectedStatus = null;
};

const getStatusEntryItem = () => {
  api.post("components/status-map-entry-option-item-select")
    .then((r) => {
      entrysItems.value = r.data;
    });
};

const moveUp = (id) => {
  api(`customer/status-maps/${route.params.id}/steps/${id}/order:up`)
    .then((r) => {
      getFlowDetail();
    });
};
const moveDown = (id) => {
  api(`customer/status-maps/${route.params.id}/steps/${id}/order:down`)
    .then((r) => {
      getFlowDetail();
    });
};

const closeDialogName = () => {
  updateFlowName.value = false;
}

watch(seitchAllowNext, () => {
  loader.value.show()
  api.patch(`customer/status-maps/${route.params.id}/steps/${stepId.value}`, { allow_prev: seitchAllowNext.value, allow_next: seitchAllowNext.value })
    .then((r) => {
      getFlowDetail();
      toast.success(t("Your operation is successful."))
    }).finally(() => {
      loader.value.hide()
    })
});

watch(seitchAllowPrev, () => {
  loader.value.show()
  api.patch(`customer/status-maps/${route.params.id}/steps/${stepId.value}`, { allow_prev: seitchAllowNext.value, allow_next: seitchAllowNext.value })
    .then((r) => {
      getFlowDetail();
      toast.success(t("Your operation is successful."))
    }).finally(() => {
      loader.value.hide()
    })
});

const updateDefaultFlow = (event) => {
  if (event) {
    loader.value.show();

    api.patch(`customer/status-maps/${route.params.id}`, { is_default: defaultFlow.value })
      .then((r) => {

      })
      .finally(() => {
        loader.value.hide();
      });
  }
};

const stepIdClick = (id) => {
  stepId.value = id;
}

const disableSelectClick = () => {
  disabledStatusSelect.value = false;
}

const closeDeleteDialog = () => {
  flowDeleteDialog.value = false;
}

</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>
    <template #header>
      <div class="flex items-center justify-between w-full h-full"
           style="background: #F8FAFC">
        <div class="flex items-center justify-center m-3">
          <span class="text-lg font-bold text-slate-700"> {{ flow?.name }} </span>
          <FontAwesomeIcon @click="openFlowNameDialog"
                           class="ml-2 text-slate-700"
                           icon="edit" />
        </div>

        <div class="flex items-center cursor-pointer">
          <el-checkbox class="pr-3"
                       v-model="defaultFlow"
                       @change="updateDefaultFlow">{{ t("Default") }}</el-checkbox>
          <ButtonToolTip :tooltipText="$t('Turn Back')"
                         position="bottom">
            <el-button class="mr-3 items-center"
                       size="small"
                       @click="navigateToNewFlowSettings">
              {{ $t('Turn Back') }}
            </el-button>
          </ButtonToolTip>

        </div>
      </div>

    </template>
    <template #content>
      <div class="mx-20 mt-16 mb-10 cursor-pointer">
        <el-collapse class="el-custom-collapse el-collapse"
                     v-model="mainActiveNames">
          <el-collapse-item v-for="(step, index) in flow?.steps.filter((x) => x.type_slug !== 'completed' && x.type_slug !== 'failed' && x.type_slug !== 'cancelled')"
                            :key="index"
                            class="mt-3"
                            :name="index">
            <template #title>
              <div class="w-full h-full flex items-center justify-between">
                <div class="ml-2">
                  <FontAwesomeIcon @click="moveDown(step.id)"
                                   icon="square-arrow-down"
                                   class="mr-2" />
                  <FontAwesomeIcon @click="moveUp(step.id)"
                                   icon="square-arrow-up"
                                   class="mr-2"
                                   v-if="index !== 0" />
                  <span>{{ index + 1 }}.{{ $t("Step") }}</span>
                  <el-popover placement="top-start"
                              :width="330"
                              trigger="click">
                    <div>
                      <div>
                        <span class="mr-3"> {{ $t("Allow Proceeding to the Next Step") }}</span>
                        <el-switch v-model="seitchAllowNext"
                                   size="small"></el-switch>
                      </div>
                      <div>
                        <span class="mr-4">{{ $t("Allow Going Back to the Previous Step") }}</span>
                        <el-switch v-model="seitchAllowPrev"
                                   size="small"></el-switch>
                      </div>
                    </div>
                    <template #reference>
                      <FontAwesomeIcon class="mx-2"
                                       @click="stepIdClick(step.id)"
                                       icon="gear" />
                    </template>
                  </el-popover>



                  <!--                  <span style="color: #97989A">Sonraki Adıma Geç</span>-->
                </div>

                <div class="mr-2 flex items-center justify-center"
                     @click.stop>
                  <FontAwesomeIcon @click="openDeleteDialog(step.id)"
                                   class="mr-2 text-red-600"
                                   icon="trash" />
                  <el-select :disabled="disabledStatusSelect"
                             v-model="step.selectedStatus"
                             size="small"
                             class="mr-2"
                             style="width: 100px !important;"
                             :placeholder="t('Select Status')">

                    <el-option-group v-for="(group, slug) in groupedStatuses"
                                     :key="slug"
                                     :label="group[0].category.name">
                      <el-option v-for="item in group"
                                 :key="item.id"
                                 :label="item.name"
                                 :value="item.id"
                                 :disabled="flow?.steps.flatMap(step => step.entries.map(entry => entry.status_id)).includes(item.id)" />
                    </el-option-group>
                  </el-select>
                  <el-button :disabled="!step.selectedStatus"
                             size="small"
                             @click="addNewFlow(step, index, 'main')">{{ $t('Add') }}</el-button>

                </div>
              </div>
            </template>

            <StepsItem @disableSelectClick="disableSelectClick"
                       :flowData="flow"
                       :inputData="inputData"
                       @refresh="getFlowDetail"
                       :index="index"
                       :step="step" />

          </el-collapse-item>
        </el-collapse>

        <div class="mt-3">
          <div @click="newStep"
               class="w-full h-full rounded border flex items-center justify-center bold mb-4"
               style="height: 60px; background: #F8FAFC; font-size: 16px;">
            <FontAwesomeIcon icon="plus" />
            <span class="pl-2">{{ $t("Add New Step") }}</span>
          </div>
          <el-collapse class="el-custom-collapse el-collapse"
                       v-model="staticActiveNames">
            <el-collapse-item v-for="(step, _index) in flow?.steps.filter((x) => x.type_slug === 'completed' || x.type_slug === 'failed' || x.type_slug === 'cancelled')"
                              :key="_index"
                              :name="_index"
                              class="mt-3">
              <template #title>
                <div class="w-full flex items-center justify-between">
                  <div class="ml-2"
                       v-if="step.type_slug === 'completed'">{{ $t("Completed") }}</div>
                  <div class="ml-2"
                       v-if="step.type_slug === 'failed'">{{ $t("Failed") }}</div>
                  <div class="ml-2"
                       v-if="step.type_slug === 'cancelled'">{{ $t("Cancelled") }}</div>

                  <div class="flex mr-2"
                       @click.stop>
                    <el-select :disabled="disabledStatusSelect"
                               v-model="step.selectedStatus"
                               size="small"
                               style="width: 100px !important;"
                               :placeholder="t('Select Status')">
                      <el-option v-for="item in statuses2.filter((r) => r.category_slug === step.type_slug)"
                                 :key="item.id"
                                 :label="item.name"
                                 :value="item.id"
                                 :disabled="step.entries.map(x => x.status_id).includes(item.id)">

                      </el-option>
                    </el-select>

                    <el-button :disabled="!step.selectedStatus"
                               @click="addNewFlow(step, _index, 'static')"
                               class="ml-2"
                               size="small">
                      {{ $t('Add') }}
                    </el-button>
                  </div>
                </div>
              </template>
              <StepsItem @disableSelectClick="disableSelectClick"
                         :flowData="flow"
                         :inputData="inputData"
                         @refresh="getFlowDetail"
                         :index="index"
                         :step="step" />
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

    </template>
  </SettingsPageLayout>
  <el-dialog v-model="updateFlowName"
             width="420px"
             center
             destroy-on-close
             top="250px">

    <template #header>
      <div class="font-semibold text-left mr-auto border-b pb-2"
           style="font-size: 18px;color: #3E4B5E">
        {{ $t("Edit Task Flow Name") }}
      </div>
    </template>

    <div class="w-full flex flex-row mb-6 justify-between items-center ">
      <div class="flex flex-col mr-2 w-full">
        <span>
          {{ $t("Flow Name") }}
        </span>

        <el-input v-model="flowName"
                  class="w-full mt-1.5">
        </el-input>
      </div>
    </div>
    <div class="flex flex-row justify-start mt-2">
      <el-button @click="closeDialogName">{{ $t("Cancel") }}</el-button>
      <el-button @click="updateflowName">
        {{ $t("Save") }}
      </el-button>
    </div>

  </el-dialog>
  <el-dialog v-model="flowDeleteDialog"
             width="520px"
             center
             destroy-on-close
             top="250px">

    <template #header>
      <div class="font-semibold text-left mr-auto  pb-2"
           style="font-size: 18px;color: #3E4B5E">
        {{ $t("Are you sure you want to delete this task step?") }}
      </div>
    </template>


    <div class="flex flex-row justify-start mt-2">
      <el-button @click="closeDeleteDialog">{{ t("Cancel") }}</el-button>
      <el-button @click="deleteStep"
                 type="danger">
        {{ t("Delete") }}
      </el-button>
    </div>

  </el-dialog>

</template>
<style scoped>
.el-collapse {

  border-bottom: none !important;
  border-top: none !important;

}
</style>
