<script>
import DetailButton from "@/renderers/DetailButton.vue";
import LogStatusRenderer from "@/renderers/LogStatusRenderer.vue";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    DetailButton,
    LogStatusRenderer
  }
};
</script>

<script setup>
import { inject, onActivated, ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import formatter from "@/class/formatter";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import ActivityDetail from "@/components/settings/drawers/detail/ActivityDetail.vue";

const { t } = useI18n();
const api = inject("api");

const selectedLog = ref();
const logDetailDrawer = ref(false);
const grid = ref();


const eventList = {
  "courier.updated": t("Courier Updated"),
  "hub.updated": t("Hub Updated"),
  "courier.created": t("Courier Created"),
  "hub.created": t("Hub Created"),
  "hub.deleted": t("Hub Deleted")
};

const logList = {
  courier: t("Courier"),
  hub: t("Hub"),
  team: t("Team")
};

const subjectList ={
  user: t("User"),
  hub: t("Hub"),
  team: t("Team"),
  task: t("Task"),
}

const columnDefs = [
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: false,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "description",
    headerName: t("Description"),
    export: true,
    sortable: false,
    cellClass: "stringType"
  },
  {
    field: "causer_id",
    headerName: t("Causer Id"),
    filter: "agTextColumnFilter",
    export: true,
    sortable: false,
    cellClass: "stringType"
  },
  {
    field: "causer.name",
    headerName: t("Transaction made by"),
    filter: "agTextColumnFilter",
    sortable: false,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "event",
    headerName: t("Event"),
    filter: "agSetColumnFilter",
    export: true,
    sortable: false,
    cellClass: "stringType",
    minWidth: 120,
    suppressSizeToFit: true,
    cellRenderer: "StatusRenderer",
    valueFormatter: (params) => params.data.event,
    filterParams: {
      values: function(params) {
        params.success(Object.keys(eventList));
      },
      valueFormatter: (params) => t(eventList[params.value])
    }
  },
  {
    field: "log_name",
    headerName: t("Log Name"),
    filter: "agSetColumnFilter",
    export: true,
    sortable: false,
    cellClass: "stringType",
    minWidth: 120,
    suppressSizeToFit: true,
    cellRenderer: "StatusRenderer",
    valueFormatter: (params) => params.data.log_name,
    filterParams: {
      values: function(params) {
        params.success(Object.keys(logList));
      },
      valueFormatter: (params) => t(logList[params.value])
    }
  },
  {
    field: "subject_id",
    headerName: t("Subject Id"),
    filter: "agTextColumnFilter",
    export: true,
    sortable: false,
    cellClass: "stringType"
  },
  {
    field: "subject_type",
    headerName: t("Subject Type"),
    filter: "agSetColumnFilter",
    export: true,
    sortable: false,
    cellClass: "stringType",
    minWidth: 120,
    suppressSizeToFit: true,
    cellRenderer: "StatusRenderer",
    valueFormatter: (params) => params.data.log_name,
    filterParams: {
      values: function(params) {
        params.success(Object.keys(subjectList));
      },
      valueFormatter: (params) => t(subjectList[params.value])
    }
  },


  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: { textAlign: "center" },
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openDetail(params);
      }
    }
  }
];


const openDetail = (id) => {
  logDetailDrawer.value = true;
  selectedLog.value = id;
};

const closeDetail = () => {
  logDetailDrawer.value = false;
};

onActivated(() => {
  grid.value && grid.value.refresh();
});

</script>
<template>
  <SettingsPageLayout>
    <template #header>
      <div class="m-3">
        <span class="text-lg font-bold text-slate-700"> {{ t("Activities") }} </span>
      </div>
    </template>
    <template #content>
      <SSDataGrid
        ref="grid"
        url="customer/activities"
        columnStateSlug="activities"
        rowKey="id"
        :restore-column-state-enabled="true"
        :columns="columnDefs"
      />
    </template>
    <template #drawers>
      <el-drawer
        v-model="logDetailDrawer"
        class="customized-drawer customized-drawer"
        :title="t('Activity Detail')"
        append-to-body
        destroy-on-close
      >
        <ActivityDetail :id="selectedLog" @close="closeDetail" />
      </el-drawer>
    </template>
  </SettingsPageLayout>
</template>
