<script>
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import DetailButton from "@/renderers/DetailButton.vue";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    StatusRenderer,
    DetailButton,
  },
};
</script>

<script setup>
import {inject, ref, onActivated,onMounted} from "vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import NewManager from "@/components/settings/drawers/new/NewManager.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ManagerActions from "@/components/settings/drawers/actions/ManagerActions.vue";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const toast = useToast()
const {t} = useI18n()
const api = inject("api");

const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const loader = ref();
const selectedManager = ref(null);
const managers = ref([]);
const newManagerDrawerVisible = ref(false);
const managerDetailDrawerVisible = ref(false);
const selectedRows = ref();
const gridApi = ref(null);

const columnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    width: 90,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
    filter: 'agTextColumnFilter',
  },
  {
    field: "name",
    headerName: t("Manager Name"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openManagerDetail(params);
      },
    },
  },
]);

function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

function handleSelectionChanged() {
  selectedRows.value = gridApi.value.getSelectedRows();
}

const openNewManagerDrawer = () => {
  newManagerDrawerVisible.value = true;
};

const closeNewManagerDrawer = (params) => {
  newManagerDrawerVisible.value = false;
  if (params) {
    getManagers()
  }
};

const openManagerDetail = (params) => {
  selectedManager.value = managers.value.find(i => i.id === params);
  managerDetailDrawerVisible.value = true;
};

const getManagers = () => {
  loader.value.show();
  api("customer/managers")
      .then((res) => {
        managers.value = res.data;
      }).catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide());
};

onActivated(() => {
  getManagers();
});
onMounted(() => {
  getManagers();
});

const onRefresh =()=>{
  getManagers()
}

</script>

<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{ t('Managers') }} </span>
      <div class="flex items-center">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom" >
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" size="lg"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Manager')" position="bottom">
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="openNewManagerDrawer"
          >
            <FontAwesomeIcon icon="plus" size="lg"/>
          </el-button>
        </ButtonToolTip>


      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxs border-b border-slate-300"
        >
          <div class="flex items-center justify-between flex-grow">
            <div class="flex px-1">
              <div class="px-1 py-2">
                {{ t('Total Manager') }} :
                <span class="font-bold">{{ managers.length }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow">
          <DataGrid
              v-model="gridApi"
              :dataSource="managers"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              @handleSelectionChanged="handleSelectionChanged"
              :autoSizeColumn="false"
          />
        </div>
      </div>
    </template>
    <template #drawers>
      <el-drawer
          v-model="newManagerDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('New Manager')"
          append-to-body
          destroy-on-close
      >
        <NewManager @close="closeNewManagerDrawer"/>
      </el-drawer>
      <ManagerActions v-model="managerDetailDrawerVisible" :selectedManager="selectedManager" @refresh="getManagers"/>
    </template>
  </SettingsPageLayout>
</template>
