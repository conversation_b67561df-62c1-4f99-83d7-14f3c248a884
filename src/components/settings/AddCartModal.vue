<script setup>
import { onMounted, ref, inject ,watch} from "vue";
import { loadStripe } from "@stripe/stripe-js";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";


const emit = defineEmits(["getData"]);
const api = inject("api");
const key = ref();
const route = useRoute();
const {t} = useI18n();
const submitVisible = ref(false);


const getSecretKey = () => {
  api.post("customer/subscriptions/start-payment")
    .then((r) => {
      key.value = r.client_secret;
    });
};

onMounted(() => {
  getSecretKey();
  boot();
  getKarts()
});

const getKarts = () => {
  api.get("customer/subscriptions/payment-methods")
    .then((r) => {

    });
};


const boot = async () => {
  var stripe = await loadStripe("pk_test_51NyrivAr7IBxBDyQp4oYtaJ2kbfaurTtI2nbjbbQt3VXnpjNjTJMnSyE208LDKBdTvNyXl7X3R4HdBRg7jumLPDa00ybyl63vq");
  var elements = stripe.elements();

  const style = {
    base: {
      color: "#32325D",
      fontFamily: "\"Helvetica Neue\", Helvetica, sans-serif",
      fontSmoothing: "antialiased",
      fontSize: "16px",
      "::placeholder": {
        color: "#AAB7C4"
      }
    },
    invalid: {
      color: "#FA755A",
      iconColor: "#FA755A"
    }
  };
  var card = elements.create("card", {
    hidePostalCode: true,
    style: style
  });
  card.mount("#card-element");
  card.addEventListener("change", function(event) {
    var displayError = document.getElementById("card-errors");
    if (event.error) {
      displayError.textContent = event.error.message;
    } else {
      displayError.textContent = "";
    }
  });

  const cardHolderName = document.getElementById("card-holder-name");
  const cardButton = document.getElementById("card-button");

  cardButton.addEventListener("click", async (e) => {
    e.preventDefault();
    const { setupIntent, error } = await stripe.confirmCardSetup(
      key.value, {
        payment_method: {
          card: card,
          billing_details: { name: cardHolderName.value }
        }
      }
    );
    if (error) {
      var errorElement = document.getElementById("card-errors");
      errorElement.textContent = error.message;
    } else {
      api.put('customer/subscriptions/payment-methods/update-default-payment-method',{payment_method:setupIntent.payment_method})
        .then((r) => {
          emit('getData', true)
        });
    }
  });
};



</script>
<template>
  <form method="GET" id="subscribe-form">
    <label class="mx-4" for="card-holder-name form-control">{{$t('Card Holder')}}</label> <br>
    <div class="w-full px-4">
      <input  id="card-holder-name" type="text" style="height: 30px; padding-left: 8px;" class="input">
    </div>

    <div class="form-row px-4 pt-3">
      <label for="card-element">{{$t("Card Information")}}</label>
      <div style="border: 1px solid #AAB7C4" id="card-element" class="form-control">
      </div>
      <!-- Used to display form errors. -->
      <div id="card-errors" role="alert"></div>
    </div>
    <div class="pt-4 px-4">{{ $t("In all payment transactions") }} <span class="text-indigo-600">Stripe</span>
      {{ $t("infrastructure is used") }}</div>
    <div class="stripe-errors"></div>
    <div class="flex items-center justify-start mt-3 mb-4 px-4">
      <button class="border py-1 px-2 rounded mr-3 bg-white">{{$t("Cancel")}}</button>
      <button id="card-button" :data-secret="key" class="border py-1 px-2 rounded bg-indigo-600 text-white">{{$t("Save Change")}}</button>
    </div>
  </form>
</template>

<style scoped>
.StripeElement {
  background-color: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  box-shadow: 0 1px 3px 0 #E6EBF1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 #CFD7DF;
}

.StripeElement--invalid {
  border-color: #FA755A;
}

.StripeElement--webkit-autofill {
  background-color: #FEFDE5 !important;
}
.input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #AAB7C4;
  box-shadow: 0 1px 3px 0 #E6EBF1;
  transition: box-shadow 150ms ease;
  padding-left: 30px;

}
</style>
