<script setup>
import { ref, onMounted, inject, watch, reactive } from "vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import router from "@/router";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import dayjs from "dayjs";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { useToast } from "vue-toastification";


const { t } = useI18n();
const route = useRoute();
const api = inject("api");
const statusMaps = ref();
const loader = ref();
const toast = useToast();
const flowId = ref();

const newFlowDialog = ref(false);
const flowDeleteDialog = ref(false);

const form = ref({
  name: null,
  is_default: false
});

const formValues = reactive({
  is_default: false
});


const navigateToNewFlowSettings = () => {
  router.push({ name: "NewStatusFlowSettings" });
};

const getStatusFlows = () => {
  loader.value.show();
  api.get("customer/status-maps")
    .then((r) => {
      statusMaps.value = r.data.sort((a, b) => dayjs(b.created_at).unix() - dayjs(a.created_at).unix());
      formValues.is_default = r.data.is_default;
    })
    .finally(() => {
      loader.value.hide();
    });
};

const deleteStatusMap = () => {
  loader.value.show()
  api.delete(`customer/status-maps/${flowId.value}`)
    .then(() => {
      getStatusFlows();
      toast.success(t("Your deletion was successful."));
      flowDeleteDialog.value = false;
    }).catch((err)=> toast.error(err.message))
    .finally(() => {
    loader.value.hide();
  });


};

const close =()=>{
  flowDeleteDialog.value = false;
}


const newStatusFlows = () => {
  let body = {
    name: form.value.name,
    is_default: form.value.is_default
  };
  api.post("customer/status-maps", body)
    .then((r) => {
      router.push({
        name: "Show Status Flow",
        params: { id: r.id }
      });
    });
};

onMounted(() => {
  getStatusFlows();
});


const openNewFlowDialog = () => {
  newFlowDialog.value = true;

};

const detailButton = (item) => {
  router.push({
    name: "Show Status Flow",
    params: { id: item.id }
  });
};

const openDeleteDialog = (item) => {
  flowId.value = item;
  flowDeleteDialog.value = true;
};

const closeNewFlowDialog = () => {
  newFlowDialog.value = false;
};


</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>
    <template #header>
      <div class="flex items-center justify-between w-full h-full" style="background: #F8FAFC">
        <span class="text-lg font-bold text-slate-700 m-3"> {{ t("Task Status Flow") }}</span>
        <div class="flex items-center">
          <ButtonToolTip :tooltipText="$t('New Flow')" position="left">
            <el-button
              class="mr-3 items-center"
              size="small"
              type="primary"

              @click="openNewFlowDialog"
            >
             {{ t("New Flow") }}
            </el-button>
          </ButtonToolTip>
        </div>
      </div>

    </template>
<template #content>
  <div class="w-full">
    <table class="table-auto w-full ">
      <thead>
      <tr style="background: #F8FAFC" class="border-b">
        <th class="px-3 py-2 font-bold text-left" style="color:#334155;">{{ t("Flow Name") }}</th>
        <th class="px-4 py-2 font-bold text-center" style="color:#334155; min-width: 150px;">{{ t("Created Date") }}</th>
        <th class="px-3 pr-40 py-2 font-bold text-right" style="color:#334155;"></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="item in statusMaps" :key="item.id" class="bg-white border-b border-slate-300">
        <td class="px-3 py-2 text-inter-regular" style="font-size: 13px;" >{{ item.name }}</td>
        <td class="px-4 py-2 text-center text-inter-regular" style="font-size: 13px;">{{ dayjs(item.created_at).format("DD.MM.YYYY") }}</td>
        <td class="px-3 py-2 text-right text-inter-regular" style="font-size: 13px;">
            <div  class=" flex items-center justify-end">
              <FontAwesomeIcon class="pr-1" v-if="item.is_default" style="color: #96BEED" icon="star" />
              <span v-if="item.is_default" class=" pr-3" style="color: #96BEED"> {{ t("Default") }}</span>
              <el-button v-if="!item.is_default" size="small" type="danger" @click="openDeleteDialog(item.id)">{{ t("Delete") }}</el-button>
              <el-button  @click="detailButton(item)" size="small">{{ t("Detail") }}</el-button>

            </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>



  </SettingsPageLayout>


  <el-dialog v-model="newFlowDialog"
             width="420px"
             center
             destroy-on-close
             top="250px">

    <template #header>
      <div class="font-semibold text-left mr-auto border-b pb-2"
           style="font-size: 18px;color: #3E4B5E">
        {{ t("New Status Flow") }}
      </div>
    </template>

    <div class="w-full flex flex-row mb-6 justify-between items-center ">
      <div class="flex flex-col mr-2 w-full">
               <span>
          {{ t("New Flow") }}
        </span>

        <el-input v-model="form.name" class="w-full mt-1.5">
        </el-input>
      </div>
    </div>
    <div class="flex flex-row justify-start mt-2">
      <el-button @click="closeNewFlowDialog">{{ t("Cancel") }}</el-button>
      <el-button @click="newStatusFlows">
        {{ t("Save") }}
      </el-button>
    </div>

  </el-dialog>
  <el-dialog v-model="flowDeleteDialog"
             width="520px"
             center
             destroy-on-close
             top="250px">

    <template #header>
      <div class="font-semibold text-left mr-auto  pb-2"
           style="font-size: 18px;color: #3E4B5E">
       {{t("Are you sure you want to delete this status flow?")}}
      </div>
    </template>


    <div class="flex flex-row justify-start mt-2">
      <el-button @click="close">{{ t("Cancel") }}</el-button>
      <el-button @click="deleteStatusMap" type="danger">
        {{ t("Delete") }}
      </el-button>
    </div>

  </el-dialog>


</template>
<style scoped>
.date {
  width: 100px;
}
</style>