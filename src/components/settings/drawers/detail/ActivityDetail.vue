<script setup>
import { ref, inject, onMounted } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { JsonTreeView } from "json-tree-view-vue3";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";

const { t } = useI18n();
const props = defineProps({
  id: { type: Number }
});

const api = inject("api");
const loader = ref();
const emit = defineEmits(["close"]);
const log = ref();
const data = ref([]);
const tableData = ref([]);
const couserData = ref([]);


const getData = () => {
  loader.value.show();
  api(`customer/activities/${props.id}`)
    .then((res) => {
      log.value = JSON.stringify(res.properties);
      data.value = res;

      tableData.value = [{
        key: "Log Adı",
        value: res.log_name
      }
        ,
        {
          key: t("Event"),
          value: res.event
        },
        {
          key: t("Description"),
          value: res.description
        },
        {
          key: t("Created At"),
          value: dayjs(res.created_at).format("DD.MM.YYYY") // Format date using Day.js
        },
        {
          key: t("Causer Type"),
          value: res.causer_type

        }
      ];
      couserData.value = [
        {
          key: t("Name"),
          value: res.causer.name
        },
        {
          key: t("Phone"),
          value: res.causer.phone_formatted
        },
        // {
        //   key: t("Phone Country"),
        //   value: res.causer.phone_country
        // },
        {
          key: "Email",
          value: res.causer.email
        }
      ];

    })
    .finally(() => loader.value.hide());
};

onMounted(() => {
  getData();
});


</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="flex flex-col w-full h-full relative p-5">
    <div class="w-full inset-0 overflow-auto">

      <el-table size="small" :fit="true" :show-header="false" :data="tableData">
        <el-table-column class-name="font-bold" prop="key" width="150" />
        <el-table-column prop="value" />
      </el-table>
      <div class="ml-2 text-base font-bold mt-4 text-slate-700 mb-2">
        {{$t('Processor')}}
      </div>
      <el-table class="mb-5" v-if="couserData" size="small" :fit="true" :show-header="false" :data="couserData">
        <el-table-column class-name="font-bold" prop="key" width="150" />
        <el-table-column prop="value" />
      </el-table>
      <JsonTreeView :data="log" />

    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: Object,
    log: Object
  }
};
</script>
