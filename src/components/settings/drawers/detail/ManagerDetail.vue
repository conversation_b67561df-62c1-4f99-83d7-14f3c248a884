<script setup>
import dayjs from "dayjs";
import StatusTag from "@/components/operations/components/StatusTag.vue";
import {reactive, inject, ref} from "vue";

const props = defineProps({
  manager: {type: Object},
});
const emit = defineEmits(["close", "showEdit"]);
const api = inject("api");
const loader = ref(null);
const manager = ref({});
const form = reactive({
  defaultTeam: ["Anadolu", "Avrupa"],
  defaultHub: "A340 - Anadolu",
});

const options = [
  {
    id: "1",
    value: "Anadolu",
  },
  {
    id: "2",
    value: "Avrupa",
  },
];
const editClick = () => {
  emit("showEdit")
};


</script>

<template>
  <LoadingBlock ref="loader"/>
  <MultiTabDetailPageLayout>
    <div class="h-24 w-full flex justify-between items-center px-5">
      <div class="h-full flex-grow flex items-center">
        <img
            class="rounded-full z-0 h-16 w-16"
            :src="props.manager?.avatar"
            alt="manager-picture"
        />
        <div class="flex-col ml-5 select-text">
          <div class="font-bold text-xl text-slate-700">{{ props.manager.name }}</div>
          <div class="text-xxs text-slate-700 opacity-80 mb-0.5">
            {{$t('Join at')}}
            {{ props.manager && dayjs(props.manager.created_at).format("DD.MM.YYYY HH:mm") }}
          </div>
          <StatusTag :status-id="1">{{$t('Active')}}</StatusTag>
        </div>
      </div>
      <div class="flex-shrink">
        <ButtonToolTip :tooltipText="$t('Edit')" position="bottom">

          <div
              @click="editClick"
              class="w-10 h-9 flex rounded-md border border-slate-300 justify-center items-center opacity-70 cursor-pointer"
          >
            <FontAwesomeIcon icon="edit"/>
          </div>
        </ButtonToolTip>

      </div>
    </div>
    <div
        class="w-full bg-slate-50 border-t border-b border-slate-300 grid grid-cols-4 gap-4 px-5 py-4 select-text"
    >
      <div class="col-span-2 md:col-span-1">
        <div class="mb-1.5">
          <div class="detail--information--label--text">{{$t('Manager')}} ID</div>
          <div class="detail--information--label--text--desc">
            # {{ props.manager.id }}
          </div>
        </div>
        <div class="mb-1.5">
          <div class="detail--information--label--text">{{$t('Phone')}}</div>
          <div class="detail--information--label--text--desc">
            {{ props.manager.phone }}
          </div>
        </div>
        <div class="">
          <div class="detail--information--label--text">Email</div>
          <div class="detail--information--label--text--desc">
            {{ props.manager.email }}
          </div>
        </div>
      </div>
      <div class="col-span-2 md:col-span-1">
<!--        <div class="detail&#45;&#45;information&#45;&#45;label&#45;&#45;text">Tag</div>-->

<!--        <div class="flex flex-wrap space-x-0.5 space-y-1">-->
<!--          <span></span>-->
<!--          <span class="custom&#45;&#45;badge&#45;&#45;text"> OutSources </span>-->
<!--          <span class="custom&#45;&#45;badge&#45;&#45;text"> Asd </span>-->
<!--        </div>-->
      </div>
    </div>
<!--    <SettingsInputRow-->
<!--        v-model="form.defaultHub"-->
<!--        label="Company Name"-->
<!--        type="select"-->
<!--    />-->
<!--    <SettingsInputRow-->
<!--        v-model="form.defaultTeam"-->
<!--        label="Company Legal Name"-->
<!--        type="multipleSelect"-->
<!--        :options="options"-->
<!--    />-->
    <!--    <div class="grid grid-cols-2 gap-4 p-5">-->

    <!--      <div class="col-span-2 md:col-span-1">-->
    <!--        <div class="flex justify-between items-center">-->
    <!--          <div class="font-bold text-md text-slate-700">Teams</div>-->
    <!--          <el-button :text="true" size="small"> Updated Team</el-button>-->
    <!--        </div>-->
    <!--        <div-->
    <!--          class="flex flex-grow flex flex-wrap text-xs text-slate-700 border border-slate-300 bg-slate-50 p-2.5"-->
    <!--        >-->
    <!--          <span class="mx-0.5 my-1">Anadolu Takım, </span>-->
    <!--          <span class="mx-0.5 my-1">Avrupa Takım, </span>-->
    <!--          <span class="mx-0.5 my-1">Küçük Takım, </span>-->
    <!--          <span class="mx-0.5 my-1">Yıldız Takım </span>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="col-span-2 md:col-span-1">-->
    <!--        <div class="flex justify-between items-center">-->
    <!--          <div class="font-bold text-md text-slate-700">Hubs</div>-->
    <!--          <el-button :text="true" size="small">Updated Hub</el-button>-->
    <!--        </div>-->
    <!--        <div-->
    <!--          class="flex flex-grow flex flex-wrap text-xs text-slate-700 border border-slate-300 bg-slate-50 p-2.5 "-->
    <!--        >-->
    <!--          <span class="mx-0.5 my-1">Anatolia D.</span>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
  </MultiTabDetailPageLayout>
</template>
