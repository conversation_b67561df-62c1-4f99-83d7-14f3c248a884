<script setup>
import { inject, ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";

const props = defineProps(
  {
    walletId: {
      type: [ Number], default: () => Number
    }
  }
);

const api = inject("api");
const { t } = useI18n();
const loader = ref();
const wallet = ref();






</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">

            <div class="flex justify-between">
              <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
            </div>
          <div class="flex justify-between">
            <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>Cüzdan adı :</span> <span>{{ wallet.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<style scoped>

</style>