<script setup>
import MultiTabDetailPageLayout from "@/components/settings/drawers/detail/MultiTabDetailPageLayout.vue";
import HubDispatchersEdit from "@/components/settings/drawers/detail/transaction/HubDispatchersEdit.vue";
import EmbedMap from "@/components/ui/EmbedMap.vue";
import ShowWorkingHourPlan from "@/components/ui/ShowWorkingHourPlan.vue";
import { onMounted, ref, inject, computed,defineProps } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import dayjs from "dayjs";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";


const toast = useToast();
const { t } = useI18n();
const api = inject("api");

const emit = defineEmits(["taskClicked", "taskDoubleClicked", "showEdit"]);
const props = defineProps({
  hubs: { type: Number }
});
const removeButtonDisabled = computed(() => selectedRows.value.length <= 0);

const loader = ref(null);
const newDispatcherVisible = ref(false);
const dialogVisible = ref(false);
const teams = ref([]);
const dispatchers = ref([]);
const selectedRows = ref([]);
const gridApi = ref(null);
const hub = ref();
const currentWorkingPlan = ref();


const columnDefs = ref([
  {
    field: "name",
    headerName: t("Name"),
    filter: true,
    sortable: true,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true
  },
  {
    field: "email",
    headerName: "Email",
    filter: true,
    sortable: true
  },
  {
    field: "phone",
    headerName: t("Phone"),
    filter: true,
    sortable: true
  }
]);

function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

function handleSelectionChanged() {
  selectedRows.value = gridApi.value.getSelectedRows();
}

const editClick = () => {
  emit("showEdit");
};


const dispatchersRemove = () => {
  loader.value.show();
  dialogVisible.value = false;
  api.delete(`customer/hubs/${props.hubs.id}/dispatchers`, {
    data: {
      dispatcher_ids: selectedRows.value.map(r => r.id)
    }
  })
    .then(() => {
      getDispatchers();
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};


const getTeams = () => {
  return new Promise((resolve, reject) => {
    api(`customer/teams/?filter[hub_id]=${props.hubs.id}`)
      .then((res) => {
        teams.value = res.data;
        resolve();
      })
      .catch(() => reject());
  });
};

const getDispatchers = () => {
  return new Promise((resolve, reject) => {
    api(`customer/dispatchers/?filter[hub_id]=${props.hubs.id}`)
      .then((res) => {
        dispatchers.value = res.data;
        resolve();
      })
      .catch(() => reject());
  });
};


const closeNewDispatcherDrawer = (param) => {
  newDispatcherVisible.value = false;
  if (param) {
    getDispatchers();
  }
};

const getHub = () => {
  api(`customer/hubs/${props.hubs.id}`)
    .then((res) => {
      currentWorkingPlan.value = res.data.working_hour_template;
      hub.value = res;
    })
    .catch((err) => toast.error(err.data.message));
};

onMounted(() => {
  loader.value.show();
  Promise.all([getHub(),getTeams(), getDispatchers()])
    .finally(() => loader.value.hide());
});

</script>

<template>

  <MultiTabDetailPageLayout>
    <LoadingBlock ref="loader" />
    <div class="h-24 w-full flex justify-between items-center px-5">
      <div class="h-full flex-grow flex items-center">
        <div class="flex-col select-all">
          <div class="font-bold text-xl text-slate-700">{{ hub?.name }}</div>
          <div class="text-xxs text-slate-700 opacity-80 mb-0.5">
            {{ t("Created at") + " " + dayjs(hub?.created_at).format("DD.MM.YYYY") }}.
          </div>
        </div>
      </div>
      <div class="flex-shrink">
        <ButtonToolTip :tooltipText="$t('Edit')" position="bottom">
          <div
            @click="editClick"
            class="w-10 h-9 flex rounded-md border border-slate-300 justify-center items-center opacity-70 cursor-pointer"
          >
            <FontAwesomeIcon icon="edit" />
          </div>
        </ButtonToolTip>

      </div>
    </div>
    <div
      class="w-full bg-slate-50 border-t border-b border-slate-300 grid grid-cols-4 gap-4 px-5 py-5 select-auto"
    >
      <div class="col-span-4 md:col-span-3">
        <div class="grid grid-cols-3 gap-4 mb-1.5">
          <div class="col-span-3 sm:col-span-1 ">
            <div class="detail--information--label--text">
              {{ t("Hub") }} No ({{ t("Special ID") }})
            </div>
            <div class="detail--information--label--text--desc">#{{ hub?.id }}</div>
          </div>

          <div class="col-span-3 sm:col-span-1 ">
<!--            <div class="detail&#45;&#45;information&#45;&#45;label&#45;&#45;text">-->
<!--              {{ t("Working Plan") }}-->
<!--            </div>-->

<!--            <el-popover-->
<!--              trigger="hover"-->
<!--              :width="400"-->
<!--            >-->
<!--              <template #default>-->
<!--                <ShowWorkingHourPlan :currentWorkingPlan="currentWorkingPlan" />-->
<!--              </template>-->
<!--              <template #reference>-->
<!--                <div class="detail&#45;&#45;information&#45;&#45;label&#45;&#45;text&#45;&#45;desc underline">-->
<!--                  {{ currentWorkingPlan?.name }}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-popover>-->
          </div>
        </div>
        <div class="grid grid-cols-3 gap-4 mb-1.5 select-text">
          <div class="col-span-3 sm:col-span-1 ">
            <div class="detail--information--label--text">{{ t("Integration Id") }}</div>
            <div class="detail--information--label--text--desc">#{{ hub?.integration_id }}</div>
          </div>
          <div class="col-span-3 sm:col-span-1">
            <div class="detail--information--label--text">{{ t("Default Service Time") }}</div>
            <div class="detail--information--label--text--desc">{{ hub?.default_service_time }}</div>
          </div>
        </div>
        <div class="mb-1.5 select-text">
          <div class="detail--information--label--text">{{ t("Address") }}</div>
          <div class="detail--information--label--text--desc">
            {{ hub?.address }}
          </div>
        </div>
        <div class="grid grid-cols-3 gap-4 select-text">
          <div class="col-span-3 sm:col-span-1">
            <div class="detail--information--label--text">{{ t("Building") + " " + t("Name") }}/No</div>
            <div class="detail--information--label--text--desc">{{ hub?.address_building }}</div>
          </div>
          <div class="col-span-3 sm:col-span-1">
            <div class="detail--information--label--text">{{ t("Floor") }}</div>
            <div class="detail--information--label--text--desc">{{ hub?.address_floor }}</div>
          </div>
          <div class="col-span-3 sm:col-span-1">
            <div class="detail--information--label--text">{{ t("Apartment") }} No</div>
            <div class="detail--information--label--text--desc">{{ hub?.address_apartment }}</div>
          </div>
        </div>
      </div>
      <div class="col-span-4 md:col-span-1">
        <div class="detail--information--label--text">{{ t("Tag") }}</div>

      </div>
    </div>
    <EmbedMap :lat="hub?.lat" :lng="hub?.lng" />
    <div class="flex justify-between items-center mx-5 my-2.5">
      <div class="font-bold text-md text-slate-700 items-center">
        {{ t("Hub Dispatchers") }}
      </div>
      <div class="flex items-center justfiy-end text-white">
        <el-button @click="dialogVisible=true" :disabled="removeButtonDisabled" type="danger" size="small">
          <FontAwesomeIcon icon="trash" />
          <span class="hidden lg:inline-block ml-2">{{ t("Remove") }}</span>
        </el-button>
        <el-button @click="newDispatcherVisible=true" type="primary" size="small">
          <template #icon>
            <FontAwesomeIcon icon="plus" />
          </template>
          <span class="hidden lg:inline-block ml-2">{{ t("Dispatcher") }}</span>
        </el-button>
      </div>
    </div>
    <div class="w-full h-40 border border-slate-300">
      <DataGrid
        v-model="gridApi"
        :dataSource="dispatchers"
        :columns="columnDefs"
        @rowClicked="rowClicked"
        @rowDoubleClicked="rowDoubleClicked"
        @handleSelectionChanged="handleSelectionChanged"
        :autoSizeColumn="false"

      />
    </div>
    <div class="grid grid-cols-2 gap-4 p-5">
      <div class="col-span-4 md:col-span-4">
        <div class="flex justify-between items-center">
          <div class="font-bold text-md text-slate-700">{{ t("Teams") }}</div>
        </div>
        <div
          class="flex flex-grow flex flex-wrap text-xs text-slate-700 border border-slate-300 bg-slate-50 p-2.5"
        >
          <span v-for="(team,index) in teams" :key="team.id" class="mx-0.5 my-1">
            {{ team.name }}
          <span v-if="index+1 !== teams.length">,</span>
          </span>
        </div>
      </div>
    </div>
    <el-drawer
      v-model="newDispatcherVisible"
      class="customized-drawer customized-drawer"
      :title="t('Dispatchers of the',{'message':hub?.name}) "
      append-to-body
      destroy-on-close
    >
      <HubDispatchersEdit @close="closeNewDispatcherDrawer" :hub="hub" :selectedDispatchers="dispatchers" />
    </el-drawer>
    <el-dialog v-model="dialogVisible" width="30%" center>
      <div class="text-center">
        {{ t("Are you accept to remove selected dispatchers from", { message: hub?.name }) }}
      </div>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t("Cancel") }}</el-button>
        <el-button @click="dispatchersRemove" type="primary">
          {{ t("Confirm") }}
        </el-button>
      </span>
      </template>
    </el-dialog>
  </MultiTabDetailPageLayout>
</template>
