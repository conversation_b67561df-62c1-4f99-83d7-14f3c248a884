<script>
import DetailButton from "@/renderers/RegionEditButton.vue";

export default {
  components: {

    DetailButton

  }
};
</script>
<script setup>
import { computed, ref, inject, onMounted } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import formatter from "@/class/formatter";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import TeamMapPoligon from "@/components/deliveries/TeamMapPoligon.vue";
import CourierUpdate from "@/components/deliveries/CourierUpdate.vue";
import NewTeamRegion from "@/components/settings/drawers/new/NewTeamRegion.vue";
import TeamRegionEdit from "@/components/settings/drawers/edit/TeamRegionEdit.vue";


const toast = useToast();
const { t } = useI18n();
const api = inject("api");
const team = ref();
const teamRegions = ref();
const region = ref();

const props = defineProps({
  team: { type: [Number, Object] }
});
const emit = defineEmits(["taskClicked", "taskDoubleClicked", "close", "showEdit", "refresh"]);


const loader = ref();
const couriers = ref([]);
const selectedRows = ref([]);
const dialogVisible = ref(false);
const drawerRegionVisible = ref(false);
const drawerRegionEditVisible = ref(false);
const gridApi = ref(null);
const regionsGridApi = ref(null);
const activeTab = ref("drivers"); // Tab kontrolü için
const rejectDialogVisible = ref(false);

const columnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    filter: true,
    sortable: true,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true
  },
  {
    field: "name",
    headerName: t("Driver"),
    filter: true,
    sortable: true
  },
  {
    field: "employment_date",
    headerName: t("Join Date"),
    filter: true,
    sortable: true,
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "phone",
    headerName: t("Phone"),
    filter: true,
    sortable: true
  },
  {
    field: "courier_detail.vehicle_type",
    headerName: t("Vehicle Type")
  }
]);
const columnDefsRegion = ref([
  {
    field: "id",
    headerName: "Id",
    filter: true,
    minWidth: 50,
    sortable: true
  },
  {
    field: "name",
    headerName: t("Name"),
    filter: true,
    minWidth: 150,
    sortable: true
  },
  {
    field: "description",
    headerName: t("Description"),
    minWidth: 150,
    filter: true,
    sortable: true
  },
  {
    field: "created_at",
    headerName: t("Created Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    minWidth: 150,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "updated_at",
    headerName: t("Updated Date"),
    filter: "agDateColumnFilter",
    export: true,
    minWidth: 150,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    maxWidth: 80,
    cellStyle: { textAlign: "center" },
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clickedEdit: (params) => {
        region.value = params;
        drawerRegionEditVisible.value = true;
      },
      clickedDelete: (params) => {
        console.log("params", params);
        rejectDialogVisible.value = true;
        region.value = params;
      }
    }
  }

]);

function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

const isTransferDisabled = computed(() => selectedRows.value.length <= 0);


function handleSelectionChanged() {
  console.log("handleSelectionChanged called, activeTab:", activeTab.value);

  // Always update selectedRows with the selected rows from the gridApi
  if (gridApi.value) {
    selectedRows.value = gridApi.value.getSelectedRows();
    console.log("Selected rows:", selectedRows.value);
  }
}

const editClick = () => {
  emit("showEdit");
};
const canTeamRemove = () => {
  dialogVisible.value = true;
};
const addRegionDrawerClick = () => {
  drawerRegionVisible.value = true;
};
const teamRemove = () => {
  loader.value.show();
  dialogVisible.value = false;
  let _courier_ids = [];
  selectedRows.value.forEach((courier) => _courier_ids.push(courier.id));
  api.delete(`customer/teams/${props.team.id}/couriers`, { data: { courier_ids: _courier_ids } })
    .then((res) => {
      emit("refresh");
      couriers.value = res.couriers;
    })
    .finally(() => loader.value.hide());
};

const getCouriers = () => {
  loader.value.show();
  api(`customer/couriers/?filter[team_id]=${props.team.id}`)
    .then((res) => couriers.value = res.data)
    .catch(() => {
      emit("close", false);
      toast.error(t("An error occurred"));
    })
    .finally(() => loader.value.hide());
};
const getTeam = () => {
  loader.value.show();
  api(`customer/teams/${props.team.id}`)
    .then((res) => team.value = res)
    .catch(() => {
      emit("close", false);
      toast.error(t("An error occurred"));
    })
    .finally(() => loader.value.hide());
};

const getTeamRegion = () => {
  loader.value.show();
  api("customer/teams/" + props.team.id + "/regions")
    .then((r) => {
      teamRegions.value = r.data;
    })
    .finally(() => loader.value.hide());
};

const rejectRegionTeam = () => {
  loader.value.show();
  api.delete(`customer/teams/${props.team.id}/regions/${region.value.id}`)
    .then((r) => {
      rejectDialogVisible.value = false;
      toast.success("İşleminiz başarılı.");
      getTeamRegion();
    })
    .catch(() => {
      toast.error(t("An error occurred"));
    })
    .finally(() => loader.value.hide());
};
onMounted(() => {
  getTeam();
  getCouriers();
  getTeamRegion();
});
</script>

<template>
  <div class="flex flex-col w-full h-full overflow-hidden">
    <LoadingBlock ref="loader" />
    <div
      class="h-24 w-full flex justify-between items-center px-5"
    >
      <div class="h-full flex-grow flex items-center select-text">
        <div class="flex-col text-slate-700" v-if="team">
          <div class="font-bold text-xl">{{ team?.name }}</div>
          <div class="font-medium text-md" v-if="team.hub">{{ team.hub.name }}</div>
          <div class="text-xxs opacity-80 mb-0.5">
            {{ t("Join at") }} {{ formatter.date({ value: team?.created_at }) }}.
          </div>
        </div>
      </div>
      <div class="flex-shrink">
        <ButtonToolTip :tooltipText="$t('Edit')" position="bottom">
          <div
            @click="editClick"
            class="w-10 h-9 flex rounded-md border border-slate-300 justify-center items-center opacity-70 cursor-pointer"
          >
            <FontAwesomeIcon icon="edit" />
          </div>
        </ButtonToolTip>
      </div>
    </div>
    <div
      class="w-full border-y border-slate-300"
      v-if="team?.lng || team?.area_json"
    >
      <TeamMapPoligon :team="team" />
    </div>

    <div v-if="team?.tags?.length > 0"
         class="w-full bg-slate-50 border-t border-b border-slate-300 grid grid-cols-4 gap-4 px-5 py-5"
    >
      <div class="col-span-4">
        <div class="detail--information--label--text">{{ t("Tag") }}</div>
        <div class="flex flex-wrap space-x-0.5 space-y-1">
          <span></span>
          <span class="custom--badge--text" v-for="tag in team.tags"> {{ tag }} </span>
        </div>
      </div>
    </div>

    <!-- Tab yapısı -->
    <div class="flex-grow flex flex-col">
      <el-tabs v-model="activeTab" class="h-full" type="border-card" tab-position="top">
        <!-- Sürücüler Tab -->
        <el-tab-pane :label="t('Drivers')" name="drivers" class="flex-grow flex flex-col">
          <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxxs border-b border-slate-300"
          >
            <div class="flex items-center justify-between flex-grow">
              <div class="flex px-1">
                <div class="px-1 py-0.5">
                  {{ t("Total Driver") }}:
                  <span class="font-bold">{{ couriers.length }}</span>
                </div>
              </div>
            </div>
            <div class="flex border-t sm:border-t-0">
              <div>
                <el-button
                  @click="canTeamRemove"
                  :disabled="isTransferDisabled"
                  type="danger"
                  size="small"
                  class="rounded-none"
                >
                  {{ t("Team Removal") }}
                </el-button>
              </div>
            </div>
          </div>

          <div class="w-full flex-grow">
            <DataGrid
              v-model="gridApi"
              :dataSource="couriers"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              @handleSelectionChanged="handleSelectionChanged"
            />
          </div>
        </el-tab-pane>

        <!-- Bölgeler Tab -->
        <el-tab-pane :label="t('Regions')" name="regions" class="flex-grow flex flex-col">
          <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxxs border-b border-slate-300"
          >
            <div class="flex items-center justify-between flex-grow">
              <div class="flex px-1">
                <!--                <div class="px-1 py-0.5">-->
                <!--                  {{ t("Total Driver") }}:-->
                <!--                  <span class="font-bold">{{ couriers.length }}</span>-->
                <!--                </div>-->
              </div>
            </div>
            <div class="flex border-t sm:border-t-0">
              <div>
                <el-button
                  @click="addRegionDrawerClick"
                  type="primary"
                  size="small"
                  class="rounded-none"
                >
                  {{ t("Takıma Bölge Ekle") }}
                </el-button>
              </div>
            </div>
          </div>

          <div class="w-full flex-grow">
            <DataGrid
              v-model="regionsGridApi"
              :dataSource="teamRegions"
              :columns="columnDefsRegion"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog v-model="dialogVisible" width="40%" center>
      <div class="text-center">
        <p>
          {{ t("Do you accept to remove selected couriers from", { message: team?.name }) }}
        </p>
      </div>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t("Cancel") }}</el-button>
        <el-button @click="teamRemove" type="primary">
          {{ t("Confirm") }}
        </el-button>
      </span>
      </template>
    </el-dialog>
    <el-drawer
      v-model="drawerRegionVisible"
      class="customized-drawer"
      :title="$t('New Region Team')"
      append-to-body
      destroy-on-close
    >
      <NewTeamRegion
        :teamId="props.team.id"
        @close="drawerRegionVisible = false"
        @refresh="getTeamRegion"
      />

    </el-drawer>
    <el-drawer
      v-model="drawerRegionEditVisible"
      class="customized-drawer"
      :title="$t('Edit Region Team')"
      append-to-body
      destroy-on-close
    >
      <TeamRegionEdit
        :region="region"
        :teamId="props.team.id"
        @close="drawerRegionEditVisible = false"
        @refresh="getTeamRegion"
      />
    </el-drawer>
  </div>


  <el-dialog v-model="rejectDialogVisible" width="30%" center>
    <div class="text-center">
      <p>
        Takıma atanmış {{ region?.name }} bölgesi silinecek emin misiniz?
      </p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">{{ t("Cancel") }}</el-button>
        <el-button @click="rejectRegionTeam" type="danger">
          {{ t("Delete") }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
/* Tab içeriğinin tam yükseklik alması için */
:deep(.el-tabs) {
  display: flex;
  height: 100%;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__content) {
  flex-grow: 1;
  display: flex;
}

:deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
