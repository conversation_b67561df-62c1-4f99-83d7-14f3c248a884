<script setup>
import {ref, inject, onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {JsonTreeView} from "json-tree-view-vue3";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const props = defineProps({
  id: {type: Number},
})

const api = inject("api")
const loader = ref()
const emit = defineEmits(["close"]);
const log = ref()

const getData = () => {
  loader.value.show()
  api(`customer/webhook-logs/${props.id}`)
      .then((res) => log.value = JSON.stringify(res))
      .finally(() => loader.value.hide())
}

onMounted(() => {
  getData()
})


</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="flex flex-col w-full h-full relative">
    <div class="absolute inset-0 overflow-auto">
          <JsonTreeView :data="log"/>
    </div>
  </div>
</template>
