<script setup>
import dayjs from "dayjs";
import StatusTag from "@/components/operations/components/StatusTag.vue";
import { reactive, inject, ref, onMounted, computed, defineProps, defineEmits } from "vue";
import { useToast } from "vue-toastification";
import DataGrid from "@/components/ui/DataGrid.vue";
import formatter from "@/class/formatter";
import { useI18n } from "vue-i18n";

const toast = useToast();
const api = inject("api");
const {t}= useI18n()

const props = defineProps({
  dispatcher: { type: Object },
});
const dispatcherHubs = computed(() => props.dispatcher.hubs);
const emit = defineEmits(["close", "showEdit"]);

const loader = ref(null);
const suspendedAt = ref(!props.dispatcher?.suspended_at);

const form = reactive({
  defaultTeam: ["Anadolu", "Avrupa"],
  defaultHub: "A340 - Anadolu",
});
const columnDefs = [
  {
    field: "integration_id",
    headerName: t("Location Integration Id"),
    filter: "agTextColumnFilter",
  },
  {
    field: "name",
    headerName: t("Hub Name"),
    filter: "agTextColumnFilter",
  },
  {
    field: "id",
    headerName: t("Hub Id"),
    filter: "agTextColumnFilter",
  },
  {
    field: "teams_count",
    headerName: t("Teams Count"),
    filter: "agTextColumnFilter",
  },
];

const options = [
  {
    id: "1",
    value: "Anadolu",
  },
  {
    id: "2",
    value: "Avrupa",
  },
];
const editClick = () => {
  emit("showEdit");
};

const onChange = (param) => {
  if (param) {
    removeSuspend();
  } else {
    suspend();
  }
};



const suspend = () => {
  loader.value.show();
  api
    .put(`customer/dispatchers/${props.dispatcher.id}/suspend`)
    .then(() => emit("close"))
    .catch((err) => {
      toast.error(err.data.message, "danger");
      suspendedAt.value = true;
    })
    .finally(() => loader.value.hide());
};

const removeSuspend = () => {
  loader.value.show();
  api
    .put(`customer/dispatchers/${props.dispatcher.id}/unsuspend`)
    .then(() => emit("close"))
    .catch((err) => {
      toast.error(err.data.message, "danger");
      suspendedAt.value = false;
    })
    .finally(() => loader.value.hide());
};
</script>

<template>
  <LoadingBlock ref="loader" />
  <MultiTabDetailPageLayout>
    <div class="h-24 w-full flex justify-between items-center px-5">
      <div class="h-full flex-grow flex items-center">
        <img
          class="rounded-full z-0 h-16 w-16"
          :src="dispatcher?.avatar_url"
          alt="dispatcher-picture"
        />
        <div class="flex-col ml-5 select-text">
          <div class="font-bold text-xl text-slate-700">{{ dispatcher?.name }}</div>
          <div class="text-xxs text-slate-700 opacity-80 mb-0.5">
            {{ $t("Join at") }}
            {{ dispatcher && dayjs(dispatcher.created_at).format("DD.MM.YYYY HH:mm") }}
          </div>
          <template v-if="!dispatcher.suspended_at">
            <StatusTag :status-id="1"> {{ $t("Active") }}</StatusTag>
          </template>
          <template v-if="dispatcher.suspended_at">
            <StatusTag status-slug="failed"> {{ $t("Suspend") }}</StatusTag>
          </template>
        </div>
      </div>
      <div class="flex items-center flex-shrink">
        <div class="mr-2">
          <el-switch v-model="suspendedAt" @change="onChange" size="small"></el-switch>
        </div>
        <div
          @click="editClick"
          class="w-10 h-9 flex rounded-md border border-slate-300 justify-center items-center opacity-70 cursor-pointer"
        >
          <FontAwesomeIcon icon="edit" />
        </div>
      </div>
    </div>
    <div
      class="w-full bg-slate-50 border-t border-b border-slate-300 grid grid-cols-4 gap-4 px-5 py-4 select-text"
    >
      <div class="col-span-2 md:col-span-1">
        <div class="mb-1.5">
          <div class="detail--information--label--text">{{ $t("Dispatcher") }} ID</div>
          <div class="detail--information--label--text--desc">
            # {{ dispatcher?.id }}
          </div>
        </div>
        <div class="mb-1.5">
          <div class="detail--information--label--text">{{ $t("Phone") }}</div>
          <div class="detail--information--label--text--desc">
            {{ dispatcher?.phone }}
          </div>
        </div>
        <div class="">
          <div class="detail--information--label--text">Email</div>
          <div class="detail--information--label--text--desc">
            {{ dispatcher?.email }}
          </div>
        </div>
      </div>
      <div class="col-span-2 md:col-span-1"></div>
    </div>
    <div class="w-full h-full">
      <DataGrid :columns="columnDefs" :dataSource="dispatcherHubs" />
    </div>
  </MultiTabDetailPageLayout>
</template>