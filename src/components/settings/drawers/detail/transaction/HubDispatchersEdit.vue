<script setup>
import {inject, ref, onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const emit = defineEmits(["close"]);
const props = defineProps({
  hub: {type: Object},
  selectedDispatchers: {type: Array}
})
const api = inject("api");
const loader = ref(null);
const dispatchers = ref([])
const selectedRows = ref([]);
const gridApi = ref(null);
const onCancel = () => {
  emit("close")
}
const columnDefs = ref([
  {
    field: "name",
    headerName: t("Name"),
    filter: true,
    sortable: true,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
  },
  {
    field: "email",
    headerName: "Email",
    filter: true,
    sortable: true,
  },
  {
    field: "phone",
    headerName: t("Phone"),
    filter: true,
    sortable: true,
  },
]);

function handleSelectionChanged() {
  selectedRows.value = gridApi.value.getSelectedRows();
}

const onSave = () => {
  loader.value.show()
  api.post(`customer/hubs/${props.hub.id}/dispatchers`, {
    dispatcher_ids: selectedRows.value.map(r => r.id)
  })
      .then(() => {
        emit("close", true)
      })
      .finally(() => loader.value.hide());
};


onMounted(() => {
  loader.value.show();
  api("customer/dispatchers")
      .then((res) => {
        dispatchers.value = res.data.filter((item) => {
          if (!props.selectedDispatchers.find(x => x.id === item.id)) {
            return item
          }
        })
      })
      .finally(() => loader.value.hide());
});
</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex flex-col">
    <div class="flex-grow">
      <DataGrid
          v-model="gridApi"
          :dataSource="dispatchers"
          :columns="columnDefs"
          @handleSelectionChanged="handleSelectionChanged"
      />
    </div>
    <div
        class="flex justify-between  w-full bg-white px-5 py-2"
        style="box-shadow: 0px 0px 5px #00000029"
    >
      <div>
        <el-button @click="onCancel">{{ t("Cancel") }}</el-button>
      </div>
      <el-button @click="onSave()" type="primary">
        <FontAwesomeIcon icon="check-square"/>
        {{ t("Save") }}
      </el-button>
    </div>
  </div>
</template>
