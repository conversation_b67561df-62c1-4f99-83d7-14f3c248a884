<script setup>
import {useI18n} from "vue-i18n";
import {inject, ref} from "vue";
import {JsonTreeView} from "json-tree-view-vue3";

const {t} = useI18n()
const props = defineProps({
  lodData: {type: Object},
})

const api = inject("api")
const loader = ref()



</script>
<template>
  <LoadingBlock ref="loader"/>
  <div class="flex flex-col w-full h-full relative">
    <div class="absolute inset-0 overflow-auto">
      <JsonTreeView :data="props.lodData"/>
    </div>
  </div>
</template>