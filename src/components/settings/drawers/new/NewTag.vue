<script setup>
import {reactive, ref, inject} from 'vue';
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useToast} from "vue-toastification";

const toast = useToast()
const api = inject("api");

const emit = defineEmits(["close"]);

const loader = ref()

const formValues = reactive({
  name: null,
  is_active: false,
})

const onSubmit = () => {
  loader.value.show();

  let body = {};
  if (formValues.name) body.name = formValues.name;
  body.is_active = formValues.is_active;

  api
    .post("customer/tags", body)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.message))
    .finally(() => loader.value.hide());
};

</script>

<template>

  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="$t('Tag')">
                <el-input
                    v-model="formValues.name"
                    :placeholder="$t('Tag')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Active') }}</div>
              <div>
                <el-switch v-model="formValues.is_active"/>
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="onSubmit" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>

          {{ $t('Create') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
