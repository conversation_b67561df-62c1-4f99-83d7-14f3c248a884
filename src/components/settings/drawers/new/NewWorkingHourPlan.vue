<script setup>
import {inject, reactive, ref} from "vue";
import formatter from "@/class/formatter";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import { useToast } from "vue-toastification";

const {t} = useI18n()
const toast = useToast()

const typelist = ref([
  {
    name: t("Hub"),
    type: "hub"
  },
  {
    name: t("Driver"),
    type: "courier"
  }
])

const defaultList = [
  {
    day_name: "monday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  },
  {
    day_name: "tuesday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  },
  {
    day_name: "wednesday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  },
  {
    day_name: "thursday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  },
  {
    day_name: "friday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  },
  {
    day_name: "saturday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  },
  {
    day_name: "sunday",
    starts_at: "09:00",
    ends_at: "22:00",
    is_close: true,
    break_time: 0,
    auto_working_on: false,
    auto_working_off: false,
    check_polygon: false
  }
]
const defaultPlanForm = reactive(defaultList)
const api = inject("api")

const emit = defineEmits(["close"])


const loader = ref()
const form = reactive({
  type: null,
  name: null,
  is_default: true,
})


const createWorkingHourTemplate = () => {
  loader.value.show();


  const prepareData = (data) => {
    const filteredData = {};
    for (const key in data) {
      if (data[key] !== null && data[key] !== '') {
        filteredData[key] = data[key];
      }
    }
    return filteredData;
  };

  let body = prepareData({
    name: form.name,
    is_default: form.is_default,
    working_hours: addWorkingHoursToCreatedTemplate(),
    type: form.type,
  });

  api.post("customer/working-hour-templates", body)
    .then(() => {
      toast.success('Çalışma planı oluşturuldu.')
      emit("close", true);
    })
    .catch((err) => toast.error(err.message))
    .finally(() => {
      loader.value.hide();
    });
};

const addWorkingHoursToCreatedTemplate = () => {
  let arr = []
  defaultPlanForm.forEach((x) => {
    let body = {
      day_name: x.day_name,
      starts_at: x.starts_at,
      ends_at: x.ends_at,
      is_open: x.is_close,
      break_time: form.type === 'courier' ? x.break_time : 0,
      auto_working_on: form.type === 'courier' ? x.auto_working_on : false,
      auto_working_off: form.type === 'courier' ? x.auto_working_off : false,
      check_polygon: form.type === 'courier' ? x.check_polygon : false
    }
    arr.push(body)
  })

  return arr

}




</script>

<template>
  <div
      class="w-full text-slate-700 text-sm working-hour-plan"
  >
    <LoadingBlock ref="loader"/>
    <div class="grid gap-4 grid-cols-5 border-b border-slate-300 p-7">
      <div class="col-span-1 flex items-center font-semibold">
        {{ $t("Type") }}
      </div>

      <el-select
          class="col-span-3"
          placeholder="Tip"
          filterable
          v-model="form.type"
      >
        <el-option
            v-for="item in typelist"
            :key="item.type"
            :label="item.name"
            :value="item.type"
        >
        </el-option>
      </el-select>
    </div>
    <div class="grid gap-4 grid-cols-5 border-b border-slate-300 p-7">
      <div class="col-span-1 flex items-center font-semibold">
        {{ $t("Plan Name") }}
      </div>

      <el-input
          v-model="form.name"
          class="col-span-3"
      />
      <div class="col-span-1 text-center">
        <el-checkbox
            v-model="form.is_default"
        >
          {{ $t('Default') }}
        </el-checkbox>
      </div>
    </div>
    <div v-for="item in defaultPlanForm"
         class="grid grid-cols-5  gap-4 border-b border-slate-300 px-4 py-2.5">

      <div class="col-span-1 flex items-center justify-end font-medium">
        {{ $t(formatter.capitalizeFirstLetter(item.day_name)) }}
      </div>
      <div class="col-span-3 grid-cols-4 flex items-center justify-evenly">
        <div class="w-32">
          <el-time-select
              class="w-32"
              v-model="item.starts_at"
              :disabled="!item.is_close"
              :clearable="false"
              :editable="false"
              start="00:00"
              end="23:30"
          >

          </el-time-select>
        </div>
        <div class="grow flex items-center justify-center">
          <FontAwesomeIcon icon="right-left"/>
        </div>
        <div class="w-32">
          <el-time-select
              class="w-32"
              v-model="item.ends_at"
              :disabled="!item.is_close"
              :clearable="false"
              :editable="false"
              start="00:00"
              end="23:30"
          >

          </el-time-select>
        </div>

      </div>
      <div v-if="form.type === 'hub'" class="col-span-1 flex items-center justify-center mr-3">
        <el-checkbox size="small" v-model="item.is_close" :label="$t('Open')"/>
      </div>
      <div v-if="form.type === 'courier'" class="col-span-1 flex items-center justify-center mr-3">
        <el-checkbox size="small" v-model="item.is_close" :label="$t('Working')"/>
      </div>
      <div v-if="form.type === 'courier'" class="col-span-1 flex items-center justify-end font-medium">
        {{ $t('Break time') }}
      </div>
<!--      <div v-if="form.type === 'courier'" class="col-span-4 flex items-center ">-->
<!--        <div class="w-32">-->
<!--          <el-input class="w-32" :disabled="!item.is_close" v-model="item.break_time" type="number"  size="small">-->
<!--            <template #suffix>DK</template>-->
<!--          </el-input>-->
<!--        </div>-->
<!--        <div class="mx-3">-->
<!--          <el-checkbox :disabled="!item.is_close" size="small" v-model="item.auto_working_on" :label="$t('Automatic Online')"/>-->
<!--        </div>-->
<!--        <div class="ml-1">-->
<!--          <el-checkbox :disabled="!item.is_close" size="small" v-model="item.auto_working_off" :label="$t('Automatic Offline')"/>-->
<!--        </div>-->
<!--      </div>-->
      <div v-if="form.type === 'courier'" class="col-span-4 flex items-center justify-between">
        <div class="w-32">
          <el-input class="w-32" :disabled="!item.is_close" v-model="item.break_time" type="number" size="small">
            <template #suffix>DK</template>
          </el-input>
        </div>
        <div>
<!--          <el-checkbox :disabled="!item.is_close" v-model="item.auto_working_on" size="small" :label="$t('Automatic Online')"/>-->
        </div>
        <div class="mr-3">
<!--          <el-checkbox :disabled="!item.is_close" v-model="item.auto_working_off" size="small" :label="$t('Automatic Offline')"/>-->
        </div>
        <div class="mr-1">
          <el-checkbox :disabled="!item.is_close" v-model="item.check_polygon" size="small" :label="$t('Check Polygon')"/>
        </div>
      </div>
    </div>

    <div class="bg-slate-50 text-right p-4">
      <el-button @click="createWorkingHourTemplate" type="primary">
        {{ $t('Save') }}
      </el-button>
    </div>
  </div>
</template>
