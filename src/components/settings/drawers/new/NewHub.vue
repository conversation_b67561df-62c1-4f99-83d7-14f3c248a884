<script setup>
import {inject, nextTick, onMounted, reactive, ref} from "vue";
import EmbedMap from "@/components/ui/EmbedMap.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import SelectBox from "@/components/ui/SelectBox.vue";
import WorkingHourPlanInput from "@/components/ui/WorkingHourPlanInput.vue";
import {useToast} from "vue-toastification";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const toast = useToast()
const {t} = useI18n()

const emit = defineEmits(["close"]);
const api = inject("api");

const loader = ref(null);
const formRef = ref();
const workingTemplates = ref([])

const preparationTimeList = [5, 10, 15, 20, 25]

const formValues = reactive({
  hubName: null,
  relatedTeam: null,
  integration_id: null,
  addressNote: null,
  searchAddress: null,
  building: null,
  flor: null,
  apartment: null,
  lat: null,
  lng: null,
  address: null,
  default_service_time: null,
  tags: [],
  working_hour_template_id: null,
  max_distance: null,
  is_active: true,
  default_preparation_time: 10,
  is_applying_stateless_invoice_items: false
});
const rules = reactive({
  hubName: [
    {
      required: true,
      message: t('Required'),
      trigger: "blur",
    },
  ],
  integration_id: [
    {
      required: true,
      message: t('Required'),
      trigger: "blur",
    },
  ],
  searchAddress: [
    {
      required: true,
      message: t('Required'),
      trigger: "blur",
    },
  ],
});

const getWorkingTemplates = () => {
  loader.value.show()
  api("customer/working-hour-templates?filter[type]=hub")
      .then((res) => {
        workingTemplates.value = res.data

        let defaultTemplate = res.data.find((x) => x.is_default)
        if (defaultTemplate) {
          formValues.working_hour_template_id = defaultTemplate.id
        } else if (res.data.length > 0) {
          formValues.working_hour_template_id = res.data[0].id
        }
      })
      .finally(() => loader.value.hide())
}


function resolveAddressFromCoordinates(lat, lng) {
  lat = parseFloat(lat.toFixed(8));
  lng = parseFloat(lng.toFixed(8));
  return new Promise((resolve) => {
    new google.maps.Geocoder().geocode(
        {location: {lat, lng}},
        (results, status) => {
          if (status === "OK") {
            if (results[0]) {
              resolve(results[0].formatted_address);
            }
          }
        }
    );
  });
}

const createHub = () => {
  loader.value.show();

  let body = {};
  if (formValues.hubName) body.name = formValues.hubName;
  if (formValues.address) body.address = formValues.address;
  if (formValues.building) body.address_building = formValues.building;
  if (formValues.apartment) body.address_apartment = formValues.apartment;
  if (formValues.flor) body.address_floor = formValues.flor;
  if (formValues.lat) body.lat = formValues.lat.toFixed(6);
  if (formValues.lng) body.lng = formValues.lng.toFixed(6);
  if (formValues.tags) body.tags = formValues.tags;
  if (formValues.integration_id) body.integration_id = formValues.integration_id;
  if (formValues.default_service_time) body.default_service_time = Number(formValues.default_service_time);
  if (formValues.working_hour_template_id) body.working_hour_template_id = formValues.working_hour_template_id;
  if (formValues.max_distance) body.max_distance = formValues.max_distance;
  if (formValues.is_active) body.is_active = formValues.is_active;
  if (formValues.default_preparation_time) body.default_preparation_time = formValues.default_preparation_time;
  if (formValues.is_applying_stateless_invoice_items) body.is_applying_stateless_invoice_items = formValues.is_applying_stateless_invoice_items;

  api
    .post("customer/hubs", body)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};


const submitForm = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      createHub();
    } else {
      return false;
    }
  });
};

const getSearchAddress =  async () => {
  const {Autocomplete} = await google.maps.importLibrary("places");
  const address = new Autocomplete(
      document.getElementById("address_search")
  );
  address.addListener("place_changed", () => {
    const place = address.getPlace();
    if (!place.geometry || !place.geometry.location) return;

    const location = place.geometry.location;
    resolveAddressFromCoordinates(location.lat(), location.lng()).then(
        (address) => {
          formValues.lat = location.lat();
          formValues.lng = location.lng();
          formValues.address = address;
        }
    );
  });
}

onMounted(() => {
  nextTick(() => {
    getWorkingTemplates()
    getSearchAddress()
  });
});

</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              :model="formValues"
              :rules="rules"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="t('Hub Name')" prop="hubName">
                <el-input
                    v-model="formValues.hubName"
                    :placeholder="t('Hub Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Hub Integration Id')" prop="integration_id">
                <el-input
                    v-model="formValues.integration_id"
                    :placeholder="t('Hub Integration Id')"
                />
              </el-form-item>
            </div>
            <div v-if="formValues.working_hour_template_id" class="col-span-12">
              <WorkingHourPlanInput
                  :templates="workingTemplates"
                  v-model="formValues.working_hour_template_id"
              />
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Quest Accept Distance')">
                <el-input
                    v-model="formValues.max_distance"
                    :placeholder="t('Quest Accept Distance')"
                    type="number"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Preparation Time')">
                <el-select
                    v-model="formValues.default_preparation_time"
                    class="w-full"
                    filterable
                    :placeholder="t('Preparation Time')"
                >

                  <el-option
                      v-for="item in preparationTimeList"
                      :key="item"
                      :label="item"
                      :value="item"
                  >

                    <span style="float: left">{{ item }}</span>
                    <span
                        style="
                        float: right;
                        color: var(--el-text-color-secondary);
                        font-size: 13px;
                        "
                    >{{ t('Min') }}</span
                    >
                  </el-option>

                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Service Time')">
                <el-input
                    v-model="formValues.default_service_time"
                    :placeholder="t('Service Time')"
                    type="number"
                >
                  <template #suffix> {{ t('Min') }}</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Address')" prop="searchAddress">
                <el-input
                    id="address_search"
                    v-model="formValues.searchAddress"
                    :placeholder="t('Address')"
                />
              </el-form-item>
              <EmbedMap
                  width="490"
                  height="140"
                  :lat="formValues.lat"
                  :lng="formValues.lng"
              />
            </div>
            <div class="col-span-12 grid grid-cols-12 gap-4">
              <div class="col-span-12 sm:col-span-4">
                <el-form-item :label="t('Building Name/No')">
                  <el-input
                      v-model="formValues.building"
                      :placeholder="t('Building Name/No')"
                  />
                </el-form-item>
              </div>
              <div class="col-span-6 sm:col-span-4">
                <el-form-item :label="t('Floor')">
                  <el-input v-model="formValues.flor" :placeholder="t('Floor')"/>
                </el-form-item>
              </div>
              <div class="col-span-6 sm:col-span-4">
                <el-form-item :label="t('Apartment No')">
                  <el-input
                      v-model="formValues.apartment"
                      :placeholder="t('Apartment No')"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Default Tag')">
                <SelectBox
                    v-model="formValues.tags"
                    url="customer/tags"
                    value="name"
                    key="name"
                    label="name"
                    :multiple="true"
                    :filterable="true"
                    :multiple-limit="1"
                    placeholder="Tags"
                />
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Is Applying Stateless Invoice Items') }}</div>
              <div>
                <el-switch v-model="formValues.is_applying_stateless_invoice_items"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Status') }}</div>
              <div>
                <el-switch v-model="formValues.is_active"/>
              </div>
            </div>

          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="submitForm(formRef)" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>
          {{ t('Create') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
