<script setup>
import {reactive, inject, ref, onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import PhoneInput from "@/components/ui/PhoneInput.vue";
import {useToast} from "vue-toastification";


const toast = useToast()
const {t} = useI18n()
const api = inject("api");

const emit = defineEmits(["close"]);
const props = defineProps({
  selectedHub: {type: Number}
})
const loader = ref(null);
const hubs = ref([]);
const teams = ref([]);

const formRef = ref();

const userPhone = reactive({
  phone: null,
  country: "TR"
})

const formValues = reactive({
  dispatcherNameSurname: "",
  email: "",
  password: "",
  password_confirmation: "",
  relatedHubIDs: [],
  relatedTeam: "",
  tag: "",
});
const validatePass = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(t("Required")));
  } else {
    if (formValues.password_confirmation !== "") {
      if (!formRef.value) return;
      formRef.value.validateField("password_confirmation", () => null);
    }
    callback();
  }
};
const validatePass2 = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(t("Please input the password again")));
  } else if (value !== formValues.password) {
    callback(new Error(t("Passwords don't match!")));
  } else {
    callback();
  }
};
const rules = reactive({
  dispatcherNameSurname: [
    {
      required: true,
      message: t("Required"),
      trigger: "blur",
    },

  ],
  password: [{validator: validatePass, trigger: "blur"}],
  password_confirmation: [{validator: validatePass2, trigger: "blur"}],
  email: [
    {
      type: "email",
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
  relatedHubIDs: [
    {
      type: "array",
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
});

const createDispatcher = () => {
  loader.value.show();

  let body = {};
  if (formValues.dispatcherNameSurname) body.name = formValues.dispatcherNameSurname;
  if (formValues.email) body.email = formValues.email;
  if (userPhone.phone) body.phone = userPhone.phone;
  if (userPhone.country) body.phone_country = userPhone.country;
  if (formValues.password) body.password = formValues.password;
  if (formValues.password_confirmation) body.password_confirmation = formValues.password_confirmation;
  if (formValues.relatedHubIDs) body.hub_ids = formValues.relatedHubIDs;

  api
    .post("customer/dispatchers", body)
    .then(() => {
      emit("close", true);
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};

const submitForm = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      createDispatcher();
    } else {
      return false;
    }
  });
};
onMounted(() => {
  loader.value.show();
  api("customer/hubs")
      .then((res) => {
        hubs.value = res.data
        if (props.selectedHub) {
          formValues.relatedHubIDs.push(props.selectedHub)
        }
      })
      .finally(() => loader.value.hide());
});
</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">

          <el-form
              ref="formRef"
              :model="formValues"
              :rules="rules"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item
                  :label="t('Dispatcher Name')"
                  required
                  prop="dispatcherNameSurname"
              >
                <el-input
                    v-model="formValues.dispatcherNameSurname"
                    :placeholder="t('Dispatcher Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <PhoneInput v-model="userPhone"/>
            </div>
            <div class="col-span-12">
              <el-form-item label="Email" required prop="email">
                <el-input
                    v-model="formValues.email"
                    type="email"
                    placeholder="Email"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Password')" required prop="password">
                <el-input
                    v-model="formValues.password"
                    type="password"
                    :placeholder="t('Password')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Password Confirmation')"
                  required
                  prop="password_confirmation"
              >
                <el-input
                    v-model="formValues.password_confirmation"
                    type="password"
                    :placeholder="t('Password Confirmation')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Related Hub')" required prop="relatedHubIDs">
                <el-select
                    v-model="formValues.relatedHubIDs"
                    :placeholder="t('Related Hub')"
                    class="w-full-important"
                    filterable
                    multiple
                >
                  <el-option
                      v-for="item in hubs"
                      :key="item.id"
                      :label="`${item.integration_id ? item.integration_id + ' - ' : ''}${item.name}`"
                      :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0px 0px 5px #00000029"
      >
        <el-button @click="submitForm(formRef)" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>
          {{ t('Create') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
