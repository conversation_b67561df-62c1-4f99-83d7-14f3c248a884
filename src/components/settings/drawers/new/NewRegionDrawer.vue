<script setup>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { useI18n } from "vue-i18n";
import { ref, defineProps, onMounted, computed, watch, inject, reactive } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useToast } from "vue-toastification";


const emit = defineEmits(["close"])
const props = defineProps({
  geoJsonData: {
    type: Object,
    required: true
  }
});




const { t } = useI18n();
const api = inject("api");
const loader = ref(null);
const toast = useToast()

const form = reactive(
  {
    name: null,
    description: null,
    is_active: false
  }
)

// Create a properly formatted GeoJSON object
const formattedGeoJson = computed(() => {
  if (!props.geoJsonData) return null;

  // Return the geometry part directly as a JSON object
  return {
    type: "Polygon",
    coordinates: props.geoJsonData.geometry.coordinates
  };
});


const displayGeoJson = computed(() => {
  if (!props.geoJsonData) return "";

  const geoJson = {
    type: "Feature",
    geometry: {
      type: props.geoJsonData.geometry.type,
      coordinates: props.geoJsonData.geometry.coordinates
    },
    properties: props.geoJsonData.properties || {}
  };

  return JSON.stringify(geoJson, null, 2);
});

// Ref for the textarea
const geoJsonText = ref(displayGeoJson.value);

// Update the textarea when the computed value changes
watch(displayGeoJson, (newValue) => {
  geoJsonText.value = newValue;
});

onMounted(() => {
  geoJsonText.value = displayGeoJson.value;
  console.log("Display GeoJSON:", geoJsonText.value);
  console.log("Formatted GeoJSON for server:", formattedGeoJson.value);

  // Log the structure of the formatted GeoJSON
  if (formattedGeoJson.value) {
    console.log("GeoJSON type:", formattedGeoJson.value.type);
    console.log("GeoJSON coordinates structure:", formattedGeoJson.value.coordinates.length);
  }
});


const createRegion = () => {
  loader.value.show()
  let body = {
    name: form.name,
    description: form.description,
    is_active: form.is_active,
    polygon_geojson: formattedGeoJson.value
  }
  api.post("customer/regions", body)
    .then((response) => {
      toast.success(t('Region successfully created.'));
      emit("close", true)
      // Optionally, you can reset the form or close the drawer here
    })
    .catch((error) => {
    toast.error(error.data?.message || t('An error occurred while creating the region.'));
      // Handle error appropriately
    }).finally(() => {
    loader.value.hide();
  });
};
</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
            label-position="top"
            class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="t('Region Name')">
                <el-input
                  v-model="form.name"
                  :placeholder="t('Region Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Description')">
                <el-input
                  v-model="form.description"
                  :placeholder="t('Description')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Region GeoJSON')">
                <el-input
                  v-model="geoJsonText"
                  type="textarea"
                  rows="10"
                  readonly
                />
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Active') }}</div>
              <div>
                <el-switch v-model="form.is_active"/>
              </div>
            </div>

          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="createRegion" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square" />
          </template>
          {{ t("Create") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
