<script setup>
import { reactive, inject, ref, onMounted } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import PinMap from "@/components/ui/PinMap.vue";

const api = inject("api");

const toast = useToast();
const { t } = useI18n();

const emit = defineEmits(["close"]);

const loader = ref(null);
const hubs = ref([]);
const tags = ref([]);
const formRef = ref();
const poligonJsonVsible = ref(true);

let formValues = reactive({
  teamName: "",
  relatedDispatcher: "",
  tags: [],
  hubId: "",
  area_json: null,
  allows_queued_tasks: false,
  assigns_pool_tasks_auto: false,
  transfers_delayed_tasks: false,
  allows_task_merging: false,
  pool_enabled: false,
  allow_support_couriers:false,
  vehicleType: [],
  task_merging_max_distance: 0,
  task_merging_minutes: 0,

});

const location = reactive({
  searchAddress: "",
  lat: null,
  lng: null,
  area_json: null
});

const rules = reactive({
  teamName: [
    {
      required: true,
      message: t("Required"),
      trigger: "blur"
    }
  ]
});

onMounted(() => {
  getData();
});

const createTeam = () => {
  loader.value.show();

  let body = {};
  body.allows_task_merging = formValues.allows_task_merging;
  body.transfers_delayed_tasks = formValues.transfers_delayed_tasks;
  body.allows_queued_tasks = formValues.allows_queued_tasks;
  body.assigns_pool_tasks_auto = formValues.assigns_pool_tasks_auto;
  body.pool_enabled = formValues.pool_enabled;
  body.allows_support_couriers = formValues.allow_support_couriers;

  if (formValues.teamName) body.name = formValues.teamName;
  if (formValues.hubId) body.hub_id = formValues.hubId;
  if (formValues.tags) body.tags = formValues.tags;
  if (location.area_json) body.area_json = location.area_json;
  if (location.lat) body.lat = location.lat;
  if (location.lng) body.lng = location.lng;
  if (formValues.vehicleType) body.vehicle_types = formValues.vehicleType;
  if (formValues.task_merging_max_distance && formValues.allows_task_merging) body.task_merging_max_distance = formValues.task_merging_max_distance;
  if (formValues.task_merging_minutes && formValues.allows_task_merging) body.task_merging_minutes = formValues.task_merging_minutes;

  api
    .post("customer/teams", body)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};


const getHubs = () => {
  return api("customer/hubs")
    .then((res) => (hubs.value = res.data));
};
const getTags = () => {
  return api("customer/tags")
    .then((res) => (tags.value = res.data));
};

const getData = () => {
  loader.value.show();
  Promise.all([getHubs(), getTags()])
    .finally(() => loader.value.hide());
};

const setAreaJson = (params) => {
  location.area_json = params;
};

const submitForm = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      createTeam();
    } else {
      return false;
    }
  });
};

</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form ref="formRef" :model="formValues" :rules="rules" label-position="top"
                   class="grid grid-cols-12 gap-4">
            <div class="col-span-12">
              <el-form-item :label="$t('Team Name')" prop="teamName">
                <el-input
                  v-model="formValues.teamName"
                  :placeholder="$t('Team Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Hub')">
                <el-select
                  v-model="formValues.hubId"
                  :placeholder="$t('Hub')"
                  filterable
                  class="w-full-important"
                >
                  <el-option
                    v-for="item in hubs"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Tags')">
                <SelectBox
                  v-model="formValues.tags"
                  url="customer/tags"
                  value="name"
                  key="name"
                  label="name"
                  :multiple="true"
                  :filterable="true"
                  placeholder="Tags"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Vehicle Type')">
                <SelectBox
                  v-model="formValues.vehicleType"
                  url="components/vehicle-type-select"
                  method="post"
                  placeholder="Vehicle Type"
                  key="slug"
                  label="name"
                  value="slug"
                  :multiple="true"
                  :filterable="true"
                />
              </el-form-item>
            </div>
            <PinMap
              v-model="location"
              @setAreaJson="setAreaJson"
              :poligonVisible="poligonJsonVsible"
            />
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Allows Queued Tasks") }}</div>
              <div>
                <el-switch v-model="formValues.allows_queued_tasks" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Pool") }}</div>
              <div>
                <el-switch v-model="formValues.pool_enabled" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Assigns Pool Tasks Auto") }}</div>
              <div>
                <el-switch v-model="formValues.assigns_pool_tasks_auto" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Transfers Delayed Tasks") }}</div>
              <div>
                <el-switch v-model="formValues.transfers_delayed_tasks" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Allows Task Merging") }}</div>
              <div>
                <el-switch v-model="formValues.allows_task_merging" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Destek Kuryesine İzin Ver") }}</div>
              <div>
                <el-switch v-model="formValues.allow_support_couriers" />
              </div>
            </div>
            <div class="col-span-12" v-if="formValues.allows_task_merging">
              <el-form-item :label="$t('Task Merging Max Distance')">
                <el-input
                  type="number"
                  v-model="formValues.task_merging_max_distance"
                  :placeholder="$t('Task Merging Max Distance')"
                >
                  <template #suffix>m</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-12" v-if="formValues.allows_task_merging">
              <el-form-item :label="$t('Task Merging Minutes')">
                <el-input
                  type="number"
                  v-model="formValues.task_merging_minutes"
                  :placeholder="$t('Task Merging Minutes')"
                >
                  <template #suffix>DK</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="submitForm(formRef)" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square" />
          </template>
          {{ $t("Create") }}
        </el-button>
      </div>
    </div>
  </div>
</template>
