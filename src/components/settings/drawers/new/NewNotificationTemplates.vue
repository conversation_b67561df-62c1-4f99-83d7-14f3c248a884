<script setup>
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useI18n } from "vue-i18n";
import { inject, ref, onMounted, reactive, watch, defineEmits } from "vue";
import { toaster } from "@/class/helpers";
import { useToast } from "vue-toastification";

const { t } = useI18n();
const api = inject("api");
const templateTypeSelectList = ref([]);
const notificationTypeSelectList = ref([]);
const loader = ref();
const newArray = ref([]);
const tagList = ref([]);
const emit = defineEmits(["close"]);
const toast = useToast();
const massegePlaceholder = ref();

const formData = reactive({
  template_type_slug: null,
  notification_type_slug: null,
  is_active: true,
  name: null,
  description: null,
  message: null
});

onMounted(() => {
  getNotificationTemplateTypeSelect();
  getNotificationTypeSelect();
});

const getNotificationTemplateTypeSelect = () => {
  return api.post("components/notification-template-type-select")
    .then((res) => {
      templateTypeSelectList.value = res.data;

    });

};

const getNotificationTypeSelect = () => {
  return api.post("components/notification-type-select")
    .then((res) => {
      notificationTypeSelectList.value = res.data;
    });

};

const createNotificationTemplate = () => {
  loader.value.show();
  let body = {
    template_type_slug: formData.template_type_slug,
    notification_type_slug: formData.notification_type_slug,
    is_active: formData.is_active,
    name: formData.name,
    description: formData.description,
    message: formData.message
  };

  api.post("customer/notification-templates", body)
    .then((res) => {
      emit("close", true);
      toast.success(t("Notification Template Created Successfully"));
    })
    .catch((err) => {
      toast.error(err.data.message);
    })
    .finally(() => loader.value.hide());

};

const addPlaceholder = (value) => {
  formData.message = (formData.message || '') + `@${value}`;
};

watch(
  () => formData.template_type_slug,
  () => {
    const selectedTemplate = templateTypeSelectList.value.find(template => template.slug === formData.template_type_slug);
    tagList.value = selectedTemplate;

    if (selectedTemplate) {
      newArray.value = Object.keys(selectedTemplate.placeholders).map(key => {
        return {
          label: key,
          value: key
        };

      });
      tagList.value = Object.keys(selectedTemplate.placeholders).map(key => {
        return {
          label: selectedTemplate.placeholders[key].description,
          value: key,
          example: selectedTemplate.placeholders[key].example,
          description: selectedTemplate.placeholders[key].description
        };
      });

    } else {
      newArray.value = [];
      tagList.value = [];
    }
  }
);
</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
            ref="formRef"
            label-position="top"
            class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="$t('Notification Type')">
                <el-select
                  v-model="formData.notification_type_slug"
                  :placeholder="$t('Notification Type')"
                  class="w-full-important"
                >
                  <el-option
                    v-for="item in notificationTypeSelectList"
                    :key="item.slug"
                    :label="item.name"
                    :value="item.slug"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Template Type')">
                <el-select
                  v-model="formData.template_type_slug"
                  :placeholder="$t('Template Type')"
                  class="w-full-important"
                >
                  <el-option
                    v-for="item in templateTypeSelectList"
                    :key="item.slug"
                    :label="item.name"
                    :value="item.slug"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Name')">
                <el-input
                  v-model="formData.name"
                  :placeholder="$t('Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Description')">
                <el-input
                  v-model="formData.description"
                  :placeholder="$t('Description')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12 ">
              <el-form-item v-if="formData.template_type_slug" :label="t('Available Placeholders')">
                <el-popover
                  v-if="formData.template_type_slug" v-for="item in tagList"
                  placement="top-start"
                  class="flex items-center w-full mr-1 mb-1"
                  :width="220"
                >
                  <div class="flex flex-col">
                    <div>
                      {{ item.label }}
                    </div>
                    <div>
                      {{ item.description }}
                    </div>
                    <div>
                      Ör: {{ item.example }}
                    </div>
                  </div>
                  <template #reference>
                    <el-button size="small" class="text-xxxs mb-1 mr-2" style="margin-left: 0px !important;" @click="addPlaceholder(item.value)">
                      @{{ item.value }}
                    </el-button>
                  </template>
                </el-popover>
              </el-form-item>
            </div>
            <div class="col-span-12" v-if="formData.template_type_slug">
              <el-form-item :label="$t('Message')">
                <el-mention
                  v-model="formData.message"
                  type="textarea"
                  :options="newArray"
                >
                </el-mention>
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Active") }}</div>
              <div>
                <el-switch v-model="formData.is_active" />
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="createNotificationTemplate" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square" />
          </template>
          {{ $t("Create") }}
        </el-button>
      </div>
    </div>
  </div>
</template>