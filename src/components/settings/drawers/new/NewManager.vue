<script setup>
import {reactive, inject, ref} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()

const api = inject("api");

const emit = defineEmits(["close"]);

const loader = ref(null);
const teams = ref([]);
const formRef = ref();

const userPhone = reactive({
  phone: null,
  country: "TR"
})

const formValues = reactive({
  managerNameSurname: "",
  email: "",
  password: "",
  password_confirmation: "",
  relatedTeam: "",
  tag: "",
});

const validatePass = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(t("Required")));
  } else {
    if (formValues.password_confirmation !== "") {
      if (!formRef.value) return;
      formRef.value.validateField("password_confirmation", () => null);
    }
    callback();
  }
};

const validatePass2 = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(t("Please input the password again")));
  } else if (value !== formValues.password) {
    callback(new Error(t("Passwords don't match!")));
  } else {
    callback();
  }
};

const rules = reactive({
  managerNameSurname: [
    {
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
  password: [{validator: validatePass, trigger: "blur"}],
  password_confirmation: [{validator: validatePass2, trigger: "blur"}],
  email: [
    {
      type: "email",
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
});

const createMenager = () => {
  loader.value.show();

  let body = {};
  if (formValues.managerNameSurname) body.name = formValues.managerNameSurname;
  if (formValues.email) body.email = formValues.email;
  if (userPhone.phone) body.phone = userPhone.phone;
  if (userPhone.country) body.phone_country = userPhone.country;
  if (formValues.password) body.password = formValues.password;
  if (formValues.password_confirmation) body.password_confirmation = formValues.password_confirmation;

  api
    .post("customer/managers", body)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};


const submitForm = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      createMenager();
    } else {
      return false;
    }
  });
};

</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              :model="formValues"
              :rules="rules"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item
                  :label="t('Menager Name')"
                  required
                  prop="managerNameSurname"
              >
                <el-input
                    v-model="formValues.managerNameSurname"
                    :placeholder="t('Menager Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <PhoneInput v-model="userPhone"/>
            </div>
            <div class="col-span-12">
              <el-form-item label="Email" required prop="email">
                <el-input
                    v-model="formValues.email"
                    type="email"
                    placeholder="Email"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Password')" required prop="password">
                <el-input
                    v-model="formValues.password"
                    type="password"
                    :placeholder="t('Password')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="t('Password Confirmation')"
                  required
                  prop="password_confirmation"
              >
                <el-input
                    v-model="formValues.password_confirmation"
                    type="password"
                    :placeholder="t('Password Confirmation')"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0px 0px 5px #00000029"
      >
        <el-button @click="submitForm(formRef)" type="primary">
          <FontAwesomeIcon icon="check-square"/>
          {{ t('Create') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
