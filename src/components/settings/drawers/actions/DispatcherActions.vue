<script>
import {defineAsyncComponent} from "vue";

export default {
  components: {
    DispatcherDetail: defineAsyncComponent(() => import("../detail/DispatcherDetail.vue")),
    DispatcherEdit: defineAsyncComponent(() => import("../edit/DispatcherEdit.vue")),
  }
}
</script>
<script setup>
import {ref, computed, inject, watch} from "vue";

const api = inject("api")
const emit = defineEmits(["update:modelValue", "refresh"])
const props = defineProps({
  selectedDispatcher: {type: Object},
  modelValue: {type: Boolean}
});
const update = ref(false)
const courier = ref({})

const showEdit = () => {
  update.value = true
}
const showDetail = () => {
  update.value = false
}
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const close = () => {
  emit("update:modelValue", false);
  emit("refresh")
}

watch(props, () => {
  update.value = false
}, {deep: true})

</script>
<template>
  <el-drawer
      v-model="visible"
      :class=" update ? 'customized-drawer' : 'customized-drawer customized-drawer--big' "
      :title=" update ? $t('Dispatcher Edit') : $t('Dispatcher Detail')"
      append-to-body
      destroy-on-close
  >
    <keep-alive>
      <component
          @showEdit="showEdit"
          @showDetail="showDetail"
          @close="close"
          :dispatcher="selectedDispatcher"
          :is="update ? 'DispatcherEdit' : 'DispatcherDetail' ">
      </component>
    </keep-alive>
  </el-drawer>
</template>
