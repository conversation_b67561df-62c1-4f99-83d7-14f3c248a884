<script>
import {defineAsyncComponent} from "vue";

export default {
  components: {
    ManagerDetail: defineAsyncComponent(() => import("../detail/ManagerDetail.vue")),
    ManagerEdit: defineAsyncComponent(() => import("../edit/ManagerEdit.vue")),
  }
}
</script>
<script setup>
import {ref, computed, inject, watch} from "vue";

const api = inject("api")
const emit = defineEmits(["update:modelValue", "refresh"])
const props = defineProps({
  selectedManager: {type: Object},
  modelValue: {type: Boolean}
});
const update = ref(false)
const courier = ref({})

const showEdit = () => {
  update.value = true
}
const showDetail = () => {
  update.value = false
}
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const close = () => {
  emit("update:modelValue", false);
  emit("refresh")
}

watch(props, () => {
  update.value = false
}, {deep: true})

</script>
<template>
  <el-drawer
      v-model="visible"
      :class=" update ? 'customized-drawer' : 'customized-drawer customized-drawer--big' "
      :title=" update ? $t('Manager Edit') : $t('Manager Detail')"
      append-to-body
      destroy-on-close
  >
    <keep-alive>
      <component
          @showEdit="showEdit"
          @showDetail="showDetail"
          @close="close"
          :manager="selectedManager"
          :is="update ? 'ManagerEdit' : 'ManagerDetail' ">
      </component>
    </keep-alive>
  </el-drawer>
</template>
