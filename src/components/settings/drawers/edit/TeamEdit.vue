<script setup>
import {reactive, inject, ref, onActivated} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const api = inject("api");

const toast = useToast()
const {t} = useI18n()

const emit = defineEmits(["close", "showDetail"]);
const props = defineProps({
  team: {type: [Number, Object]},
})

const loader = ref(null);
const loading = ref(false)
const tags = ref([])
const hubs = ref([])
const formRef = ref()
const poligonJsonVsible = ref(true)
const visible = ref(true)


let formValues = reactive({
  teamName: "",
  relatedDispatcher: "",
  tags: [],
  hubId: "",
  allows_queued_tasks: false,
  assigns_pool_tasks_auto: false,
  transfers_delayed_tasks: false,
  allows_task_merging: false,
  allow_support_couriers:false,
  pool_enabled: false,
  vehicleType:[],
  task_merging_max_distance:0,
  task_merging_minutes:0,
});

const location = reactive({
  searchAddress: '',
  lat: null,
  lng: null,
  area_json: null
})


const rules = reactive({
  teamName: [
    {
      required: true,
      message: t("Required"),
      trigger: 'blur',
    },
  ],
})


const updateTeam = () => {
  loader.value.show();

  let body = {};
  body.allows_task_merging = formValues.allows_task_merging;
  body.transfers_delayed_tasks = formValues.transfers_delayed_tasks;
  body.allows_queued_tasks = formValues.allows_queued_tasks;
  body.assigns_pool_tasks_auto = formValues.assigns_pool_tasks_auto;
  body.pool_enabled = formValues.pool_enabled;
  body.allows_support_couriers = formValues.allow_support_couriers;

  if (formValues.teamName) body.name = formValues.teamName;
  if (formValues.hubId) body.hub_id = formValues.hubId;
  if (formValues.tags) body.tags = formValues.tags;
  if (location.area_json) body.area_json = location.area_json;
  if (location.lat) body.lat = location.lat;
  if (location.lng) body.lng = location.lng;
  if (formValues.vehicleType) body.vehicle_types = formValues.vehicleType;
  if (formValues.task_merging_max_distance && formValues.allows_task_merging) body.task_merging_max_distance = formValues.task_merging_max_distance;
  if (formValues.task_merging_minutes && formValues.allows_task_merging) body.task_merging_minutes = formValues.task_merging_minutes;

  api
    .put(`customer/teams/${props.team.id}`, body)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};


const getHubs = () => {
  return api("customer/hubs")
      .then((res) => (hubs.value = res.data))
}

const getTags = () => {
  return api("customer/tags")
      .then((res) => (tags.value = res.data))
}


const getTeam = () => {
  loader.value.show();
  api(`customer/teams/${props.team.id}`)
    .then((res) => {
      formValues.teamName = res.name;
      formValues.hubId = res.hub.id;
      formValues.tags = [...res.tags];
      formValues.vehicleType = res.vehicle_types.map(v => v.slug);
      location.area_json = res.area_json;
      location.lng = res.lng;
      location.lat = res.lat;
      formValues.allows_queued_tasks = res.allows_queued_tasks;
      formValues.assigns_pool_tasks_auto = res.assigns_pool_tasks_auto;
      formValues.transfers_delayed_tasks = res.transfers_delayed_tasks;
      formValues.allows_task_merging = res.allows_task_merging;
      formValues.task_merging_max_distance = res.task_merging_max_distance;
      formValues.task_merging_minutes = res.task_merging_minutes;
      formValues.allow_support_couriers = res.allows_support_couriers;
      formValues.pool_enabled = res.pool_enabled;
      getHubs();
      getTags();
    })
    .finally(() => {
      loading.value = true;
      loader.value.hide();
    });
}

//
// const getData = () => {
//   loader.value.show();
//   Promise.all([])
//       .then(() => {
//
//         formValues.hubId = props.team.hub.id
//         formValues.teamName = props.team.name
//         formValues.tags = [...props.team.tags]
//         formValues.vehicleType = [...props.team.vehicle_type]
//         location.area_json = props.team.area_json
//         location.lng = props.team.lng
//         location.lat = props.team.lat
//         formValues.allows_queued_tasks = props.team.allows_queued_tasks
//         formValues.assigns_pool_tasks_auto = props.team.assigns_pool_tasks_auto
//         formValues.transfers_delayed_tasks = props.team.transfers_delayed_tasks
//         formValues.allows_task_merging = props.team.allows_task_merging
//
//       })
//       .finally(() => {
//         loading.value = true
//         loader.value.hide()
//       });
// }


const setAreaJson = (params) => {
  location.area_json = params
}

onActivated(() => {
  getTeam()

})

const onCancel = () => {
  emit("showDetail")
}

const submitForm = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      updateTeam()
    } else {
      return false
    }
  })
}


</script>

<template>
  <div class="w-full h-full flex">
    <LoadingBlock ref="loader"/>
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form ref="formRef" :model="formValues" :rules="rules" label-position="top"
                   class="grid grid-cols-12 gap-4">
            <div class="col-span-12">
              <el-form-item :label="t('Team Name')" prop="teamName">
                <el-input
                    v-model="formValues.teamName"
                    :placeholder="t('Team Name')"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Hub')">
                <el-select
                    v-model="formValues.hubId"
                    :placeholder="t('Hub')"
                    class="w-full-important"
                    filterable
                >
                  <el-option
                      v-for="item in hubs"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Tags')">
                <SelectBox
                    v-model="formValues.tags"
                    url="customer/tags"
                    value="name"
                    key="name"
                    label="name"
                    :multiple="true"
                    :filterable="true"
                    placeholder="Tags"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Vehicle Type')">
                <SelectBox
                  v-model="formValues.vehicleType"
                  url="components/vehicle-type-select"
                  method="post"
                  placeholder="Vehicle Type"
                  key="slug"
                  label="name"
                  value="slug"
                  :multiple="true"
                  :filterable="true"
                />
              </el-form-item>
            </div>
            <PinMap
                v-if="loading"
                v-model="location"
                @setAreaJson="setAreaJson"
                :poligonVisible="poligonJsonVsible"
            />
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Allows Queued Tasks') }}</div>
              <div>
                <el-switch v-model="formValues.allows_queued_tasks"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Pool") }}</div>
              <div>
                <el-switch v-model="formValues.pool_enabled" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Assigns Pool Tasks Auto') }}</div>
              <div>
                <el-switch v-model="formValues.assigns_pool_tasks_auto"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Transfers Delayed Tasks') }}</div>
              <div>
                <el-switch v-model="formValues.transfers_delayed_tasks"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Allows Task Merging') }}</div>
              <div>
                <el-switch v-model="formValues.allows_task_merging"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Destek Kuryesine İzin Ver") }}</div>
              <div>
                <el-switch v-model="formValues.allow_support_couriers" />
              </div>
            </div>
            <div class="col-span-12" v-if="formValues.allows_task_merging">
              <el-form-item :label="$t('Task Merging Max Distance')">
                <el-input
                  type="number"
                  v-model="formValues.task_merging_max_distance"
                  :placeholder="$t('Task Merging Max Distance')"
                >
                  <template #suffix>m</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-12" v-if="formValues.allows_task_merging">
              <el-form-item :label="$t('Task Merging Minutes')">
                <el-input
                  type="number"
                  v-model="formValues.task_merging_minutes"
                  :placeholder="$t('Task Merging Minutes')"
                >
                  <template #suffix>DK</template>
                </el-input>
              </el-form-item>
            </div>

          </el-form>
        </div>

      </div>
      <div
          class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <div>
          <el-button @click="onCancel">{{ t('Cancel') }}</el-button>
        </div>
        <div>
          <el-button @click="submitForm(formRef)" type="primary">
            <template #icon>
              <FontAwesomeIcon icon="check-square"/>
            </template>
            {{ t('Save') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
