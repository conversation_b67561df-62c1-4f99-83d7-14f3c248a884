<script setup>
import {inject, onMounted,  ref} from "vue";
import formatter from "@/class/formatter";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";



const planList = ref([])
const api = inject("api")

const emits = defineEmits(["close"])
const props = defineProps({
  planId: {type: Number}
})


const loader = ref()
const selectedWorkingHourPlan = ref()

const createWorkingHourTemplate = () => {
  loader.value.show()

  api.patch(`customer/working-hour-templates/${props.planId}`, {
    name: selectedWorkingHourPlan.value.name,
    is_default: selectedWorkingHourPlan.value.is_default,
    working_hours: addWorkingHoursToCreatedTemplate(),
    type: selectedWorkingHourPlan.value.type
  })
      .then(() => {
        emits("close", true)
      })
      .finally(() => {
        loader.value.hide()
      })
}

const addWorkingHoursToCreatedTemplate = () => {
  let arr = []
  planList.value.forEach((x) => {
    let body = {
      day_name: x.day_name,
      starts_at: x.starts_at,
      ends_at: x.ends_at,
      is_open: x.is_closed,
      break_time: selectedWorkingHourPlan.value.type === 'courier' ? x.break_time : 0,
      auto_working_on: selectedWorkingHourPlan.value.type === 'courier' ? x.auto_working_on : false,
      auto_working_off: selectedWorkingHourPlan.value.type === 'courier' ? x.auto_working_off : false,
      check_polygon: selectedWorkingHourPlan.value.type === 'courier' ? x.check_polygon : false
    }
    arr.push(body)
  })

  return arr

}

const getData = () => {
  loader.value.show()
  api(`customer/working-hour-templates/${props.planId}`)
      .then((res) => {
        selectedWorkingHourPlan.value = res

        // defaultWorkingHourPlan.value = res

        let arr = []
        let _list = res.working_hours.map(x => {
          return {
            ...x,
            starts_at: x.starts_at ? x.starts_at.split(":").slice(0, 2).join(":") : null,
            ends_at: x.ends_at ? x.ends_at.split(":").slice(0, 2).join(":") : null,
            is_closed: x.is_open,
            break_time: res.type === 'courier' ? x.break_time : null,
            auto_working_on: res.type === 'courier' ? x.auto_working_on : null,
            auto_working_off: res.type === 'courier' ? x.auto_working_off : null,
            check_polygon: res.type === 'courier' ? x.check_polygon : null,
          }
        })
        arr.push(_list.find(x => x.day_name === "monday"))
        arr.push(_list.find(x => x.day_name === "tuesday"))
        arr.push(_list.find(x => x.day_name === "wednesday"))
        arr.push(_list.find(x => x.day_name === "thursday"))
        arr.push(_list.find(x => x.day_name === "friday"))
        arr.push(_list.find(x => x.day_name === "saturday"))
        arr.push(_list.find(x => x.day_name === "sunday"))

        planList.value = [...arr]
      })
      .finally(() => {
        loader.value.hide()
      })

}
onMounted(() => {
  if (props.planId) {
    getData()
  }
})

</script>

<template>
  <div
      class="w-full text-slate-700 text-sm working-hour-plan"
  >
    <LoadingBlock ref="loader"/>
    <div v-if="selectedWorkingHourPlan" class="grid gap-4 grid-cols-5 border-b border-slate-300 p-7">
      <div class="col-span-1 flex items-center font-semibold">
        {{ $t("Plan Name") }}
      </div>

      <el-input
          v-model="selectedWorkingHourPlan.name"
          class="col-span-3"
      />
      <div class="col-span-1 text-center">
        <el-checkbox
            v-model="selectedWorkingHourPlan.is_default"
        >
          {{ $t('Default') }}
        </el-checkbox>
      </div>
    </div>
    <div v-for="item in planList"
         class="grid grid-cols-5 gap-4 border-b border-slate-300 px-4 py-2.5">
      <div class="col-span-1 flex items-center justify-end font-medium">
        {{ $t(formatter.capitalizeFirstLetter(item.day_name)) }}
      </div>
      <div class="col-span-3 grid-cols-3 flex items-center justify-evenly">
        <div class="w-32">
          <el-time-select
            class="w-32"
            v-model="item.starts_at"
            :disabled="!item.is_closed"
            :clearable="false"
            :editable="false"
            start="00:00"
            end="23:30"
          >
          </el-time-select>
        </div>
        <div class="grow flex items-center justify-center">
          <FontAwesomeIcon icon="right-left"/>
        </div>
        <div class="w-32">
          <el-time-select
            class="w-32"
            v-model="item.ends_at"
            :disabled="!item.is_closed"
            :clearable="false"
            :editable="false"
            start="00:00"
            end="23:30"
          >
          </el-time-select>
        </div>
      </div>
      <div v-if="selectedWorkingHourPlan.type === 'hub'" class="col-span-1 flex items-center justify-center mr-3">
        <el-checkbox
          v-model="item.is_closed"
          size="small"
          :label="$t('Open')"/>
      </div>
      <div v-if="selectedWorkingHourPlan.type === 'courier'" class="col-span-1 flex items-center justify-center mr-3">
        <el-checkbox
          v-model="item.is_closed"
          size="small"
          :label="$t('Working')"/>
      </div>
      <div v-if="selectedWorkingHourPlan.type === 'courier'"
           class="col-span-1 flex items-center justify-end font-medium ">
        {{ $t('Break time') }}
      </div>
      <div v-if="selectedWorkingHourPlan.type === 'courier'" class="col-span-4 flex items-center justify-between">
        <div class="w-32">
          <el-input class="w-32" :disabled="!item.is_closed" v-model="item.break_time" type="number" size="small">
            <template #suffix>DK</template>
          </el-input>
        </div>
        <div>
<!--          <el-checkbox :disabled="!item.is_closed" v-model="item.auto_working_on" size="small" :label="$t('Automatic Online')"/>-->
        </div>
        <div class="mr-3">
<!--          <el-checkbox :disabled="!item.is_closed" v-model="item.auto_working_off" size="small" :label="$t('Automatic Offline')"/>-->
        </div>
        <div class="mr-1">
          <el-checkbox :disabled="!item.is_closed" v-model="item.check_polygon" size="small" :label="$t('Check Polygon')"/>
        </div>
      </div>



    </div>
    <div class="bg-slate-50 text-right p-4">
      <el-button @click="createWorkingHourTemplate" type="primary">
        {{ $t('Save') }}
      </el-button>
    </div>
  </div>
</template>
