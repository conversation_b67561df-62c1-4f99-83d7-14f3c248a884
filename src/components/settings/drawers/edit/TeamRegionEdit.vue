<script setup>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { useI18n } from "vue-i18n";
import { ref, defineProps, onMounted, inject, reactive, watch, computed } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useToast } from "vue-toastification";
import TeamRegionMapPoligon from "@/components/deliveries/TeamRegionMapPoligon.vue";

const emit = defineEmits(["close", "refresh"]);
const props = defineProps({
  region: {
    type: Object,
    required: true
  },
  teamId: {
    type: [Number, String],
    required: true
  }
});

const { t } = useI18n();
const api = inject("api");
const loader = ref(null);
const toast = useToast();
const selectedRegion = ref(null);
const regions = ref([]);

const form = reactive({
  region_id: null,
  is_active: true,
  color: "#FFCCAA"
});

// Function to convert RGBA to HEX
const rgbaToHex = (rgba) => {
  // If it's already a hex color, return it
  if (rgba.startsWith('#')) {
    return rgba;
  }

  // Parse RGBA values
  const rgbaMatch = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)/);
  if (!rgbaMatch) return rgba;

  const r = parseInt(rgbaMatch[1]);
  const g = parseInt(rgbaMatch[2]);
  const b = parseInt(rgbaMatch[3]);

  // Convert RGB to HEX
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()}`;
};

// Computed property for hex color
const hexColor = computed(() => {
  return rgbaToHex(form.color);
});

// Initialize form with data from props
const initializeForm = () => {
  if (props.region && props.region.region_details) {
    form.region_id = props.region.id;
    form.is_active = props.region.region_details.is_active;
    form.color = props.region.region_details.color;

    // Fetch the region details to get the polygon data
    fetchRegionDetails(props.region.id);
  }
};

const fetchRegions = () => {
  if (loader.value) loader.value.show();
  api("customer/regions")
    .then((response) => {
      regions.value = response.data || [];
    })
    .catch((error) => {
      toast.error(error.data?.message || t('An error occurred while fetching regions.'));
    })
    .finally(() => {
      if (loader.value) loader.value.hide();
    });
};

const fetchRegionDetails = (regionId) => {
  if (!regionId) return;

  if (loader.value) loader.value.show();
  api(`customer/regions/${regionId}`)
    .then((response) => {
      selectedRegion.value = response.data;
    })
    .catch((error) => {
      toast.error(error.data?.message || t('An error occurred while fetching region details.'));
    })
    .finally(() => {
      if (loader.value) loader.value.hide();
    });
};

const updateRegion = () => {
  if (!props.teamId) {
    toast.error(t('Team ID not found.'));
    return;
  }

  if (!props.region || !props.region.id) {
    toast.error(t('Region ID not found.'));
    return;
  }

  if (loader.value) loader.value.show();

  // Create a new object with the form data but use the hex color
  const formData = {
    is_active: form.is_active,
    color: hexColor.value
  };

  api.patch(`customer/teams/${props.teamId}/regions/${props.region.id}`, formData)
    .then(() => {
      toast.success(t('Region successfully updated.'));
      emit("close", true);
      emit("refresh");
    })
    .catch((error) => {
      toast.error(error.data?.message || t('An error occurred while updating region.'));
    })
    .finally(() => {
      if (loader.value) loader.value.hide();
    });
};

// Watch for changes to props.region
watch(() => props.region, () => {
  initializeForm();
}, { immediate: true, deep: true });

// Watch for changes to form.region_id
// watch(() => form.region_id, (newRegionId) => {
//   if (newRegionId) {
//     fetchRegionDetails(newRegionId);
//   } else {
//     selectedRegion.value = null;
//   }
// });

onMounted(() => {
  console.log("props.region:", props.region);
  fetchRegions();
  initializeForm();
});
</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
            label-position="top"
            class="grid grid-cols-12 gap-4"
          >
<!--            <div class="col-span-12">-->
<!--              <el-form-item :label="t('Region')">-->
<!--                <el-select-->
<!--                  v-model="form.region_id"-->
<!--                  :placeholder="t('Select Region')"-->
<!--                  class="w-full"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="region in regions"-->
<!--                    :key="region.id"-->
<!--                    :label="region.name"-->
<!--                    :value="region.id"-->
<!--                  />-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </div>-->

            <div class="col-span-12">
              <el-form-item :label="t('Color')">
                <el-color-picker
                  v-model="form.color"
                  show-alpha
                  class="w-full"
                />
              </el-form-item>
            </div>

            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Active') }}</div>
              <div>
                <el-switch v-model="form.is_active"/>
              </div>
            </div>

            <!-- Map container - only show when region details are available -->
            <div v-if="selectedRegion" class="col-span-12 mt-4">
              <div class="text-sm text-slate-700 font-medium mb-2">{{ $t('Region Map') }}</div>
              <TeamRegionMapPoligon :region="selectedRegion" :color="form.color" />
            </div>
          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="$emit('close')" class="mr-2">
          {{ t("Cancel") }}
        </el-button>
        <el-button @click="updateRegion" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="pen" />
          </template>
          {{ t("Update") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-color-picker {
  width: 100%;
}
</style>
