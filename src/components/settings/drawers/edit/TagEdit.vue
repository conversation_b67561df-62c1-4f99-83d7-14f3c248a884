<script setup>
import {reactive, ref, inject, onMounted} from 'vue';
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useToast} from "vue-toastification";

const toast = useToast()
const emit = defineEmits(["close"]);
const props = defineProps({
  tag: {type: Object}
})
const api = inject("api");
const loader = ref()
const formValues = reactive({
  name: null,
  is_active: false,
})


const onSubmit = () => {
  loader.value.show()
  api.patch(`customer/tags/${props.tag.id}`, formValues)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.message))
    .finally(() => loader.value.hide())
}


const getData = () => {
  formValues.name = props.tag.name
  formValues.is_active = props.tag.is_active
}

onMounted(() => {
  getData()
})


</script>

<template>

  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="$t('Tag')">
                <el-input
                    v-model="formValues.name"
                    :placeholder="$t('Tag')"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Active') }}</div>
              <div>
                <el-switch v-model="formValues.is_active"/>
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="onSubmit" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>

          {{ $t('Save') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
