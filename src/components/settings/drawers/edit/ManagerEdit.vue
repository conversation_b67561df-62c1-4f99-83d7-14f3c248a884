<script setup>
import {reactive, inject, ref, onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()

const api = inject("api");

const props = defineProps({
  manager: {type: Object}
})
const emit = defineEmits(["showDetail", "close", "showEdit"]);

const loader = ref(null);
const teams = ref([]);
const formRef = ref();

const userPhone = reactive({
  phone: null,
  country: "TR"
})

const formValues = reactive({
  managerNameSurname: "",
  email: "",
  password: "",
  password_confirmation: "",
  relatedTeam: "",
  tag: "",
});



const validatePass2 = (rule, value, callback) => {
if (value !== formValues.password) {
    callback(new Error(t("Passwords don't match!")));
  } else {
    callback();
  }
};

const rules = reactive({
  managerNameSurname: [
    {
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
  phone: [
    {
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
  password: [{ trigger: "blur"}],
  password_confirmation: [{validator: validatePass2, trigger: "blur"}],
  email: [
    {
      type: "email",
      required: true,
      message: t("Required"),
      trigger: "blur",
    },
  ],
});

const updateManager = () => {
  loader.value.show();

  let body = {};
  if (userPhone.phone) body.phone = userPhone.phone;
  if (userPhone.country) body.phone_country = userPhone.country;
  if (formValues.managerNameSurname) body.name = formValues.managerNameSurname;
  if (formValues.email) body.email = formValues.email;
  if (formValues.password) body.password = formValues.password;
  if (formValues.password_confirmation) body.password_confirmation = formValues.password_confirmation;

  if (!formValues.password) {
    delete body.password;
  }

  if (!formValues.password_confirmation) {
    delete body.password_confirmation;
  }

  api
    .put(`customer/managers/${props.manager.id}`, body)
    .then(() => emit("close", true))
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};


const submitForm = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      updateManager();
    } else {
      return false;
    }
  });
};

const onCancel = () => {
  emit("showDetail")
}

onMounted(() => {
  formValues.managerNameSurname = props.manager.name
  formValues.email = props.manager.email
  userPhone.country = props.manager.phone_country
  userPhone.phone = props.manager.phone
})

</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              :model="formValues"
              :rules="rules"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item
                  :label="$t('Menager Name')"
                  required
                  prop="managerNameSurname"
              >
                <el-input
                    v-model="formValues.managerNameSurname"
                    :placeholder="$t('Menager Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <PhoneInput v-model="userPhone"/>
            </div>
            <div class="col-span-12">
              <el-form-item label="Email" required prop="email">
                <el-input
                    v-model="formValues.email"
                    type="email"
                    placeholder="Email"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Password')" >
                <el-input
                    v-model="formValues.password"
                    type="password"
                    :placeholder="$t('Password')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item
                  :label="$t('Password Confirmation')"
              >
                <el-input
                    v-model="formValues.password_confirmation"
                    type="password"
                    :placeholder="$t('Password Confirmation')"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <div>
          <el-button @click="onCancel">{{ $t('Cancel') }}</el-button>
        </div>
        <div>
          <el-button @click="submitForm(formRef)" type="primary">
            <FontAwesomeIcon icon="check-square"/>
            {{ t('Save') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
