<script setup>
import {ref,inject,onMounted,reactive,defineEmits} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const props = defineProps({
  payload: {type: Object}
})
const emit = defineEmits(["close"])

const api = inject('api')
const hubs = ref()
const loader = ref()
const {t} = useI18n()
const toast = useToast()


const formValues = reactive({
  hub_id: [],
  title: null,
})

const setFormValues = () => {

  formValues.title = props.payload.title
  formValues.hub_id = props.payload.hubs.map(h=>h.id)
}


onMounted(()=>{
  getHubs()
  setFormValues()
})
const getHubs = () => {
  loader.value.show()
  api.get('customer/hubs')
    .then((r) => hubs.value = r.data)
    .finally(()=>loader.value.hide())
}

const createToken = () => {
  loader.value.show()

  let body = {
    title: formValues.title,
    hub_ids: formValues.hub_id
  }
  api.patch('customer/api-tokens/'+props.payload.id, body)
    .then(() => {
      loader.value.hide()
      toast.success(t('Token edited'))
      emit('close', true)
    })
    .catch((err) => {

      toast.error(err.message)
    }).finally(()=>loader.value.hide())
}
</script>

<template>

  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">

          <el-form class="grid grid-cols-12 gap-4"  label-position="top">
            <div class="col-span-12">
              <el-form-item :label="t('Token Name')" prop="hubName">
                <el-input
                  v-model="formValues.title"
                  :placeholder="t('Token Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Hubs')">
                <el-select
                  class="w-full"
                  filterable
                  v-model="formValues.hub_id"
                  multiple
                  :placeholder="t('Hubs')"
                >

                  <el-option
                    v-for="hub in hubs"
                    :key="hub.id"
                    :label="hub.name"
                    :value="hub.id"
                  >
                  </el-option>

                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="createToken" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>
          {{ $t('Edit') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
