<script setup>
import {defineProps, reactive, onMounted, inject, ref, defineEmits} from 'vue'
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import MoneyInput from "@/components/ui/MoneyInput.vue";
import {useToast} from "vue-toastification";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";


const emit = defineEmits(["close"])
const props = defineProps({
  order: {type: Object},
})
const phone = reactive({
  country: "TR",
  phone: null,
});

const {t} = useI18n()
const api = inject('api')
const loader = ref()
const toast = useToast()

const paymentTypes = ref()
const amount = ref(0)
const discountedAmount = ref(props.order.discounted_amount ? props.order.discounted_amount : 0)
const currency = ref("TRY")


const location = reactive({
  searchAddress: '',
  lat: null,
  lng: null,
  area_json: null,

})

const form = reactive({
  notes: null,
  recipient_notes: null,
  tags: [],
  destination_address: null,
  destination_name: null,
  payment_type: null
})


const setFields = () => {
  form.notes = props.order.notes
  form.recipient_notes = props.order.recipient_notes
  form.tags = props.order.tags
  form.destination_address = props.order.destination_address
  location.lat = props.order.destination_lat
  location.lng = props.order.destination_lng
  form.destination_name = props.order.destination_name
  location.searchAddress = props.order.destination_address
  form.payment_type = props.order.payment_type
  amount.value = props.order.amount
  discountedAmount.value = props.order.discounted_amount
  phone.phone = props.order.destination_phone
  phone.country = props.order.destination_phone_country

}
const getPaymentTypes = () => {
  loader.value.show()
  return api.post("components/payment-type-select")
      .then((r) => paymentTypes.value = r)
      .finally(() => loader.value.hide())
}


onMounted(() => {
  setFields()
  getPaymentTypes()
})


const updateTask = () => {
  loader.value.show()
  let body = {
    notes: form.notes,
    recipient_notes: form.recipient_notes,
    tags: props.order.tags,
    amount: props.order.amount,
    discounted_amount: discountedAmount.value,
    payment_type: form.payment_type,
    destination: {
      name: form.destination_name,
      lat: location.lat,
      lng: location.lng,
      address: location.address ? location.address : props.order.destination_address
    }
  }
  api.put('customer/tasks/' + props.order.id, body)
      .then((r) => {
        toast.success(t('Düzenleme işleminiz başarılı.'))
        emit('close', true)
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide())
}

</script>

<template>
  <div class="w-full h-full flex">
    <LoadingBlock ref="loader"/>
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form label-position="top"
                   class="grid grid-cols-12 gap-4">
            <div class="col-span-12" v-if="props.order.channel !== 'ui'">
              <div style="background-color: #A8B4F6 ; color: black"
                   class="px-2 py-0.5 rounded text-xxs border border-grey-300 flex justify-center items-center">
                <div>
                  <FontAwesomeIcon style="color: #1450FF;" size="lg" icon="fa-circle-info" class="mr-2"/>
                </div>
                <div>
                  Bu görev platform tarafından oluşturulduğu için yalnızca adres bilgisi düzenlenebilir.
                </div>
              </div>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Customer')">
                <el-input
                    :disabled="props.order.channel !== 'ui'"
                    v-model="form.destination_name"
                    :placeholder="t('Customer')"
                >
                </el-input>
              </el-form-item>

            </div>
            <div class="col-span-12">
              <PhoneInput :disabled="true" v-model="phone"/>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Customer Note')">
                <el-input
                    :disabled="props.order.channel !== 'ui'"
                    v-model="form.recipient_notes"
                    :placeholder="t('Customer Note')"
                >
                </el-input>
              </el-form-item>
            </div>
            <PinMap
                v-model="location"
                :poligonVisible="false"
            />
            <div class="col-span-12">
              <el-form-item :label="$t('Payment Type')">
                <el-select
                    :disabled="props.order.channel !== 'ui'"
                    class="w-full-important"
                    :placeholder="$t('Payment Type')"
                    v-model="form.payment_type"
                    filterable
                >
                  <el-option
                      v-for="item in paymentTypes"
                      :key="item.slug"
                      :label="item.description"
                      :value="item.slug"
                  >

                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <MoneyInput
                  :disabled="props.order.channel !== 'ui' ? true : false"
                  v-model="discountedAmount"
                  :options="{currency}"
                  :label="t('Amount')"
                  :placeholder="t('Amount')"
                  :min="0"
              ></MoneyInput>
            </div>

            <div class="col-span-12">
              <el-form-item :label="t('Notes')" prop="teamName">
                <el-input
                    :disabled="props.order.channel !== 'ui'"
                    v-model="form.notes"
                    :placeholder="t('Notes')"
                ></el-input>
              </el-form-item>
            </div>


          </el-form>
        </div>

      </div>
      <div
          class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <div>

        </div>
        <div>
          <el-button @click="updateTask" type="primary">
            <template #icon>
              <FontAwesomeIcon icon="check-square"/>
            </template>
            {{ t('Edit') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

