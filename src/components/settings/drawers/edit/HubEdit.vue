<script setup>
import {inject, nextTick, onMounted, reactive, ref} from "vue";
import EmbedMap from "@/components/ui/EmbedMap.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import {emitter} from "@/plugins/mitt";

const toast = useToast()
const {t} = useI18n()
const api = inject("api");

const emit = defineEmits(["close", "showDetail"]);
const props = defineProps({
  hubs: {type: Object},
})
const preparationTimeList = [5, 10, 15, 20, 25]

const loader = ref(null);
const formRef = ref();
const tags = ref([])
const workingTemplates = ref([])

const formValues = reactive({
  hubName: "",
  searchAddress: "",
  building: "",
  floor: "",
  apartment: "",
  lat: "",
  lng: "",
  address: "",
  tags: [],
  integration_id: null,
  default_service_time: null,
  working_hour_template_id: null,
  max_distance: null,
  is_active: true,
  default_preparation_time: 10,
  is_applying_stateless_invoice_items:false

});

const rules = reactive({
  hubName: [
    {
      required: true,
      message: t('Required'),
      trigger: "blur",
    },
  ],
  integration_id: [
    {
      required: true,
      message: t('Required'),
      trigger: "blur",
    },
  ],
  searchAddress: [
    {
      required: true,
      message: t('Required'),
      trigger: "blur",
    },
  ],
});
const onCancel = () => {
  emit("showDetail")
}


const getTags = () => {
  return api("customer/tags")
      .then((response) => tags.value = response.data)

}

const getWorkingTemplates = () => {
  return api("customer/working-hour-templates?filter[type]=hub")
      .then((res) => {
        workingTemplates.value = res.data
      })
}
const getHub = () => {
  api(`customer/hubs/${props.hubs.id}`)
    .then((res) => {
      if (res && res.name) {
        formValues.hubName = res.name;
        formValues.building = res.address_building;
        formValues.floor = res.address_floor;
        formValues.apartment = res.address_apartment;
        formValues.lat = res.lat;
        formValues.lng = res.lng;
        formValues.address = res.address;
        formValues.tags = [...res.tags];
        formValues.default_service_time = res.default_service_time;
        formValues.integration_id = res.integration_id;
        formValues.working_hour_template_id = res.working_hour_template_id;
        formValues.max_distance = res.max_distance;
        formValues.is_active = res.is_active;
        formValues.default_preparation_time = res.default_preparation_time;
        formValues.is_applying_stateless_invoice_items = res.is_applying_stateless_invoice_items;
      } else {
        console.error('API response does not contain expected fields:', res);
      }
    })
    .catch((err) => {
      toast.error(err?.data?.message || 'Error fetching hub details');
      console.error('Error fetching hub:', err);
    });
};



const getData = () => {
  loader.value.show()
  Promise.all([getTags(), getWorkingTemplates()])
      .finally(() => {
        loader.value.hide()
      })
}

const getSearchAddress = async () => {
  const {Autocomplete} = await google.maps.importLibrary("places");

  const address = new Autocomplete(
      document.getElementById("address_search")
  );
  address.addListener("place_changed", () => {
    const place = address.getPlace();
    if (!place.geometry || !place.geometry.location) {
      // window.alert("No details available for input: '" + place.name + "'");
      return;
    }
    const location = place.geometry.location;

    resolveAddressFromCoordinates(location.lat(), location.lng()).then(
        (address) => {
          formValues.lat = location.lat();
          formValues.lng = location.lng();
          formValues.address = address;
        }
    );
  });
}
onMounted(() => {
  if (props.hubs && props.hubs.id) {
    getHub();
  } else {
    console.error('Hubs prop is missing or invalid:', props.hubs);
  }

  nextTick(() => {
    getData();
    getSearchAddress();
  });
});


function resolveAddressFromCoordinates(lat, lng) {
  lat = parseFloat(lat.toFixed(8));
  lng = parseFloat(lng.toFixed(8));

  return new Promise((resolve, reject) => {
    new google.maps.Geocoder().geocode(
        {location: {lat, lng}},
        (results, status) => {
          if (status === "OK") {
            if (results[0]) {
              resolve(results[0].formatted_address);
            }
          }
        }
    );
  });
}

const createHub = () => {
  loader.value.show();
  let body = {
    name: formValues.hubName,
    address: formValues.address,
    address_building: formValues.building,
    address_apartment: formValues.apartment,
    address_floor: formValues.floor,
    lat: formValues.lat,
    lng: formValues.lng,
    tags: formValues.tags,
    integration_id: formValues.integration_id,
    default_service_time: Number(formValues.default_service_time),
    working_hour_template_id: formValues.working_hour_template_id,
    max_distance: formValues.max_distance,
    is_active: formValues.is_active,
    default_preparation_time: formValues.default_preparation_time
  };
  api
    .put(`customer/hubs/${props.hubs.id}`, body)
    .then((r) => {
      emit("close", true);
      emitter.emit('hubUpdated', r)
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => loader.value.hide());
};

const submitForm = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      createHub();
    } else {
      return false;
    }
  });
};

</script>

<template v-if="formValues.hubName">
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              :model="formValues"
              :rules="rules"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="t('Hub Name')" prop="hubName">
                <el-input
                    v-model="formValues.hubName"
                    :placeholder="t('Hub Name')"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Hub Integration Id')" prop="integration_id">
                <el-input
                    v-model="formValues.integration_id"
                    :placeholder="t('Hub Integration Id')"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <WorkingHourPlanInput
                  :templates="workingTemplates"
                  v-model="formValues.working_hour_template_id"
              />
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Quest Accept Distance')">
                <el-input
                    v-model="formValues.max_distance"
                    :placeholder="t('Quest Accept Distance')"
                    type="number"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Preparation Time')">
                <el-select
                    v-model="formValues.default_preparation_time"
                    class="w-full"
                    filterable
                    :placeholder="t('Preparation Time')"
                >

                  <el-option
                      v-for="item in preparationTimeList"
                      :key="item"
                      :label="item"
                      :value="item"
                  >

                    <span style="float: left">{{ item }}</span>
                    <span
                        style="
                        float: right;
                        color: var(--el-text-color-secondary);
                        font-size: 13px;
                        "
                    >{{ t('Min') }}</span
                    >
                  </el-option>

                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Service Time')">
                <el-input
                    v-model="formValues.default_service_time"
                    :placeholder="t('Service Time')"
                    type="number"
                >
                  <template #suffix> {{ t('Min') }}</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Address')">
                <el-input
                    id="address_search"
                    v-model="formValues.searchAddress"
                    :placeholder="t('Address')"
                />
              </el-form-item>
              <EmbedMap
                  width="490"
                  height="140"
                  :lat="formValues.lat"
                  :lng="formValues.lng"
              />
            </div>
            <div class="col-span-12 grid grid-cols-12 gap-4">
              <div class="col-span-12 sm:col-span-4">
                <el-form-item :label="t('Building Name/No')">
                  <el-input
                      v-model="formValues.building"
                      :placeholder="t('Building Name/No')"
                  />
                </el-form-item>
              </div>
              <div class="col-span-6 sm:col-span-4">
                <el-form-item :label="t('Floor')">
                  <el-input v-model="formValues.floor" :placeholder="t('Floor')"/>
                </el-form-item>
              </div>
              <div class="col-span-6 sm:col-span-4">
                <el-form-item :label="t('Apartment No')">
                  <el-input
                      v-model="formValues.apartment"
                      :placeholder="t('Apartment No')"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Default Tag')">
                <SelectBox
                    v-model="formValues.tags"
                    url="customer/tags"
                    value="name"
                    key="name"
                    label="name"
                    :multiple="true"
                    :filterable="true"
                    placeholder="Tags"
                    :multipleLimit="1"
                />
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Is Applying Stateless Invoice Items') }}</div>
              <div>
                <el-switch v-model="formValues.is_applying_stateless_invoice_items"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Status') }}</div>
              <div>
                <el-switch v-model="formValues.is_active"/>
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2 z-20"
          style="box-shadow: 0 0 5px #00000029"
      >
        <div>
          <el-button @click="onCancel">{{ t('Cancel') }}</el-button>
        </div>
        <div>
          <el-button @click="submitForm(formRef)" type="primary">
            <template #icon>
              <FontAwesomeIcon class="mr-1" icon="check-square"/>
            </template>
            {{ t('Save') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
