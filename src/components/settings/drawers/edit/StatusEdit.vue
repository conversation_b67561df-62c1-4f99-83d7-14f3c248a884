<script setup>
import {reactive, ref, inject, onMounted, watch} from 'vue';
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()
const emit = defineEmits(["close"]);
const props = defineProps({
  status: {type: Object}
})
const api = inject("api");
const loader = ref()
const statusCategories = ref([])
const statusList = ref([])
const formValues = reactive({
  category_slug: null,
  name: null,
  description: null,
  is_active: false,
  is_sms_enabled: false,
  is_mail_enabled: false,
  is_push_enabled: false,
  status_order: null,
  slug: null,
  is_default: null,
  options: {
    max_distance: null,
    check_destination_distance: false,
    check_origin_distance: false,
    allow_prev_status: false,
    allow_next_status: true,
    needs_task_confirmation: false,
    needs_pod_photo: false,
    requires_pod_photo: false,
    requires_notes: false,
    allow_sibling_status: false,
    disabled_status_ids:[],
    check_items:false
  }
})



const setFields = () => {
  formValues.category_slug = props.status.category_slug
  formValues.name = props.status.name
  formValues.description = props.status.description
  formValues.is_active = props.status.is_active
  formValues.slug = props.status.slug
  formValues.is_sms_enabled = props.status.is_sms_enabled
  formValues.is_mail_enabled = props.status.is_mail_enabled
  formValues.is_push_enabled = props.status.is_push_enabled
  formValues.status_order = props.status.status_order
  formValues.is_default = props.status.is_default
  formValues.options.max_distance = props.status.meta.options.max_distance
  formValues.options.check_destination_distance = props.status.meta.options.check_destination_distance
  formValues.options.check_origin_distance = props.status.meta.options.check_origin_distance
  formValues.options.allow_prev_status = props.status.meta.options.allow_prev_status
  formValues.options.allow_next_status = props.status.meta.options.allow_next_status
  formValues.options.needs_pod_photo = props.status.meta.options.needs_pod_photo
  formValues.options.needs_task_confirmation = props.status.meta.options.needs_task_confirmation
  formValues.options.requires_pod_photo = props.status.meta.options.requires_pod_photo
  formValues.options.requires_notes = props.status.meta.options.requires_notes
  formValues.options.allow_sibling_status = props.status.meta.options.allow_sibling_status
  formValues.options.disabled_status_ids = props.status.meta.options.disabled_status_ids
  formValues.options.check_items = props.status.meta.options.check_items
}

onMounted(() => {
  getData()

})
const getData = () => {
  loader.value.show()
  api.post("components/task-status-category-select")
      .then((response) => {
        statusCategories.value = response.data
        setFields()
      })
      .finally(() => loader.value.hide())
}

const getStatus = () => {
  api('customer/statuses')
      .then((r) => {
        statusList.value = r.data.filter((s) => s.category_slug === formValues.category_slug && s.is_active === true)
      })
}

onMounted(() => {
  getData()
})
const onSubmit = () => {
  canCreate()
      .then(() => upadateStatus())
      .catch((err) => toast.error(t(err)));
};

const upadateStatus = () => {
  loader.value.show()
  api.patch(`customer/statuses/${props.status.id}`, formValues)
      .then(() => emit("close", true))
      .catch((err) => toast.error(err.message))
      .finally(() => loader.value.hide())

}

const canCreate = () => {
  return new Promise((resolve, reject) => {
    if (!formValues.slug) {
      return reject(t("Slug is required"))
    }

    if (formValues.options.check_destination_distance || formValues.options.check_origin_distance) {


      if (!formValues.options.max_distance)
        return reject(t("Maximum distance is required"));

      if (Number(formValues.options.max_distance) < 100) {
        return reject(t("Maximum distance area must be at least 100m"));
      }
    }


    return resolve();
  });
};

watch(() => formValues.options.check_origin_distance, (param) => {
  if (param) {
    formValues.options.check_destination_distance = false;
  }

});

watch(() => formValues.options.check_destination_distance, (param) => {
  if (param) {
    formValues.options.check_origin_distance = false
  }
});

watch(() => formValues.options.allow_sibling_status && formValues.category_slug, () => {
  formValues.options.disabled_status_ids = []
  getStatus()
})





</script>

<template>

  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
              ref="formRef"
              label-position="top"
              class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <el-form-item :label="$t('Status Category')">
                <el-select
                    disabled
                    v-model="formValues.category_slug"
                    :placeholder="$t('Status Category')"
                    class="w-full-important"
                >
                  <el-option
                      v-for="item in statusCategories"
                      :key="item.slug"
                      :label="item.name"
                      :value="item.slug"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Status Name')">
                <el-input
                    v-model="formValues.name"
                    :placeholder="$t('Status Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Slug')">
                <el-input
                    v-model="formValues.slug"
                    :placeholder="$t('Slug')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Status Description')">
                <el-input
                    v-model="formValues.description"
                    :placeholder="$t('Status Description')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Status Order')">
                <el-input
                    type="number"
                    v-model="formValues.status_order"
                    :placeholder="$t('Status Order')"
                />
              </el-form-item>
            </div>

            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Default') }}</div>
              <div>
                <el-switch v-model="formValues.is_default"/>
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Active') }}</div>
              <div>
                <el-switch v-model="formValues.is_active"/>
              </div>
            </div>
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">Mail</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.is_mail_enabled"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Push') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.is_push_enabled"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">Sms</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.is_sms_enabled"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Check Destination Distance') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.check_destination_distance"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Check Origin Distance') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.check_origin_distance"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div  v-if="formValues.options.check_destination_distance || formValues.options.check_origin_distance"  class="col-span-12">-->
<!--              <el-form-item :label="$t('Max Distance')">-->
<!--                <el-input-->
<!--                    type="number"-->
<!--                    v-model="formValues.options.max_distance"-->
<!--                    :placeholder="$t('Max Distance')"-->
<!--                />-->
<!--              </el-form-item>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Allow Prev Status') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.allow_prev_status"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Allow Sibling Status') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.allow_sibling_status"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div v-if="formValues.options.allow_sibling_status && formValues.category_slug" class="col-span-12">-->
<!--              <el-form-item :label="$t('Canceled States')">-->
<!--                <el-select-->
<!--                    v-model="formValues.options.disabled_status_ids"-->
<!--                    :placeholder="$t('Canceled States')"-->
<!--                    class="w-full-important"-->
<!--                    multiple-->
<!--                >-->
<!--                  <el-option-->
<!--                      v-for="item in statusList"-->
<!--                      :key="item.id"-->
<!--                      :label="item.name"-->
<!--                      :value="item.id"-->
<!--                  >-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Allow Next Status') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.allow_next_status"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Needs Task Confirmation') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.needs_task_confirmation"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t('Requires Notes') }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.requires_notes"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div v-if="formValues.category_slug === 'completed'" class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium">{{ $t("Check Product List") }}</div>-->
<!--              <div>-->
<!--                <el-switch v-model="formValues.options.check_items"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium opacity-5">{{ $t('Needs Pod Photo') }}</div>-->
<!--              <div>-->
<!--                <el-switch :disabled="true" v-model="formValues.options.needs_pod_photo"/>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-span-12 flex items-center justify-between">-->
<!--              <div class="text-sm text-slate-700 font-medium opacity-5">{{ $t('Requires Pod Photo') }}</div>-->
<!--              <div>-->
<!--                <el-switch :disabled="true" v-model="formValues.options.requires_pod_photo"/>-->
<!--              </div>-->
<!--            </div>-->
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0 0 5px #00000029"
      >
        <el-button @click="onSubmit" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>

          {{ $t('Save') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
