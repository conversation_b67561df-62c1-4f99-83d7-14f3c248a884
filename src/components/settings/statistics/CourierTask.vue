<script setup>
import { useI18n } from "vue-i18n";
import { inject, ref, onMounted, computed ,watch,reactive} from "vue";
import { KJUR } from "jsrsasign";
import { useStore } from "vuex";


const api = inject("api");

const couriers = ref();

const courier = ref();


const { dispatch, getters } = useStore();
const me = computed(() => getters["auth/me"]);


const METABASE_SITE_URL = import.meta.env.VITE_METABASE_SITE_URL;
const METABASE_SECRET_KEY = import.meta.env.VITE_METABASE_SECRET_KEY;
const METABASE_ENABLE = import.meta.env.VITE_METABASE_ENABLE;

const header = { alg: "HS256", typ: "JWT" };
var payload = {
  resource: { question: 1224 },
  params: {
    "courier_id": "4552"
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};
var secretKey = METABASE_SECRET_KEY;

var sHeader = JSON.stringify(header);
var sPayload = JSON.stringify(payload);

var token = KJUR.jws.JWS.sign("HS256", sHeader, sPayload, { utf8: secretKey });

var iframeUrl = METABASE_SITE_URL + "/embed/question/" + token + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";


const getCouriers = () => {
  api("customer/couriers").then((res) => {
    couriers.value = res.data;
  });
};



// you will need to install via 'npm install jsonwebtoken' or in your package.json


onMounted(() => {
  getCouriers();
});

const { t } = useI18n();
</script>

<template>
  <div class="w-full h-full">
    <div class="w-full h-12 flex justify-between items-center" style=" background: #F8FAFC; border-bottom: 1px solid; border-bottom-color: #EAEAEA">
      <div>
        <el-select
          size="small"
          style="width: 300px;"
          :placeholder="$t('Courier')"
          v-model="courier"
          filterable
        >
          <el-option
            v-for="item in couriers"
            :key="item.id"
            :label="`${item.id} - ${item.name}`"
            :value="item.id"
          >
          </el-option>

        </el-select>
<!--        <el-select-->

<!--          size="small"-->
<!--          style="width: 140px; padding-left:19px; "-->
<!--          :placeholder="$t('Date')"-->
<!--          v-model="datenew"-->
<!--          filterable-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in dateOptions"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item"-->
<!--          >-->
<!--          </el-option>-->
<!--        </el-select>-->
      </div>


      <div  style="padding-right: 14px; font: normal normal 600 16px/18px Inter; color: #3E4B5E;">
       Sürücü Performansı
      </div>
    </div>

    <div class="w-full h-full bg-white" style=" padding-bottom: 80px;padding-left:45px;padding-right: 45px; padding-top: 45px ">
      <iframe :src="iframeUrl" class=" rounded-lg border" style="width: 100%; height:  100%;"></iframe>
    </div>
  </div>

</template>