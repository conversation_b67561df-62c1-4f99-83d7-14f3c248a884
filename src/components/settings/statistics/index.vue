<script setup>
import { onMounted, ref } from "vue";
import statistic from "@/router/statistic";
import { groupBy } from "@/class/helpers";
import router from "@/router";

const isMenuPanelOpen = true;

const test = ref([]);

onMounted(() => {

  const statistic = router.getRoutes()

    .find(x => x.name === "Home").children
    .find(x => x.name === "Statistics").children;

  test.value = groupBy(statistic, "subMenuTitle");

});

const goToRoute = (param) => {
  router.push({ path: param.path });
};

const isHovered = false;
const secondaryColor = false;


</script>
<template>
  <div class="absolute inset-0 ">
    <div class="w-full h-full flex">
      <div class="flex flex-col">
        <div class="text-xl flex items-center"
             style="height: 48px; background: #F8FAFC; border-bottom: 1px solid; border-bottom-color: #EAEAEA;">
          <div style="padding-left: 14px; font: normal normal 600 16px/18px Inter; color: #3E4B5E;">
            {{  $t("Settings") }}
          </div>

        </div>
        <el-menu
          :unique-opened="true"
          class="el-menu-vertical-demo grow overflow-y-auto el-custom-menu"
          :collapse="!isMenuPanelOpen"
          :router="true"
          :default-active="$route.path"
          style="background: white; "
        >
          <template v-for="(routes,key) in test" :key="key">

            <el-sub-menu :index="routes[0].path">
              <template #title>
                <span>  {{ $t(key) }}</span>
              </template>
              <template v-for="(x,index) in routes" :key="index">
                <el-menu-item :index="x.path" @click="goToRoute(x)" class="bg-white">
                  {{  $t(x.name) }}
                </el-menu-item>
              </template>
            </el-sub-menu>
          </template>
        </el-menu>
      </div>
      <div class="grow h-full w-full">
        <router-view />
      </div>
    </div>
  </div>


</template>