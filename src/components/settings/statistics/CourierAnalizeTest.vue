<script setup>
import { useI18n } from "vue-i18n";
import { inject, ref, onMounted, computed, watch, reactive } from "vue";
import { KJUR } from "jsrsasign";
import { useStore } from "vuex";
import dayjs from "dayjs";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useRoute } from "vue-router";

const api = inject("api");

const { t } = useI18n();

const filters = reactive({
  courier_id: null,
  date: null
});

const route = useRoute();

const couriers = ref();

const courier = ref();
const loader = ref();



const dateOptions = ref([
  {
    name: "Son 7 Gün",
    value: { startDate: dayjs().subtract(6, "day").format("YYYY-MM-DD"), endDate: dayjs().format("YYYY-MM-DD") },
    id: 1
  },
  {
    name: "Son 15 Gün",
    value: { startDate: dayjs().subtract(13, "day").format("YYYY-MM-DD"), endDate: dayjs().format("YYYY-MM-DD") },
    id: 2
  },
  {
    name: "Bu Ay",
    value: { startDate: dayjs().startOf("month").format("YYYY-MM-DD"), endDate: dayjs().format("YYYY-MM-DD") },
    id: 4
  },
  {
    name: "Son Ay",
    value: { startDate: dayjs().subtract(1, "month").format("YYYY-MM-DD"), endDate: dayjs().format("YYYY-MM-DD") },
    id: 3
  }
]);


const { dispatch, getters } = useStore();
const me = computed(() => getters["auth/me"]);


const METABASE_SITE_URL = import.meta.env.VITE_METABASE_SITE_URL;
const METABASE_SECRET_KEY = import.meta.env.VITE_METABASE_SECRET_KEY;
const METABASE_ENABLE = import.meta.env.VITE_METABASE_ENABLE;


const iframe_url = ref(null);
const iframeUrl2 = ref(null);
const iframeUrl3 = ref(null);
const iframeUrl4 = ref(null);
const iframeUrl5 = ref(null);
const iframeUrl6 = ref(null);
const getIframeUrl = (params) => {


  const header = { alg: "HS256", typ: "JWT" };
  var payload = {
    resource: { question: 1225 },
    params: {
      "courier_id": params.courier_id,
      "date_range_start": params.date.value.startDate,
      "date_range_end": params.date.value.endDate
    },
    exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
  };

  var secretKey = METABASE_SECRET_KEY;

  var sHeader = JSON.stringify(header);
  var sPayload = JSON.stringify(payload);

  var token = KJUR.jws.JWS.sign("HS256", sHeader, sPayload, { utf8: secretKey });

  iframe_url.value = METABASE_SITE_URL + "/embed/question/" + token + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";

};


const getIframeUrl2 = (params) => {


  const header = { alg: "HS256", typ: "JWT" };
  var payload = {
    resource: { question: 1227 },
    params: {
      "courier_id": params.courier_id,
      "start_date": params.date.value.startDate,
      "end_date": params.date.value.endDate
    },
    exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
  };
  var payload2 = {
    resource: { question: 1229 },
    params: {
      "courier_id": params.courier_id,
      "start_date": params.date.value.startDate,
      "end_date": params.date.value.endDate
    },
    exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
  };

  var payload3 = {
    resource: { question: 1255 },
    params: {
      "kurye_id": params.courier_id,
      "start_date": params.date.value.startDate,
      "end_date": params.date.value.endDate
    },
    exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
  };


  var payload4 = {
    resource: { question: 1256 },
    params: {
      "kurye_id": params.courier_id,
      "start_date": params.date.value.startDate,
      "end_date": params.date.value.endDate
    },
    exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
  };


  var payload5 = {
    resource: { question: 1257 },
    params: {
      "kurye_id": params.courier_id,
      "start_date": params.date.value.startDate,
      "end_date": params.date.value.endDate
    },
    exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
  };


  var secretKey = METABASE_SECRET_KEY;

  var sHeader = JSON.stringify(header);
  var sPayload = JSON.stringify(payload);

  var token = KJUR.jws.JWS.sign("HS256", sHeader, sPayload, { utf8: secretKey });


  var sPayload2 = JSON.stringify(payload2);
  var token2 = KJUR.jws.JWS.sign("HS256", sHeader, sPayload2, { utf8: secretKey });
  var sPayload3 = JSON.stringify(payload3);
  var token3 = KJUR.jws.JWS.sign("HS256", sHeader, sPayload3, { utf8: secretKey });
  var sPayload4 = JSON.stringify(payload4);
  var token4 = KJUR.jws.JWS.sign("HS256", sHeader, sPayload4, { utf8: secretKey });
  var sPayload5 = JSON.stringify(payload5);
  var token5 = KJUR.jws.JWS.sign("HS256", sHeader, sPayload5, { utf8: secretKey });

  iframeUrl2.value = METABASE_SITE_URL + "/embed/question/" + token + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";
  iframeUrl3.value = METABASE_SITE_URL + "/embed/question/" + token2 + "#font=Inter&bordered=false&titled=false&hide_download_button=true";
  iframeUrl4.value = METABASE_SITE_URL + "/embed/question/" + token3 + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";
  iframeUrl5.value = METABASE_SITE_URL + "/embed/question/" + token4 + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";
  iframeUrl6.value = METABASE_SITE_URL + "/embed/question/" + token5 + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";


};


watch(filters, (next) => {

  if (filters.date && filters.courier_id) {
    getIframeUrl(filters);
    getIframeUrl2(filters);
  } else {
    iframe_url.value = null;
  }


}, { deep: true });

const setFielts = () => {
  filters.date = dateOptions.value[0];
};


const getCouriers = () => {
  api("customer/couriers").then((res) => {
    couriers.value = res.data;
  }).finally(() => {
    filters.courier_id = couriers.value[0].id;
  });

};

const getData = () => {
  loader.value.show();

  Promise.all([
    getCouriers(),
    setFielts()
  ])
    .finally(() => {
      loader.value.hide();
    });
};


onMounted(() => {
  getData();
});


</script>

<template>

  <div class="w-full h-full">
    <LoadingBlock ref="loader" class="z-50" />
    <div class="sticky-header w-full h-12 flex justify-between items-center"
         style=" background: #F8FAFC; border-bottom: 1px solid; border-bottom-color: #EAEAEA">

      <div>
        <el-select
          size="small"
          style="width: 300px;"
          :placeholder="$t('Courier')"
          v-model="filters.courier_id"
          filterable
        >
          <el-option
            v-for="item in couriers"
            :key="item.id"
            :label="`${item.id} - ${item.name}`"
            :value="item.id"
          >
          </el-option>

        </el-select>
        <el-select

          size="small"
          style="width: 140px; padding-left:19px; "
          :placeholder="$t('Date')"
          v-model="filters.date"
          filterable
        >
          <el-option
            v-for="item in dateOptions"
            :key="item.id"
            :label="item.name"
            :value="item"
          >
          </el-option>
        </el-select>
      </div>


      <div style="padding-right: 14px; font: normal normal 600 16px/18px Inter; color: #3E4B5E;">
        {{ route.name }}
      </div>
    </div>
    <div class="w-full h-full responsive-div overflow-hidden">
      <div v-if="iframeUrl4"
           style="color: #334155;font: normal normal 600 14px/18px 'Inter';  font-family: 'Inter', sans-serif; padding-left: 40px; padding-right: 40px; padding-top: 35px; "
           class=" flex justify-between items-center  bg-white"
      >
        <div>

          <div
            style="color: #334155;font: normal normal 600 14px/18px 'Inter'; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
            Total Task
          </div>
          <iframe :src="iframeUrl4"
                  class="full-size-iframe rounded-lg border"
                  style="width: 247px; height: 170px;"
          >


          </iframe>
        </div>
        <div>

          <div
            style="color: #334155;font: normal normal 600 14px/18px 'Inter'; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
            Distribution By Day
          </div>
          <iframe :src="iframeUrl2"
                  class="full-size-iframe rounded-lg border"
                  style="width: 247px; height: 170px;"
          >


          </iframe>
        </div>
        <div class="flex flex-col">
          <div>
            <div
              style="color: #334155;font: normal normal 600 14px/18px 'Inter'; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
              Task Duration
            </div>
            <iframe :src="iframeUrl6"
                    class="full-size-iframe rounded-lg border"
                    style="width: 247px; height: 170px;"
            >

            </iframe>
          </div>

        </div>
        <div class=" flex flex-col">
          <div>
            <div
              style="color: #334155;font: normal normal 600 14px/18px 'Inter'; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
              Delayed
            </div>
            <iframe :src="iframeUrl5"
                    class="full-size-iframe rounded-lg border"
                    style="width: 247px; height: 170px;"
            >

            </iframe>
          </div>

        </div>


      </div>
      <!--      <div class="border border-grey-400 w-full " style="padding-left: 55px;padding-right: 55px;">-->

      <!--      </div>-->
      <div
        v-if="iframe_url"
        class="responsive-div w-full flex flex-col  bg-white"
        style="padding-left: 40px; padding-right: 40px; padding-top: 40px; padding-bottom: 40px;"
      >
        <div
          style="color: #334155;font: normal normal 600 14px/18px 'Inter'; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
          Distribution By Day
        </div>
        <iframe :src="iframe_url" class="full-size-iframe rounded-lg border"
                style="width: 100%; height: 400px;"></iframe>
      </div>
      <div
        v-if="iframeUrl3"
        class="responsive-div w-full flex flex-col  bg-white"
        style="padding-left: 40px; padding-right: 40px; padding-bottom: 60px;"
      >
        <div
          style="color: #334155;font: normal normal 600 14px/18px 'Inter'; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
          Operation Km Date Range
        </div>
        <iframe :src="iframeUrl3" class="full-size-iframe rounded-lg border"
                style="width: 100%; height: 400px;"></iframe>
      </div>
    </div>


  </div>

</template>
<style scoped>
.responsive-div {
  flex-grow: 1;
  flex-direction: column;
  overflow: auto; /* Eklenen kod */
  max-height: 100vh;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
}
</style>