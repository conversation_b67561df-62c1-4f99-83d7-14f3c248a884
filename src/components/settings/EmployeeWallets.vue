<!--<script>-->
<!--import WalletDetailButton from "@/renderers/DetailButton.vue";-->

<!--export default {-->
<!--  components: {-->
<!--    WalletDetailButton,-->
<!--  },-->
<!--};-->
<!--</script>-->
<script setup>
import { inject, onMounted, ref } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { useI18n } from "vue-i18n";
import formatter from "@/class/formatter";
import Formatter from "@/class/formatter";
import WalletDetail from "@/components/settings/drawers/detail/WalletDetail.vue";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";

const api = inject("api");
const { t } = useI18n();
const loader = ref();
const gridApi = ref();
const grid = ref();

const columnDefs = [
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "uuid",
    headerName: "UUID",
    width: 90,
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "name",
    headerName: t("Name"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "company.name",
    headerName: t("Company Name"),
    filter: "agTextColumnFilter"
  },
  {
    field: "iban",
    headerName: t("Iban"),
    filter: "agTextColumnFilter",
    sortable: true
  },
  {
    field: "amount",
    headerName: t("Amount"),
    filter: "agTextColumnFilter",
    sortable: true,
    valueFormatter: (props) => Formatter.currency(Number(props.value), props.data.currency_unit)
  },
  {
    field: "updated_at",
    headerName: t("Updated At"),
    filter: 'agDateColumnFilter',
    export: true,
    sortable: true,
    cellClass: 'dateType',
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  }
];


const refresh = () => {
 grid.value.refresh()
};
</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>

    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3">{{ t("Employee Wallets") }} </span>
      <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
        <el-button
          class="m-3 items-center"
          size="small"
          @click="refresh"
        >
          <FontAwesomeIcon icon="refresh" size="lg" />
        </el-button>
      </ButtonToolTip>

    </template>
    <template #content>
      <div class="h-full w-full">
        <SSDataGrid
          ref="grid"
          :columns="columnDefs"
          url="customer/courier-finance/wallets"
          :auto-size-column="false"
        />
      </div>
    </template>
  </SettingsPageLayout>
</template>

<style scoped>

</style>