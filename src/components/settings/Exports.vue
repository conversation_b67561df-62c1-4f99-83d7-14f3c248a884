<script>
import DownloadButton from "@/renderers/DownloadButton.vue"




export default {
  components: {
    DownloadButton,

  }
}
</script>
<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import {computed, inject, onActivated, onDeactivated, ref,onMounted} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import formatter from "@/class/formatter";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ExportDetail from "@/components/settings/drawers/detail/ExportDetail.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";


const toast = useToast()
const {t} = useI18n()
const {getters, dispatch} = useStore();


const api = inject("api");

const activeCompany = computed(() => getters["auth/activeCompany"]);
const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);


const loader = ref();
const grid = ref()
const openDeleteDialog = ref(false)
const logDetailDrawer = ref(false)
const gridId = ref()
const log = ref()
const gridApi = ref()
const date = ref()


const statues = {
  created: "Created",
  assigned: "Assigned",
  in_progress: "In Progress",
  on_delivery: "On Delivery",
  failed: "Failed",
  completed: "Completed",
  cancelled: "Cancelled",
}

const columnDefs = [
  // {
  //   field: "id",
  //   headerName: "",
  //   pinned: "left",
  //   cellStyle: {textAlign: "center", display: 'flex', alignItems: 'center', justifyContent: 'center'},
  //   cellRenderer: "LoadingRenderer",
  //   cellRendererParams: {
  //     onProgress: (params) => {
  //      console.log(params)
  //     },
  //
  //   },
  //   width: 60,
  // },
  {
    field: "id",
    filter: 'agTextColumnFilter',
    export: true,
    cellClass: 'stringType',
  },
  {
    field: "status_slug",
    headerName: t("Status"),
    filter: 'agTextColumnFilter',
    sortable: true,
    cellClass: 'stringType',
  },
  {
    field: "filename",
    headerName: t("Name"),
    filter: 'agTextColumnFilter',
    export: true,
    cellClass: 'stringType',
    valueFormatter: (params) => params.value + "." + params.data.filetype_slug,
  },

  {
    field: "description",
    headerName: t("Description"),
    filter: 'agTextColumnFilter',
    export: true,
    cellClass: 'stringType',
  },
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    export: true,
    cellClass: 'dateType',
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "updated_at",
    headerName: t('Updated At'),
    filter: 'agDateColumnFilter',
    export: true,
    cellClass: 'dateType',
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),

  },
  {
    field: 'job_id',
    headerName: t('Job Id'),
    filter: 'agNumberColumnFilter'

  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 154,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DownloadButton",
    cellRendererParams: {
      onDelete: (params) => {
        openDeleteDialog.value = true
        gridId.value = params
      },
      onLogDetail: (params) => {
        logDetailDrawer.value = true
        log.value = JSON.stringify(params)

      }

    }
  },
]

const deleteExport = () => {

  loader.value.show()
  api.delete(`customer/exports/${gridId.value}`)
      .then(() => {
        openDeleteDialog.value = false
        toast.success(t('Your deletion was successful.'))
        deleteRow(gridId.value)
      })
      .catch((err) => toast.error(err))
      .finally(() => loader.value.hide())
}


onActivated(() => {
  grid.value && grid.value.refresh()
  Echo.private(companyChannel.value).listen(".export.completed", (data) => updateRow(data))
})

onMounted(() => {
  grid.value && grid.value.refresh()
  Echo.private(companyChannel.value).listen(".export.completed", (data) => updateRow(data))
})

onDeactivated(() => {
  Echo.private(companyChannel.value).stopListening(".export.completed")
})
const updateRow = (data) => {
  let row = gridApi.value.getRowNode(data.export.id)


  if (row) {
    row.url = data.export.url
    row.status_slug = data.export.status_slug
    row.updated_at = data.export.updated_at
    row.filename = data.export.filename
    row.created_at = data.export.created_at
    row.job_id = data.export.job_id

    gridApi.value.applyTransaction({update: [row]})
  }
}

const deleteRow =(id)=>{
  const row = gridApi.value.getRowNode(id)
  if (row) {
    gridApi.value.applyTransaction({remove: [row]})
  }
}


const refresh = () => {
  grid.value.refresh()
}

const close =()=>{
  openDeleteDialog.value = false
}


</script>

<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>

    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3">{{ t('Exports') }} </span>
      <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
        <el-button
            class="m-3 items-center"
            size="small"
            @click="refresh"
        >
          <FontAwesomeIcon icon="refresh" size="lg"/>
        </el-button>
      </ButtonToolTip>

    </template>
    <template #content>
      <div class="w-full h-full">
        <SSDataGrid
            v-model="gridApi"
            ref="grid"
            url="customer/exports"
            :columns="columnDefs"
            :auto-size-column="false"
            :paginationPageSize="100"
        />
      </div>
      <el-dialog v-model="openDeleteDialog" :title="t('Are you sure?')" width="450px" center>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="close">{{ t("Cancel") }}</el-button>
            <el-button @click="deleteExport" type="danger">
              {{ t("Delete") }}
            </el-button>
          </div>
        </template>
      </el-dialog>
    </template>
    <template #drawers>
      <el-drawer
          v-model="logDetailDrawer"
          class="customized-drawer customized-drawer"
          :title="t('Export Detail')"
          append-to-body
          destroy-on-close
      >
        <ExportDetail :lodData="log"/>
      </el-drawer>
    </template>
  </SettingsPageLayout>


</template>
