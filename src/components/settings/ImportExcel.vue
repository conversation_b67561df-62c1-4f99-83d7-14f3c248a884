<script setup>
import { groupBy } from "@/class/helpers";
import { computed, inject, onMounted, onUnmounted, reactive, ref, watch, onActivated } from "vue";
import { useI18n } from "vue-i18n";
import { read, utils } from "xlsx";
import { useToast } from "vue-toastification";
import EntityCellRenderer from "@/components/settings/ImportExcel/EntityCellRenderer.vue";
import { useStore } from "vuex";
import PinMap from "../ui/PinMap.vue";
import dayjs from "dayjs";

const { t } = useI18n();
const toast = useToast();
const { getters, dispatch } = useStore();
const primaryColor = computed(() => getters["ui/primaryColor"]);
const assignWarn = ref(false);

const dialogVisible = ref(false);

const barWidth = screen.width;

const editFormValues = reactive({
  address: "",
  address_building: null,
  address_floor: null,
  address_apartment: null,
  // address_postal_code: null,
  // address_district: null,
  // address_province: null,
  // phone: null,
  // phone_country: null,
  name: "",
  lat: null,
  lng: null
  // address_description: null,
});

const editModalEntity = ref({});

const payloadKeys = [
  "hub_id",
  "address",
  "address_building",
  "address_floor",
  "address_apartment",
  "address_description",
  "lat",
  "lng",
  "name",
  "phone",
  "phone_country"
];

const handleSubmit = () => {
  if (editFormValues.modalType === "Origin") {
    var payload = { origin: {} };

    for (let i = 0; i < payloadKeys.length; i++) {
      const key = payloadKeys[i];
      const keyWithPrefix = "origin_" + key;
      const value = editModalEntity.value[keyWithPrefix];
      if (value) {
        payload.origin[key] = value;
      }
    }

    payload.origin["lat"] = locationEdit.value.lat || payload.origin["lat"];
    payload.origin["lng"] = locationEdit.value.lng || payload.origin["lng"];
    payload.origin["address_apartment"] = editFormValues.address_apartment || payload.origin["address_apartment"];
    payload.origin["address_building"] = editFormValues.address_building || payload.origin["address_building"];
    payload.origin["address_floor"] = editFormValues.address_floor || payload.origin["address_floor"];
    payload.origin["address"] = locationEdit.value.address || payload.origin["address"];
  } else {
    var payload = { destination: {} };

    for (let i = 0; i < payloadKeys.length; i++) {
      const key = payloadKeys[i];
      const keyWithPrefix = "destination_" + key;
      const value = editModalEntity.value[keyWithPrefix];
      if (value) {
        payload.destination[key] = value;
      }
    }

    payload.destination["lat"] = locationEdit.value.lat || payload.destination["lat"];
    payload.destination["lng"] = locationEdit.value.lng || payload.destination["lng"];
    payload.destination["address_apartment"] = editFormValues.address_apartment || payload.destination["address_apartment"];
    payload.destination["address_building"] = editFormValues.address_building || payload.destination["address_building"];
    payload.destination["address_floor"] = editFormValues.address_floor || payload.destination["address_floor"];
    payload.destination["address"] = locationEdit.value.address || payload.destination["address"];
  }


  api.patch(`customer/import/tasks/${editFormValues.import_id}/entities/${editFormValues.id}`, payload)
    .then((res) => {

      entitiesList.value = entitiesList.value?.map((e) => {
        if (e.id === res.id) {
          return {
            ...e,
            ...res
          };
        } else {
          return e;
        }
      });
      const _message = t("Address edited");
      toast.success(_message);
      dialogVisible.value = false;
      entitiesKey.value += 1;
    })
    .catch((err) => {

      loader.value?.hide();
    });
};

const downloadExcelTemplate = () => {
  // 
  api.get("customer/import/templates?file=template-1.xlsx", { responseType: "blob" })
    .then((response) => {
      var blob = new Blob([response]);
      var link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.href.replace("localhost:3000", "https://dashboard.dev.qdelivery.app/");
      link.download = "template-1.xlsx";
      link.click();
      window.URL.revokeObjectURL(link.href);
    });
};
const downloadExcelTemplate2 = () => {
  //
  api.get("customer/import/templates?file=template-2.xlsx", { responseType: "blob" })
    .then((response) => {
      var blob = new Blob([response]);
      var link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.href.replace("localhost:3000", "https://dashboard.dev.qdelivery.app/");
      link.download = "template-1.xlsx";
      link.click();
      window.URL.revokeObjectURL(link.href);
    });
};

const downloadExcelTemplate3 = () => {
  //
  api.get("customer/import/templates?file=template-3.xlsx", { responseType: "blob" })
    .then((response) => {
      var blob = new Blob([response]);
      var link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.href.replace("localhost:3000", "https://dashboard.dev.qdelivery.app/");
      link.download = "template-1.xlsx";
      link.click();
      window.URL.revokeObjectURL(link.href);
    });
};


const visibleCols = [
  "allow_import",
  "id",
  "task_id",
  "destination_name",
  "destination_address",
  "resolved_destination_address",
  "destination_lat",
  "destination_lng",
  "origin_address",
  "origin_lat",
  "origin_lng",
  "starts_at",
  "ends_at"
];

const locationEdit = ref({
  searchAddress: "",
  lat: null,
  lng: null,
  area_json: null
});

const readFile = (fileData) => {
  const reader = new FileReader();

  reader.onload = (e) => {
    const bstr = e.target.result;
    const wb = read(bstr, { type: "binary", cellDates: true });
    const wsname = wb.SheetNames[0];
    const ws = wb.Sheets[wsname];
    const data = utils.sheet_to_json(ws, {
      blankrows: false,
      header: 1,
      dateNF: "dd/mm/yyyy HH:MM",
      raw: false,
      defval: ""
    });
    // hide empty columns
    var emptyCols = [];
    if (data[0]) {
      for (let i = 0; i < data[0].length; i++) {
        if (!data[0][i]) {
          const iData = data.find((arr) => arr[i]);
          if (!iData) {
            emptyCols.push(i);
          }
        }
      }
    }
    emptyColumns.value = emptyCols;
    //
    totalLineCount.value = hasHeader.value ? data.length - 1 : data.length;
    var size = 10;
    var arrayOfArrays = [];
    const startLine = hasHeader.value ? 1 : 0;
    for (var i = startLine; i < data.length; i += size) {
      arrayOfArrays.push(data.slice(i, i + size));
    }
    ;
    excelContent.value = arrayOfArrays;
    headerContent.value = hasHeader.value && data[0];
    // currentPage.value = 'preview';
    screenState.value = "preview";
  };
  reader.readAsBinaryString(fileData);
};

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);
//
const importId = ref("");
const convertBtnState = ref(true);
const selectedHub = ref();
const columnList = ref([]);
const totalLineCount = ref();
const headerContent = ref([]);
const excelContent = ref([]);
const emptyColumns = ref([]);
const api = inject("api");
const screenState = ref("list");
const fileList = ref([]);
//list, details, 
const loader = ref(null);
const entitiesKey = ref(0);
const hasHeader = ref(true);
const fileId = ref();
const route = ref("");
const entitiesList = ref([]);
const selectedFile = reactive({ file: "" });
const file = ref();
const currentExcelPage = ref(0);
//
const mounted = ref(false);
const importedList = ref([]);
const scrollRef = ref();

const setCurrentExcelPage = (value) => {
  // if (value < 0 && currentExcelPage.value) {
  //   currentExcelPage.value -= 1;
  // }
  //
  // if (value > 0 && currentExcelPage.value < excelContent.value.length - 1) {
  //   currentExcelPage.value += 1;
  // }
  if (value < 0) {
    scrollRef.value.scrollLeft -= 100;
  } else {
    scrollRef.value.scrollLeft += 100;
  }
};

const handleSelectFile = (e) => {
  selectedFile.file = e.target.files[0];
  readFile(e.target.files[0]);
};

const staticData = reactive({
  teams: [],
  slots: [],
  slotInfos: []
});

const getTeams = () => {
  // if (formValues.owner) {
  // return api(`customer/teams?filter[hub_id]=${formValues.owner}`)
  // .then((res) => (staticData.teams = res.data))
  // } else {
  return api("customer/teams")
    .then((res) => (staticData.teams = res.data));
  // }
};

const getSlots = () => {
  return api(`customer/task-slots`)
    .then((r) => {
      const slots = groupBy(r.data, "slot");
      const keys = Object.keys(slots);
      staticData.slots = keys;
      staticData.slotInfos = r.data;
    });
};

const hubs = ref([]);

const getHubs = () => {
  return api("customer/hubs").then((res) => {
    hubs.value = res.data;
    if (res.data.length === 1) {
      // to do ->
      // formValues.owner = res.data[0].id
      // formValues.service = res.data[0].default_service_time
      // formValues.tags = [...res.data[0].tags]
    }
  });
};

const getFileList = () => {
  loader.value?.show();
  api.get(`customer/import/tasks`)
    .then((res) => {
      const resData = res.data;
      fileList.value = resData.map((file) => {
        const title = file.file?.indexOf("imports\/") !== -1 ? file.file.replace("imports\/", "") : file.file;
        const createdAt = new Date(file.created_at);
        const hourData = createdAt.getHours() + ":" + createdAt.getMinutes();
        const date = file.created_at?.split("T")[0].split("-").reverse().join(".") + " " + hourData;
        return {
          ...file,
          visual: {
            title, date
          }
        };
      });
    })
    .catch((err) => {

      loader.value?.hide();
    });
  Promise.all([
    getTeams(),
    getSlots()
  ]).finally(() => loader?.value?.hide());
};

onMounted(() => {
  getFileList();
});


onMounted(() => {
  getHubs();
  api.post(`components/import-columns`)
    .then((res) => {
      columnList.value = [{ slug: "not_mapped", name: t("Clear Selection") }].concat(res.data?.map((d) => ({
        ...d,
        selectedCol: -1
      })));
    })
    .catch((err) => {
    });
});

watch(() => entitiesList.value, () => {
  const anyImportable = entitiesList.value?.find((ent) => {
    if (!ent.task_id && ent.allow_import) {
      return true
    } else {
      return false
    }
  });

  convertBtnState.value = !!anyImportable;
});

const handleGoDetails = (_fileId, routeValue = false) => {
  fileId.value = _fileId;
  loader.value?.show();
  api.get(`customer/import/tasks/${_fileId}/entities`)
    .then((res) => {
      const resData = res.data;
      if (route.value === "import" && routeValue) {
        entitiesList.value = resData?.map((k) => ({ ...k, import_loader: true, loader: false }));
      } else {
        entitiesList.value = resData?.map((k) => ({ ...k, loader: false }));
      }
      screenState.value = "details";
    })
    .catch((err) => {

    }).finally(() => loader.value?.hide());
};

const handleBackList = () => {
  getFileList()
  screenState.value = "list";

};

const detailsHub = ref("");

const handleImportCreate = () => {
  convertBtnState.value = true;
  loader.value?.show();
  const formData = new FormData();
  formData.append("file", selectedFile.file);
  const selectedFields = [];

  columnList.value.map((c) => {
    if (c.selectedCol > -1) {
      const column = c.selectedCol + 1;
      const propName = "column_mapping[" + column + "]";
      formData.append(propName, c.slug);
      selectedFields.push(c.slug);
    }
  });

  formData.append("has_header", hasHeader.value ? 1 : 0);
  formData.append("hub_id", selectedHub.value);

  api.post(`customer/import/tasks`, formData)
    .then((res) => {
      toast.success(t("Imported"));
      localStorage.setItem("excel_import_hub", selectedHub.value);
      route.value = "import";
      handleGoDetails(res.id, true);
      // loader.value?.hide();
    })
    .catch((err) => {
      const errMessage = err.data?.message;
      toast.error(errMessage);
      loader.value?.hide();
    });
};

const handleOptionChange = (index, value) => {
  columnList.value = columnList.value.map((c) => {
    if (value === "not_mapped") {
      return (c.selectedCol === index ? { ...c, selectedCol: -1 } : c);
    } else {
      if (c.selectedCol === index) {
        return { ...c, selectedCol: -1 };
      } else {
        return (c.slug === value ? { ...c, selectedCol: index } : c);
      }
    }
  });
};

function refreshList(_id) {
  handleGoDetails(_id, false);
};

const handleEditClick = (_line, isOrigin = false) => {
  editModalEntity.value = _line;
  if (isOrigin) {
    editFormValues.modalType = "Origin";
    editFormValues.name = _line.origin_name;
    editFormValues.address_building = _line.origin_address_building;
    editFormValues.address_floor = _line.origin_address_floor;
    editFormValues.address_apartment = _line.origin_address_apartment;
    editFormValues.address = _line.origin_address;
    editFormValues.id = _line.id;
    editFormValues.import_id = _line.import_id;
    locationEdit.value.searchAddress = _line.origin_address;
    locationEdit.value.address = _line.origin_address;
    locationEdit.value.lat = _line.origin_lat && parseFloat(_line.origin_lat);
    locationEdit.value.lng = _line.origin_lng && parseFloat(_line.origin_lng);
  } else {
    editFormValues.modalType = "Destination";
    editFormValues.name = _line.destination_name;
    editFormValues.address_building = _line.destination_address_building;
    editFormValues.address_floor = _line.destination_address_floor;
    editFormValues.address_apartment = _line.destination_address_apartment;
    editFormValues.address = _line.destination_address;
    editFormValues.id = _line.id;
    editFormValues.import_id = _line.import_id;
    locationEdit.value.searchAddress = _line.destination_address;
    locationEdit.value.address = _line.destination_address;
    locationEdit.value.lat = _line.destination_lat && parseFloat(_line.destination_lat);
    locationEdit.value.lng = _line.destination_lng && parseFloat(_line.destination_lng);
  }
  dialogVisible.value = true;
};

watch(() => hasHeader.value, () => {
  selectedFile.file && readFile(selectedFile.file);
});


watch(() => entitiesList?.value?.[0]?.hub_id, (val) => {
  val && api.get(`customer/hubs/${val}`)
    .then((res) => {
      detailsHub.value = res?.name;
    })
    .catch((err) => {
      loader.value?.hide();
    });
});

const fileVisual = ref({});

onMounted(() => {
  mounted.value = true;
  if (importedList.length) {

    for (let i = 0; i < importedList.value.length; i++) {
      const element = importedList.value[i];
      runEntityImport(element);
    }
  }
  ;
});

const runEntityImport = (entity) => {
  if (importId.value && entity) {
    api.get(`customer/import/tasks/${importId.value}/entities/${entity}`)
      .then((res) => {
        const updatedEntities = entitiesList.value.map((e) => {
          if (res.id == e.id) {
            return {
              ...res,
              import_loader: false,
              loader: false
            };
          } else {
            return e;
          }
        });

        entitiesList.value = updatedEntities;
        entitiesKey.value += 1;
      })
      .catch((err) => {

        loader.value?.hide();
      });
  }
};

onMounted(() => {
  listener();
});

onUnmounted(() => {
  stopListener();
  // screenState.value = '';
});

const listener = () => {
  Echo.private(companyChannel.value).listen(".import.created", (_dt) => {

    importId.value = _dt.eventData.import?.id;
    fileVisual.value = {
      title: _dt.eventData.import.file?.replace("imports/", ""),
      date: dayjs(Date.now()).format("DD/MM/YYYY")
    };
  });
  Echo.private(companyChannel.value).listen(".imported.entity.allow.status.updated", (_dt) => {

  });
  Echo.private(companyChannel.value).listen(".imported.entity.updated", (_dt) => {

    //
    const imported_entity_id = _dt.eventData?.imported_entity?.id;

    if (!mounted.value) {
      importedList.value.push(imported_entity_id);
    } else {
      setTimeout(() => {
        runEntityImport(imported_entity_id);
      }, 1500);
    }
  });

  Echo.private(companyChannel.value).listen(".convert.completed", (_dt) => {

    convertBtnState.value = false;
    toast.success(t("Convert operation completed"));

  });
  Echo.private(companyChannel.value).listen(".imported.entity.converted", (_dt) => {

    const importedEntity = _dt.eventData?.imported_entity;
    updateEntity(importedEntity.id, importedEntity.task_id);
  });
};

const stopListener = () => {
  Echo.private(companyChannel.value).stopListening(".import.created");
  Echo.private(companyChannel.value).stopListening(".imported.entity.allow.status.updated");
  Echo.private(companyChannel.value).stopListening(".imported.entity.updated");
  Echo.private(companyChannel.value).stopListening(".imported.entity.converted");
  Echo.private(companyChannel.value).stopListening(".convert.completed");
};

const startImport = async (payload) => {
  try {
    const _id = typeof fileId === "number" ? fileId : fileId.value;

    api.get(`customer/import/tasks/${_id}/entities`)
      .then((res) => {
        entitiesList.value = res.data.map(e => {
          if (e.allow_import && !e.task_id) {
            return {
              ...e,
              loader: true
            };
          } else {
            return {
              ...e,
              loader: false
            };
          }
        });
      });

    const res = await api.post(`customer/import/tasks/${_id}/entities:convert`, payload);
    toast.success(t("Operation started"));

    entitiesKey.value += 1;

  } catch (err) {
    loader.value = false; // Reset the loading state here
    // if (err.data?.errors?.["assignment.type"]) {
    //   assignWarn.value = true;
    // }
    toast.error(err.message)
  } finally {
    if (!assignWarn.value) {
      loader.value = false;
    }
  }
};

const updateEntity = (id, task_id) => {
  const updatedEntities = entitiesList.value.map((e) => {
    if (e.id == id) {
      return {
        ...e,
        task_id,
        loader: false
      };
    } else {
      return e;
    }
  });

  entitiesList.value = updatedEntities;
  entitiesKey.value += 1;
};

watch(() => fileId.value, () => {
  const heyMyFile = fileList?.value.find((f) => f.id == fileId?.value);
  fileVisual.value = heyMyFile?.visual || {};
});

const isImportDisabled = computed(() => {
  if (!selectedHub.value) {
    return true;
  }
  // to do : reqired alanların kontrolü yapılacak . . 
  // to do : task hub sb ye icon eklenmesi 

});

const closeDialog = () => {
  dialogVisible.value = false;
};

const previewLineCount = computed(() => totalLineCount.value < 10 ? totalLineCount : 10).value;

const mappingCompleted = reactive({ value: false });
const mappingList = ref([]);

const clearWarn = () => {
  assignWarn.value = false;
};

</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="bg-white font-semibold pl-4 pr-6 py-2 text-md flex flex-row justify-between items-center"
       v-if="screenState === 'list' || screenState === 'details'">
    <span style="font-size: 16px;">
      {{ t("Import") }}
    </span>
    <el-button @click="file.click()"
               class="text-black"
               v-if="screenState === 'list'">
      <template #icon>
        <FontAwesomeIcon icon="file-excel"
                         class="text-indigo-600" />
      </template>
      {{ $t("Import New File") }}
    </el-button>
    <input @change="handleSelectFile"
           type="file"
           id="fileElem"
           :accept="'.xlsx'"
           ref="file"
           :hidden="true">
  </div>
  <div class="bg-slate-50 py-4 pl-12 text-gray-500 border-t border-b"
       v-if="screenState === 'list'">
    <span v-if="fileList?.length">
      {{ t("Currently has file uploads", { message: fileList?.length }) }} </span>
  </div>
  <div v-if="screenState === 'list'"
       class="flex px-8 pt-1 flex-col h-full bg-white flex-1 overflow-auto"
       style="padding-bottom: 120px;">
    <div v-for="file in fileList"
         style="background-color: #f8fafc; border: 1px solid #eaeaea;"
         class="flex flex-row items-center w-full p-4 my-2">
      <FontAwesomeIcon icon="file-excel"
                       size="2xl"
                       class="mx-2 mr-4 text-indigo-600 text-4xl" />
      <div class="flex flex-col flex-1">
        <div class="font-semibold">{{ file.visual.title }}</div>
        <div>{{ t("Uploaded on", { message: file.visual.date }) }}</div>
      </div>
      <div @click="() => {
        convertBtnState = true;
        handleGoDetails(file.id)
      }"
           style="border:1px solid #eaeaea; background-color: white;"
           class="p-2 rounded cursor-pointer">
        {{ t("View") }}
      </div>
    </div>
  </div>
  <div v-else-if="screenState === 'details'"
       class="flex flex-row h-full border-t bg-white">

    <div class="w-9/12 flex flex-col relative bg-white overflow-x-scroll"
         style="overflow-x:scroll; height: fit-content; max-height: 96%;">
      <div style="box-shadow: inset -5px -9px 7px -8px #888; height:100%; position: absolute; right: 0; width: 5px;">

      </div>
      <div class="flex-1 overflow-x-scroll overflow-y-auto w-full flex">

        <div class="overflow-x-auto min-w-max"
             :key="entitiesKey">
          <div class="bg-slate-50 py-3 pl-4 border-b font-medium flex flex-row items-center justify-between"
               style="border-color: #eaeaea; font-size: 12px;"
               @click="handleBackList">
            <div>
              <FontAwesomeIcon icon="chevron-left"
                               class="mr-4 text-lg cursor-pointer" />
              <span>
                {{ t("Import List") }} /
              </span>
              <FontAwesomeIcon icon="file-excel"
                               class="text-indigo-600 ml-2 mr-1" />
              {{ fileVisual?.title }} /
              <span style="color: #bcc0c6; padding-left: 7px;">
                {{ t("Uploaded on", { message: fileVisual?.date }) }}
              </span>
            </div>

            <span class="pr-10"
                  style="margin-left: 12%;"
                  v-show="!!detailsHub">
              Hub Id:
              <span class="text-gray-400">
                {{ detailsHub }}
              </span>
            </span>

          </div>
          <div class="row__line flex flex-row border-b"
               style="border-color: #eaeaea; background-color: #f8fafc;">
            <EntityCellRenderer v-for="cell in visibleCols"
                                :cell="cell"
                                :line="{}"
                                :isHeader="true" />
          </div>
          <div v-for="line in entitiesList"
               class="row__line flex flex-row border-b"
               style="border-color: #eaeaea;">
            <EntityCellRenderer v-for="cell in visibleCols"
                                :cell="cell"
                                :line="line"
                                :refreshList="refreshList"
                                @showLoader="() => loader.show()"
                                @hideLoader="() => loader.hide()"
                                @openModal="(isOrigin) => handleEditClick(line, isOrigin)" />
          </div>
        </div>
      </div>
    </div>
    <div class="w-3/12 border-l flex flex-col overflow-y-scroll bg-white p-2">
      <RightContext :assignWarn="assignWarn"
                    @clearWarn="clearWarn"
                    :teams="staticData.teams"
                    :slots="staticData.slots"
                    :slotInfos="staticData.slotInfos"
                    :formValues="formValues"
                    :fileId="fileId"
                    :entitiesList="entitiesList"
                    :updateEntity="updateEntity"
                    @startImport="startImport"
                    :convertBtnState="convertBtnState" />
    </div>
  </div>

  <div v-show="screenState === 'preview'"
       class="flex flex-1 bg-white h-full ag-theme-quartz flex-col">

    <div class="bg-white font-semibold pl-4 pr-6 py-2 text-md flex flex-row items-center border-b">
      <FontAwesomeIcon @click="handleBackList"
                       icon="chevron-left"
                       class="mr-4 text-lg cursor-pointer" />
      <span style="font-size: 16px;">
        {{ t("Import") }} / <span class="text-gray-400 text-sm font-light ml-1">{{ t("File Preview") }}</span>
      </span>
    </div>
    <div ref="scrollRef"
         class="flex w-full overflow-x-scroll relative previewcontainer"
         style="height: 70%">
      <div class="absolute left-0 arrowcontainer justify-between px-1"
           :style="{ top: '45%', width: barWidth + 'px' }">
        <FontAwesomeIcon icon="fa-solid fa-forward"
                         class="bg-white fa-flip-horizontal px-2 py-2 pl-4 border text-lg cursor-pointer text-indigo-600  text-gray-400"
                         @click="() => setCurrentExcelPage(-1)" />
        <FontAwesomeIcon class="bg-white px-2 py-2 pr-4 border text-lg cursor-pointer text-indigo-600"
                         icon="fa-solid fa-forward"
                         @click="() => setCurrentExcelPage(1)" />

      </div>
      <div>
        <div class="flex flex-row"
             style="background-color: #f9fafc; border-bottom: 1px solid #eaeaea;">
          <div class="w-10"
               style="border-right: 1px solid #eaeaea; padding-left: 4px; padding-top: 3px; padding-bottom: 3px;">{{ " "
            }}
          </div>
          <div v-for="(cola, columnIndex) in excelContent[currentExcelPage]?.[0]"
               v-show="emptyColumns.indexOf(columnIndex) === -1"
               class="w-48 py-4 px-1"
               style="border-right: 1px solid #eaeaea;">
            <GroupedSelectBox :columnList="columnList"
                              @change="(value) => {
        handleOptionChange(columnIndex, value);
      }"
                              :id="columnIndex"
                              :columnLength="columnLength"
                              :mappingCompleted="mappingCompleted"
                              :mappingList="mappingList" />
          </div>
        </div>

        <!-- // header -->
        <div v-if="hasHeader"
             class="flex flex-row"
             style="background-color: #eaf4ff; border-bottom: 1px solid #eaeaea;">
          <div class="w-10"
               style="border-right: 1px solid #eaeaea; background-color: #f9fafc;">{{ " " }}
          </div>
          <div v-for="(headerData, columnIndex) in headerContent"
               class="w-48 py-1"
               v-show="hasHeader && emptyColumns.indexOf(columnIndex) === -1"
               style="border-right: 1px solid #eaeaea; padding-left: 4px;">
            <span class="font-bold"
                  :style="columnList?.find((c) => c.selectedCol === columnIndex) ? { color: 'black' } : { color: '#999797' }">
              {{ headerData }}
            </span>
          </div>
        </div>
        <!-- header end  -->
        <div v-for="(line, lineIndex) in excelContent[currentExcelPage]"
             class="flex flex-row"
             :style="['border-bottom: 1px solid #eaeaea; border-right: 1px solid #eaeaea;']">
          <div class="w-10 font-bold"
               style="justify-content:center;align-items: center;display:flex;border-right: 1px solid #eaeaea; background-color: #F9FAFC;">
            {{ (currentExcelPage * 10) + lineIndex + 1 }}
          </div>
          <div v-for="(cell, colInd) in line"
               v-show="emptyColumns.indexOf(colInd) === -1"
               class="w-48"
               style="border-right: 1px solid #eaeaea; padding-left: 4px; padding-top: 3px; padding-bottom: 3px;">

            <span
                  :style="columnList?.find((c) => c.selectedCol === colInd) ? { color: 'black' } : { color: '#b4b4b4' }">
              {{ cell }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="flex flex-row justify-between pt-4 items-center">
      <!-- <FontAwesomeIcon
        icon="fa-solid fa-forward"
        :class="['fa-flip-horizontal px-2 py-2 pl-4 border text-lg cursor-pointer', currentExcelPage > 0 ? 'text-indigo-600' : 'text-gray-400']"
        @click="() => setCurrentExcelPage(-1)"
      /> -->
      <div class="font-semibold flex flex-col items-center justify-center w-full">
        <div>
          <el-checkbox v-model="hasHeader"
                       size="small">
            <span class="text-sm font-bold text-slate-700">
              {{ t("The first line is the title") }}
            </span>
          </el-checkbox>
        </div>

        <span class="font-light">
          {{ t("The first lines are shown in quantity", { message: previewLineCount }) }}
        </span>
      </div>
      <!-- <FontAwesomeIcon
        :class="['px-2 py-2 pr-4 border text-lg cursor-pointer', currentExcelPage < excelContent.length - 1 ? 'text-indigo-600' : 'text-gray-400']"
        icon="fa-solid fa-forward"
        @click="() => setCurrentExcelPage(1)"
      /> -->
    </div>
  </div>
  <div v-show="screenState === 'preview'"
       class="bottom-0 absolute w-full flex flex-row px-8 py-4 items-center justify-between border-t bg-white">
    <div class="w-5/12 font-light">
      <i18n-t keypath="Please contact us or download our sample Excel file if you are experiencing any issues, we would be happy to assist you."
              tag="p">

        <template v-slot:excel>
          <el-popover placement="top"
                      :width="205"
                      trigger="hover"
                      content="this is content, this is content, this is content">
            <div class="flex items-center justify-center">
              <div @click="downloadExcelTemplate"
                   class="flex flex-col items-center justify-between cursor-pointer">
                <el-popover placement="top"
                            :width="400"
                            trigger="hover"
                            content="this is content, this is content, this is content">
                  <div>
                    İçeri yüklemek isteyeceğiniz en basit excel örneğidir.
                  </div>
                  <template #reference>
                    <FontAwesomeIcon class="pb-1"
                                     size="xl"
                                     style="color: #0D7F41;"
                                     icon="file-excel" />
                  </template>
                </el-popover>

                <div>
                  Örnek 1
                </div>
              </div>
              <div @click="downloadExcelTemplate2"
                   class="mx-3 flex flex-col items-center justify-center cursor-pointer">
                <el-popover placement="top"
                            :width="450"
                            trigger="hover"
                            content="this is content, this is content, this is content">
                  <div>
                    İçeri yüklemek isteyeceğiniz biraz daha kapsamlı excel örneğidir.
                  </div>

                  <template #reference>
                    <FontAwesomeIcon class="pb-1"
                                     size="xl"
                                     style="color: #0D7F41;"
                                     icon="file-excel" />
                  </template>
                </el-popover>
                <div>
                  Örnek 2
                </div>
              </div>
              <div @click="downloadExcelTemplate3"
                   class="flex flex-col items-center justify-center cursor-pointer">
                <el-popover placement="top"
                            :width="400"
                            trigger="hover"
                            content="this is content, this is content, this is content">
                  <div>
                    İçeri yüklemek isteyeceğiniz en kapsamlı excel örneğidir.
                  </div>

                  <template #reference>
                    <FontAwesomeIcon class="pb-1"
                                     size="xl"
                                     style="color: #0D7F41;"
                                     icon="file-excel" />
                  </template>
                </el-popover>
                <div>
                  Örnek 3
                </div>
              </div>

            </div>

            <template #reference>
              <span class="text-indigo-600 cursor-pointer">{{ "Excel" }}</span>
            </template>
          </el-popover>
        </template>
      </i18n-t>
    </div>

    <!-- Task Hub selectbox -->
    <div class="flex flex-row items-center ml-auto">
      <div style="border-radius: 4px"
           class="flex items-center flex-1">
        <div class="font-light w-2/6 pl-1 text-right mr-2"
             style="color: #334155">{{ t("Task Hub") }}
        </div>
        <el-select filterable
                   :placeholder="t('Select Hub')"
                   class="w-4/6 custom-drop-container"
                   v-model="selectedHub"
                   @change="onChange">

          <template #prefix>
            <div class="text-base"
                 :style="{ color: primaryColor }">
              <FontAwesomeIcon icon="store" />
            </div>
          </template>
          <el-option v-for="item in hubs"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="flex flex-row items-center font-light">
      <el-button type="primary ml-2"
                 @click="handleImportCreate"
                 :disabled="isImportDisabled">
        {{ t("Import Tasks") }}
      </el-button>
    </div>
  </div>
  <!-- edit modal  -->
  <el-dialog v-model="dialogVisible"
             width="720px"
             center
             @close="onDialogClose"
             top="30px">

    <template #header>
      <div class="text-indigo-600 font-semibold text-left mr-auto border-b pb-2"
           style="font-size: 18px;">
        {{ t("Address Update") }}
      </div>
    </template>
    <div class="flex flex-row">
      <div class="flex flex-col mr-8">
        <span class="font-light">{{ editFormValues.modalType }} <span>
            {{ t("Name") }}
          </span></span>
        <span class="font-semibold">{{ editFormValues.name }}</span>
      </div>
      <div class="flex flex-col">
        <span>{{ editFormValues.modalType }}
          <span>{{ t("Phone") }}</span></span>
        <div class="flex flex-row">
          <span>{{ editFormValues.modalType === "origin" ? editModalEntity?.origin_phone_country :
        editModalEntity?.destination_phone_country }}</span>
          <span>{{ editFormValues.modalType === "origin" ? editModalEntity?.origin_phone :
        editModalEntity?.destination_phone }}</span>
        </div>
      </div>
    </div>
    <div class="flex flex-col mt-4">
      <span>{{ t("Currently Address") }}</span>
      <span class="font-semibold">{{ editFormValues.address }}</span>
    </div>
    <div class="w-full border-b my-4"></div>
    <div class="flex flex-row mb-4">
      <div class="flex flex-col mr-2">
        <span>
          {{ t("Building Name/No") }}
        </span>
        <el-input v-model="editFormValues.address_building">
        </el-input>
      </div>
      <div class="flex flex-col mr-2">
        <span>
          {{ t("Floor") }}
        </span>
        <el-input v-model="editFormValues.address_floor">
        </el-input>
      </div>
    </div>
    <span>
      {{ t("New Address Search") }}
    </span>
    <div>
      <PinMap v-if="dialogVisible"
              :hidden-place-holder="true"
              v-model="locationEdit"
              :poligonVisible="false"
              :customHeight="'h-56'" />
    </div>

    <template #footer>
      <div class="flex flex-row justify-between">
        <el-button @click="closeDialog">{{ t("Cancel") }}</el-button>
        <el-button @click="handleSubmit">
          {{ t("Update Task") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>


<style>
.arrowcontainer {
  display: none;
}

.previewcontainer:hover .arrowcontainer {
  display: flex;
  position: fixed;
}

.arrowcontainer>.cursor-pointer:hover {
  border-width: 2px !important;
}

.custom-drop-container * {
  border: none !important;
  border-width: 0 !important;
}


.pale__title {
  color: #96beed;
  font-weight: 600;
}

.custom_primary_color {
  color: #0b46f4
}

</style>
