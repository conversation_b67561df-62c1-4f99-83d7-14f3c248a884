<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import { inject, reactive, ref, computed, onActivated, onMounted, watch } from "vue";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import WebhookItem from "@/components/settings/components/WebhookItem.vue";
import NewWebHook from "@/components/settings/components/NewWebHook.vue";
import { useToast } from "vue-toastification";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import AddProduct from "@/components/qconnect/product/AddProduct.vue";
import NewApiToken from "@/components/settings/drawers/new/NewApiToken.vue";
import dayjs from "dayjs";
import ApiTokenEdit from "@/components/settings/drawers/edit/ApiTokenEdit.vue";

let doc_url = "https://docs.qdelivery.app/";

const toast = useToast();
const { t } = useI18n();
const { dispatch, getters } = useStore();

const api = inject("api");

const activeCompany = computed(() => getters["auth/activeCompany"]);

const loader = ref();
const webhooks = ref([]);
const newWebhooks = ref([]);
const webhookTypes = ref([]);
const rowModel = ref(null);
const dialogIsActiveVisible = ref(false);
const isActivateId = ref(null);
const editDrawer = ref(false);
const tokenItem = ref();

const apiTokens = ref();
const newApiTokenDrawer = ref(false);

const form = reactive({
  status_update_callback: null,
  courier_location_callback: null,
  courier_working_callback: null,
  slot_callback: null
});

const copyText = () => {
  navigator.clipboard.writeText(rowModel.value);
  toast.success(t("Copied to clipboard"));
};

const handleGenerate = () => {
  api.put("customer/settings/token")
    .then((r) => rowModel.value = r.token)
    .catch(() => {
      toast.error(t("An error occurred"));
      rowModel.value = null;
    });
};

const setFormValues = () => {
  form.status_update_callback = activeCompany.value.status_update_callback;
  form.courier_location_callback = activeCompany.value.courier_location_callback;
  form.courier_working_callback = activeCompany.value.courier_working_status_callback;
  form.slot_callback = activeCompany.value.slot_callback;
  rowModel.value = activeCompany.value.token;
};

const openDoc = () => {
  window.open(doc_url, "_blank");
};

const addNewWebHook = () => {
  newWebhooks.value.push({
    id: Math.random(),
    type: null,
    trigger: null,
    url: null,
    is_active: true,
    newWebhook: true
  });
};

const removeFromNewWebHook = (id) => {
  newWebhooks.value = newWebhooks.value.filter(x => x.id !== id);
};

const getData = () => {
  loader.value.show();
  api("customer/webhooks")
    .then((res) => {
      webhooks.value = res.data;
    })
    .finally(() => loader.value.hide());
};

const getWebHookTypes = () => {
  api.post("components/webhook-types-select")
    .then((res) => {
      webhookTypes.value = res;
    });
};

const addWebHook = (hook) => {
  loader.value.show();

  // prepareData fonksiyonunu ekleyelim
  const prepareData = (data) => {
    const filteredData = {};
    for (const key in data) {
      if (data[key] !== null && data[key] !== "") {
        filteredData[key] = data[key];
      }
    }
    return filteredData;
  };

  let body = {
    type: hook.type,
    trigger: hook.trigger,
    url: hook.url,
    is_active: hook.is_active,
    hub_ids: hook.hubs
  };

  body = prepareData(body);

  api.post("customer/webhooks", body)
    .then(() => {
      removeFromNewWebHook(hook.id);
      getData();
    })
    .catch((err) => {
      loader.value.hide();
      toast.error(err.message);
    });
};


onActivated(() => {
  setFormValues();
  getData();
  getWebHookTypes();
  getApiTokens();
});


onMounted(() => {
  setFormValues();
  getData();
  getWebHookTypes();
  getApiTokens();
});

const refreshWebhook = () => {
  getData();
};


const getApiTokens = () => {
  api("customer/api-tokens")
    .then((r) => {
      apiTokens.value = r.data.map(token => ({ ...token, showPassword: false }));
    });

};


const openTokenDrawer = () => {
  newApiTokenDrawer.value = true;
};
const close = () => {
  newApiTokenDrawer.value = false;
};

const togglePasswordVisibility = (item) => {
  item.showPassword = !item.showPassword;
};


const copyToken = (token) => {
  navigator.clipboard.writeText(token);
};

const deleteToken = (id) => {
  loader.value.show();
  api.delete(`customer/api-tokens/${id}`)
    .then(() => {
      getApiTokens();
    }).then(() => loader.value.hide());
};


const openModal = (item) => {
  dialogIsActiveVisible.value = true;
  isActivateId.value = item;
};

const tt = () => {

  loader.value.show();
  let body = {
    is_active: isActivateId.value.is_active
  };
  api.patch(`customer/api-tokens/${isActivateId.value.id}`, body)
    .then(() => {
      getApiTokens();
      dialogIsActiveVisible.value = false;
    }).finally(() => loader.value.hide());
};


const openEditDrawer = (item) => {
  editDrawer.value = true;
  tokenItem.value = item;
};


const closeEditDrawer = () => {
  editDrawer.value = false;
  getApiTokens();
};

const closeDrawer = () => {
  newApiTokenDrawer.value = false;
  getApiTokens();
};

const closeDialog = () => {
  dialogIsActiveVisible.value = false;
};


</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{ t("Api Settings") }} </span>
    </template>
    <template #content>
      <div
        class="border-b border-slate-300 text-base p-5 text-slate-700  bg-slate-50 flex items-center justify-between">
        <div>
          Webhook
        </div>
        <div>
          <el-button

            class="items-center"
            @click="addNewWebHook"
          >
            <FontAwesomeIcon icon="plus" />
          </el-button>
        </div>
      </div>
      <template v-for="(webhook,index) in newWebhooks" :key="index">
        <NewWebHook :webhook="webhook" :webhookTypes="webhookTypes" @save="addWebHook" @remove="removeFromNewWebHook" />
      </template>
      <template v-for="(webhook,index) in webhooks" :key="index">
        <WebhookItem :webhook="webhook" :webhookTypes="webhookTypes" @refreshWebhook="refreshWebhook" />
      </template>

      <div class="flex items-center justify-between p-5 border-b bg-slate-50">
        <div class="text-lg text-slate-700">
          {{ t("Api Tokens") }}
        </div>
        <div>
          <el-button @click="openTokenDrawer">
            <FontAwesomeIcon icon="plus" />
          </el-button>
        </div>
      </div>
      <div v-for="item in apiTokens" class="flex flex-col border-b p-6 space-y-2">
        <div class="flex justify-between items-center">
          <div>
            <span class="font-bold text-base text-slate-700">{{ item.title }}</span>

            <div class="flex items-center justify-center ">
              <div class="text-slate-700">
                {{ t("Creation") }} :
              </div>
              <div class="text-gray-400 pl-1 pr-4">
                {{ dayjs(item.created_at).format("DD.MM.YYYY HH:mm") }}
              </div>
              <div class="text-slate-700" v-if="item.last_used_at">
                {{ t("Last Used") }}:
              </div>
              <div v-if="item.last_used_at" class="text-gray-400 px-1">
                {{ dayjs(item.last_used_at).format("DD.MM.YYYY HH:mm") }}
              </div>
            </div>
          </div>

          <div class="flex flex-col">
            <div class="pl-2">
              <span class="mr-1"> {{ t("Active") }}</span>
              <el-switch v-model="item.is_active" size="small" @change="openModal(item)"></el-switch>
            </div>
          </div>
        </div>

        <div class="flex">
          <el-input
            disabled
            v-model="item.token"
            :type="item.showPassword ? 'text' : 'password'"
            class="grow"
            placeholder="Please input"
          >

            <template #append>
              <FontAwesomeIcon v-if="item.showPassword" @click="togglePasswordVisibility(item)"
                               class="text-indigo-600 cursor-pointer" icon="eye">
              </FontAwesomeIcon>
              <FontAwesomeIcon class="cursor-pointer" v-if="!item.showPassword" @click="togglePasswordVisibility(item)"
                               icon="eye-slash">
              </FontAwesomeIcon>
            </template>
          </el-input>

          <el-button-group class="ml-3 flex-none">
            <el-button @click="copyToken(item.token)">
              <FontAwesomeIcon class="cursor-pointer" icon="copy">
              </FontAwesomeIcon>
            </el-button>
            <el-button @click="openEditDrawer(item)">
              <FontAwesomeIcon icon="pen"></FontAwesomeIcon>
            </el-button>
            <el-button @click="deleteToken(item.id)" type="danger" :disabled="!item.is_deletable">
              <FontAwesomeIcon icon="trash">
              </FontAwesomeIcon>
            </el-button>
          </el-button-group>

        </div>
        <div class="flex gap-1">
          <el-tag v-for="hub in item.hubs" type="primary">
            {{ hub.name }}
          </el-tag>
        </div>
      </div>
      <div
        class="flex-grow  items-center border-b border-slate-300 settings-input-row text-sm font-medium text-gray-500"
      >
        <div class="p-5">
          {{
            t("It contains API information with which you can integrate your business processes in detail. Integration Documents")
          }}:
          <span @click="openDoc" class="cursor-pointer text-indigo-600">{{ doc_url }}</span>
        </div>
      </div>

      <el-drawer
        v-model="newApiTokenDrawer"
        class="customized-drawer"
        :title="$t('Create Token')"
        append-to-body
        destroy-on-close
      >
        <NewApiToken @close="closeDrawer" />
      </el-drawer>
      <el-drawer
        v-model="editDrawer"
        class="customized-drawer"
        :title="$t('Edit Token')"
        append-to-body
        destroy-on-close
      >
        <ApiTokenEdit @close="closeEditDrawer" :payload="tokenItem" />
      </el-drawer>
      <el-dialog
        v-model="dialogIsActiveVisible"
        :width="300"

        :title=" t('Are you sure?')"

      >
        <div class="dialog-footer flex items-center justify-end">
          <el-button
            @click="closeDialog"
            size="small"
          >
            {{ t("Cancel") }}
          </el-button>
          <el-button
            size="small"
            :type="isActivateId.is_active?'primary':'danger'"
            @click="tt"
          >
            {{ t("Yes") }}
          </el-button>
        </div>
      </el-dialog>
    </template>
  </SettingsPageLayout>
</template>
