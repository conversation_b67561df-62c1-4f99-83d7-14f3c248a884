<script>
import DetailButton from "@/renderers/DetailButton.vue";
import LogStatusRenderer from "@/renderers/LogStatusRenderer.vue";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    DetailButton,
    LogStatusRenderer,
  },
};
</script>

<script setup>
import WebhookLogDetail from "@/components/settings/drawers/detail/WebhookLogDetail.vue";
import {inject, onActivated, ref,onMounted} from 'vue';
import {useI18n} from "vue-i18n";
import formatter from "@/class/formatter";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";

const {t} = useI18n()
const api = inject("api")

const selectedLog = ref()
const logDetailDrawer = ref(false)
const grid = ref()


const columnDefs = [
  {
    field: "response_code",
    headerName: "Response Code",
    cellRenderer: "LogStatusRenderer",
    filter: 'agTextColumnFilter',
    export: true,
    cellClass: 'stringType',
  },
  {
    field: "webhook_type",
    headerName: "Webhook Type",
    filter: 'agTextColumnFilter',
    export: true,
    cellClass: 'stringType',
  },
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    export: true,
    cellClass: 'dateType',
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openDetail(params);
      },
    },
  },
]


const openDetail = (id) => {
  logDetailDrawer.value = true
  selectedLog.value = id
}

const closeDetail = () => {
  logDetailDrawer.value = false
}

onActivated(() => {
  grid.value && grid.value.refresh()
})

onMounted(()=>{
  grid.value && grid.value.refresh()
})

</script>
<template>
  <SettingsPageLayout>
    <template #header>
      <div class="m-3">
        <span class="text-lg font-bold text-slate-700"> {{ t('Webhook Logs') }} </span>
      </div>
    </template>
    <template #content>
      <SSDataGrid
          ref="grid"
          url="customer/webhook-logs"
          :columns="columnDefs"
          :auto-size-column="false"
      />
    </template>
    <template #drawers>
      <el-drawer
          v-model="logDetailDrawer"
          class="customized-drawer customized-drawer"
          :title="t('Webhook Detail')"
          append-to-body
          destroy-on-close
      >
        <WebhookLogDetail :id="selectedLog" @close="closeDetail"/>
      </el-drawer>
    </template>
  </SettingsPageLayout>
</template>
