<script>
import NotificationTemplatesButton from "@/renderers/NotificationTemplatesButton.vue";

export default {
  components: {
    NotificationTemplatesButton
  }
};
</script>
<script setup>
import { ref, inject, onMounted } from "vue";
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useI18n } from "vue-i18n";
import NewNotificationTemplates from "@/components/settings/drawers/new/NewNotificationTemplates.vue";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import formatter from "@/class/formatter";
import { useToast } from "vue-toastification";
import NotificationEdit from "@/components/settings/drawers/edit/NotificationEdit.vue";

const api = inject("api");
const notification = ref();
const { t } = useI18n();
const loader = ref();
const deleteDialogVisible = ref(false);
const notificationTemplatesVisible = ref(false);
const editDrawerVisible = ref(false);
const toast = useToast();
const grid = ref();

const columnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: "agTextColumnFilter"
  },
  {
    field: "name",
    headerName: t("Name"),
    filter: "agTextColumnFilter"


  },
  {
    field: "template_type.name",
    headerName: t("Template Name"),
    filter: "agTextColumnFilter"
  },
  {
    field: "notification_type.name",
    headerName: t("Notification Name"),
    filter: "agTextColumnFilter"
  },
  {
    field: "created_at",
    headerName: t("Created Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "updated_at",
    headerName: t("Updated Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 100,
    cellStyle: { textAlign: "center" },
    cellRenderer: "NotificationTemplatesButton",
    cellRendererParams: {
      onComplete: (params) => {

        notification.value = params;
        editDrawerVisible.value = true;
      },
      onDelete: (params) => {

        notification.value = params;
        deleteDialogVisible.value = true;
      }
    }
  }
]);

const deleteNotificaitons = () => {
  loader.value.show();
  api.delete("customer/notification-templates/" + notification.value.id)
    .then(res => {
      grid.value.refresh();
      deleteDialogVisible.value = false;
      toast.success(t("Notification Templates Deleted Successfully"));
    })
    .finally(() => {
      loader.value.hide();
    });
};



const closeDeleteDialog = () => {
  deleteDialogVisible.value = false;
  grid.value.refresh();
};


const openNotificationTemplatesDrawer = () => {
  notificationTemplatesVisible.value = true;
};

const closeNewDrawer = () => {
  notificationTemplatesVisible.value = false;
  grid.value.refresh()
};

const closeEditDrawer = () => {
  editDrawerVisible.value = false;
  grid.value.refresh()
};
</script>

<template>
  <LoadingBlock ref="loader" />
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{ t("Notification Templates") }} </span>
      <div class="flex items-center">
<!--        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">-->
<!--          <el-button-->

<!--            class="mr-3 items-center"-->
<!--            size="small"-->

<!--          >-->
<!--            <FontAwesomeIcon icon="refresh" size="lg" />-->
<!--          </el-button>-->
<!--        </ButtonToolTip>-->
        <ButtonToolTip  :tooltipText="$t('New')" position="left">
          <el-button
            class="mr-3 items-center"
            size="small"
            @click="openNotificationTemplatesDrawer"
          >
            <FontAwesomeIcon icon="plus" size="lg" />
          </el-button>
        </ButtonToolTip>
      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div class="flex-grow">
          <SSDataGrid
            ref="grid"
            :columns="columnDefs"
            url="customer/notification-templates"
            :auto-size-column="false"
          />
        </div>
      </div>
    </template>
  </SettingsPageLayout>
  <el-drawer
    v-model="notificationTemplatesVisible"
    class="customized-drawer"
    :title="t('New Notification Templates')"
    destroy-on-close
    append-to-body
  >
    <NewNotificationTemplates  @close="closeNewDrawer"/>
  </el-drawer>
  <el-dialog v-model="deleteDialogVisible" width="30%" center>
    <div class="text-center text-lg text-slate-700 font-bold">
      {{ t("Are you sure you want to delete?") }}
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDeleteDialog">{{ t("Cancel") }}</el-button>
        <el-button @click="deleteNotificaitons" type="danger">
          {{ t("Delete") }}
        </el-button>
      </span>
    </template>
  </el-dialog>
  <el-drawer
    v-model="editDrawerVisible"
    class="customized-drawer"
    :title="t('Notification Templates Edit')"
    destroy-on-close
    append-to-body

  >
    <NotificationEdit @close="closeEditDrawer" :notification="notification" />
  </el-drawer>
</template>

<style scoped>

</style>