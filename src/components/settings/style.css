/* .ag-theme-quartz {
    --ag-row-border-color: 'red';
    --ag-row-border-style: 'solid';
    --ag-borders: '2px solid red'
} */

.custom-header {
    display: flex;
    flex: 1;
    flex-direction: column;
    /* background-color: yellow; */
}

.cust-header-btn {
    /* height: 30px; */
    /* background-color: yellowgreen; */
    /* align-items: center; */
    /* justify-content: center; */
    display: flex;
}

.ag-theme-balham .ag-header {
    /* background-color: aqua; */
    height: 80px !important;
}

.ag-theme-balham .ag-header-row {
    /* background-color:violet; */
    height: 100% !important;
}

.ag-theme-balham .ag-header-cell-comp-wrapper {
    padding: 0 !important;
}

.cust-title-con {
    /* background-color: rebeccapurple; */
    height: 30px;
    display: flex;
    align-items: center;
    /* justify-content: center; */
}