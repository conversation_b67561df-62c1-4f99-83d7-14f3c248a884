<script>
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import DetailButton from "@/renderers/DetailButton.vue";
import StatusActivePassiveRenderer from "@/renderers/StatusActivePassiveRenderer.vue";

export default {
  components: {
    StatusRenderer,
    DetailButton,
    StatusActivePassiveRenderer
  },
};
</script>

<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import NewHub from "@/components/settings/drawers/new/NewHub.vue";
import {inject, onActivated, ref,onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import HubActions from "@/components/settings/drawers/actions/HubActions.vue";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import {useI18n} from 'vue-i18n'
import {Loader} from "@googlemaps/js-api-loader";
import {useToast} from "vue-toastification";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const toast = useToast()
const {t} = useI18n()
const api = inject("api");

const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const selectedRows = ref();
const selectedHub = ref(null);
const loader = ref();
const newHubDrawerVisible = ref(false);
const hubDetailDrawerVisible = ref(false);
const hubs = ref([]);
const gridApi = ref(null);


const columnDefs = ref([
  {
    field: "id",
    headerName: "Id",
    width: 90,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
    filter: 'agTextColumnFilter',
  },
  {
    field: 'is_active',
    headerName: t('Status'),
    filter: "agSetColumnFilter",
    width: 120,
    valueFormatter: (params) => params.value ? t("Active") : t("Passive"),
    cellRenderer: "StatusActivePassiveRenderer",
    filterParams: {
      valueFormatter: (params) => {
        if (params.value === 'true'){
          return  t("Active")
        }
        else if (params.value === 'false'){
          return t("Passive")
        }
      }
    }

  },
  {
    field: "integration_id",
    headerName: t("Integration Id"),
    width: 90,
    filter: 'agTextColumnFilter',
  },
  {
    field: "max_distance",
    headerName: t("Max Distance (m)"),
    width: 90,
    filter: 'agTextColumnFilter',

  },

  {
    field: "name",
    headerName: t("Hub Name"),
    filter: 'agTextColumnFilter',
  },
  {field: "address", headerName: t("Address"), filter: 'agTextColumnFilter',},
  {field: "lat", headerName: t("Latitude"), filter: 'agTextColumnFilter',},
  {field: "lng", headerName: t("Longitude"), filter: 'agTextColumnFilter',},
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "DetailButton",
    cellRendererParams: {
      clicked: (params) => {
        openHubDetail(params);
      },
    },
  },
]);

onActivated(() => {
  getHubs();
});
onMounted(() => {
  getHubs();
});


function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

function handleSelectionChanged() {
  selectedRows.value = gridApi.value.getSelectedRows();
}

const openNewHubDrawer = () => {
  newHubDrawerVisible.value = true;
};

const closeNewHubDrawer = (param) => {
  newHubDrawerVisible.value = false;
  if (param) {
    getHubs();
  }
};

const openHubDetail = (params) => {
  selectedHub.value = hubs.value.find(i => i.id === params);
  hubDetailDrawerVisible.value = true;
};

const getHubs = () => {
  loader.value.show();
  api("customer/hubs")
      .then((res) => hubs.value = res.data)
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide());
};

const onRefresh =()=>{
  getHubs()
}


</script>

<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{ t("Hubs") }} </span>
      <div class="flex items-center">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom" >
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" size="lg"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Hub')" position="bottom" >
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="openNewHubDrawer"
          >
            <FontAwesomeIcon icon="plus" size="lg"/>
          </el-button>
        </ButtonToolTip>

      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxs border-b border-slate-300"
        >
          <div class="flex items-center justify-between flex-grow">
            <div class="flex px-1">
              <div class="px-1 py-2">
                {{ t("Total Hub") }}:
                <span class="font-bold">{{ hubs.length }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow">
          <DataGrid
              v-model="gridApi"
              :dataSource="hubs"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              @handleSelectionChanged="handleSelectionChanged"
              :autoSizeColumn="true"
          />
        </div>
      </div>
    </template>
    <template #drawers>
      <el-drawer
          v-model="newHubDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('New Hub')"
          append-to-body
          destroy-on-close
      >
        <NewHub @close="closeNewHubDrawer"/>
      </el-drawer>
      <HubActions v-model="hubDetailDrawerVisible" :selectedHub="selectedHub" @refresh="getHubs"/>
    </template>
  </SettingsPageLayout>
</template>
