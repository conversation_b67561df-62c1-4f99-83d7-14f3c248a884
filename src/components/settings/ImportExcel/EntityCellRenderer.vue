<script setup>
import { inject, defineProps, ref, onMounted, watch } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import dayjs from "dayjs";

const props = defineProps({
  cell: { type: String },
  line: { type: Object },
  isHeader: { type: Boolean },
  refreshList: {
    type: Function
  }
});

const emits = defineEmits(["showLoader", "hideLoader", "openModal"]);
const toast = useToast();
const api = inject("api");

const { t } = useI18n();

const headerNames = {
  id: t("ID"),
  task_id: t("QDelivery Task Id"),
  destination_name: t("Destination Name"),
  destination_address: t("Destination Address"),
  resolved_destination_address: t("Resolved Destination Address"),
  destination_lat: t("Destination Lat"),
  destination_lng: t("Destination Long"),
  starts_at: t("Started Time"),
  ends_at: t("Ended Time"),
  origin_address: t("Origin Address"),
  origin_lat: t("Origin Lat"),
  origin_lng: t("Origin Long")
};

const item = ref(null);

onMounted(() => {
  setFields();
});

watch(() => props.line.loader, () => {

  setFields();
}, { deep: true });

const setFields = () => {


  if (props.isHeader) {
    item.value = headerNames[props.cell];
  } else if (props.cell === "id") {

    if (props.line.loader) {
      item.value = "Loader";
    } else {
      item.value = props.line[props.cell];
    }


  } else if (props.cell === "task_id") {
    if (!props.line.allow_import && props.line.loader && !!!props.line.task_id) {
      item.value = "Loader";
    } else if (!props.line[props.cell]) {
      item.value = t("Not Transferred");
    } else {
      item.value = props.line[props.cell];
    }
  } else if (props.cell === "starts_at" || props.cell === "ends_at") {
    item.value = dayjs(props.line[props.cell]).format("DD/MM/YYYY hh:mm");
  } else {
    item.value = props.line[props.cell];
  }
};


const handleClickAllowImport = (val) => {
  emits("showLoader", true);
  const import_id = props.line.import_id;
  const id = props.line.id;
  api.post(`customer/import/tasks/${import_id}/entities/${id}/allow`, { allow: val })
    .then((res) => {

      props.refreshList(import_id);
    })
    .catch((err) => {

      toast.error(err?.message);
    })
    .finally(() => {
      emits("hideLoader", true); // api.post işlemi başarılı olsun ya da olmasın hideLoader fonksiyonunu çağır
    });
};


const unresolved_dest = !props.line.destination_lat || !props.line.destination_lng;
const unresolved_origin = (!props.line.origin_lat || !props.line.origin_lng);
// to do : convert tasks butonunun aktifliği ile ilgili caseler yazılacak.

const dest_styles = {
  icon: unresolved_dest ? "fa-location-dot-slash" : "fa-location-dot",
  color: unresolved_dest ? "#FF1441" : "#26EB73"
};

const origin_styles = {
  icon: unresolved_origin ? "fa-location-dot-slash" : "fa-location-dot",
  color: unresolved_origin ? "#FF1441" : "#26EB73"
};

// const themeBgColor = props.isHeader ? "#f8fafc" : unresolved_dest ? "#fff3f5" : !!props.line.task_id ? "white" : "#f4fef8";

const handleAddressClick = (isOrigin) => {
  emits("openModal", isOrigin);
};

</script>

<template>
  <div v-bind:style="{ backgroundColor: props.isHeader ? '#f8fafc' : unresolved_dest ? '#fff3f5' : !!props.line.task_id ? 'white' : '#f4fef8' }"
       :class="(!isHeader || !item) && 'text-center'">
    <div v-if="cell === 'allow_import'"
         class="w-12 default__cell"
         style="text-wrap: nowrap; overflow: hidden;">
      <el-checkbox v-show="!props.line['task_id'] && !unresolved_dest"
                   v-model="item"
                   @change="handleClickAllowImport"
                   size="small"
                   :disabled="unresolved_dest" />
    </div>
    <div v-else-if="cell === 'id'"
         class="w-16 default__cell items-center flex justify-center">
      <img v-if="!isHeader && unresolved_dest"
           src="../../../assets/images/loading.svg"
           class="h-4 self-center"
           alt />
      <img v-else-if="!isHeader && item === 'Loader'"
           src="../../../assets/images/loading.svg"
           class="h-4 self-center"
           alt />
      <span v-else>{{ item }}</span>
    </div>
    <div v-else-if="cell === 'destination_address'"
         class="w-96 default__cell flex flex-row items-center showicon"
         @click="!props.line['task_id'] && handleAddressClick(false)">
      <FontAwesomeIcon v-show="!isHeader && !!!props.line['task_id']"
                       icon="pen-to-square"
                       :style="{ color: '#1450ff' }"
                       class="mx-1 hiddenicon" />
      <FontAwesomeIcon v-show="!isHeader"
                       :icon="dest_styles.icon"
                       :style="{ color: dest_styles.color }"
                       class="ml-1 mr-2" />
      <span :class="['flex-1 flex', !isHeader && !props.line['task_id'] && 'underline cursor-pointer']"
            style="text-overflow: ellipsis;">
        {{ item }}
      </span>
    </div>
    <!-- origin  -->
    <div v-else-if="cell === 'origin_address'"
         class="w-96 default__cell flex flex-row items-center showicon"
         @click="!props.line['task_id'] && handleAddressClick(true)">
      <FontAwesomeIcon v-show="!isHeader && !!!props.line['task_id']"
                       icon="pen-to-square"
                       :style="{ color: '#1450ff' }"
                       class="mx-1 hiddenicon" />
      <FontAwesomeIcon v-show="!isHeader && (origin_styles.icon === 'fa-location-dot-slash' ? !!props.line['origin_address'] : true)"
                       :icon="origin_styles.icon"
                       :style="{ color: origin_styles.color }"
                       class="mx-2" />
      <span :class="['flex-1 flex', !isHeader && !props.line['task_id'] && 'underline cursor-pointer']"
            style="text-overflow: ellipsis;">
        {{ item }}
      </span>
    </div>
    <!-- /// -->
    <div v-else-if="cell === 'task_id'"
         :class="['w-48 default__cell flex', !isHeader && 'items-center justify-center flex']">
      <img v-if="!isHeader && item === 'Loader'"
           src="../../../assets/images/loading.svg"
           class="h-4 self-center"
           alt />
      <span v-else>{{ item }}</span>
    </div>
    <div v-else-if="cell === 'resolved_destination_address'"
         class="w-96 default__cell text-left"
         style="padding-left: 8px;"
         v-bind:style="!isHeader && 'padding-left:10px'"
         >
      {{ item }}
    </div>
    <div v-else
         class="w-48 default__cell">
      {{ item }}
    </div>
  </div>
</template>

<style scoped>
.default__cell {
  text-wrap: nowrap;
  overflow: hidden;
  border-right: 1px solid #eaeaea;
  padding: 4px;
  padding-top: 7px;
  padding-bottom: 2px;
  /* text-align: center; */
  color: #334155;
  text-overflow: ellipsis;
  font-size: 12px !important;
  height: 100%
}

.hiddenicon {
  display: none;
}

.showicon:hover .hiddenicon {
  display: block;
}
</style>