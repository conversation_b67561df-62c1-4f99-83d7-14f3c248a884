<script setup>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { computed, defineEmits } from "vue";
import { useI18n } from "vue-i18n";

const emit = defineEmits(["update:modelValue", "select", "change"]);

const {t} = useI18n();

const props = defineProps({
    label: { type: String },
    options: { type: Array },
    modelValue: { type: Object },
    placeholder: String,
    hideInfo: { type: Boolean },
    hideBorder: { type: Boolean },
    disabled: { type: Boolean }
});

const val = computed({
    get() {
        return props.modelValue;
    },
    set(newValue) {
        emit("update:modelValue", newValue);
    },
});

const onChange = (data) => {
    emit("change", data)
    props.multiple && emit("select", data.find(x => x[props.value] === data))
}

</script>
<template>
    <div class="flex flex-row items-center">
        <div
            v-bind:style="[!props.hideBorder ? 'border: 1px solid #f0f0f0;' : 'border: none;']"
            class="flex items-center flex-1"
        >
            <div
                class="font-semibold w-2/6 pl-1"
                style="color: #96beed"
            >{{ label }}</div>
            <el-select
                filterable
                :placeholder="t('Select Team')"
                class="w-4/6 custom-drop-container"
                v-model="val"
                @change="onChange"
                :disabled="props.disabled"
            >
                <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
        </div>
      <el-popover
        placement="left"
        :width="540"
        trigger="hover"
        content="this is content, this is content, this is content"
      >
        <div>
          <div>
            Atamak istediğiniz bir takım seçebilir veya Takımı Otomatik Algıla seçerek uygun takımı sistemin bulmasına izin verebilirsiniz.
          </div>
        </div>
        <template #reference>
          <FontAwesomeIcon
            v-show="!props.hideInfo"
            icon="square-info"
            size="xl"
            class="mx-2"
            style="color: #96beed"
          />
        </template>
      </el-popover>

    </div>
</template>


<style scoped>
.el-input__wrapper {
    border-radius: 0;
}
</style>