<script setup>
import { reactive, ref, computed, watch } from "vue";
import { Options } from "@/class";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";

const { t } = useI18n()

const props = defineProps({
    teams: { type: Object },
    fileId: { type: String },
    entitiesList: { type: Object },
    slots: { type: Object },
    slotInfos: { type: Object },
    convertBtnState: { type: Boolean },
    assignWarn: { type: Boolean }
});

const extendedSlots = ref(['Create Slot', ...props.slots]);

const timesDisabled = ref(false);
const createSlotVal = ref('');
const startsAt = ref();
const endsAt = ref();
const assignmentType = ref('');
const slotValue = ref('');
const isLocked = ref(false);
const isConfirmed = ref(false);

const taskTab = ref('manuel');

const emit = defineEmits(["close", "startImport", "clearWarn"]);
const loader = ref();

const requirements = reactive({
    pod_photo_mandatory: false,
    pod_photo: false,
    delivery_code_mandatory: false,
    delivery_code: false
});

const assignment_options = reactive({
    team_id: null,
    team_id_by_polygon: false,
    courier_online_status: 'all',
    courier_duty_status: 'all',
    obligation: 'mandatory',
    distribution: 'balanced',
    courier_shift_status: '',
    min_capacity: 1
});

const setTaskTab = (value) => {
    taskTab.value = value;
};

watch(() => taskTab.value, () => {
    if (taskTab.value === 'instant') {
        assignment_options.courier_shift_status = 'all'
        assignment_options.courier_online_status = 'all'
        assignment_options.courier_duty_status = 'all'
    } else if (taskTab.value === 'routed') {
        assignment_options.courier_shift_status = 'all'
    }
})

const entitiesCount = computed(() => props.entitiesList?.filter((e) => { return e.allow_import && !e.task_id}).length);

watch(() => assignmentType.value, () => {
    emit('clearWarn')
})

const handleImportCreate = () => {

    var payload = {
        assignment_options: {
            team_id: assignment_options.team_id,
            team_id_by_polygon: assignment_options.team_id_by_polygon,
        },
        requirements: {
            pod_photo_mandatory: requirements.pod_photo_mandatory,
            pod_photo: requirements.pod_photo,
            delivery_code_mandatory: requirements.delivery_code_mandatory,
            delivery_code: requirements.delivery_code
        },
        // service_time: serviceTime.value ? parseInt(serviceTime.value) : 0,
        assignment: {
            type: 'draft'
        }
        // locked: isLocked.value,
        // confirmed: isConfirmed.value
    };

    if (taskTab.value === 'instant' || taskTab.value === 'routed') {
        payload = {
            ...payload,
            assignment_options: {
                ...payload.assignment_options,
                courier_online_status: assignment_options.courier_online_status,
                courier_duty_status: assignment_options.courier_duty_status,
                obligation: assignment_options.obligation,
                distribution: assignment_options.distribution,
                courier_shift_status: assignment_options.courier_shift_status,
                min_capacity: assignment_options.min_capacity
            }
        }
    }

    if (taskTab.value === 'instant') {
        payload.assignment = { type: assignmentType.value };
    }

    if (taskTab.value === 'routed') {
        payload.slot = createSlotVal.value
        // start
        const [h1, m1] = startsAt.value.split(":");
        const ms_start = new Date().setHours(h1, m1);
        const date_start = new Date(ms_start)
        payload.slot_starts_at = date_start;
        // end
        const [h2, m2] = endsAt.value.split(":");
        const ms_end = new Date().setHours(h2, m2);
        const date_end = new Date(ms_end)
        //
        payload.slot_ends_at = date_end;
        // opts are default values.
        payload.slot_options = {
            pool: false,
            tour: true,
            dispatch_early: false,
            from_hub: true,
            accelerate: false,
            unassigned_strategy: "nearest_plan",
        };

        payload.assignment = {
            type: 'slot'
        }

        if (timesDisabled.value) {
            payload = {
                ...payload,
                assignment_options: {
                    team_id: assignment_options.team_id,
                    team_id_by_polygon: assignment_options.team_id_by_polygon,
                    // min_capacity: 1
                }
            }
        }
    }


    emit('startImport', payload);
};

const showNewSlotInput = ref(false);

watch(() => taskTab.value, () => {
    timesDisabled.value = false;
})

watch(() => startsAt.value, () => {
    if (!createSlotVal.value) return;
    const newGen = createSlotVal.value.substring(0, 8) + startsAt.value.replace(':', '') + createSlotVal.value.substring(12, 16);;
    createSlotVal.value = newGen;
});

watch(() => endsAt.value, () => {
    if (!createSlotVal.value) return;
    const newGen = createSlotVal.value.substring(0, 12) + endsAt.value.replace(':', '');
    createSlotVal.value = newGen;
});

watch(() => slotValue.value, () => {
    if (slotValue.value === 'Create Slot') {
        showNewSlotInput.value = true;
        timesDisabled.value = false;
        createSlotVal.value = '';
        startsAt.value = '08:00';
        endsAt.value = '20:00';
        const dayjsDate = dayjs(new Date()).format('YYYYMMDD')
        const generatedName = dayjsDate + startsAt.value.replace(':', '') + endsAt.value.replace(':', '');

        createSlotVal.value = generatedName
        //
    } else {
        createSlotVal.value = slotValue.value;
        const selectedSlotInfo = props.slotInfos.find((s) => s.slot === slotValue.value);
        const tIndex = selectedSlotInfo.starts_at.indexOf('T') + 1;
        startsAt.value = selectedSlotInfo.starts_at.substring(tIndex, tIndex + 5);
        endsAt.value = selectedSlotInfo.ends_at.substring(tIndex, tIndex + 5);
        timesDisabled.value = true;
        showNewSlotInput.value = false;
    }
});

</script>

<template>
    <LoadingBlock ref="loader" />
    <div class="w-full h-full flex bg-white">
        <div class="flex-grow relative">
            <div class="absolute inset-0 overflow-y-auto px-2.5 pb-24">
                <div class="flex flex-col flex-1 right__container">
                    <span class="text-lg border-b pb-2 mb-2">{{ t('Import Settings') }}</span>
                    <el-checkbox
                        v-model="assignment_options.team_id_by_polygon"
                        size="small"
                        class="mb-2"
                    >
                        <span class="text-sm text-slate-700">
                            {{ t('Auto detect team') }}
                        </span>
                    </el-checkbox>
                    <SelectBoxWithInfo
                        :label="t('Team Select')"
                        :options="teams"
                        v-model="assignment_options.team_id"
                        :placeholder="t('Select Team')"
                        :disabled="assignment_options.team_id_by_polygon"
                        :hideBorder="true"
                    />
                    <span class="text-slate-700 text-sm mt-3 mb-2.5">{{ t('Task Strategy') }}</span>
                    <div class="flex flex-row w-full items-center mb-3">
                        <div
                            style="border-radius: 4px;border:1px solid #f0f0f0;"
                            class="flex flex-row flex-1 py-2 justify-around cursor-pointer text-slate-300 font-semibold"
                        >
                            <div
                                @click="setTaskTab('manuel')"
                                :class="['flex-1 text-center border-r', taskTab === 'manuel' && 'custom_primary_color']"
                            >{{ t('Manual') }}</div>
                            <div
                                @click="setTaskTab('instant')"
                                :class="['flex-1 text-center border-r', taskTab === 'instant' && 'custom_primary_color']"
                            >{{ t('Instant') }}</div>
                            <div
                                @click="setTaskTab('routed')"
                                :class="['flex-1 text-center', taskTab === 'routed' && 'custom_primary_color']"
                            >{{ t('Routed') }}</div>
                        </div>
                      <el-popover
                        placement="left"
                        :width="560"
                        trigger="hover"
                        content="this is content, this is content, this is content"
                      >
                        <div>
                         <div>
                           <span class="font-bold">Manuel</span>: Rotayı manuel olarak oluşturabilir ve düzenleyebilirsiniz.
                         </div>
                          <div>
                            <span class="font-bold"> Anlık</span>: Anlık olarak belirlenen durumlara göre rotalama yapar.
                          </div>
                          <div>
                            <span class="font-bold">Rotalanacak Siparişler</span>: Belirli bir slota göre siparişleri kümelenip rotalama yapar.
                          </div>
                        </div>
                        <template #reference>
                          <FontAwesomeIcon
                          icon="square-info"
                          size="xl"
                          class="mx-2"
                          style="color: #96beed"
                        />
                        </template>
                      </el-popover>

                    </div>
                    <div>
                        <div v-show="taskTab === 'instant'">
                            <span class="text-slate-700 text-sm">{{ t('Automatic Assign') }}</span>
                            <el-select
                                v-model="assignmentType"
                                placeholder="Select"
                                :class="['w-full-important mt-2 mb-2', props.assignWarn && 'warn_assign']"
                            >
                                <el-option
                                    v-for="item in Options.assignment_options.filter((o) => o.slug !== 'slot')  "
                                    :key="item.slug"
                                    :label="t(item.title)"
                                    :value="item.slug"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div v-show="taskTab === 'routed'">
                            <span class="text-slate-700 text-sm">{{ t('Slot Select') }}</span>
                            <el-select
                                v-model="slotValue"
                                :placeholder="t('Select Slot')"
                                class="w-full-important mt-2 mb-2"
                            >
                                <el-option
                                    v-for="item in extendedSlots"
                                    :key="item"
                                    :label="item === 'Create Slot' ? t('Create Slot') : item"
                                    :value="item"
                                >
                                </el-option>
                            </el-select>
                            <el-input
                                v-show="showNewSlotInput"
                                v-model="createSlotVal"
                            />
                            <div class="flex flex-row gap-3 my-2">
                                <el-time-select
                                    v-model="startsAt"
                                    start="00:00"
                                    end="23:59"
                                    :disabled="timesDisabled"
                                    :placeholder="t('Start Time')"
                                >
                                </el-time-select>
                                <el-time-select
                                    v-model="endsAt"
                                    start="00:00"
                                    end="23:59"
                                    :disabled="timesDisabled"
                                    :placeholder="t('End Time')"
                                >
                                </el-time-select>
                            </div>
                        </div>
                        <div
                            v-show="!timesDisabled && (taskTab === 'instant' || taskTab === 'routed')"
                            class="flex flex-row"
                        >
                            <div class="w-1/2">
                                <CustomRadio
                                    :options="[
                                        {
                                            value: 'working',
                                            label: t('Working')
                                        }, {
                                            value: 'resting',
                                            label: t('Resting')
                                        }
                                    ]"
                                    :setSelection="(value) => {
                                        assignment_options.courier_shift_status = value;
                                    }"
                                    :label="t('Working Status')"
                                    :multiple="true"
                                    :selection="assignment_options.courier_shift_status"
                                />
                            </div>
                            <div class="w-1/2 ml-4">
                                <CustomRadio
                                    :options="[{
                                        value: 'on_duty',
                                        label: t('On Duty')
                                    }, {
                                        value: 'idle',
                                        label: t('Idle')
                                    }]"
                                    :setSelection="(value) => {
                                        assignment_options.courier_duty_status = value;
                                    }"
                                    :label="t('Duty Status')"
                                    :multiple="true"
                                    :selection="assignment_options.courier_duty_status"
                                />
                            </div>
                        </div>

                        <div
                            v-show="!timesDisabled && (taskTab === 'instant' || taskTab === 'routed')"
                            class="flex flex-row mt-2"
                        >
                            <div class="w-1/2">
                                <CustomRadio
                                    :options="[{
                                        value: 'online',
                                        label: t('Online')
                                    }, {
                                        value: 'offline',
                                        label: t('Offline')
                                    }]"
                                    :setSelection="(value) => {
                                        assignment_options.courier_online_status = value;
                                    }"
                                    :label="t('Online Status')"
                                    :multiple="true"
                                    :selection="assignment_options.courier_online_status"
                                />
                            </div>
                            <div class="w-1/2 ml-4">
                                <CustomRadio
                                    :options="[{
                                        value: 'mandatory',
                                        label: t('Mandatory')
                                    }, {
                                        value: 'optional',
                                        label: t('Optional')
                                    }]"
                                    :setSelection="(value) => {
                                        assignment_options.obligation = value;
                                    }"
                                    :label="t('Obligation')"
                                    :selection="assignment_options.obligation"
                                />
                            </div>
                        </div>
                        <!-- // to do entity line ları component haline getir  -->
                        <div
                            v-show="!timesDisabled && (taskTab === 'instant' || taskTab === 'routed')"
                            class="w-1/2 pr-2 mt-2"
                        >
                            <CustomRadio
                                :options="[{
                                    value: 'balanced',
                                    label: t('Balanced')
                                }, {
                                    value: 'adaptive',
                                    label: t('Adaptive')
                                }]"
                                :setSelection="(value) => {
                                    assignment_options.distribution = value;
                                }"
                                :label="t('Distribution')"
                                :selection="assignment_options.distribution"
                            />
                        </div>
                    </div>

                    <span class="text-slate-700 text-sm mb-2.5 mt-3">{{ t('Task Rules') }}</span>
                    <div class="flex flex-row items-center w-full pale__title">
                        <div class="flex flex-1 justify-between">
                            <div class="flex flex-col">
                                <div class="flex flex-row items-center justify-between px-2 flex-1">
                                    {{ t('SMS Code') }}
                                    <el-switch
                                        class="ml-4"
                                        v-model="requirements.delivery_code"
                                    />
                                </div>
                            </div>
                            <div class="flex flex-col">
                                <div class="flex flex-row items-center justify-between px-2 flex-1 ml-3">
                                    {{ t('Photo') }}
                                    <el-switch
                                        class="ml-4"
                                        v-model="requirements.pod_photo_mandatory"
                                    />
                                </div>
                            </div>
                        </div>

                      <el-popover
                        placement="left"
                        :width="570"
                        trigger="hover"
                        content="this is content, this is content, this is content"
                      >
                        <div>
                          <div>
                            <span class="font-bold">SMS Kodu</span>: Sürücünün müşteriye giden SMS OTP şifresi ile teslim edilmesi beklenir.
                          </div>
                          <div>
                            <span class="font-bold">Fotoğraf</span>: Sürücünün teslimata dair bir fotoğraf çekmesi beklenir.
                          </div>
                        </div>
                        <template #reference>
                          <FontAwesomeIcon
                            icon="square-info"
                            size="xl"
                            class="mx-2"
                            style="color: #96beed"
                          />
                        </template>
                      </el-popover>


                    </div>
                    <div v-show="false">
                        <span class="text-slate-700 text-sm">{{ ' ' }}</span>
                        <div class="flex flex-row items-center w-full pale__title">
                            <div class="flex flex-1 justify-between">
                                <div class="flex flex-col flex-1">
                                    <div class="flex flex-row items-center justify-between border px-2 flex-1">
                                        {{ t('Locked') }}
                                        <el-switch v-model="isLocked" />
                                    </div>
                                </div>
                                <div class="flex flex-col flex-1">
                                    <div class="flex flex-row items-center justify-between border px-2 flex-1 ml-3">
                                        {{ t('Confirmed') }}
                                        <el-switch v-model="isConfirmed" />
                                    </div>
                                </div>
                            </div>
                            <FontAwesomeIcon
                                icon="square-info"
                                size="xl"
                                class="mx-2"
                                style="color: #96beed"
                            />
                        </div>
                    </div>

                </div>
            </div>
            <div
                class="flex justify-end absolute bottom-4 w-full bg-white pr-4 py-2"
                style="padding-bottom: 24px"
            >
                <div class="w-full flex flex-row items-center justify-end">
                    <span class="font-light">{{ t('x selected tasks', { message: entitiesCount }) }}</span>
                    <el-button
                        type="primary ml-2"
                        @click="handleImportCreate"
                        :disabled="!props.convertBtnState"
                    >
                        {{ t('Convert Tasks') }}
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.el-input__wrapper {
    border-radius: 0 !important;
}

.warn_assign {
    border: 2px solid #ffc007;
}
</style>