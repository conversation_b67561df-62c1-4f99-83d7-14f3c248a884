<script setup>

const props = defineProps({
    options: { type: Array },
    setSelection: { type: Function },
    selection: { type: String },
    label: { type: String },
    multiple: { type: Boolean }
});

const handleSelection = (opt) => {
    var _selection = '';
    if (props.multiple) {
        if (props.selection === 'all') {
            _selection = props.options.find((o) => o.value != opt.value);
            _selection = _selection?.value;
        } else if (props.selection === '') {
            _selection = opt.value;
        }
        else if (props.selection == opt.value) {
            _selection = ''
        } else {
            _selection = 'all';
        }
    } else {
        _selection = opt.value;
    }
    props.setSelection(_selection);
};

</script>

<template>
    <span class="text-slate-700 text-sm">{{ label }}</span>
    <div
        style="border:1px solid #f0f0f0;"
        class="flex flex-row flex-1 py-2 justify-around cursor-pointer text-slate-300 font-semibold mt-2"
    >
        <div
            v-for="(option, ind) in options"
            @click="() => {
                handleSelection(option)
            }"
            :class="[(selection == option.value || selection === 'all') && 'custom_primary_color',
            ind == 0 && 'border-r',
                'flex-1 text-center']"
        >{{ option?.label }}</div>
    </div>
</template>