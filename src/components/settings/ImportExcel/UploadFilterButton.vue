<script setup>
import { computed } from "vue";

const props = defineProps({
    isActive: { type: Boolean },
    filters: { type: Object },
    type: String
});

const colors = {
    assigned: 'gray',
    assignable: '#26EB73',
    in_progress: '#ff1441'
};

const icons = {
    assigned: 'fa-check',
    assignable: 'fa-location-dot',
    in_progress: 'fa-location-dot-slash'
};
const emits = defineEmits(["handleFilterPress"])

const color = computed(() => {
    return props.filters[props.type] ? colors[props.type] : 'gray';
})

</script>
<template>
    <FontAwesomeIcon
        @click="() => {
            emits('handleFilterPress', type)
        }"
        class="ml-1 w-4 h-4 border p-2 rounded"
        :icon="icons[type]"
        :style="{ color, borderColor: '#f0f0f0' }"
    />
</template>