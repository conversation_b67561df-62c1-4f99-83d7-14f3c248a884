<script setup>
import { groupBy } from "@/class/helpers";
import { defineProps, ref, onMounted, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const { t } = useI18n();

const props = defineProps({
  handleOptionChange: (a, b) => {
  },
  columnIndex: Number,
  columnList: Object,
  value: { type: Object },
  id: { type: Number },
  columnLength: { type: Number },
  mappingCompleted: { type: <PERSON>olean },
  mappingList: { type: Object }
});

const emits = defineEmits(["change"]);

const selectedValue = ref();
const dropdownState = ref(false);

const handleClick = (e) => {
  e.preventDefault();
  dropdownState.value = !dropdownState.value;
};

onMounted(() => {
  selectedValue.value = props.columnList?.find((x) => x.slug === "not_mapped");
  // console.log('req',grouped.value);
});

// watch(()=>grouped,()=>{
//   console.log('watch',grouped.value);
// })

const handleClickSelection = (colData) => {
  emits("change", colData.slug);
  selectedValue.value = colData;
  dropdownState.value = false;
};

const visibleColList = computed(() => {
    // return props.columnList.filter((x) => !(x.selectedCol > -1));
    return props.columnList;
  }
);

const dateTypeSelected = computed(() => {
  const _val = props.columnList.filter((x) => {
    return x.slug === "date" || x.slug === "starts_time_at" || x.slug === "ends_time_at";
  }).find((y) => y.selectedCol !== -1);

  return _val;
});

const grouped = computed(() => groupBy(
  visibleColList.value, "group_name"
));

window.addEventListener("click", (el) => {
  if (!dropdownState.value) return;

  if (el.target?.id !== props.id?.toString()) {
    dropdownState.value = false;
  }
});

const dateTypes = ["starts_at", "ends_at"];

</script>

<template>
  <div
    class="bg-white border rounded px-1 flex flex-row justify-between items-center"
    :id="id"
    @click="handleClick"
    :style="selectedValue?.slug === 'not_mapped' ? { borderColor: '#cbcbcb', borderWidth: 1, backgroundColor: '#e4e4e4', color: 'black' }
            : { color: 'black' }"
  >
    <div :id="id">
      {{ selectedValue?.slug === "not_mapped" ? t("Not Mapped") : selectedValue?.name }}
    </div>
    <FontAwesomeIcon
      :id="id"
      :icon="dropdownState ? 'chevron-up' : 'chevron-down'"
      class="text-gray-400"
    />
  </div>
  <el-dialog
    v-model="dropdownState"
    top="30px"
    width="fit-content"
  >

    <div class="flex flex-row border-b">
      <div v-for="(group, ind) in  Object.keys(grouped).filter((k) => k !== 'undefined') ">
        <div :class="[ind !== 0 && 'border-l', 'flex flex-col']">
          <div class="font-semibold border-b pb-1 pl-2">{{ group }}</div>
          <div v-for="item in  grouped[group] ">
            <div
              :class="['py-1 hover:bg-slate-100 px-2 mb-1', dateTypeSelected && dateTypes.indexOf(item.slug) !== -1 && 'infohover']"
              @click="() => {
                                if (!(dateTypeSelected && dateTypes.indexOf(item.slug) !== -1)) {
                                    handleClickSelection(item)
                                }
                            }"
              v-if="item.selectedCol < 0"
            >

              <!--                            <div-->
              <!--                                class="infobox"-->
              <!--                                v-if="dateTypeSelected && dateTypes.indexOf(item.slug) !== -1"-->
              <!--                            >-->
            <!--                                {{ t('A single date format must be selected') }}-->
              <!--                            </div>-->



              <div
                class="infobox text-white  p-3"
                v-if="dateTypeSelected && dateTypes.indexOf(item.slug) !== -1"
                style="background-color: rgb(51 65 85); padding: 6px; border-radius: 6px;"
              >
                {{ t('A single date format must be selected') }}
              </div>
              {{ item.name }}
              <span class="text-red-500 font-semibold">
                                {{ item.rules?.required ? '*' : '' }}
                            </span>

            </div>
            <!-- <div
                class="py-1 px-2 mb-1"
                style="background-color: #dffdea; color: gray; border-radius: 4px;"
                v-else-if="dateTypeSelected && dateTypes.indexOf(item.slug) !== -1"
            >
                {{ item.name }} www
                <span class="text-red-500 font-semibold">
                    {{ item.rules?.required ? '*' : '' }}
                </span>
            </div> -->
            <div
              class="py-1 px-2 mb-1"
              :style="{ backgroundColor: '#dffdea', color: 'gray', borderRadius: '4px' }"
              v-else
            >
              {{ item.name }}
              <span class="text-red-500 font-semibold">
                                {{ item.rules?.required ? "*" : "" }}
                            </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      @click="handleClickSelection(grouped['undefined'][0])"
      class="font-semibold text-red-500 py-1 hover:bg-slate-100"
    >
      <FontAwesomeIcon icon="trash-slash" />
      {{ grouped["undefined"][0]?.name }}
    </div>
    <!-- </div> -->
  </el-dialog>
</template>

<style scoped>
.custom__dropdown {
  background-color: white;
  position: absolute;
  padding: 6px;
  z-index: 20;
  /* width: 550px; */
}

.el-dialog__body {
  padding: 0 !important;
}

.infobox {
  display: none;
  position: absolute;
  left: -50px;
  top: -20px;
  background-color: white;
  border: 1px solid gray;
  padding: 2px;
}

.infohover:hover .infobox {
  display: block;
}

.infohover {
  position: relative;
  background-color: rgb(228, 228, 228)
}

.infohover:hover {
  background-color: #f1f5f9 !important;
}

/* 
.el-dialog {
    --el-dialog-margin-top: 10vh !important;
} */

</style>