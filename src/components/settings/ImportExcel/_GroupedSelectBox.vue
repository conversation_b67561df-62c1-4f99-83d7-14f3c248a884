<script setup>
import { groupBy } from "@/class/helpers";
import { defineProps, ref, onMounted, watch, computed } from "vue"
import { useI18n } from "vue-i18n";

const { t } = useI18n()

const props = defineProps({
    handleOptionChange: (a, b) => { },
    columnIndex: Number,
    columnList: Object,
    value: { type: Object },
    id: { type: Number },
    columnLength: { type: Number },
    mappingCompleted: { type: Boolean },
    mappingList: { type: Object }
});

const emits = defineEmits(['change']);

const selectedValue = ref();
const dropdownState = ref(false);

const handleClick = (e) => {
    e.preventDefault()
    dropdownState.value = !dropdownState.value;
};

onMounted(() => {
    selectedValue.value = props.columnList?.find((x) => x.slug === 'not_mapped');
});

const handleClickSelection = (colData) => {
    emits('change', colData.slug);
    selectedValue.value = colData;
    dropdownState.value = false;
};

const visibleColList = computed(() => {
    // return props.columnList.filter((x) => !(x.selectedCol > -1));
    return props.columnList
}
);

const grouped = computed(() => groupBy(
    visibleColList.value, 'group_name'
));

window.addEventListener('click', (el) => {
    if (!dropdownState.value) return;

    if (el.target?.id !== props.id?.toString()) {
        dropdownState.value = false;
    }
});

</script>

<template>
    <div
        class="bg-white border rounded px-1 flex flex-row justify-between items-center"
        :id="id"
        @click="handleClick"
        :style="selectedValue?.slug === 'not_mapped' ? { borderColor: '#cbcbcb', borderWidth: 1, backgroundColor: '#e4e4e4', color: 'black' }
            : { color: 'black' }"
    >
        <div :id="id">
            {{ selectedValue?.slug === 'not_mapped' ? t('Not Mapped') : selectedValue?.name }}
        </div>
        <FontAwesomeIcon
            :id="id"
            :icon="dropdownState ? 'chevron-up' : 'chevron-down'"
            class="text-gray-400"
        />
    </div>
    <div
        v-show="dropdownState"
        :class="[columnLength < columnIndex + 2 && 'right-0', 'custom__dropdown border rounded flex flex-col']"
    >
        <div class="flex flex-row border-b">
            <div v-for="(group, ind) in Object.keys(grouped).filter((k) => k !== 'undefined')">
                <div :class="[ind !== 0 && 'border-l', 'flex flex-col']">
                    <div class="font-semibold border-b pb-1 pl-2">{{ group }}</div>
                    <div v-for="item in grouped[group]">
                        <div
                            class="py-1 hover:bg-slate-100 px-2 mb-1"
                            @click="() => {
                                handleClickSelection(item)
                            }"
                            v-if="item.selectedCol < 0"
                        >
                            {{ item.name }}
                            <span class="text-red-500 font-semibold">
                                {{ item.rules?.required ? '*' : '' }}
                            </span>
                        </div>
                        <div
                            class="py-1 px-2 mb-1"
                            style="background-color: #dffdea; color: gray; border-radius: 4px;"
                            v-else
                        >
                            {{ item.name }}
                            <span class="text-red-500 font-semibold">
                                {{ item.rules?.required ? '*' : '' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div
            @click="handleClickSelection(grouped['undefined'][0])"
            class="font-semibold text-red-500 py-1 hover:bg-slate-100"
        >
            <FontAwesomeIcon icon="trash-slash" />
            {{ grouped['undefined'][0]?.name }}
        </div>
    </div>
</template>

<style>
.custom__dropdown {
    background-color: white;
    position: absolute;
    padding: 6px;
    z-index: 20;
    width: 550px;
}
</style>