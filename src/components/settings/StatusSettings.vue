<script>
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import EditButton from "@/renderers/EditButton.vue";
import Block from "@/renderers/Block.vue";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    StatusRenderer,
    EditButton,
    Block
  },
};
</script>

<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import {inject, onActivated, ref,onMounted} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import NewStatus from "@/components/settings/drawers/new/NewStatus.vue";
import StatusEdit from "@/components/settings/drawers/edit/StatusEdit.vue";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const toast = useToast()
const {t} = useI18n()
const api = inject("api");

const emit = defineEmits(["taskClicked", "taskDoubleClicked"]);

const selectedRows = ref();
const selectedStatus = ref(null);
const loader = ref();
const newStatusDrawerVisible = ref(false);
const editStatusDrawerVisible = ref(false);
const statuses = ref([]);
const gridApi = ref(null);


const columnDefs = ref([
  {
    field: "is_default",
    headerName: "",
    pinned: "left",
    suppressSizeToFit: true,
    maxWidth: 50,
    cellStyle: {textAlign: "center"},
    cellRenderer: "Block",
    cellRendererParams: {
      clicked: (params) => {
        openStatusEdit(params);
      },
    },
  },
  {
    field: "is_active",
    headerName: t("Active"),
    filter: 'agSetColumnFilter',
    width: 90,
    valueFormatter: (params) => params.value ? t("Active") : t("Passive"),
  },
  {
    field: "id",
    headerName: "Id",
    width: 90,
    filter: 'agTextColumnFilter',
  },
  {
    field: "status_order",
    headerName: t("Order"),
    sort: 'asc',
    width: 40,
  },
  {
    field: "slug",
    headerName: "Slug",
    filter: 'agTextColumnFilter',
  },
  {
    field: "category.name",
    headerName: t("Status Name"),
    rowGroup: true,
    hide: true,
    filter: 'agTextColumnFilter',
  },
  {
    field: "name",
    headerName: t("Status Name"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "created_at",
    headerName: t("Created At"),
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    width: 80,
    cellStyle: {textAlign: "center"},
    cellRenderer: "EditButton",
    cellRendererParams: {
      clicked: (params) => {
        openStatusEdit(params);
      },
    },
  },
]);

function rowClicked() {
  emit("taskClicked");
}

function rowDoubleClicked(e) {
  emit("taskDoubleClicked", e.data);
}

function handleSelectionChanged() {
  selectedRows.value = gridApi.value.getSelectedRows();
}

const openNewStatusDrawer = () => {
  newStatusDrawerVisible.value = true;
};

const openStatusEdit = (params) => {
  selectedStatus.value = statuses.value.find(i => i.id === params);
  editStatusDrawerVisible.value = true;
}

const initialGroupOrderComparator = (params) => {
  const a = params.nodeA.allLeafChildren[0].data.category.order
  const b = params.nodeB.allLeafChildren[0].data.category.order
  return a < b ? -1 : a > b ? 1 : 0;
};

const closeStatusDrawer = (param) => {
  newStatusDrawerVisible.value = false;
  editStatusDrawerVisible.value = false
  if (param) {
    getStatuses();
  }
};

const getStatuses = () => {
  loader.value.show();
  api("customer/statuses")
      .then((res) => statuses.value = res.data)
      .catch((err) => toast.error(err.data.message))
      .finally(() => loader.value.hide());
};

onActivated(() => {
  getStatuses();
});
onMounted(() => {
  getStatuses();
})


const onRefresh = () => {
  getStatuses()
}

</script>

<template>
  <LoadingBlock ref="loader"/>
  <SettingsPageLayout>
    <template #header>
      <span class="text-lg font-bold text-slate-700 m-3"> {{t('Statuses')}} </span>
      <div class="flex items-center">
        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" size="lg"/>
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('New Status')" position="bottom">
          <el-button
              class="mr-3 items-center"
              size="small"
              @click="openNewStatusDrawer"
          >
            <FontAwesomeIcon icon="plus" size="lg"/>
          </el-button>
        </ButtonToolTip>
      </div>
    </template>
    <template #content>
      <div class="h-full flex flex-col">
        <div
            class="flex flex-col sm:flex-row sm:items-center w-full bg-white text-xxs border-b border-slate-300"
        >
          <div class="flex items-center justify-between flex-grow">
            <div class="flex px-1">
              <div class="px-1 py-2">
                {{ t('Total Status') }}:
                <span class="font-bold">{{ statuses.length }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow">
          <DataGrid
              v-model="gridApi"
              :dataSource="statuses"
              :columns="columnDefs"
              @rowClicked="rowClicked"
              @rowDoubleClicked="rowDoubleClicked"
              @handleSelectionChanged="handleSelectionChanged"
              :autoSizeColumn="false"
              :initialGroupOrderComparator="initialGroupOrderComparator"
          />
        </div>
      </div>
    </template>
    <template #drawers>
      <el-drawer
          v-model="newStatusDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('New Status')"
          append-to-body
          destroy-on-close
      >
        <NewStatus @close="closeStatusDrawer"/>
      </el-drawer>
      <el-drawer
          v-model="editStatusDrawerVisible"
          class="customized-drawer customized-drawer"
          :title="t('Status Edit')"
          append-to-body
          destroy-on-close
      >
        <StatusEdit @close="closeStatusDrawer" :status="selectedStatus"/>
      </el-drawer>
    </template>
  </SettingsPageLayout>
</template>
<!--<style scoped>-->
<!--.ag-theme-balham .ag-row.ag-row-level-0 {-->
<!--  background-color: #f1f5f9;-->
<!--}-->


<!--</style>-->
