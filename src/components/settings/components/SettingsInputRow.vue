<script setup>
import {ref, computed} from "vue";

const emit = defineEmits(["update:modelValue", "save","labelIconTriggered","cancel"]);
const props = defineProps({
  type: {type: String, default: "input"},
  disabled: {type: Boolean, default: false},
  options: {type: Array, default: () => []},
  options_label: {type: String, default: "label"},
  options_key: {type: String, default: "key"},
  formDataName: {type: String, default: ""},
  label: {type: String, default: ""},
  labelIcon: {type: String, default: ""},
  modelValue: {type: [Number, String, Array, Date], default: ""},
});
const mode = ref(false);
const rowModel = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const toggle = () => {
  if (mode.value) {
    emit("save", {name: props.formDataName, value: props.modelValue})
  }
  mode.value = !mode.value;
};
const labelIconTriggered = () => {
emit("labelIconTriggered")
}
const onReset = () => {
  mode.value = false
  emit("cancel")
}
</script>

<template>

    <div
        class="flex-grow sm:grid sm:grid-cols-4 sm:gap-4 px-4 py-2 items-center border-b border-slate-300 settings-input-row"
    >
      <dt class="col-span-1 text-sm font-medium text-gray-500 flex items-center">
        <FontAwesomeIcon v-if="labelIcon" @click="labelIconTriggered" :icon="props.labelIcon" class="mr-2 cursor-pointer"/>
        {{ label }}
      </dt>
      <dd
          class="mt-1 flex text-sm items-center text-gray-900 sm:mt-0 sm:col-span-3"
      >
        <div class="flex-grow">
          <div v-if="mode" class="flex">
            <template v-if="type === 'input'">
              <el-input v-if="mode" v-model="rowModel"/>
            </template>

            <template v-if="type === 'select'">
              <el-select
                  v-model="rowModel"
                  class="w-full-important"
                  placeholder="Select"
                  filterable
              >
                <el-option
                    v-for="item in options"
                    :key="item[options_key]"
                    :label="item[options_label]"
                    :value="item[options_key]"
                >
                </el-option>
              </el-select>
            </template>
            <template v-if="type === 'multipleSelect'">
              <el-select
                  v-model="rowModel"
                  multiple
                  class="w-full-important"
                  placeholder="Select"
                  filterable
              >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
            <template v-if="type === 'color'">
              <div class="flex pl-4 items-center">
                <el-color-picker v-model="rowModel" class="mr-4"/>
                <div>{{ rowModel }}</div>
              </div>
            </template>
          </div>
          <div v-else class="flex pl-4 h-10 items-center">
            <div v-if="type === 'color'" class=" flex h-10 w-10 mr-4 rounded justify-center items-center ">
              <div class="h-7.5 w-7.5" :style="`background-color:${rowModel}`">
              </div>
            </div>
            <div v-else-if="type ==='select'" style="width: 100% !important;">
              <el-select
                  v-model="rowModel"
                  class="w-full"
                  placeholder="Select"
                  filterable
                  disabled

              >
                <el-option
                    v-for="item in options"
                    :key="item[options_key]"
                    :label="item[options_label]"
                    :value="item[options_key]"
                >
                </el-option>
              </el-select>
            </div>
            <div v-else-if="type==='multipleSelect'" class="flex flex-wrap">
              <el-select
                  v-model="rowModel"
                  multiple
                  class="w-full-important"
                  placeholder="Select"
                  filterable
                  disabled
              >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div v-else>
              {{ rowModel ? rowModel : "" }}
            </div>
          </div>
        </div>
        <div v-if="!disabled" class="ml-4 flex-shrink-0 flex items-center">
          <slot v-if="mode" name="buttons"></slot>
          <el-button :text="true"  @click="toggle">
            {{ !mode ? $t("Edit") : $t("Update" )}}
          </el-button>
          <div @click="onReset" v-if="mode" class="ml-2 text-slate-300 cursor-pointer">
            <FontAwesomeIcon icon="ban"/>
          </div>
        </div>
      </dd>
    </div>

</template>
