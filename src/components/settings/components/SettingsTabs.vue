<script setup>
import {computed, watch} from "vue";
import {useStore} from "vuex";
import SettingsTabsItem from "@/components/settings/components/SettingsTabsItem.vue";
import settings from "@/router/settings";
import router from "@/router";

const {dispatch, getters} = useStore();

const currentlyOpenMainTabs = computed(() => settings.children)

const goToRoute = (param) => {
  router.push({path: param.path})
}


</script>

<template>
  <nav class=" h-full justify-end overflow-auto">
    <div class="flex flex-col sticky top-6 py-4">
      <template v-for="route in currentlyOpenMainTabs" :key="route.name">
        <SettingsTabsItem
            :active="$route.name === route.name"
            @click="goToRoute(route)"
        >{{ $t(route.name) }}
        </SettingsTabsItem>
      </template>
    </div>
  </nav>
</template>


