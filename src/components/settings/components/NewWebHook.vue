<script setup>

import {inject, reactive, ref, watch,onMounted} from "vue";
import {useI18n} from "vue-i18n";

const {t} = useI18n()
const api = inject("api")
const emit = defineEmits(["save","remove"])
const props = defineProps({
  webhook: {
    type: Object, default: () => {
    }
  },
  webhookTypes: {
    type: Array, default: () => []
  },
})

const form = reactive({
  type: null,
  trigger: null,
  url: null,
  is_active: true,
  hubs:[]
})
const mode = ref(true)
const triggers = ref([])
const hubs = ref([])


const toggle = () => {
  mode.value = !mode.value

}

const close = () => {
  mode.value = false
  emit("remove", props.webhook.id)
}
const onSubmit = () => {
  mode.value = false
  emit("save", {...form,id:props.webhook.id})
}

const getHubs = () => {
  api('customer/hubs')
      .then((r) => hubs.value = r.data)
}

onMounted(() => {
  getHubs()
})

watch(() => form.type, () => {
  props.webhookTypes.forEach((x) => {
    if (x.slug === form.type) {
      triggers.value = x.triggers
      form.trigger = x.triggers.find((y) => y.is_default).slug
    }
  })
})


</script>
<template>

  <div class="px-4 py-2 flex border-b border-slate-300">
    <div class="grow grid gap-4 grid-cols-12">
      <div class="col-span-12 sm:col-span-6 lg:col-span-2">
        <el-select
            v-model="form.type"
            class="w-full-important"
            :placeholder="t('Webhook Type')"
            :disabled="!mode"
            filterable
        >
          <el-option
              v-for="item in webhookTypes"
              :key="item.slug"
              :label="item.name"
              :value="item.slug"
          >
          </el-option>
        </el-select>
      </div>
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <el-select
          v-model="form.hubs"
          class="w-full-important"
          :placeholder="t('Hubs')"
          :disabled="!form.type"
          collapse-tags
          collapse-tags-tooltip
          multiple
          filterable
        >
          <el-option
            v-for="item in hubs"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="col-span-12 sm:col-span-6 lg:col-span-2">
        <el-select
            v-model="form.trigger"
            :disabled="!mode"
            class="w-full-important"
            placeholder="Trigger"
            filterable
        >
          <el-option
              v-for="item in triggers"
              :key="item.slug"
              :label="item.description"
              :value="item.slug"
          >
          </el-option>
        </el-select>
      </div>
      <div class="col-span-12 lg:col-span-5">
        <el-input
            v-model="form.url"
            :disabled="!mode"
            placeholder="Url"
        >
        </el-input>
      </div>
    </div>

    <div class="pl-2">
      <div class="h-full md:flex justify-between items-center">
        <div @click="close" class="text-xl text-slate-300 cursor-pointer md:mr-4">
          <FontAwesomeIcon icon="ban"/>
        </div>
        <el-button @click="onSubmit" :text="true">
          {{ t("Save") }}
        </el-button>
      </div>
    </div>
  </div>
</template>
