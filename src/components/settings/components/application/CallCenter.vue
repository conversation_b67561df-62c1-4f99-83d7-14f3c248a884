<script setup>
import {ref, reactive, onMounted, computed, inject} from 'vue';
import {useI18n} from "vue-i18n";
import {useStore} from "vuex";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()
const {getters, dispatch} = useStore()

const api = inject("api")

const activeCompany = computed(() => getters["auth/activeCompany"])

const mode = ref(false)

const form = reactive({
  call_center_enabled: false,
  call_center_options: {
    provider: "netgsm",
    phone: null,
    auto_call: null,
    call_by: null
  },
})

const close = () => {
  mode.value = false
  setFields()
}

const toggle = () => {
  mode.value = !mode.value
  if (!mode.value) {
    saveRow()
  }
}

let activePassive = [
  {
    key: true,
    label: t("Active"),
  },
  {
    key: false,
    label: t("Passive")
  }
]
let callByList = [
  {
    key: null,
    label: "None"
  },
  {
    key: "task_id",
    label: "Task Id",
  },
  {
    key: "integration_id",
    label: t("Integration Id")
  }
]

const setFields = () => {

  form.call_center_enabled = activeCompany.value.call_center_enabled
  form.call_center_options = {
    provider: activeCompany.value.call_center_options?.provider ? activeCompany.value.call_center_options?.provider : "netgsm",
    phone: activeCompany.value.call_center_options?.phone,
    auto_call: activeCompany.value.call_center_options?.auto_call,
    call_by: activeCompany.value.call_center_options?.auto_call ? activeCompany.value.call_center_options?.call_by : null
  }
}

const saveRow = () => {
  let formData = new FormData();

  formData.append("call_center_enabled", Number(form.call_center_enabled));
  formData.append("call_center_options[provider]", form.call_center_options.provider);
  formData.append("call_center_options[auto_call]", !form.call_center_options.call_by ? 0 : 1);
  formData.append("call_center_options[phone]", form.call_center_options.phone);
  formData.append("call_center_options[call_by]", form.call_center_options.call_by);
  formData.append("_method", "PUT")
  api.post(`customer/companies`, formData)
      .then((res) => {
        dispatch("auth/setActiveCompany", res)
        setFields()
      })
      .catch(() => toast.error(t('An error occurred')))
}

onMounted(() => {
  setFields()
})
</script>

<template>
  <div
      class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
  >
    <dt class="col-span-1 text-sm font-medium text-gray-500">CallCenter</dt>
    <dd
        class="mt-1 flex text-sm items-center justify-between text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
    >

      <div class="flex flex-grow col-span-3 items-center">
        <div class="flex-grow grid grid-cols-6 gap-4 h-10 items-center mx-4">
          <div class="col-span-2">
            <el-select
                v-model="form.call_center_enabled"
                :disabled="!mode"
                class="w-full-important"
                placeholder="Enabled"
                filterable
            >
              <el-option
                  v-for="item in activePassive"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
              >
              </el-option>
            </el-select>
          </div>
          <div class="col-span-2">
            <el-input
                v-model="form.call_center_options.phone"
                :disabled="!mode || !form.call_center_enabled"
                placeholder="Phone"
                type="number"
                class=""
            >
            </el-input>
          </div>
          <div class="col-span-2">
            <el-select
                v-model="form.call_center_options.call_by"
                :disabled="!mode || !form.call_center_options.phone || !form.call_center_enabled"
                class="w-full-important"
                placeholder="Enabled"
                filterable
            >
              <el-option
                  v-for="item in callByList"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <el-button class="ml-2" :text="true"  @click="toggle">
          {{ !mode ? t("Edit") : t("Update") }}
        </el-button>
        <div v-if="mode" class="ml-2 text-slate-300 cursor-pointer" @click="close">
          <FontAwesomeIcon icon="ban"/>
        </div>
      </div>
    </dd>
  </div>

</template>
