<script setup>

import {ref, onMounted, computed, inject} from 'vue';
import {useI18n} from "vue-i18n";
import {useStore} from "vuex";
import {useToast} from "vue-toastification";
import {groupBy} from "@/class/helpers";

const toast = useToast()
const {t} = useI18n()
const {getters, dispatch} = useStore()

const api = inject("api")

const activeCompany = computed(() => getters["auth/activeCompany"])

const update_status_after_task_confirmation = ref(false)
const status_id_after_task_confirmation = ref()
const mode = ref()
const loading = ref()
const statusList = ref([])

let activePassive = [
  {
    key: true,
    label: t("Active"),
  },
  {
    key: false,
    label: t("Passive")
  }
]

const statues = {
  created: "Created",
  assigned: "Assigned",
  in_progress: "In Progress",
  on_delivery: "On Delivery",
  failed: "Failed",
  completed: "Completed",
  cancelled: "Cancelled",
  pool: "Pool"
}

const close = () => {
  mode.value = false
  setFields()
}

const toggle = () => {
  mode.value = !mode.value
  if (!mode.value) {
    saveRow()
  }
}

onMounted(() => {
  setFields()
  getStatus()
})

const setFields = () => {

  update_status_after_task_confirmation.value = activeCompany.value.update_status_after_task_confirmation

  if (update_status_after_task_confirmation.value) {
    status_id_after_task_confirmation.value = activeCompany.value.status_id_after_task_confirmation
  }
}

const saveRow = () => {

  let formData = new FormData();

  formData.append("update_status_after_task_confirmation", Number(update_status_after_task_confirmation.value));
  formData.append("status_id_after_task_confirmation", status_id_after_task_confirmation.value);
  formData.append("_method", "PUT")

  api.post(`customer/companies`, formData)
      .then((res) => {
        dispatch("auth/setActiveCompany", res)
        setFields()
      })
      .catch(() => toast.error(t('An error occurred')))
}

const getStatus = () => {
  loading.value = true
  api('customer/statuses')
      .then((r) => {

        const groupList = groupBy(r.data.filter((s) => s.is_active), 'category_slug')

        let arr = []

        Object.keys(groupList).forEach((x) => {
          arr.push({
            label: statues[x],
            options: groupList[x]
          })
        })

        statusList.value = arr
      })
      .finally(() => loading.value = false)
}


</script>


<template>
  <div
      class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
  >
    <dt class="col-span-1 text-sm font-medium text-gray-500">{{ t('Change Status After Task Approval') }}</dt>
    <dd
        class="mt-1 flex text-sm items-center justify-between text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
    >
      <div class="flex flex-grow col-span-3 items-center ">
        <div class="flex-grow grid grid-cols-4 h-10 items-center mx-4">
          <div class="col-span-2">
            <el-select
                :disabled="!mode"
                v-model="update_status_after_task_confirmation"
                class="w-full-important"
                placeholder="Select"
                filterable
            >
              <el-option
                  v-for="item in activePassive"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
              >
              </el-option>
            </el-select>
          </div>
          <div class="col-span-2 ml-4 ">
            <el-select
                v-if="update_status_after_task_confirmation"
                :disabled="!mode"
                v-model="status_id_after_task_confirmation"
                class="w-full-important"
                :loading="loading"
                :placeholder="t('Select Status')"
                filterable
            >
              <el-option-group
                  v-for="group in statusList"
                  :key="group.label"
                  :label="t(group.label)"
              >
                <el-option
                    v-for="item in group.options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-option-group>
            </el-select>

          </div>
        </div>
        <el-button class="ml-2" :text="true" @click="toggle">
          {{ !mode ? t("Edit") : t("Update") }}
        </el-button>
        <div @click="close" v-if="mode" class="ml-2 text-slate-300 cursor-pointer">
          <FontAwesomeIcon icon="ban"/>
        </div>
      </div>
    </dd>
  </div>

</template>
