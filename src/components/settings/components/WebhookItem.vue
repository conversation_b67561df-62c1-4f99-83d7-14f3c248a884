<script setup>

import { inject, onMounted, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const api = inject("api");
const emit = defineEmits(["update", "refreshWebhook",]);
const props = defineProps({
  webhook: {
    type: Object, default: () => {
    }
  },
  webhookTypes: {
    type: Array, default: () => []
  }
});
const dialogVisible = ref(false);
const form = reactive({
  type: null,
  trigger: null,
  url: null,
  is_active: true,
  hubs: []
});
const mode = ref(false);
const triggers = ref([]);
const hubs = ref([]);


const toggle = () => {
  mode.value = !mode.value;
  if (!mode.value) {

  }
};

const close = () => {
  mode.value = false;
  setField();
};
const onDelete = () => {

  dialogVisible.value = false;
  mode.value = false;
  emit("delete", props.webhook.id);
};
const onSubmit = () => {
  mode.value = false;
  emit("update", { ...form, id: props.webhook.id });
};

const setField = () => {
  form.type = props.webhook.type;
  form.trigger = props.webhook.trigger;
  form.url = props.webhook.url;
  form.is_active = props.webhook.is_active;
  form.hubs = props.webhook.hubs ? props.webhook.hubs.map((x) => x.id) : [];
};
onMounted(() => {
  setField();
  getHubs();
});

const getHubs = () => {
  api("customer/hubs")
    .then((r) => hubs.value = r.data);
};


const deleteWebhook = () => {
  api.delete('customer/webhooks/' + props.webhook.id)
    .then(() => {
      emit("refreshWebhook", true);
    })
};


watch(() => form.type, () => {

  props.webhookTypes.forEach((x) => {
    if (x.slug === form.type) {
      triggers.value = x.triggers;
      form.trigger = x.triggers.find((y) => y.is_default).slug;
    }
  });
});
watch(() => form.is_active, () => {

  api.patch('customer/webhooks/'+props.webhook.id , {is_active: form.is_active})
    .then(()=>{
      emit("refreshWebhook", true)
    })


});


</script>
<template>

  <div class="p-5 flex border-b border-slate-300">
    <div class="grow grid gap-4 grid-cols-12">
      <div class="col-span-12 sm:col-span-6 lg:col-span-2">
        <el-select
          v-model="form.type"
          class="w-full-important"
          :placeholder="t('Webhook Type')"
          :disabled="!mode"
          filterable
        >
          <el-option
            v-for="item in webhookTypes"
            :key="item.slug"
            :label="item.name"
            :value="item.slug"
          >
          </el-option>
        </el-select>
      </div>
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <el-select
          v-model="form.hubs"
          class="w-full-important"
          :placeholder="t('Hubs')"
          :disabled="!mode"
          collapse-tags
          collapse-tags-tooltip
          multiple
          filterable
        >
          <el-option
            v-for="item in hubs"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="col-span-12 sm:col-span-6 lg:col-span-2">
        <el-select
          v-model="form.trigger"
          :disabled="!mode"
          class="w-full-important"
          placeholder="Trigger"
          filterable
        >
          <el-option
            v-for="item in triggers"
            :key="item.slug"
            :label="item.description"
            :value="item.slug"
          >
          </el-option>
        </el-select>
      </div>
      <div class="col-span-12 lg:col-span-5">
        <el-input
          v-model="form.url"
          :disabled="!mode"
          placeholder="Url"
        >
        </el-input>
      </div>
    </div>

    <div class="pl-2 flex-col md:flex">
      <div v-if="!mode" class="h-full w-full md:flex justify-between items-center">
        <div class="mr-4">
          <el-switch
            v-model="form.is_active"
          >
          </el-switch>
        </div>
        <el-button @click="toggle">

          <FontAwesomeIcon icon="edit" />

        </el-button>
      </div>
      <div v-else class="h-full  md:flex justify-between items-center">
        <div class="flex w-full items-center justify-between">
          <div @click="close" class="text-xl text-slate-300 cursor-pointer md:mr-4">
            <FontAwesomeIcon icon="ban" />
          </div>
          <div @click="dialogVisible = true" class="text-xl text-red-300 cursor-pointer md:mr-4">
            <FontAwesomeIcon icon="remove" />
          </div>
        </div>
        <el-button @click="onSubmit" :text="true">
          {{ t("Update") }}
        </el-button>

      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" width="30%" center>
    <div class="text-center text-lg text-slate-700 font-bold">
      {{ t("Are you sure?") }}
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t("Cancel") }}</el-button>
        <el-button @click="deleteWebhook" type="danger">
          {{ t("Delete") }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
