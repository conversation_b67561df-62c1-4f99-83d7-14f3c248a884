<script setup>
import {ref, watch} from 'vue';
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const emits = defineEmits(["onSelectToEdit", "update"])
const props = defineProps({
  title:{
    type:String,
    default:""
  },
  workingTemplatesData:{
    type:Array,
    default:()=> []
  }
})

const mode = ref(false)
const currentTemplate = ref()

watch(()=>props.workingTemplatesData, ()=>{
  getCurrentTemplateFromTemplates()
})

const getCurrentTemplateFromTemplates = () => {
  let item = props.workingTemplatesData.find((x) => x.is_default)
  if (item) {
    currentTemplate.value = item.id
  }
}
const toggle = () => {
  if (mode.value && currentTemplate.value) {
      emits("update", currentTemplate.value)
  }
  mode.value = !mode.value

}
const onSelectToEdit = () => {
  emits("onSelectToEdit",currentTemplate.value)
}

const onReset = () => {
  mode.value = false
  getCurrentTemplateFromTemplates()
}

</script>

<template>
  <div class="flex flex-col">
    <div
        class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
    >
      <dt class="col-span-1 text-sm font-medium text-gray-500">{{ props.title }}</dt>
      <dd
          class="mt-1 flex text-sm items-center text-gray-900 sm:mt-0 sm:col-span-3"
      >
        <div class="flex-grow flex items-center">

          <el-select
              :disabled="!mode"
              v-model="currentTemplate"
              class="w-full-important"
              filterable
          >
            <el-option
                v-for="item in workingTemplatesData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            >
            </el-option>
          </el-select>

          <div v-if="mode"
               @click="onSelectToEdit"
               class="ml-2 flex items-center justify-center border border-slate-300 p-2 text-indigo-600 cursor-pointer">
            <FontAwesomeIcon icon="calendar"/>
          </div>

        </div>
        <el-button class="ml-2" :text="true" @click="toggle">
          {{ !mode ? $t("Edit") : $t("Update") }}
        </el-button>
        <div @click="onReset" v-if="mode" class="ml-2 text-slate-300 cursor-pointer">
          <FontAwesomeIcon icon="ban"/>
        </div>
      </dd>
    </div>
  </div>
</template>
