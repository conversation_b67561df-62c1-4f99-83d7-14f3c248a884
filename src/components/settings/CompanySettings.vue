<script setup>
import SettingsPageLayout from "@/components/settings/components/SettingsPageLayout.vue";
import SettingsInputRow from "@/components/settings/components/SettingsInputRow.vue";
import {computed, inject, onActivated, reactive, ref,onMounted} from "vue";
import {useStore} from "vuex";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()
const {getters, dispatch} = useStore();

const api = inject("api");

const activeCompany = computed(() => getters["auth/activeCompany"]);

const mode = ref(false)
const wallet = ref()

const form = reactive({
  company: null,
  companyShortName: null,
  country: null,
  city: null,
  address: null,
  taxId: null,
  taxOffice: null,
  iban: null,
});

const setFormValues = () => {
  form.company = activeCompany.value.name;
  form.companyShortName = activeCompany.value.short_name;
  form.country = activeCompany.value.country_code;
  form.taxId = activeCompany.value.tax_id;
  form.taxOffice = activeCompany.value.tax_office;
  form.address = activeCompany.value.address;
}

const saveRow = (param) => {
  let formData = new FormData();
  formData.append(param.name, param.value);
  formData.append("_method", "PUT");
  api
      .post(`customer/companies`, formData)
      .then((res) => dispatch("auth/setActiveCompany", res))
      .catch(() => {
        toast.error(t("An error occurred"));
        setFormValues()
      });
};

onActivated(() => {
  setFormValues()
  getWallets()
});

onMounted(() => {
  setFormValues()
  getWallets()
})

const getWallets = () => {
  api("customer/wallets")
      .then((res) => {
        wallet.value = res.data.find(x => x.currency_unit === "TRY")
        form.iban = wallet.value.iban
      })

}


const updateIban = () => {
  api.put(`customer/wallets/${wallet.value.uuid}`, {
    iban: form.iban,
    _method: "PATCH",
  })
}


const toggle = () => {
  if (mode.value) {
    updateIban()
  }
  mode.value = !mode.value;
}

const onReset = () => {
  setFormValues()
  mode.value = false
}

</script>

<template>
  <SettingsPageLayout>
    <template #header>

        <span class="text-lg font-bold text-slate-700 m-3">{{ t('Company Settings') }} </span>

    </template>
    <template #content>
      <SettingsInputRow
          v-model="form.company"
          :label="t('Company Name')"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="name"
          :disabled="true"
      />
      <SettingsInputRow
          @save="saveRow"
          @cancel="setFormValues"
          v-model="form.companyShortName"
          form-data-name="short_name"
          :label="t('Company Short Name')"
          :disabled="true"
      />
      <SettingsInputRow
          v-model="form.address"
          @cancel="setFormValues"
          :label="t('Address')"
          @save="saveRow"
          form-data-name="address"
          :disabled="true"
      />
      <SettingsInputRow
          v-model="form.taxId"
          :label="t('Tax Id')"
          @save="saveRow"
          @cancel="setFormValues"
          form-data-name="tax_id"
          :disabled="true"
      />
      <SettingsInputRow
          v-model="form.taxOffice"
          :label="t('Tax Office')"
          @cancel="setFormValues"
          @save="saveRow"
          form-data-name="tax_office"
          :disabled="true"
      />
      <div
          v-if="activeCompany.company_product_type !== 'qd'"
          class="px-4 py-2 sm:grid sm:grid-cols-4 sm:gap-4 border-b border-slate-300 items-center settings-input-row "
      >
        <dt class="col-span-1 text-sm font-medium text-gray-500">IBAN</dt>
        <dd
            class="mt-1 flex text-sm items-center justify-between text-gray-900 sm:mt-0 sm:col-span-3 grid-cols-3"
        >

          <div class="flex flex-grow col-span-3 items-center">
            <div class="flex-grow grid grid-cols-4 h-10 items-center">
              <div class="col-span-4">
                <el-input
                    v-if="mode"
                    v-model="form.iban"
                    class="w-full-important"
                    placeholder="Select"
                    filterable
                />
                <div v-else class="ml-4">
                  {{ form.iban }}
                </div>
              </div>
            </div>

          </div>
        </dd>
      </div>
    </template>
  </SettingsPageLayout>
</template>
