<script setup>
import {onMounted, ref, computed} from 'vue';
import DefaultMapStyle from "@/map-styles/default.json";
import {useI18n} from "vue-i18n";
import OnlineCourier from "@/assets/images/map/marker/Destination_Selected_Start.png";
import dayjs from "dayjs";
import DestinationCourier from "@/assets/images/map/marker/Destination_Selected_Finish.png";
import LiveCourierPin from "@/assets/images/map/marker/duty_courier.png"


const props = defineProps({
  payload: {type: Object},
  statusVisible: {type: Boolean, default: true},
})


const pinmap = ref()
const map = ref()
const {t} = useI18n()
const infowindows = ref([])
const originMarkerInfoWindow = ref([])
const destinationMarkerInfoWindow = ref([])
const courierMarker = ref()
const destinationMarker= ref()
const originMarker = ref()



const boundd = [
  {
    lat: Number(props.payload?.origin_lat),
    lng: Number(props.payload?.origin_lng)
  },
  {
    lat: Number(props.payload?.destination_lat),
    lng: Number(props.payload?.destination_lng)

  },
  {
    lat: Number( props.payload.task_provider_courier.courier_location.lat),
    lng: Number(props.payload.task_provider_courier.courier_location.lng)
  }
]


const mapPromise = async () => {
  return new Promise((resolve) => {
    map.value = new google.maps.Map(pinmap.value, {
      center: {
        lat: Number(props.payload?.origin_lat),
        lng: Number(props.payload?.origin_lng),
      },
      zoom: 14,
      mapTypeControl: false,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery"],
      },
    });
    const bounds = new google.maps.LatLngBounds();
    bounds.extend(boundd[0]);
    bounds.extend(boundd[1]);
    if (props.payload.task_provider_courier){
      bounds.extend(boundd[2]);
    }
    map.value.fitBounds(bounds);

    map.value.mapTypes.set(
        "qdelivery",
        new google.maps.StyledMapType(DefaultMapStyle, {name: "QDelivery"})
    );
    map.value.setMapTypeId("qdelivery");



    if (props.payload.task_provider_courier){

      courierMarker.value = new google.maps.Marker({
        map: map.value,
        position: {
          lat: props.payload.task_provider_courier.courier_location.lat,
          lng: props.payload.task_provider_courier.courier_location.lng
        },
        icon:{
          url:LiveCourierPin,
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(13, 26),
          labelOrigin: new google.maps.Point(13, 9),
        }

      })


    }






    originMarker.value = new google.maps.Marker({
      map: map.value,
      position: {lat: Number(props.payload?.origin_lat), lng: Number(props.payload?.origin_lng)},
      icon: {
        url: OnlineCourier,
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(13, 26),
        labelOrigin: new google.maps.Point(13, 9)
      },
    });

    originMarkerInfoWindow.value = new google.maps.InfoWindow({
      content: `
    <div class="w-70 h-20 flex flex-col rounded m-0 text-xxxs">
      <div class="justify-between items-center flex py-0.5 px-1">
      <div>
      <img style="width:30px;margin-right: 10px;" src="/src/assets/images/map/marker/Destination_Selected_Start.png" alt>
      <span style="font-size: 14px;margin-right: 14px; color: #2F4EFF"><strong>Çıkış</strong></span>
       </div>
        <div class="text-xs font-bold flex items-center">
          <div class="flex flex-col items-start justify-center">
          <div class="border-b border-slate-300 text-xxxs">
          <div>${dayjs(props.payload?.starts_at).format("DD.MM.YYYY HH:mm")}</div>
          </div>
            <div class="pt-1">
              ${props.payload?.origin_name}
            </div>
            <div class="text-xxxs font-medium">
             ${props.payload?.origin_address}
            </div>
                 <div class="text-xxxs font-medium select-all">
               ${props.payload?.origin_lat} , ${props.payload?.origin_lng}
            </div>
          </div>
        </div>
      </div>
    </div>
  `
    });

    originMarker.value.addListener("click", () => {
      closeInfowindows()
      originMarkerInfoWindow.value.open({
        anchor: originMarker.value,
        map: map.value,
      });
    });


     destinationMarker.value = new google.maps.Marker({
      map: map.value,
      position: {lat: Number(props.payload?.destination_lat), lng: Number(props.payload?.destination_lng)},
      icon: {
        url: DestinationCourier,
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(13, 26),
        labelOrigin: new google.maps.Point(13, 9),

      },
    });

    destinationMarkerInfoWindow.value = new google.maps.InfoWindow({
      content: `
    <div class="w-70 h-20 flex flex-col rounded m-0 text-xxxs">
      <div class="justify-between items-center flex py-0.5 px-1">
      <div>
      <img style="width:30px;margin-right: 10px;" src="/src/assets/images/map/marker/Destination_Selected_Finish.png" alt>
      <span style="font-size: 14px;margin-right: 14px; color: #2F4EFF"><strong>Varış</strong></span>
       </div>
        <div class="text-xs font-bold flex items-center">
          <div class="flex flex-col items-start justify-center">
          <div class="border-b border-slate-300 text-xxxs">
          <div>${dayjs(props.payload?.ends_at).format("DD.MM.YYYY HH:mm")}</div>
          </div>
            <div class="pt-1">
              ${props.payload?.destination_name}
            </div>
            <div class="text-xxxs font-medium">
             ${props.payload?.destination_address}
            </div>
                 <div class="text-xxxs font-medium select-all">
              ${props.payload?.destination_lat} , ${props.payload?.destination_lng}
            </div>
          </div>
        </div>
      </div>
    </div>
  `
    });


    destinationMarker.value.addListener("click", () => {
      closeInfowindows()
      destinationMarkerInfoWindow.value.open({
        anchor: destinationMarker,
        map: map.value,
      });
    });
  })


}

import { throttle } from "@/views/Tracking/GoogleMapsLoaderService";

// Throttled fitBounds function - maksimum 2 saniyede bir çalışır
const throttledFitBounds = throttle(() => {
  if (map.value && courierMarker.value && originMarker.value && destinationMarker.value) {
    const bounds = new google.maps.LatLngBounds();
    bounds.extend(courierMarker.value.getPosition());
    bounds.extend(originMarker.value.getPosition())
    bounds.extend(destinationMarker.value.getPosition())
    map.value.fitBounds(bounds);
  }
}, 2000);

const listenChannel = () => {
  Echo.channel("courier.location").listen(".courier.location.updated", (d) => {
    if (d.courier.id !== props.payload.task_provider_courier.id) {
      return
    }

    // Marker pozisyonunu güncelle
    courierMarker.value.setPosition({
      lat: d.courier.courier_location.lat,
      lng: d.courier.courier_location.lng
    })

    // Throttled fitBounds - aşırı API çağrısını önler
    throttledFitBounds();
  });
}


function closeInfowindows() {
  originMarkerInfoWindow.value.close()
  destinationMarkerInfoWindow.value.close()

  infowindows.value.forEach(function (iw) {
    iw.close()
  })
}


onMounted(() => {
  mapPromise()
  listenChannel()
});


</script>
<template>
  <div ref="pinmap" class="h-96 w-full"></div>
</template>

