<script>
import { AgGridVue } from "ag-grid-vue3";
import DelayRenderer from "@/renderers/DelayRenderer.vue";
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import LockandConfirm from "@/renderers/LockandConfirm.vue";
import TaskInformationIcons from "@/renderers/activeTasks/TaskInformationIcons.vue";
import OriginDestinationEditRenderer from "@/renderers/OriginDestinationEditRenderer.vue";
import CourierRenderer from "@/renderers/CourierRenderer.vue";

export default {
  components: {
    TaskInformationIcons,
    AgGridVue,
    DelayRenderer,
    StatusRenderer,
    LockandConfirm,
    OriginDestinationEditRenderer,
    CourierRenderer
  }
};
</script>
<script setup>
import { computed, inject, onActivated, onMounted, onUnmounted, ref, watch, reactive } from "vue";
import { groupBy } from "@/class/helpers";
import { useStore } from "vuex";
import UpdateStatusDrawer
  from "@/components/operations/components/taskTabs/subTabTask/components/UpdateStatusDrawer.vue";
import ProcessView from "@/components/operations/components/taskTabs/subTabTask/components/ProcessView.vue";
import ExcelImport from "@/components/operations/components/taskTabs/subTabTask/components/excel/ExcelImport.vue";
import formatter from "@/class/formatter";
import { useI18n } from "vue-i18n";
import UpdateSlot from "@/components/operations/components/taskTabs/subTabTask/components/UpdateSlot.vue";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import TaskHoursFilteredByDay
  from "@/components/operations/components/taskTabs/subTabTask/components/TaskHoursFilteredByDay.vue";
import { useToast } from "vue-toastification";
import DestinationEditDrawer
  from "@/components/operations/components/taskTabs/subTabTask/components/DestinationEditDrawer.vue";
import OriginEditDrawer from "@/components/operations/components/taskTabs/subTabTask/components/OriginEditDrawer.vue";
import PhoneInput from "@/components/ui/PhoneInput.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import PinMap from "@/components/ui/PinMap.vue";
import TagTaskFilter from "@/components/operations/components/taskTabs/subTabTask/components/TagTaskFilter.vue";

const toast = useToast();
const { t } = useI18n();
const { getters, dispatch } = useStore();

const dayjs = inject("dayjs");
const api = inject("api");
const emitter = inject("emitter");

const emit = defineEmits(["taskClicked"]);

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);
const selectedCourier = computed(() => getters["operations/selectedCourier"]);
const selectedHub = computed(() => getters["operations/selectedHub"]);
const couriers = computed(() => getters["operations/couriersFilteredByHubId"]);
const selectedDate = computed(() => getters["operations/selectedDate"]);
const reasonform = reactive({
  status_reason_slug:null,
  notes:null
})
const reasonList = ref();

const hubIdOfSelectedRows = computed(() => {
  let hubGroup = groupBy(selectedRows.value, "hub_id");
  if (Object.keys(hubGroup).length === 1) {
    let group = Object.values(hubGroup)[0].filter(x => !["assigned", "created", "failed"].includes(x.status_category_slug) || x.plan_id);
    if (group.length > 0) return null;
  }
  return Object.keys(hubGroup)[0];
});

const filter = reactive({});


const statues = {
  created: "Created",
  assigned: "Assigned",
  in_progress: "In Progress",
  on_delivery: "On Delivery",
  failed: "Failed",
  completed: "Completed",
  cancelled: "Cancelled",
  pool: "Pool"
};

const channels ={
  ui:'ui',
  api:'api',
  qconnect:'qconnect',
  kuik:'kuik'
}


const locationEdit = ref({
  searchAddress: "",
  lat: null,
  lng: null,
  area_json: null
});


const destinationUpdateForm = reactive({
  name: null,
  address: null
});

const tasks = ref([]);
const selectedRows = ref([]);
const gridApi = ref(null);
const grid = ref(null);
const updateSlotVisible = ref(false);
const updateStatusVisible = ref(false);
const importExcelDrawerVisible = ref(false);
const dialogVisible = ref(false);
const selectedRowDisable = ref(false);
const destinationDrawerVisible = ref(false);
const mapVisible = ref(false);
const data = ref();
const originDrawerVisible = ref(false);
const hubAddressDialogVisible = ref(false);
const qrVisible = ref(false);
const loader = ref();
const dayOffTime = ref([]);
const qrForm = reactive({
  origin_name: "",
  origin_address: ""
});

const dateUpdateVisible = ref(false);

const shouldDisableButton = computed(() => {
  if (selectedRows.value.length !== 1) {
    return true;
  }

  const selectedRow = selectedRows.value[0];
  if (selectedRow.plan_id !== null) {
    return true;
  }

  if (selectedRow.status_category_slug === "completed" || selectedRow.status_category_slug === "cancelled") {
    return true;
  }

  return false;
});

const phoneDialogVisible = ref(false);
const base64Image = ref(null);

const formPhoneDialog = reactive({
  show_destination_phone: false,
  show_origin_phone: false
});

const userPhone = reactive({
  phone: null,
  country: "TR"
});

const getBarkodİmage = () => {
  let body = {
    show_destination_phone: formPhoneDialog.show_destination_phone,
    show_origin_phone: formPhoneDialog.show_origin_phone
  };

  if (qrForm.origin_name) {
    body.origin_name = qrForm.origin_name;
  }

  if (qrForm.origin_address) {
    body.origin_address = qrForm.origin_address;
  }

  if (userPhone.phone) {
    body.origin_phone = userPhone.phone;
  }

  api.post(`customer/tasks/${selectedRows.value[0].id}/label`, body)
    .then((r) => {
      base64Image.value = r.label;
      phoneDialogVisible.value = true;
      // setTimeout(()=>{
      //   printImage(r.label)
      // },1000)

    });
};

const hubAddressDialogOpen = () => {
  hubAddressDialogVisible.value = true;
  mapVisible.value = false;
};


const columnDefs = [
  {
    field: "",
    headerName: "",
    pinned: "left",
    suppressSizeToFit: true,
    width: 220,
    maxWidth: 220,
    suppressColumnsToolPanel: true,
    cellRenderer: "TaskInformationIcons",
    export: false,
    cellRendererParams: {
      onFilterByMergedTaskId: (id) => {
        getDataOfSelectedMergedTaskId(id);
      },
      openDestination: (param) => {
        destinationDrawerVisible.value = true;
        data.value = param;
      },
      onpenOrigin: (param) => {
        originDrawerVisible.value = true;
        data.value = param;
      }
    }
  },
  // {
  //   field: "needs_destination_coordinates",
  //   headerName: "",
  //   pinned: "left",
  //   suppressSizeToFit: true,
  //   maxWidth: 70,
  //   cellRenderer: "OriginDestinationEditRenderer",
  //   export: false,
  //   cellRendererParams: {
  //     openDestination: (param) => {
  //       console.log(param)
  //       destinationDrawerVisible.value = true
  //       data.value = param
  //     },
  //     onpenOrigin: (param) => {
  //       originDrawerVisible.value = true
  //       data.value = param
  //     }
  //   }
  // },
  {
    field: "id",
    headerName: "Id",
    pinned: "left",
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
    filter: "agTextColumnFilter",
    export: true,
    cellClass: "stringType"
  },
  {
    field: "integration_id",
    headerName: t("Integration Id"),
    pinned: "left",
    filter: "agTextColumnFilter",
    export: true,
    cellClass: "stringType"
  },
  // {
  //   field: "channel",
  //   headerName: t("Channel"),
  //   filter: "agTextColumnFilter",
  //   export: true,
  //   cellClass: "stringType"
  // },
  {
    field: "channel",
    headerName: t("Channel"),
    filter: "agSetColumnFilter",
    export: true,
    sortable: true,
    cellClass: "stringType",
    minWidth: 120,
    suppressSizeToFit: true,
    valueFormatter: (params) => params.data.channel,
    filterParams: {
      values: function(params) {
        params.success(Object.keys(channels));
      },
      valueFormatter: (params) => t(channels[params.value])
    }
  },

  {
    field: "hub.integration_id",
    headerName: t("Hub I. Id"),
    filter: "agTextColumnFilter",
    export: true,
    cellClass: "stringType"
  },
  {
    field: "hub_id",
    headerName: t("Hub Id"),
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "hub.name",
    headerName: t("Hub Name"),
    filter: "agTextColumnFilter",
    export: true,
    cellClass: "stringType"
  },
  {
    field: "courier_id",
    headerName: t("Driver"),
    filter: "agSetColumnFilter",
    export: true,
    cellClass: "stringType",
    sortable: true,
    cellRenderer: "CourierRenderer",
    valueFormatter: (params) => {
      return params.value ? params.data.courier.name : params.value;
    },
    filterParams: {
      values: function(params) {
        let data = couriers.value.map(x => x.id);
        data.push("");
        params.success(data);
      },
      valueFormatter: (params) => {
        return params.value ? couriers.value.find(x => x.id === Number(params.value))?.name : params.value;
      }
    }
  },
  {
    field: "status_category_slug",
    headerName: t("Status"),
    filter: "agSetColumnFilter",
    export: true,
    sortable: true,
    cellClass: "stringType",
    minWidth: 120,
    suppressSizeToFit: true,
    cellRenderer: "StatusRenderer",
    valueFormatter: (params) => params.data.status_name,
    filterParams: {
      values: function(params) {
        params.success(Object.keys(statues));
      },
      valueFormatter: (params) => t(statues[params.value])
    }
  },
  {
    field: "starts_at",
    headerName: t("Start Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "ends_at",
    headerName: t("End Date"),
    filter: "agDateColumnFilter",
    export: true,
    cellClass: "dateType",
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "origin_address",
    headerName: t("Origin"),
    sortable: true,
    cellClass: "stringType",
    maxWidth: 300,
    suppressSizeToFit: true,
    export: true
  },
  {
    field: "destination_address",
    headerName: t("Destination"),
    sortable: true,
    cellClass: "stringType",
    maxWidth: 300,
    suppressSizeToFit: true,
    export: true
  },
  {
    field: "unit_quantity",
    headerName: t("Unit Capacity"),
    filter: "agTextColumnFilter",
    cellClass: "stringType",
    sortable: true,
    maxWidth: 300,
    suppressSizeToFit: true,
    export: true

  },
  {
    field: "was_delayed",
    headerName: t("Delay"),
    filter: "agSetColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType",
    cellStyle: { textAlign: "center", display: "flex", alignItems: "center", justifyContent: "center" },
    width: 120,
    cellRenderer: "DelayRenderer",
    filterParams: {
      values: function(params) {
        params.success([true, false]);
      },
      valueFormatter: (params) => {
        return !JSON.parse(params.value) ? "Gecikmedi" : "Gecikti";
      }
    }
  },
  {
    field: "distance",
    headerName: `${t("Distance")} (km)`,
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType",
    valueFormatter: (params) => (params.value / 1000).toFixed(2)
  },
  {
    field: "duration",
    headerName: `${t("Duration")} (${t("Min")})`,
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType",
    valueFormatter: (param) => (param.value / 60).toFixed(1)
  },
  {
    field: "origin_name",
    headerName: t("Sender"),
    sortable: true,
    export: true,
    cellClass: "stringType",
    filter: "agTextColumnFilter"
  },
  {
    field: "destination_name",
    headerName: t("Recipient"),
    sortable: true,
    export: true,
    cellClass: "stringType",
    filter: "agTextColumnFilter"
  },
  {
    field: "tracking_code",
    headerName: t("Tracking Code"),
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "merged_task_id",
    headerName: t("Merged Task Id"),
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "type_slug",
    headerName: t("Type"),
    filter: "agTextColumnFilter",
    valueFormatter: (params) => formatter.typeSlug(params),
    sortable: true,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "slot",
    headerName: "Slot",
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "action_slug",
    headerName: t("Delivery Direction"),
    filter: "agSetColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType",
    valueFormatter: (params) => {
      return params.value === "pickup" ? t("Pickup") : t("Drop off");
    },
    filterParams: {
      values: function(params) {
        params.success(["pickup", "drop_off"]);
      },
      valueFormatter: (params) => {
        return params.value === "pickup" ? t("Pickup") : t("Drop off");
      }
    }
  },
  {
    field: "updated_at",
    headerName: t("Updated"),
    filter: "agDateColumnFilter",
    sortable: true,
    export: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "created_at",
    headerName: t("Created Date"),
    filter: "agDateColumnFilter",
    export: true,
    sortable: true,
    cellClass: "dateType",
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params)
  },
  {
    field: "team_id",
    headerName: t("Team Id"),
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType"
  },
  {
    field: "team.name",
    headerName: t("Team Name"),
    filter: "agTextColumnFilter",
    sortable: true,
    export: true,
    cellClass: "stringType"
    // hide:true
  },
  {
    field: "tags",
    headerName: t("Tags"),
    export: false,
    cellClass: "stringType",
    valueFormatter: (params) => {
      if (params.value && Array.isArray(params.value)) {
        return params.value.join(", ");
      }
      return params.value;
    }
    // hide:true
  },
  {
    field: "items",
    headerName: t("Reference No"),
    filter: "agTextColumnFilter",
    hide: true,
    sortable: false,
    valueGetter: params => params.data.items.map(item => item.order_id),
    valueFormatter: params => params.value.join(", ")
  },
  {
    field: "unlocked_at",
    hide: true,
    filter: "agSetColumnFilter",
    keyCreator: (params) => params.value ? "Kilit Açık" : "Kilitli"
  }

];


const optionalFilter = [
  {
    old: "items",
    new: "items.order_id"
  }
];

const getCouriers = () => {
  api("customer/couriers")
    .then((response) => {
      const couriersResponse = response.data.sort(function(a, b) {
        return a.name.localeCompare(b.name);
      });
      dispatch("operations/setCouriers", [...couriersResponse]);
    });
};
const getReasonSelect = () => {
  return api.post("/components/task-status-reason-select")
    .then((response) => {
      reasonList.value = response.data;
    });
};


onMounted(() => {
  listener();
  onEmitter();
  getCouriers();
  getReasonSelect();
});


onUnmounted(() => {
  offEmitter();
});

onActivated(() => {

  if (!gridApi.value) return;

  let selectedRows = gridApi.value.getSelectedRows();

  if (selectedRows.length === 1) dispatch("operations/setShowSpecificTaskOnMap", selectedRows[0]);

  setTasksOnMap();

});
const setTasksOnMap = () => {
  let rowData = [];
  gridApi.value.forEachNodeAfterFilter(node => rowData.push(node.data));
  dispatch("operations/setShowTasksOnMap", rowData);

};


watch(couriers, () => {
  if (gridApi.value) {
    gridApi.value.destroyFilter("courier_id");
    const courierId = gridApi.value.getFilterInstance("courier_id");
    courierId.refreshFilterValues();
  }
}, { deep: true });


const listener = () => {
  Echo.private(companyChannel.value).listen(".task.status.updated", taskStatusUpdated);
  Echo.private(companyChannel.value).listen(".task.lock.updated", taskLockUpdated);
  Echo.private(companyChannel.value).listen(".task.confirmed", taskConfirmed);
  Echo.private(companyChannel.value).listen(".task.claimed", taskClaimed);
  Echo.private(companyChannel.value).listen(".task.coordinates.updated", taskCoordinatesUpdated);
};
const taskStatusUpdated = (param) => {
  if (grid.value?.loading) return;

  let newTask = gridApi.value?.getRowNode(param.task.id);
  if (newTask) {
    let _newTask = newTask.data;
    _newTask.status = param.status;
    _newTask.status_category_slug = param.status_category_slug;
    gridApi.value.applyTransaction({ update: [_newTask] });

  }
};

const taskCoordinatesUpdated = (param) => {
  api(`customer/tasks/${param.task.id}`)
    .then((r) => {
      task.value = r;
    }).finally(() => {
    let updatedTask = gridApi.value?.getRowNode(param.task.id);
    if (updatedTask) {

      let _updatedTask = task.value;
      if (param.type === "origin") {
        _updatedTask.needs_origin_coordinates = param.needs_origin_coordinates;
      }
      if (param.type === "destination") {
        _updatedTask.needs_destination_coordinates = param.needs_destination_coordinates;
      }
      gridApi.value.applyTransaction({ update: [_updatedTask] });
      gridApi.value.refreshCells({
        rowNodes: [updatedTask],
        force: true
      });
      dispatch("operations/setShowSpecificTaskOnMap", task.value);
    }
  });
};
const taskLockUpdated = (param) => {
  if (grid.value?.loading) return;

  let newTask = gridApi.value?.getRowNode(param.task.id);
  if (newTask) {
    let _newTask = newTask.data;
    param.action === "lock" ? _newTask.unlocked_at = null : _newTask.unlocked_at = dayjs().toISOString();
    gridApi.value.applyTransaction({ update: [_newTask] });
    gridApi.value.refreshCells({
      force: true
    });
  }
};

const taskConfirmed = (param) => {
  if (grid.value?.loading) return;

  let newTask = gridApi.value?.getRowNode(param.task.id);
  if (newTask) {
    let _newTask = newTask.data;
    _newTask.confirmed_at = dayjs().toISOString();
    if (!_newTask.unlocked_at) {
      _newTask.unlocked_at = dayjs().toISOString();
    }
    gridApi.value.applyTransaction({ update: [_newTask] });
    gridApi.value.refreshCells({
      force: true
    });
  }

};


const updateDestination = () => {
  if (mapVisible.value) {
    loader.value.show();
    let bodyy = {
      destination: {
        name: destinationUpdateForm.name,
        address: locationEdit.value.address,
        lat: locationEdit.value.lat,
        lng: locationEdit.value.lng
      }
    };
    api.put(`customer/tasks/${selectedRows.value[0].id}`, bodyy)
      .then((r) => {
        destinationUpdateForm.address = r.destination_address;
        hubAddressDialogVisible.value = false;
        grid.value.refresh();
        toast.success(t("Operation Successful"));
        dispatch("operations/setShowSpecificTaskOnMap", r);
        mapVisible.value = false;
      }).catch((err) => {
      toast.error(err.data.message);
    }).finally(() => loader.value.hide());
  } else {
    loader.value.show();
    let body = {
      destination: {
        name: destinationUpdateForm.name,
        address: destinationUpdateForm.address
      }
    };
    api.put(`customer/tasks/${selectedRows.value[0].id}`, body)
      .then((r) => {
        hubAddressDialogVisible.value = false;
        grid.value.refresh();
        toast.success(t("Operation Successful"));
        dispatch("operations/setShowSpecificTaskOnMap", r);
      }).catch((err) => {
      toast.error(err.data.message);
    }).finally(() => loader.value.hide());
  }
};


const taskClaimed = (param) => {
  if (grid.value?.loading) return;

  let newTask = gridApi.value?.getRowNode(param.task.id);
  if (newTask) {
    let _newTask = newTask.data;
    _newTask.courier = param.courier;
    _newTask.courier_id = param.courier.id;
    _newTask.status = param.task.status;
    _newTask.status_category_slug = param.task.status.status_category_slug;
    gridApi.value.applyTransaction({ update: [_newTask] });
    gridApi.value.refreshCells({
      force: true
    });
  }
};

const activeHubChanged = () => {
  grid.value && grid.value.refresh();
};

const getDataOfSelectedCourier = (param) => {
  if (param) {
    if (couriers.value.find(courier => courier.id === param.id)) {
      const courier = gridApi.value.getFilterInstance("courier_id");
      courier.setModel({
        values: [param.id]
      });
      gridApi.value.onFilterChanged();
    }
  } else {
    gridApi.value.destroyFilter("courier_id");
  }
};

const getDataOfSelectedMergedTaskId = (param) => {

  if (param) {

    const courier = gridApi.value.getFilterInstance("merged_task_id");

    courier.setModel({
      filterType: "text",
      type: "startsWith",
      filter: param
    });

    gridApi.value.onFilterChanged();

  } else {
    gridApi.value.destroyFilter("merged_task_id");
  }

};

const rowClicked = (param) => {
  dispatch("operations/setShowSpecificTaskOnMap", param.data);
};

const rowDoubleClicked = (e) => {
  gridApi.value.showLoadingOverlay();
  api(`customer/tasks/${e.data.id}`)
    .then((response) => emitter.emit("task_double_clicked", response))
    .finally(() => gridApi.value.hideOverlay());
};

const handleSelectionChanged = () => {

  selectedRows.value = gridApi.value.getSelectedRows();

  if (!selectedRows.value.length) emitter.emit("clear_task_markers");

  if (selectedRows.value.length === 1) {

    const task = selectedRows.value[0];

    dispatch("operations/setShowSpecificTaskOnMap", task);

    return;
  }

  dispatch("operations/setShowSpecificTaskOnMap", null);
};

const openUpdateSlotDrawer = () => {
  updateSlotVisible.value = true;
};

const openUpdateStatusDrawer = () => {
  updateStatusVisible.value = true;
};

const closeUpdateSlotDrawer = (param) => {
  updateSlotVisible.value = false;
  if (param) {
    grid.value && grid.value.refresh();
  }
};

const closeUpdateStatusDrawer = () => {
  updateStatusVisible.value = false;
};

const openDialog = () => {
  if (selectedRows.value?.length > 0) {
    console.log("selectedCourier", selectedCourier.value);
    dialogVisible.value = true;
  }
};

const openImportExcelDrawer = () => {
  importExcelDrawerVisible.value = true;
};

const closeImportExcelDrawer = () => {
  importExcelDrawerVisible.value = false;
};

const courierChange = () => {
  dialogVisible.value = false;
  gridApi.value.showLoadingOverlay();
  api.patch(`customer/task-assignment/${selectedCourier.value.id}`, { task_ids: selectedRows.value.map((x) => x.id), status_reason_slug: reasonform.status_reason_slug,notes:reasonform.notes})
    .then(() => {
      grid.value && grid.value.refresh();
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => gridApi.value.hideOverlay());
};

const clearFilter = () => {
  gridApi.value.setFilterModel(null);
};

// const startAtFilterSelectedHour = (param) => {
//
//   debugger
//
//
//   if (param) {
//     const starts_at = gridApi.value.getFilterInstance("starts_at")
//     let example = {
//       dateFrom: dayjs(param.starts_at).format("YYYY-MM-DD HH:mm"),
//       dateTo: dayjs(param.ends_at).format("YYYY-MM-DD HH:mm"),
//       filterType: "date",
//       type: "inRange",
//     }
//     starts_at.setModel(example)
//     gridApi.value.onFilterChanged()
//   } else {
//     gridApi.value.destroyFilter("starts_at")
//   }
// }

const exportExcel = () => grid.value.openExportExcelDrawer();

const onRefresh = () => grid.value && grid.value.refresh();

const onEmitter = () => {
  emitter.on("filter_by_courier", getDataOfSelectedCourier);
  emitter.on("active.hub.changed", activeHubChanged);
  emitter.on("refresh_tasks", () => grid.value && grid.value.refresh());
};

const offEmitter = () => {
  emitter.off("filter_by_courier", getDataOfSelectedCourier);
  emitter.off("active.hub.changed", activeHubChanged);
  emitter.off("refresh_tasks", () => grid.value && grid.value.refresh());
};
watch(selectedRows, () => {

  if (selectedRows.value.find((x) => x.status_category_slug === "completed")) {
    selectedRowDisable.value = true;
  } else if (selectedRows.value.find((x) => x.status_category_slug === "cancelled")) {
    selectedRowDisable.value = true;
  } else {
    selectedRowDisable.value = false;
  }

});

const startAtFilterSelectedHour = (param) => {


  if (param) {

    const starts_at = gridApi.value.getFilterInstance("starts_at");
    let example = {
      dateFrom: dayjs(param.starts_at).format("YYYY-MM-DD HH:mm"),
      dateTo: dayjs(param.ends_at).format("YYYY-MM-DD HH:mm"),
      filterType: "date",
      type: "inRange"
    };
    starts_at.setModel(example);
    gridApi.value.onFilterChanged();

    return;
  }

  if (selectedDate.value) {

    const startsAt = dayjs(selectedDate.value).startOf("day");
    const endsAt = dayjs(selectedDate.value).endOf("day");

    const starts_at = gridApi.value.getFilterInstance("starts_at");
    let example = {
      dateFrom: dayjs(startsAt).format("YYYY-MM-DD HH:mm"),
      dateTo: dayjs(endsAt).format("YYYY-MM-DD HH:mm"),
      filterType: "date",
      type: "inRange"
    };
    starts_at.setModel(example);
    gridApi.value.onFilterChanged();
  } else {
    gridApi.value.destroyFilter("starts_at");
  }
};


const onGridReady = () => {
  startAtFilterSelectedHour();
};

watch(selectedDate, () => {
  startAtFilterSelectedHour();
});

const closeDestinationDrawer = () => {
  destinationDrawerVisible.value = false;
};

const closeOriginDrawer = () => {
  originDrawerVisible.value = false;
};

const openQrVisible = () => {
  qrVisible.value = true;
  phoneDialogVisible.value = false;
  formPhoneDialog.show_origin_phone = false;
  formPhoneDialog.show_destination_phone = false;


};

watch(selectedRows, () => {
  if (selectedRows.value && selectedRows.value[0]) {
    locationEdit.value.searchAddress = selectedRows.value[0].destination_address;
    locationEdit.value.lat = selectedRows.value[0].destination_lat;
    locationEdit.value.lng = selectedRows.value[0].destination_lng;
    destinationUpdateForm.name = selectedRows.value[0].destination_name;
    destinationUpdateForm.address = selectedRows.value[0].destination_address;
  }
}, { deep: true });

watch(dialogVisible, () => {
  if (dialogVisible.value === false) {
    reasonform.status_reason_slug = null
    reasonform.notes = null
  }
})

const printImage = () => {
  // Base64 formatındaki görüntüyü al
  const base64Imagee = base64Image.value; // Gerçek base64 verisini buraya ekleyin


  // Yeni bir pencere oluştur
  let printWindow = window.open("", "_blank");

  // Pencerenin içeriğini base64 formatındaki görüntü ile doldur
  printWindow.document.write("<img src=\"" + base64Imagee + "\"/>");


  // Pencerenin içeriğini yazdır
  printWindow.document.close();
  printWindow.print();


};


const dateUpdate = () => {

  let body = {
    starts_at: dayjs(dayOffTime.value[0]).utc().format(),
    ends_at: dayjs(dayOffTime.value[1]).utc().format(),
    _method: "PUT"
  };
  api.post("customer/tasks/" + selectedRows.value[0].id, body)
    .then(() => {
      dateUpdateVisible.value = false;
      grid.value && grid.value.refresh();

      toast.success("İşlem Başarılı");
    }).catch((err) => {
    toast.error(err.data.message);
  });
};
const openMap = () => {
  mapVisible.value = true;
  hubAddressDialogVisible.value = false;
};

const closeMapPin = () => {
  mapVisible.value = false;
};
const selectedTags = (param, alltags) => {
  // Önce mevcut tag filtrelerini temizle
  delete filter["filter[tags]"];
  delete filter["filter[tags_all]"];

  if (param && (!Array.isArray(param) || param.length > 0)) {
    const paramArray = Array.isArray(param) ? param.flat() : [param];

    if (alltags) {
      filter["filter[tags_all]"] = paramArray;
    } else {
      filter["filter[tags]"] = paramArray;
    }
  }

  grid.value.refresh(); // ag-Grid'i güncelle
};
const closeDialog = () => {
  qrVisible.value = false;
};
const backFirst = () => {
  phoneDialogVisible.value = false;
};

const closeHubAddressDialog = () => {

  hubAddressDialogVisible.value = false;
};

const sideBar = {
  toolPanels: [
    {
      id: "columns",
      labelDefault: "Columns",
      labelKey: "columns",
      iconKey: "columns",
      toolPanel: "agColumnsToolPanel",
      toolPanelParams: {
        suppressRowGroups: true,
        suppressSyncLayoutWithGrid: true,
        suppressValues: true,
        suppressPivots: true,
        suppressPivotMode: true,
        suppressColumnFilter: true,
        suppressColumnSelectAll: true,
        suppressColumnExpandAll: true
      }
    }
  ],
  defaultToolPanel: "columns"
};

const dateUpdateOpen = () => {

  dayOffTime.value = [
    dayjs(selectedRows.value[0].starts_at).toDate(),
    dayjs(selectedRows.value[0].ends_at).toDate()
  ];
  dateUpdateVisible.value = true;
};

const closeDateUpdate = () => {
  dateUpdateVisible.value = false;
};
</script>

<template>

  <div class="flex flex-col h-full w-full overflow-hidden">
    <ProcessView />
    <div
      v-if="gridApi"
      class="flex flex-col sm:flex-row sm:items-center w-full text-xxxs bg-slate-50 border-t border-b border-slate-300 px-2">
      <TaskHoursFilteredByDay
        :dayChangeEnabled="true"
        @startAtFilterSelectedHour="startAtFilterSelectedHour"
      />
      <TagTaskFilter
        @selectedTags="selectedTags"
      />
      <div
        class="flex items-center justify-start sm:justify-end flex-grow border-t border-slate-300 py-1.5 sm:border-none space-x-2">

        <el-dropdown trigger="click">

          <ButtonToolTip :tooltipText="$t('Actions')" position="left">
            <el-button
              size="small"
            >
              <FontAwesomeIcon icon="bolt-lightning" />
            </el-button>
          </ButtonToolTip>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                :disabled="selectedRows.length === 0 ||  selectedRows.length > 10 "
                @click="openUpdateStatusDrawer()"
              >

                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="pen" />
                  <div class="ml-1.5">
                    {{ $t("Update Status") }}
                  </div>
                </div>

              </el-dropdown-item>
            </el-dropdown-menu>
            <el-dropdown-menu>
              <el-dropdown-item
                :disabled="!hubIdOfSelectedRows  || selectedHub === null"
                @click="openUpdateSlotDrawer"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="clock" />
                  <div class="ml-1.5">
                    {{ $t("Slot Shifting") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
            <el-dropdown-menu>
              <el-dropdown-item
                :disabled="selectedRows.length !== 1"
                @click="dateUpdateOpen"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="calendar-pen" />
                  <div class="ml-1.5">
                    {{ $t("Date Update") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>


            <el-dropdown-menu>
              <el-dropdown-item
                :disabled="!selectedCourier || selectedRows.length <= 0 || selectedRowDisable"
                @click="openDialog"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="exchange-alt" />
                  <div class="ml-1.5">
                    {{ $t("Transfer") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
            <el-dropdown-menu>
              <el-dropdown-item
                :disabled="shouldDisableButton"
                @click="hubAddressDialogOpen"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="location-dot" />
                  <div class="ml-1.5">
                    {{ $t("Address Update") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>

            <el-dropdown-menu>
              <el-dropdown-item
                @click="openImportExcelDrawer"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="upload" />
                  <div class="ml-1.5">
                    {{ $t("Import") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>


            <el-dropdown-menu>
              <el-dropdown-item
                @click="exportExcel"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="file-excel" />
                  <div class="ml-1.5">
                    {{ $t("Export") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
            <el-dropdown-menu>
              <el-dropdown-item
                :disabled="selectedRows.length !== 1"
                @click="openQrVisible"
              >
                <div class="flex items-center justify-start">
                  <FontAwesomeIcon icon="qrcode" />
                  <div class="ml-1.5">
                    {{ $t("Tag") }}
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>


          </template>
        </el-dropdown>


        <ButtonToolTip :tooltipText="$t('Refresh')" position="bottom">
          <el-button
            size="small"
            @click="onRefresh"
          >
            <FontAwesomeIcon icon="refresh" />
          </el-button>
        </ButtonToolTip>
        <ButtonToolTip :tooltipText="$t('Clear Filters')" position="left">
          <el-button
            size="small"
            @click="clearFilter"
          >
            <FontAwesomeIcon icon="filter-circle-xmark" />
          </el-button>
        </ButtonToolTip>
      </div>
    </div>
    <div class="h-full w-full overflow-hidden">
      <SSDataGrid
        ref="grid"
        v-model="gridApi"
        url="customer/tasks"
        exportUrl="customer/export/tasks"
        columnStateSlug="active_tasks"
        :restore-column-state-enabled="true"
        :columns="columnDefs"
        :sideBar="sideBar"
        :filter="filter"
        :paginationPageSize="200"
        :optionalFilter="optionalFilter"
        :waitFilters="true"
        excelExportType="tasks"
        @rowDoubleClicked="rowDoubleClicked"
        @handleSelectionChanged="handleSelectionChanged"
        :rowLength="selectedRows.length ? selectedRows.length : null"
        @rowDataUpdated="setTasksOnMap"
        @onFilterChanged="setTasksOnMap"
        @onGridReady="onGridReady"
      />
    </div>
  </div>
  <el-drawer
    v-model="importExcelDrawerVisible"
    class="customized-drawer"
    :title="t('Batch Task Creation')"
    append-to-body
    destroy-on-close
  >
    <ExcelImport
      @close="closeImportExcelDrawer"
    />
  </el-drawer>
  <el-drawer
    v-model="updateSlotVisible"
    class="customized-drawer"
    :title="t('Slot Shifting')"
    append-to-body
    destroy-on-close
  >
    <UpdateSlot
      :selectedTasks="selectedRows"
      @close="closeUpdateSlotDrawer"
      :hubIdOfSelectedRows="hubIdOfSelectedRows"
    />
  </el-drawer>
  <el-drawer
    v-model="updateStatusVisible"
    class="customized-drawer"
    :title="t('Update Status')"
    append-to-body
    destroy-on-close
  >
    <UpdateStatusDrawer
      :selectedTask="selectedRows"
      @close="closeUpdateStatusDrawer"
    />
  </el-drawer>
  <el-dialog
    v-model="dialogVisible"
    title="Transfer"
    class="customized-header__dialog"
    destroy-on-close
    :width="450"
    center>
    <div class="p-6 bg-white rounded-lg shadow w-full max-w-2xl">
      <div class="p-4 text-sm">

        <span class="font-bold">{{ selectedCourier.name }}</span> sürücüsüne <span
        class="font-bold">{{ selectedRows.length }} teslimat</span> transfer etmek için güncelleme sebebi seçin:
      </div>
      <!--      {{ t('Do you accept to selected deliveries transfer', { message: selectedRows.length }) }}-->
      <el-form label-position="top" class="px-4">
        <div class="flex flex-col  gap-4">
          <el-form-item class="w-full">
            <el-select v-model="reasonform.status_reason_slug" placeholder="Güncelleme Sebebi Seçin" class="w-full">
              <el-option  v-for="item in reasonList"
                          :key="item.slug"
                          :label="item.name"
                          :value="item.slug" />

            </el-select>
          </el-form-item>

          <el-form-item class="w-full">
            <el-input v-model="reasonform.notes" placeholder="Not" rows="3" class="w-full" />
          </el-form-item>
        </div>

        <div class="flex justify-end gap-2 mt-6">
          <el-button @click="dialogVisible = false">{{ t("Cancel") }}</el-button>
          <el-button @click="courierChange" type="primary">
            {{ t("Confirm") }}
          </el-button>
        </div>
      </el-form>
    </div>
    <!--    <div class="text-center pt-3">-->
    <!--      {{ t('Do you accept to selected deliveries transfer', { message: selectedRows.length }) }}-->
    <!--    </div>-->
    <!--    <el-form label-position="top"-->
    <!--             class="mt-7 space-y-12">-->
    <!--      <div class="flex flex-col lg:flex-row w-full gap-6">-->
    <!--        <el-form-item >-->
    <!--          <el-select  placeholder="Transfer sebebi seçin" class=" mb-2">-->
    <!--            <el-option label="Kurye hasta" value="courier_sick"></el-option>-->
    <!--            <el-option label="Araç arızası" value="vehicle_problem"></el-option>-->
    <!--            <el-option label="Rota optimizasyonu" value="route_optimization"></el-option>-->
    <!--            <el-option label="Diğer" value="other"></el-option>-->
    <!--          </el-select>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item >-->
    <!--          <el-input  placeholder="Not alanı" rows="3" class="w-full"></el-input>-->
    <!--        </el-form-item>-->
    <!--      </div>-->
    <!--    </el-form>-->
    <!--    <template #footer>-->
    <!--      <div class="dialog-footer pb-6">-->
    <!--        <el-button @click="dialogVisible = false">{{ t('Cancel') }}</el-button>-->
    <!--        <el-button @click="courierChange" type="primary">-->
    <!--          {{ t('Confirm') }}-->
    <!--        </el-button>-->
    <!--      </div>-->
    <!--    </template>-->
  </el-dialog>
  <el-drawer
    v-model="destinationDrawerVisible"
    class="customized-drawer"
    :title="$t('Destination Edit')"
    append-to-body
    destroy-on-close
  >
    <DestinationEditDrawer

      :data="data"
      @close="closeDestinationDrawer"
    />
  </el-drawer>
  <el-drawer
    v-model="originDrawerVisible"
    class="customized-drawer"
    :title="$t('Origin Edit')"
    append-to-body
    destroy-on-close
  >
    <OriginEditDrawer

      :data="data"
      @close="closeOriginDrawer"
    />
  </el-drawer>
  <el-dialog
    v-model="qrVisible"
    :title="t('Tag')"
    class="el-dialog customized-dialog customized-dialog--medium el-overlay-dialog "
    style="bottom:initial !important;"

    center
    top="0"
  >
    <div class="w-full  flex " :class="[phoneDialogVisible ? 'h-120': 'h-108']">
      <div class="flex-grow relative">
        <div class="absolute inset-0 overflow-y-auto">
          <div class="p-1 " :class="phoneDialogVisible ?'flex items-center justify-center':''">
            <img v-if="phoneDialogVisible" class="w-full" :src="base64Image">
            <el-form v-if="phoneDialogVisible === false" label-position="top" class="grid grid-cols-12 gap-4">
              <div class="col-span-12 flex items-center justify-between">
                <div class="text-sm text-slate-700 font-medium">{{ $t("Show Destination Phone") }}</div>
                <div>
                  <el-switch v-model="formPhoneDialog.show_destination_phone" />
                </div>
              </div>
              <div class="col-span-12 flex items-center justify-between">
                <div class="text-sm text-slate-700 font-medium">{{ $t("Show Origin Phone") }}</div>
                <div>
                  <el-switch v-model="formPhoneDialog.show_origin_phone" />
                </div>
              </div>
              <div class="col-span-12">
                <el-form-item :label="t('Exit Point Name')" prop="hubName">
                  <el-input
                    v-model="qrForm.origin_name"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="col-span-12">
                <el-form-item :label="t('Exit Point Address')" prop="hubName">
                  <el-input v-model="qrForm.origin_address"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="col-span-12">
                <el-form-item :label="t('Exit Point Phone')" prop="hubName">
                  <PhoneInput class="w-full" v-model="userPhone" />
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="phoneDialogVisible === false" @click="closeDialog">{{ t("Cancel") }}</el-button>
        <el-button v-if="phoneDialogVisible === false" @click="getBarkodİmage" type="primary">
          {{ t("Show") }}
        </el-button>
         <el-button v-if="phoneDialogVisible" @click="backFirst">{{ t("Turn Back") }}</el-button>
        <el-button v-if="phoneDialogVisible" @click="printImage" type="primary">
          {{ t("Print") }}
        </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="hubAddressDialogVisible"
             width="720px"
             center
             destroy-on-close
             top="30px">

    <template #header>
      <div class="text-indigo-600 font-semibold text-left mr-auto border-b pb-2"
           style="font-size: 18px;">
        {{ t("Address Update") }}
      </div>
    </template>
    <LoadingBlock ref="loader" />
    <div class="grid grid-cols-4 gap-4 mb-5" v-if="selectedRows[0]">
      <div class="col-span-1">
        <span class="font-bold text-slate-600">Alıcı Adı</span>
        <hr class="my-2"> <!-- Divider eklenmiştir -->
        <div class="text-xs">{{ selectedRows[0].destination_name }}</div>
      </div>
      <div class="col-span-3">
        <span class="font-bold text-slate-600">Alıcı Adres</span>
        <hr class="my-2"> <!-- Divider eklenmiştir -->
        <div class="text-xs">{{ selectedRows[0].destination_address }}</div>
      </div>
    </div>
    <div class="flex flex-row mb-6 justify-between items-center">
      <div class="flex flex-col mr-2">
               <span>
          {{ t("Customer") }}
        </span>

        <el-input v-model="destinationUpdateForm.name">
        </el-input>
      </div>
      <div v-if="!mapVisible" class="flex flex-col mr-2">
               <span>
          {{ t("Current Address") }}
        </span>

        <el-input style="width: 320px;" :disabled="mapVisible" v-model="destinationUpdateForm.address">
        </el-input>
      </div>
      <div class="flex flex-col mr-2 mt-4">
        <el-checkbox v-model="mapVisible">
          {{ t("Update with Coordinates") }}
        </el-checkbox>
      </div>
    </div>

    <span v-if="mapVisible">
      {{ t("New Address Search") }}
    </span>
    <div v-if="mapVisible">
      <PinMap v-if="hubAddressDialogVisible"
              :hidden-place-holder="true"
              v-model="locationEdit"
              :poligonVisible="false"
              :customHeight="'h-56'" />
    </div>


    <template #footer>
      <div class="flex flex-row justify-between">
        <el-button @click="closeHubAddressDialog">{{ t("Cancel") }}</el-button>
        <el-button @click="updateDestination">
          {{ t("Update Task") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dateUpdateVisible"
    title="Tarih Güncelleme"
    class="customized-header__dialog"
    center>
    <div>
      <el-form label-position="top">
        <el-form-item :label="t('Başlama Tarihi / Bitiş Tarihi')" required>

          <el-date-picker
            v-model="dayOffTime"
            type="datetimerange"
            format="YYYY-MM-DD HH:mm"
            range-separator="İle"
            start-placeholder="Başlangış Tarihi"
            end-placeholder="Bitiş Tarihi"
          />
        </el-form-item>
      </el-form>

    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDateUpdate">{{ t("Cancel") }}</el-button>
        <el-button @click="dateUpdate" type="primary">
          {{ t('Confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>



