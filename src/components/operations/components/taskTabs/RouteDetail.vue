<script>
import DelayRenderer from "@/renderers/DelayRenderer.vue";
import PriorityRenderer from "@/renderers/PriorityRenderer.vue";
import StatusRenderer from "@/renderers/StatusRenderer.vue";

export default {
  components: {
    DelayRenderer,
    PriorityRenderer,
    StatusRenderer
  },
};
</script>
<script setup>
import {inject, computed, ref, onMounted, onActivated, onDeactivated} from "vue";
import {useStore} from "vuex"
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import dayjs from "dayjs";
import formatter from "@/class/formatter";
import {dateFilterParam} from "@/class";
import PlanTransferDrawer from "@/components/operations/components/taskTabs/components/PlanTransferDrawer.vue";
import {useI18n} from "vue-i18n";
import debounce from "lodash.debounce";
import {useToast} from "vue-toastification";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

const toast = useToast()
const {t} = useI18n()
const {dispatch, getters} = useStore();

const api = inject("api");
const emitter = inject("emitter");

const emit = defineEmits(["taskClicked"]);
const props = defineProps({
  payload: {
    type: Object,
    default: () => {
    },
  }
})

const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);
const activeTaskTab = computed(() => getters["tabs/activeTaskTab"]);
const selectedCourierForTransferTasks = computed(() => getters["operations/selectedCourier"])

const tasks = ref([])
const selectedRows = ref([])
const loader = ref()
const gridApi = ref(null);
const visiblePlanTransferDrawer = ref(false)
const data = ref()
const dialogVisible = ref(false);
const newRotate = ref(false);

const columnDefs = [
  {
    field: "id",
    headerName: "Id",
    pinned: "left",
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    checkboxSelection: true,
    filter: 'agTextColumnFilter',
  },
  {
    field: "route_order",
    headerName: t("Priority"),
    cellStyle: {textAlign: "center"},
    sort: 'asc',
    cellRenderer: "PriorityRenderer",
    filter: 'agTextColumnFilter',
  },
  {
    field: "slot",
    headerName: "Slot",
    filter: 'agTextColumnFilter',
  },
  {
    field: "tracking_code",
    headerName: t("Tracking Code"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "status_category_slug",
    headerName: t("Status"),
    filter: 'agSetColumnFilter',
    export: true,
    sortable: true,
    cellClass: 'stringType',
    minWidth: 120,
    suppressSizeToFit: true,
    cellRenderer: "StatusRenderer",
    valueFormatter: (params) => params.data.status_name,
    keyCreator: (params) => t(statues[params.value]),
  },
  {
    field: "action_slug",
    headerName: t("Delivery Direction"),
    filter: 'agSetColumnFilter',
    valueFormatter: (params) => params.value === "pickup" ? t("Pickup") : t("Drop off"),
    keyCreator: (params) => params.value === "pickup" ? t("Pickup") : t("Drop off"),
  },
  {
    field: "was_delayed",
    headerName: t("Delay"),
    cellStyle: {textAlign: "center"},
    width: 90,
    filter: true,
    cellRenderer: "DelayRenderer"
  },

  {
    field: "starts_at",
    headerName: t("Start Date"),
    width: 90,
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParam,
    valueFormatter: (params) => dayjs(params.value).format('DD.MM.YYYY'),
  },
  {
    field: "starts_at_on_route",
    headerName: t("Starts"),
    width: 90,
    valueFormatter: (params) => formatter.dateTime(params),
  },
  {
    field: "ends_at_on_route",
    headerName: t("Ends"),
    width: 90,
    valueFormatter: (params) => formatter.dateTime(params),
  },
  {
    field: "updated_at",
    headerName: t("Updated"),
    width: 90,
    valueFormatter: (params) => formatter.dateTime(params),
  },

  {
    field: "distance",
    headerName: `${t('Distance')} (km)`,
    valueFormatter: (param) => (param.value / 1000).toFixed(1) + " km",
  },
  {
    field: "duration",
    headerName: `${t('Duration')} (${t('Min')})`,
    valueFormatter: (param) => (param.value / 60).toFixed(1),
  },
  {
    field: "destination_name",
    headerName: t("Recipient"),
    filter: 'agTextColumnFilter',
  },
  {
    field: "destination_address",
    headerName: t("Destination"),
    filter: 'agTextColumnFilter',
  },
]

onMounted(() => {
  data.value = props.payload
  tasks.value = props.payload.tasks;
  listener()
})

onActivated(() => {
  dispatch("operations/setShowSpecificPlanOnMap", props.payload)
})

onDeactivated(() => {
  dispatch("operations/setShowSpecificPlanOnMap", null)
  gridApi.value.deselectAll();
})

const getPlanTaskList = () => {
  return api(`customer/task-plans/${props.payload.id}`)
      .then((response) => {
        data.value = response
        tasks.value = response.tasks;

      })

}

const debounceGetData = debounce(getPlanTaskList, 300)

const listener = () => {
  Echo.private(companyChannel.value).listen(".task.status.updated", taskStatusUpdated)
}

const stopListener = () => {
  Echo.private(companyChannel.value).stopListening(".task.status.updated")
}

const taskStatusUpdated = (param) => {

  let task = tasks.value.find(x => x.id === param.task_id)

  if (task) {
    emitter.emit("update_destination_marker", {param, task})
    debounceGetData()
  }
}

const openTransferDrawer = () => {
  visiblePlanTransferDrawer.value = true
}

const closeTransferDrawer = (param) => {
  visiblePlanTransferDrawer.value = false
  if (param) {
    emitter.emit("refresh_route_list")
    debounceGetData()
  }
}

const rowDoubleClicked = (e) => {
  loader.value.show()
  api(`customer/tasks/${e.data.id}`)
      .then((response) => emitter.emit("task_double_clicked", response))
      .finally(() => {
        loader.value.hide()
      })
}

const handleSelectionChanged = () => {
  selectedRows.value = gridApi.value.getSelectedRows()

  if (!selectedRows.value.length) emitter.emit("clear_task_markers")

  if (selectedRows.value.length === 1) {

    const task = selectedRows.value[0]

    dispatch("operations/setShowSpecificTaskOnMap", task)

    return
  }

  if (activeTaskTab.value.name === "TabTaskDetail") return;
  dispatch("operations/setShowSpecificTaskOnMap", null)
}

const closeTaskTab = () => {
  dispatch("tabs/closeTaskTab", activeTaskTab.value);
}

const openDialog = () => {
  if (selectedRows.value.length > 0) {
    dialogVisible.value = true;
  }
};

const courierChange = () => {

  dialogVisible.value = false
  gridApi.value.showLoadingOverlay();
  api.patch(`customer/task-assignment/${selectedCourierForTransferTasks.value.id}`, {task_ids: selectedRows.value.map((x) => x.id), reroute_plans: true})
      .then(() => {
        getPlanTaskList()
        dispatch("tasks/setSelectedCourierForTransferTasks", null)
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() => gridApi.value.hideOverlay())

}

const showLiveRoute = () => {
  let url = `'${data.value.hub.lat + "," + data.value.hub.lng}'/` + tasks.value.map(x => `'${x.destination_lat + "," + x.destination_lng}'`).join("/") + `/'${data.value.hub.lat + "," + data.value.hub.lng}'`
  window.open(`https://www.google.com/maps/dir/${url}`, "_blank");
}

const rowClicked = (param) => {
  emitter.emit("toggle_bounce_specific_task", {taskId: param.data.id, courierId: param.data.courier_id})
}




</script>

<template>
  <div v-if="data" class="flex flex-col h-full w-full overflow-hidden border-slate-300">
    <LoadingBlock ref="loader"/>
    <div
        class="flex flex-col md:flex-row md:items-center w-full bg-slate-50 border-t border-b border-slate-300"
    >
      <CourierTabView
          :courier="data.courier"
          :removeButtonVisible="false"
      />
      <div
          class="flex items-center justify-start sm:justify-end flex-grow border-t border-slate-300 py-2 sm:border-none space-x-2 pr-2">
        <ButtonToolTip :tooltipText="$t('Transfer')" position="bottom">
          <el-button
              @click="openDialog"
              :disabled="!selectedCourierForTransferTasks || selectedRows.length <= 0"
              size="small"
          >
            <FontAwesomeIcon icon="exchange-alt"/>
          </el-button>
        </ButtonToolTip>

        <ButtonToolTip :tooltipText="$t('Change Driver')" position="bottom">
          <el-button
              @click="openTransferDrawer"
              size="small"
          >
            <FontAwesomeIcon icon="person-walking-arrow-loop-left"/>
          </el-button>
        </ButtonToolTip>

        <ButtonToolTip :tooltipText="$t('Live Route')" position="left">
          <el-button
              @click="showLiveRoute"
              size="small"
          >
            <FontAwesomeIcon icon="route"/>
          </el-button>
        </ButtonToolTip>

      </div>
    </div>
    <div class="w-full h-full">
      <DataGrid
          v-model="gridApi"
          :dataSource="tasks"
          :columns="columnDefs"
          @rowClicked="rowClicked"
          @rowDoubleClicked="rowDoubleClicked"
          @handleSelectionChanged="handleSelectionChanged"
          :autoSizeColumn="true"
      />
    </div>
    <div
        class="flex flex-col md:flex-row md:items-center w-full bg-slate-50 border-t border-b border-slate-300 px-2 "
    >
      <div class="grid gap-4 grid-cols-2 md:grid-cols-4 text-slate-700 text-xs py-2">

        <div class="flex items-center font-bold ">{{ t('Plan Status') }}:<span
            class="font-medium ml-1"> {{ data.status.name }}</span></div>
        <div class="flex items-center font-bold ">{{ t('Distance') }}:<span
            class="font-medium ml-1"> {{ (data.distance / 1000).toFixed(1) + " km" }}</span></div>
        <div class="flex items-center font-bold ">{{ t('Duration') }}:<span
            class="font-medium ml-1"> {{ (data.duration / 60).toFixed(1) + " " + t('Min') }}</span></div>
        <div class="flex items-center font-bold ">{{ t('Start Point') }}:<span
            class="font-medium ml-1"> {{ data.tasks[0].origin_name }} </span></div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" width="30%" center>


    <div class="flex flex-col items-center">
      <div class="text-center">
<!--        {{ t('Do you accept to selected deliveries transfer', {message: selectedRows.length}) }}-->
<!--        {{ t('Do you accept to selected deliveries transfer', {message: selectedRows.length}) + ' ' + selectedCourierForTransferTasks.name + ' ' + t('adlı sürücüye atamak istediğinize emin misiniz?') }}-->
        {{  selectedRows.length + ' ' + t('Are you sure you want to assign the')  + ' ' + selectedCourierForTransferTasks.name + ' ' +t('selected tasks to the driver named ?') }}
      </div>
      <div class="flex items-center justify-center m-2">
        <el-checkbox v-model="newRotate" size="small" >
      <span class="text-xs">
        {{ $t("Rebuild the Remaining Route") }}
      </span>
        </el-checkbox>
      </div>
    </div>


    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t('Cancel') }}</el-button>
        <el-button @click="courierChange" type="primary">
          {{ t('Confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
  <el-drawer
      v-model="visiblePlanTransferDrawer"
      class="customized-drawer"
      :title="t('Plan Transfer')"
      append-to-body
      destroy-on-close
  >
    <PlanTransferDrawer :planId="data.id" :selectedOwnerId="data.hub_id" @close="closeTransferDrawer"/>
  </el-drawer>

</template>
