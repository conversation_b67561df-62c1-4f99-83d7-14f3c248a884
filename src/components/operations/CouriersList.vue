<script>
import { computed, reactive, toRefs, onBeforeUnmount, onMounted, inject, watch, ref ,nextTick} from "vue";
import { useStore } from "vuex";
import CouriersListItem from "@/components/operations/components/CouriersListItem.vue";
import CouriersListFilters from "@/components/operations/CouriersListFilters.vue";
import debounce from "lodash.debounce";
import { Options } from "@/class";
import { useI18n } from "vue-i18n";
import CourierHubFilter from "@/components/operations/components/filters/CourierHubFilter.vue";
import CourierTeamFilter from "@/components/operations/components/filters/CourierTeamFilter.vue";
import CourierVehicleTypeFilter from "@/components/operations/components/filters/CourierVehicleTypeFilter.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import CourierUpdate from "@/components/deliveries/CourierUpdate.vue";
import { useScroll } from "@vueuse/core";
import TabNewDeliverys from "@/views/Home/MainTabs/TabNewDeliverys.vue";
import TabTaskArchiveDeliveryes from "@/views/Home/MainTabs/TabTaskArchiveDeliveryes.vue";
import { useToast } from "vue-toastification";
import dayjs from "dayjs";
import NewBreak from "@/components/deliveries/components/NewBreak.vue";
import BreakEdit from "@/components/deliveries/components/BreakEdit.vue";
import { RecycleScroller } from 'vue3-virtual-scroller';
import 'vue3-virtual-scroller/dist/vue3-virtual-scroller.css';



export default {
  name: "CouriersList",
  methods: { dayjs },
  components: {
    BreakEdit,
    NewBreak,
    RecycleScroller,
    TabTaskArchiveDeliveryes, TabNewDeliverys,
    CourierUpdate,
    ButtonToolTip,
    CourierVehicleTypeFilter,
    CourierTeamFilter,
    CourierHubFilter,
    CouriersListItem,
    CouriersListFilters
  },
  props: {
    couriers: {
      type: Array,
      default: () => []
    },
    pinnedCourier: {
      type: Object
    },
    loading: {
      type: Boolean
    }
  },
  emits: ["onFiltered", "onRefresh"],
  setup(props, { emit }) {
    const { t } = useI18n();
    const emitter = inject("emitter");
    const { getters, dispatch } = useStore();

    const defaultFilters = {
      filter: {
        search: null,
        hub_id: 0,
        team_id: "all",
        activity: "all",
        connection: "all",
        transport: "all",
        is_working: "working",
        on_break:"all"
      },
      sort: {
        key: "name",
        dir: "asc"
      },
      options: {
        applyFiltersToMap: true
      }
    };


    const filterOptions = computed(() => {
      let options = localStorage.getItem("courierFilterOptions");
      return options ? options : "";
    });
    const selectedHub = computed(() => getters["operations/selectedHub"]);
    const isCouriersPanelOpen = computed(() => getters["ui/isCouriersPanelOpen"]);
    const show = computed(() => getters["ui/isCouriersFilterPanelOpen"]);

    const state = reactive({
      checkAll: false,
      selectedCouriers: false,
      isIndeterminate: false
    });
    const api = inject("api");
    const vehicleTypes = ref([]);
    const filteredCouriers = ref([]);
    const sortByNameIcon = ref("sort-alpha-down");
    const aproveDialogVisible = ref(false);
    const rejectModalVisible = ref(false);
    const deleteRejectModalVisible = ref(false);
    const rejectDialogVisible = ref(false);
    const sortByCapacityIcon = ref(null);
    const filterForm = ref(defaultFilters);
    const selectedCourierForEdit = ref(null);
    const courierListContainer = ref();
    const visible = ref(false);
    const newBreakRequestDrawer = ref(false);
    const editBreakRequestDrawer = ref(false);
    const breakRequests = ref();
    const request = ref();
    const breakRequestId = ref();
    const selectedCourier = ref({});
    const CourierBreakInfo = ref();
    const visibleItems = ref([]);
    const scrollTop = ref(0);


    const breakRequestRejection = ref();
    const toast = useToast();
    const rejectReasons = ref();
    const rejectReason = ref();
    const totalRequest = ref();
    const nextButonVisible = ref();
    const prevButtonVisible = ref();
    const pageValue = ref(1);
    const itemHeight = 57;
    const recycleScrollerRef = ref(null);
    const scrollPosition = ref(0);

    const requestForm = reactive({
      reviewerNotes: null,
      rejectionReasonSlug: null
    });
    const customColors = ref([
      { color: "#5ad660", percentage: 10 },
      { color: "#81c784", percentage: 20 },
      { color: "#aed581", percentage: 30 },
      { color: "#fff176", percentage: 40 },
      { color: "#ffd54f", percentage: 50 },
      { color: "#ffb74d", percentage: 60 },
      { color: "#ff8a65", percentage: 70 },
      { color: "#ef5350", percentage: 80 },
      { color: "#e53935", percentage: 90 },
      { color: "#ff0000", percentage: 100 }
    ]);


    const { y: formContainerScrollPosition } = useScroll(courierListContainer);

    const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);


    const toggleCouriersPanel = () => dispatch("ui/toggleCouriersPanel");

    const toggle = () => dispatch("ui/toggleCouriersFilterPanel");

    function sortCourierList(key) {
      const dir = filterForm.value.sort.dir === "asc" ? "desc" : "asc";

      filterForm.value.sort = { key, dir };

      sortByNameIcon.value =
        dir === "asc" ? "sort-alpha-down" : "sort-alpha-up";

      sortByCapacityIcon.value =
        dir === "asc" ? "sort-amount-up" : "sort-amount-down";

      switch (key) {
        case "name":
          sortByCapacityIcon.value = null;
          break;

        case "current_tasks_of_active_company_count":
          sortByNameIcon.value = null;
          break;
      }
    }

    const debouncedApplyCourierListFilters = debounce(applyCourierListFilters, 300);

    function applyCourierListFilters(debounce) {
      // props.couriers kontrolü
      if (!props.couriers || !Array.isArray(props.couriers)) {
        filteredCouriers.value = [];
        return;
      }

      try {
        filteredCouriers.value = props.couriers
          .filter((courier) => {
            if (!courier) return false;

            const q = filterForm.value.filter.search
              ? filterForm.value.filter.search.toLowerCase()
              : null;

            return q
              ? (courier.name && courier.name.toLowerCase().includes(q))
              : true;
          })
          .filter((courier) => {
            if (!courier || !courier.hub_ids) return false;

            if (filterForm.value.filter.hub_id === 0) {
              return true;
            }

            return courier.hub_ids.includes(filterForm.value.filter.hub_id);
          })
          .filter((courier) => {
            if (!courier || !courier.courier_location) return false;

            const workingStatus = courier.courier_location.is_working;
            if (filterForm.value.filter.is_working === "working") {
              return workingStatus === true;
            }

          if (filterForm.value.filter.is_working === "resting") {
            return workingStatus === false;
          }
          return true;
        })
          .filter((courier) => {
            if (!courier || !courier.courier_location) return false;

            const breakStatus = courier.courier_location.on_break;
            if (filterForm.value.filter.on_break === "on_break") {
              return breakStatus === true;
            }

            if (filterForm.value.filter.on_break === "working") {
              return breakStatus === false;
            }
            return true;
          })
        // .filter((courier) => {
        //   const onlineStatus = courier.courier_location.is_online;
        //
        //     if (filterForm.value.filter.connection === "online") {
        //       return onlineStatus === true;
        //     }
        //
        //     if (filterForm.value.filter.connection === "offline") {
        //       return onlineStatus === false;
        //     }
        //
        //     return true;
        //   })
          .filter((courier) => {
            if (!courier || !courier.team_ids) return false;

            if (filterForm.value.filter.team_id === "all") {
              return true;
            }

            return courier.team_ids.includes(filterForm.value.filter.team_id);
          })
          .filter((courier) => {
            if (!courier) return false;

            const activeTaskCount = courier.current_tasks_of_active_company_count;

            if (filterForm.value.filter.activity === "all") {
              return true;
            }

            if (filterForm.value.filter.activity === "on-duty") {
              return activeTaskCount > 0;
            }

            if (filterForm.value.filter.activity === "idle") {
              return activeTaskCount < 1;
            }

            // ÖNEMLİ: Burada eksik return değeri vardı, bu hataya neden oluyordu
            return true;
          })
          .filter((courier) => {
            if (!courier || !courier.courier_detail) return false;

            if (filterForm.value.filter.transport === "all") {
              return true;
            }

            return (
              courier.courier_detail.vehicle_type_slug ===
              filterForm.value.filter.transport
            );
          })
          .sort(function(a, b) {
            if (!a || !b) return 0;

            const key = filterForm.value.sort.key;

            // Null kontrolü yapılmalı
            if (!a[key] || !b[key]) return 0;

            try {
              return filterForm.value.sort.dir === "asc"
                ? a[key].toString().localeCompare(b[key].toString())
                : b[key].toString().localeCompare(a[key].toString());
            } catch (e) {
              console.error("Sıralama hatası:", e);
              return 0;
            }
          });

        // Filtrelenmiş kuryelerin güvenlik kontrolü
        if (!filteredCouriers.value) {
          filteredCouriers.value = [];
        }

        if (filterForm.value.filter.team_id === "all") {
          emitter.emit("create_team_filter", []);
        } else {
          // Güvenli bir şekilde isimleri çıkarma
          const courierNames = filteredCouriers.value
            .filter(courier => courier && courier.name)
            .map(courier => courier.name);

          emitter.emit("create_team_filter", courierNames || []);
        }

        // Güvenli bir şekilde ID'leri çıkarma
        const courierIds = filteredCouriers.value
          .filter(courier => courier && courier.id)
          .map(courier => courier.id);

        emitter.emit("filtered-courier", {
          filters: filterForm.value,
          courierIds: filterForm.value.options.applyFiltersToMap ? courierIds : [],
          debounce
        });

      } catch (error) {
        console.error("Filtreleme sırasında hata oluştu:", error);
        filteredCouriers.value = [];
      }
    }


    watch(filterForm, () => {
      debouncedApplyCourierListFilters(true);
    }, { deep: true });

    watch(() => props.couriers, (newCouriers) => {
      if (newCouriers) {  // Check if newCouriers exists
        applyCourierListFilters(false);
      }
    }, { deep: true });

    const getReasonSelect = () => {
      return api.post("/components/break-request-rejection-reason-select")
        .then((response) => {
          breakRequestRejection.value = response.data;
        });
    };
    const trackScrollPosition = () => {

      if (recycleScrollerRef.value && recycleScrollerRef.value.$el) {

        scrollPosition.value = recycleScrollerRef.value.$el.scrollTop;

      }

      else if (courierListContainer.value) {
        scrollPosition.value = courierListContainer.value.scrollTop;
      }
    };

    const ensureOptions = () => {
      if (!Options || !Options.workingStatus || !Options.status || !Options.duty_type || !Options.breakStatus) {
        console.error('Options nesnesi henüz yüklenmemiş veya bazı özellikleri eksik.');
        return false;
      }
      return true;
    };


    onMounted(() => {
      applyCourierListFilters();
      getBreakRequest();
      getReasonSelect();
      listener();
      setTimeout(() => {

        if (recycleScrollerRef.value && recycleScrollerRef.value.$el) {
          recycleScrollerRef.value.$el.addEventListener('scroll', trackScrollPosition);
        }
        else if (courierListContainer.value) {
          courierListContainer.value.addEventListener('scroll', trackScrollPosition);
        }
      }, 100);
      if (!ensureOptions()) {
        // Options yüklenmemişse, kısa bir gecikme sonra filtreleri yeniden uygulayalım
        setTimeout(() => {
          if (ensureOptions()) {
            applyCourierListFilters(false);
          }
        }, 500);
      }


    });


    onBeforeUnmount(() => {
      if (recycleScrollerRef.value && recycleScrollerRef.value.$el) {
        recycleScrollerRef.value.$el.removeEventListener('scroll', trackScrollPosition);
      } else if (courierListContainer.value) {
        courierListContainer.value.removeEventListener('scroll', trackScrollPosition);
      }

      stopListener();
    });

    function refresh() {
      emit("onRefresh");
    }

    const resetFilters = () => {
      filterForm.value = {
        filter: {
          search: null,
          hub_id: selectedHub.value ? selectedHub.value : 0,
          team_id: "all",
          activity: "all",
           // connection: "all",
          transport: "all",
          is_working: "working"
        },
        sort: {
          key: "name",
          dir: "asc"
        },
        options: {
          applyFiltersToMap: true
        }
      };
    };

    const onSelect = (param, rect) => {
      scrollToTop(rect);
      trackScrollPosition();
      state.selectedCourier = param;


      dispatch("operations/setSelectedCourier", JSON.parse(JSON.stringify(param)));
    };
    watch(() => state.selectedCourier, (newVal, oldVal) => {
      if (newVal !== oldVal) {
        nextTick(() => {

          setTimeout(() => {
            if (recycleScrollerRef.value && recycleScrollerRef.value.$el) {
              recycleScrollerRef.value.$el.scrollTop = scrollPosition.value;
            } else if (courierListContainer.value) {
              courierListContainer.value.scrollTop = scrollPosition.value;
            }
          }, 10);
        });
      }
    });


    const onRemove = () => {
      dispatch("operations/setSelectedCourier", null);
    };


    const scrollToItemById = (courierId) => {
      const index = filteredCouriers.value.findIndex(c => c.id === courierId);
      if (index !== -1 && recycleScrollerRef.value) {
        recycleScrollerRef.value.scrollToItem(index);
      }
    };


    const selectCourierFromMap = (param) => {

      if (filteredCouriers.value.find(x => x.id === param.id)) {
        dispatch("operations/setSelectedCourier", JSON.parse(JSON.stringify(param)));
        return;
      }

      dispatch("operations/setSelectedCourier", null);
    };

    const selectCourierFromGrid = (id) => {

      let param = filteredCouriers.value.find(x => x.id === id);

      if (!param) {

        dispatch("operations/setSelectedCourier", null);
        return;
      }

      dispatch("operations/setSelectedCourier", JSON.parse(JSON.stringify(param)));
    };


    const showCourierUpdate = (param) => {
      selectedCourierForEdit.value = param;
      visible.value = true;
    };

    const closeCourierUpdate = () => {
      selectedCourierForEdit.value = null;
      visible.value = false;
      refresh();
    };
    const closeRequestDrawer = () => {
      selectedCourierForEdit.value = null;
      newBreakRequestDrawer.value = false;
      refresh();
    };
    const closeEditRequestDrawer = () => {
      editBreakRequestDrawer.value = false;
      selectedCourierForEdit.value = null;
      refresh();
    }


    const getBreakRequest = () => {
      dispatch("ui/setLoading", true);
      api(`customer/break-requests?filter[status_slug]=pending?page=${pageValue.value}&per_page=15`)
        .then((response) => {
          breakRequests.value = response.data;
          totalRequest.value = response.meta.total;
          nextButonVisible.value = response.links.next;
          prevButtonVisible.value = response.links.prev;
        })
        .catch(() => {
        })
        .finally(() => dispatch("ui/setLoading", false));
    };


    const onFilter = (courier) => {
      emitter.emit("filter_by_courier", courier);
    };


    const openApproveDialog = (id) => {
      breakRequestId.value = id;
      aproveDialogVisible.value = true;
      dispatch("ui/setLoading", true);
      api("/customer/break-requests/" + id)
        .then((response) => {
          request.value = response;

        }).finally(() => {
        dispatch("ui/setLoading", false);
      });
    };

    const approveBreakRequest = () => {
      dispatch("ui/setLoading", true);
      api.post("/customer/break-requests/" + breakRequestId.value + "/approve", { reviewer_notes: requestForm.reviewerNotes })
        .then(() => {
          aproveDialogVisible.value = false;
          getBreakRequest()
          toast.success(t("Break request approved"));
        })
        .catch((err) => {
          toast.error(err.data.message);
        })
        .finally(() => {

          dispatch("ui/setLoading", false);
        });
    };
    const getRejectReasons = () => {
      dispatch("ui/setLoading", true);
      return api.post("/components/break-request-rejection-reason-select")
        .then((response) => {
          rejectReasons.value = response.data;
          getBreakRequest()
        })
        .finally(() => {
          dispatch("ui/setLoading", false);
        });
    };

    const getBreakRequests = () => {
      const audio = new Audio("requestAlarm.mp3");
      audio.play();

      setTimeout(() => {
        audio.pause();
        audio.currentTime = 0;
      }, 5000);

      toast.success(t("You Have a New Break Reques"));
      api("customer/break-requests?filter[status_slug]=pending")
        .then((response) => {
          breakRequests.value = response.data;
          totalRequest.value = response.meta.total;
        })
        .catch(() => {

        })
        .finally(() => {
        });
    };


    const getBreakRequestsDeleted = () => {
      dispatch("ui/setLoading", true);
      api("customer/break-requests?filter[status_slug]=pending")
        .then((response) => {
          breakRequests.value = response.data;
          totalRequest.value = response.meta.total;
        })
        .catch(() => {
        })
        .finally(() => {
          dispatch("ui/setLoading", false);
        });
    };



    const breakRequestReject = () => {
      dispatch("ui/setLoading", true);
      let body = {
        reviewer_notes: requestForm.reviewerNotes,
        rejection_reason_slug: rejectReason.value
      };

      api.post("/customer/break-requests/" + breakRequestId.value + "/reject", body)
        .then(() => {
          aproveDialogVisible.value = false;
          getBreakRequest();
          toast.success(t("Request for a break denied"));
        })
        .catch((err) => toast.error(err.data.message))
        .finally(() => {
          dispatch("ui/setLoading", false);
        });
    };




    const listener = () => {
      Echo.private(companyChannel.value).listen(".break.request.created", getBreakRequests);
      Echo.private(companyChannel.value).listen(".break.request.deleted", getBreakRequestsDeleted);
      Echo.private(companyChannel.value).listen(".scheduled.break.created", (r) => {
        if (filteredCouriers.value && filteredCouriers.value.length > 0) {
          const updatedCouriers = filteredCouriers.value.map(courier => {

            if (courier.id === r.eventData.shift.user_id) {
              return { ...courier, scheduled_break: true };
            }
            return courier;
          });


          filteredCouriers.value = updatedCouriers;

        }
      });
      Echo.private(companyChannel.value).listen(".break.started", (r) => {

        if (filteredCouriers.value && filteredCouriers.value.length > 0) {
          const updatedCouriers = filteredCouriers.value.map(courier => {

            if (courier.id === r.eventData.shift.user_id) {
              return { ...courier, active_break: true, scheduled_break: null };
            }
            return courier;
          });


          filteredCouriers.value = updatedCouriers;

        }
      });
      Echo.private(companyChannel.value).listen(".break.ended", (r) => {


        if (filteredCouriers.value && filteredCouriers.value.length > 0) {
          const updatedCouriers = filteredCouriers.value.map(courier => {

            if (courier.id === r.eventData.shift.user_id) {
              return { ...courier, active_break: false };
            }
            return courier;
          });


          filteredCouriers.value = updatedCouriers;

          // // Seçili kurye varsa ve güncellenen kurye ise onu da güncelle
          // if (selectedCourier.value && selectedCourier.value.id === r.shift.user_id) {
          //   dispatch("operations/setSelectedCourier", { ...selectedCourier.value, active_break: false });
          // }
        }
      });


    };


    const getCourierBreakInfo = () => {
      api(`customer/couriers/${selectedCourier.value.id}/break`)
        .then((r) => {
          CourierBreakInfo.value = r;
        });
    };


    watch(selectedCourier, () => {
      if (selectedCourier.value.active_break) {
        getCourierBreakInfo();
      }

    });


    const stopListener = () => {
      Echo.private(companyChannel.value).stopListening(".break.request.created");
    };
    watch(pageValue, () => {
      getBreakRequest();
    });

    const openNewBreakRequestDrawer = (courier) => {
      selectedCourier.value = courier;
      newBreakRequestDrawer.value = true;
    };
    const openEditBreakRequestDrawer = (courier) => {
      selectedCourier.value = courier;
      editBreakRequestDrawer.value = true;
    };

    watch(aproveDialogVisible, () => {
      if (!aproveDialogVisible.value) {
        requestForm.reviewerNotes = null;
        requestForm.rejectionReasonSlug = null;
        rejectReason.value = null;
      }
    });

    const openRejectCourierModal = (courier) => {
      selectedCourier.value = courier;
      rejectModalVisible.value = true;
    };
    const openDeleteRejectModal = (courier) => {
      selectedCourier.value = courier;
      deleteRejectModalVisible.value = true;
    };

    const rejectCourierModal = () => {
      dispatch("ui/setLoading", true);
      api.delete(`/customer/couriers/${selectedCourier.value.id}/break`)
        .then(()=>{
          toast.success(t("Break is canceled"));
          rejectModalVisible.value = false
          refresh();
          dispatch("ui/setLoading", true);
        })
        .catch((err) => toast.error(err.data.message))
        .finally(() => {
          dispatch("ui/setLoading", false);
        });
    };


    emitter.on("select_courier_from_map", selectCourierFromMap);
    emitter.on("select_courier_from_grid", selectCourierFromGrid);



const cancelDrawer = () => {
  visible.value = false;
}




    const totalHeight = computed(() => {
      return filteredCouriers.value.length * itemHeight;
    });

    const handleScroll = () => {
      if (!courierListContainer.value) return;

      scrollTop.value = courierListContainer.value.scrollTop;
      updateVisibleItems();
    };

    const updateVisibleItems = () => {
      if (!courierListContainer.value) return;

      const containerHeight = courierListContainer.value.clientHeight;

      const startIndex = Math.floor(scrollTop.value / itemHeight);
      const endIndex = Math.min(
        filteredCouriers.value.length - 1,
        Math.ceil((scrollTop.value + containerHeight) / itemHeight)
      );

      const bufferSize = 5;
      const start = Math.max(0, startIndex - bufferSize);
      const end = Math.min(filteredCouriers.value.length - 1, endIndex + bufferSize);


      const indices = [];
      for (let i = start; i <= end; i++) {
        indices.push(i);
      }

      visibleItems.value = indices;
    };

    watch(filteredCouriers, () => {
      nextTick(() => {
        updateVisibleItems();
      });
    }, { immediate: true });

    onMounted(() => {
      updateVisibleItems();
      window.addEventListener('resize', updateVisibleItems);
    });


    onBeforeUnmount(() => {
      window.removeEventListener('resize', updateVisibleItems);
    });


    const scrollToTop = (value) => {
      if (courierListContainer.value) {
        courierListContainer.value.scrollTo({
          top: value || 0,
          behavior: "smooth"
        });
      }
    };





    return {
      ...toRefs(state),
      isCouriersPanelOpen,
      toggleCouriersPanel,
      filterForm,
      sortCourierList,
      refresh,
      vehicleTypes,
      sortByNameIcon,
      sortByCapacityIcon,
      filteredCouriers,
      pageValue,
      Options,
      toggle,
      show,
      resetFilters,
      onSelect,
      onRemove,
      visible,
      closeCourierUpdate,
      showCourierUpdate,
      getRejectReasons,
      selectedCourierForEdit,
      CourierBreakInfo,
      aproveDialogVisible,
      filterOptions,
      courierListContainer,
      scrollToTop,
      customColors,
      openApproveDialog,
      onFilter,
      getBreakRequest,
      rejectDialogVisible,
      breakRequests,
      getReasonSelect,
      breakRequestReject,
      approveBreakRequest,
      getBreakRequests,
      getBreakRequestsDeleted,
      requestForm,
      breakRequestRejection,
      nextButonVisible,
      prevButtonVisible,
      closeEditRequestDrawer,
      rejectModalVisible,
      openDeleteRejectModal,
      breakRequestId,
      rejectReason,
      totalRequest,
      selectedCourier,
      editBreakRequestDrawer,
      openNewBreakRequestDrawer,
      openEditBreakRequestDrawer,
      getCourierBreakInfo,
      openRejectCourierModal,
      rejectCourierModal,
      request,
      toast,
      newBreakRequestDrawer,
      deleteRejectModalVisible,
      closeRequestDrawer,
      scrollToItemById,
      recycleScrollerRef,
      totalHeight,
      visibleItems,
      itemHeight,
      handleScroll,
      scrollTop,
      trackScrollPosition,
      scrollPosition,
      ensureOptions,
      cancelDrawer,
      t
    };
  }
};
</script>

<template>

  <SectionLayout :loading="loading" class="h-full relative">
    <div class="absolute pt-24 -left-6">

      <PanelPaddle
        position="left"
        :is-panel-open="isCouriersPanelOpen"
        @click="toggleCouriersPanel"
      />
    </div>

    <el-tabs class=" h-full overflow-auto delivery_detail_tab" type="border-card" tab-position="top">
      <el-tab-pane :label="$t('Drivers')" class="h-full w-full relative overflow-hidden">
        <div class=" flex items-center text-xs bg-white overflow-auto border-b-2 border-slate-300">
          <div class="flex-grow ">
            <div class="flex items-center divide-x divide-slate-300">
              <button
                @click="sortCourierList('name')"
                type="button"
                class="px-2 py-1.5 whitespace-nowrap space-x-1 bg-white text-slate-700"
              >
                <span>{{ t("Driver") }}</span>

                <FontAwesomeIcon
                  v-if="sortByNameIcon"
                  :icon="sortByNameIcon"
                  fixed-width
                />

              </button>
              <button
                @click="sortCourierList('current_tasks_of_active_company_count')"
                type="button"
                class="px-2 py-1.5 whitespace-nowrap space-x-1 bg-white text-slate-700"
              >
                <span>{{ t("Capacity") }}</span>
                <FontAwesomeIcon
                  v-if="sortByCapacityIcon"
                  :icon="sortByCapacityIcon"
                  fixed-width
                />
              </button>
            </div>
          </div>
          <div class="flex">
            <ButtonToolTip :tooltipText="$t('Refresh')" position="left">
              <button
                @click="refresh"
                type="button"
                class="px-2 py-1.5 whitespace-nowrap space-x-1 bg-white"
              >
                <FontAwesomeIcon icon="sync" fixed-width />
              </button>
            </ButtonToolTip>
          </div>
        </div>
        <div
          ref="courierListContainer"
          class="w-full overflow-auto relative"
          :class="show ? 'h-[calc(100%-300px)]' : 'h-full pb-20'"
          @scroll="handleScroll"
        >
          <div :style="{ height: totalHeight + 'px', position: 'relative' }">
            <div
              v-for="i in visibleItems"
              :key="i.id"
              :style="{
        position: 'absolute',
        top: i * itemHeight + 'px',
        width: '100%',
        height: itemHeight + 'px'
      }"
            >
              <CouriersListItem
                v-if="filteredCouriers[i]?.id"
                :courier="filteredCouriers[i]"
                @select="onSelect"
                @remove="onRemove"
                @openRejectCourierModal="openRejectCourierModal"
                @openRequestDrawer="openNewBreakRequestDrawer"
                @edit="showCourierUpdate"
                @openEditBreakRequestDrawer="openEditBreakRequestDrawer"
                @scrollTo="scrollToTop"
                @filter="onFilter"
              />
            </div>
          </div>
        </div>



        <div>
          <div class="bg-slate-50 z-50 absolute bottom-0 w-full">
            <div class="absolute -top-6 right-3">

              <PanelPaddle position="ground" :is-panel-open="show" @click="toggle" />
            </div>
            <div
              class="w-full bg-white border-t border-b border-slate-300 text-xxxs"
            >

              <el-form v-show="!show && filterOptions" label-position="top" size="small"
                       class="px-3 py-2 courier-list-filter-content border-b border-slate-300">
                <div class="grid gap-2.5 grid-cols-2">
                  <div class="col-span-1">
                    <CourierHubFilter
                      v-model="filterForm.filter.hub_id"
                    />
                  </div>
                  <div class="col-span-1">
                    <CourierTeamFilter
                      v-model="filterForm.filter.team_id"
                      :selectedHub="filterForm.filter.hub_id"
                    />
                  </div>
                </div>
              </el-form>
              <div class="flex items-center">

                <div class="px-1 py-0.5">
                  {{ t("Filtered") }}:
                  <span class="font-bold">{{ filteredCouriers?.length }}</span>
                </div>
                <div class="px-1 py-0.5">
                  {{ t("Total") }}: <span class="font-bold">{{ couriers?.length }}</span>
                </div>
              </div>
            </div>
            <div :class="[show ? '' : 'hidden']">
              <div class="px-3 py-2 flex flex-col space-y-2.5">
                <div class="flex items-center justify-between">
                  <h3 class="text-sm font-bold">
                    <FontAwesomeIcon icon="filter" />
                    {{ t("Driver Search") }}
                  </h3>
                  <div class="text-xs cursor-pointer">
                    <label class="flex items-center text-xs space-x-2.5">
                      <span class="font-semibold text-xxxs">{{ t("On Map") }}</span>
                      <el-checkbox
                        v-model="filterForm.options.applyFiltersToMap"
                        size="small"
                      />
                    </label>
                  </div>
                </div>
                <el-form label-position="top" size="small" class="courier-list-filter-content">
                  <div class="grid gap-2.5 grid-cols-3">
                    <div class="col-span-3">
                      <el-input
                        v-model="filterForm.filter.search"
                        :placeholder="t('Search')"
                        size="small"
                      />
                    </div>
                    <div class="col-span-1">
                      <CourierHubFilter
                        v-model="filterForm.filter.hub_id"
                      />
                    </div>
                    <div class="col-span-1">
                      <CourierTeamFilter
                        v-model="filterForm.filter.team_id"
                        :selectedHub="filterForm.filter.hub_id"
                      />
                    </div>
                    <div class="col-span-1">
                      <el-form-item :label="t('Working Status')">
                        <el-select
                          v-model="filterForm.filter.is_working"
                          class="w-full"
                          size="small"
                        >
                          <el-option
                            v-for="item in Options.workingStatus"
                            :key="item.value"
                            :label="t(item.label)"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </div>
<!--                    <div class="col-span-1">-->
<!--                      <el-form-item :label="t('Connection')">-->
<!--                        <el-select-->
<!--                          v-model="filterForm.filter.connection"-->
<!--                          class="w-full"-->
<!--                          size="small"-->
<!--                          placeholder="Status"-->
<!--                        >-->
<!--                          <el-option-->
<!--                            v-for="item in Options.status"-->
<!--                            :key="item.value"-->
<!--                            :label="t(item.label)"-->
<!--                            :value="item.value"-->
<!--                          >-->
<!--                          </el-option>-->
<!--                        </el-select>-->
<!--                      </el-form-item>-->
<!--                    </div>-->
                    <div class="col-span-1">
                      <el-form-item :label="t('Duty Status')">
                        <el-select
                          v-model="filterForm.filter.activity"
                          class="w-full"
                          size="small"
                        >
                          <el-option
                            v-for="item in Options.duty_type"
                            :key="item.value"
                            :label="t(item.label)"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                    <div class="col-span-1">
                      <el-form-item :label="t('Break Status')">
                        <el-select
                          v-model="filterForm.filter.on_break"
                          class="w-full"
                          size="small"
                        >
                          <el-option
                            v-for="item in Options.breakStatus"
                            :key="item.value"
                            :label="t(item.label)"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                    <div class="col-span-1">
                      <CourierVehicleTypeFilter
                        v-model="filterForm.filter.transport"
                      />
                    </div>

                  </div>
                </el-form>
                <div class="flex justify-end">
                  <el-button @click="resetFilters" :text="true" size="small">
                    {{ t("Reset") }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane class="h-full w-full">
        <template #label>
      <span>
        {{ $t("Break Requests") }}
        <el-tag v-if="totalRequest" size="small" round effect="dark" type="danger">{{ totalRequest }}</el-tag>
      </span>
        </template>
        <div class="overflow-hidden">
          <div class="flex items-center text-xs bg-white border-b-2 border-slate-300 relative">
            <div class="flex-grow ">
              <div class="flex items-center divide-x divide-slate-300">

              </div>
            </div>
            <div class="flex">
              <ButtonToolTip :tooltipText="$t('Refresh')" position="left">
                <button
                  @click="getBreakRequest"
                  type="button"
                  class="px-2 py-1.5 whitespace-nowrap space-x-1 bg-white"
                >
                  <FontAwesomeIcon icon="sync" fixed-width />
                </button>
              </ButtonToolTip>
            </div>
          </div>
          <!-- Scrollable Content -->
          <div class="overflow-hidden flex flex-col absolute w-full" style="height: calc(100% - 40px);">
            <!-- Scrollable Content -->

            <div class="overflow-auto flex-1" style="height: calc(100% - 20px);">
              <div v-if="breakRequests" v-for="item in breakRequests" :key="item?.id">
                <div  @click="openApproveDialog(item.id)" class="flex items-center justify-between p-2 border-b hover:bg-indigo-100 cursor-pointer" >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex flex-col text-sm w-full">
                      <div class="flex">
                        <span class="text-xs font-bold">{{ item.courier?.name }}</span>
                      </div>
                      <span class="font-semibold text-slate-500 text-xxs">{{ item.courier.phone }}</span>
                      <div class="flex">
                        <el-progress
                          class="w-1/2"
                          :show-text="false"
                          :percentage="(item.shift?.used_break_time / item.shift?.given_break_time) * 100"
                          :color="customColors"
                        />
                        <span class="font-semibold text-slate-500 text-xxs ml-2">
                {{ item.shift?.used_break_time }} {{t('Min') }} / {{ item.shift?.given_break_time }} {{t('Min') }}
              </span>
                      </div>
                    </div>

                    <div class="flex items-center justify-end w-full">
                      <div class="flex flex-col items-end text-xxs">
              <span class="font-medium text-xxs">
                {{ dayjs(item.starts_at).isBefore(dayjs()) ? $t("Now") : dayjs(item.starts_at).format("HH:mm") }}
            <span class="font-bold underline">{{ item.break_duration
              }} DK</span> - {{ dayjs(item.ends_at).format("HH:mm") }}
              </span>
                        <span class="font-semibold text-slate-500 text-xxs mt-1 italic">
                {{ dayjs(item.created_at).fromNow() }}
              </span>
                        <div>
                          <FontAwesomeIcon class="cursor-pointer" size="lg" @click="openApproveDialog(item.id)"
                                           icon="arrow-right"
                                           fixed-width />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="!breakRequests || breakRequests.length === 0" class="flex items-between justify-center text font-bold h-full mt-8 text-slate-700">
                {{ $t("There is no break request.") }}
              </div>

            </div>

            <!-- Fixed Pagination Bar (stays at bottom) -->
          </div>
          <div
            class="w-full flex items-center justify-between px-2 text-slate-700 border-t border-slate-300 bg-white absolute bottom-0 "
            style="height: 20px;"
          >
            <div class="flex items-center justify-end font-semibold overflow-hidden w-full ">
              <div
                @click="pageValue = Math.max(1, pageValue - 1)"
                class="cursor-pointer text-xxs py-0.5 px-2"
                :class="[
                  prevButtonVisible ? 'text-slate-700' : 'text-slate-300'
              ]"
              >
                {{ $t("Previous") }}
              </div>
              <div class="text-xxs py-0.5 px-2 custom-primary-color font-bold">
                {{ pageValue }}
              </div>
              <div
                @click="nextButonVisible && (pageValue += 1)"
                class="cursor-pointer text-xxs py-0.5 px-2"
                :class="[
                  nextButonVisible ? 'text-slate-700' : 'text-slate-300 pointer-events-none'
                ]"
              >
                {{ $t("Next") }}
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>


    <el-drawer
      v-model="visible"
      class="customized-drawer"
      :title="$t('Driver Edit')"
      append-to-body
      destroy-on-close
    >
      <CourierUpdate
        @cancelDrawer="cancelDrawer"
        @close="closeCourierUpdate"
        :courier="selectedCourierForEdit"
      >
      </CourierUpdate>
    </el-drawer>
    <el-drawer
      v-model="newBreakRequestDrawer"
      class="customized-drawer "
      :title="$t('Create a Break')"
      append-to-body
      destroy-on-close
    >
      <NewBreak
        @close="closeRequestDrawer"
        :courier="selectedCourier"
      />
    </el-drawer>
    <el-drawer
      v-model="editBreakRequestDrawer"
      class="customized-drawer "
      :title="$t('Edit Break')"
      append-to-body
      destroy-on-close
    >
      <BreakEdit
        @close="closeEditRequestDrawer"
        :courier="selectedCourier"
        :CourierBreakInfo="CourierBreakInfo"
      />
    </el-drawer>
    <el-dialog v-model="aproveDialogVisible" width="40%">
      <div class="flex flex-col items-start justify-center">
        <div class="text-lg text-slate-700 font-bold">
          {{ request?.courier?.name }}
        </div>

        <div class="text-sm text-slate-700 font-bold">
          <span class="text-green-600">  {{ request?.starts_at && dayjs(request.starts_at).isBefore(dayjs())
            ? $t("Now")
            : request?.starts_at
              ? dayjs(request.starts_at).format("HH:mm")
              : "" }}
          </span>
          <span> - {{ request?.break_duration ? `${request.break_duration} DK` : "" }}</span>
        </div>
        <div class="text-xs italic" v-if="request?.requester_notes">
          <span class="text-gray-600">{{ $t("Driver Note") }}:</span> {{ request?.requester_notes }}
        </div>
        <div class="w-full"
             v-if="request?.courier && (request.courier.task_counts_now.assigned > 0 || request.courier.task_counts_now.failed > 0 || request.courier.task_counts_now.in_progress > 0 || request.courier.task_counts_now.on_delivery > 0)">
          <div class="text-sm text-slate-700 mt-4">
            Sürücünün
            {{ request?.courier?.task_counts_now ? Object.values(request.courier.task_counts_now).reduce((sum, value) => sum + value, 0) : 0
            }} aktif görevi var. Yinede onaylamak istiyor musunuz?
          </div>
          <table class="mt-4 w-full border border-gray-300">
            <thead>
            <tr>
              <th class="border border-gray-300 px-0.5 py-1 text-xxs">{{ $t("Assigned") }}</th>
              <th class="border border-gray-300 px-0.5 py-1 text-xxs">{{ $t("Failed") }}</th>
              <th class="border border-gray-300 px-0.5 py-1 text-xxs">{{ $t("In Progress") }}</th>
              <th class="border border-gray-300 px-0.5 py-1 text-xxs">{{ $t("On Delivery") }}</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td class="border border-gray-300 px-0.5 py-1 text-xxs text-center align-middle">
                {{ request.courier.task_counts_now.assigned }}
              </td>
              <td class="border border-gray-300 px-0.5 py-1 text-xxs text-center align-middle">
                {{ request.courier.task_counts_now.failed }}
              </td>
              <td class="border border-gray-300 px-0.5 py-1 text-xxs text-center align-middle">
                {{ request.courier.task_counts_now.in_progress }}
              </td>
              <td class="border border-gray-300 px-0.5 py-1 text-xxs text-center align-middle">
                {{ request.courier.task_counts_now.on_delivery }}
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="mt-4 w-full">
          <el-form label-position="top" class="space-y-12">
            <div class="flex flex-col lg:flex-row w-full gap-6">
              <el-form-item :label="t('Note')" class="w-full-important">
                <el-input v-model="requestForm.reviewerNotes" :placeholder="t('Note')" />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-between ">
          <el-button class="mt-5" @click="approveBreakRequest" type="primary">
            {{ t("Approve") }}
          </el-button>
          <div>
            <div class="flex items-center justify-start text-xs text-slate-700">
              {{ $t("Select Reason for Cancellation") }}
            </div>
            <div class="flex max-w-xl">
              <select
                class="border border-gray-300 text-xs px-3  rounded-md rounded-tr-none rounded-br-none focus:outline-none min-w-[200px] whitespace-nowrap overflow-hidden text-ellipsis"
                v-model="rejectReason"
                style="height: 32px"
              >
                <option
                  v-for="item in breakRequestRejection"
                  :key="item.slug"
                  :value="item.slug"
                >
                  {{ item.name }}
                </option>
              </select>

              <button
                @click="breakRequestReject"
                style="height: 32px"
                :disabled="!rejectReason"
                :class="{
                    'bg-red-600': rejectReason,
                    'bg-red-300': !rejectReason
                }"
                class="text-white text-sm px-4 rounded-r-md">
                {{ $t("Reject") }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="rejectModalVisible" width="40%">
      <div class="flex flex-col items-center justify-center">
        <div v-if="selectedCourier?.active_break" class="text-lg text-slate-700 font-bold mt-3">
          {{ t("The driver's active break will be cancelled. Confirm?") }}
        </div>
        <div v-if="selectedCourier?.scheduled_break" class="text-lg text-slate-700 font-bold mt-3">
          {{ t("Can you cancel and approve a driver's future break?") }}
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-between ">
          <div>

          </div>
          <el-button class="mt-5" @click="rejectCourierModal" type="primary">
            {{ t("Approve") }}
          </el-button>

        </div>
      </template>
    </el-dialog>


  </SectionLayout>
</template>
<style scoped>
.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}
</style>

