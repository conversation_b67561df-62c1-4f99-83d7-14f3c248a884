<script setup>
import {useStore} from "vuex";
import {computed, inject, onMounted, reactive, ref, watch} from "vue";
import DefaultMapStyle from "@/map-styles/default.json";
import {CreateInfoWindow, CreateMarker, create<PERSON><PERSON><PERSON>, getCourierMarkerIcon} from "@/components/operations/helper";
import {pointMarkers} from "@/components/operations/pointMarkers";
import {MarkerClusterer} from "@googlemaps/markerclusterer";
import BasketCluster from "@/assets/images/map/cluster/basket-cluster.png"
import BasketClusterBlue from "@/assets/images/map/cluster/basket-cluster-blue.png"
import PersonCluster from "@/assets/images/map/cluster/person-cluster.png"
import PersonClusterBlue from "@/assets/images/map/cluster/person-cluster-blue.png"
import ClusterDestinationMarker from "@/assets/images/map/marker/ClusterDestinationMarker.png"
import { loadGoogleMaps, throttle } from "@/views/Tracking/GoogleMapsLoaderService";


const {dispatch, getters} = useStore()

const api = inject("api");
const emitter = inject("emitter");

let markerNames = ["courier", "origin", "destination"]
let visibleStatusPointSlugs = ["in_progress", "assigned", "completed", "failed", "cancelled"]
let cluster = null

const props = defineProps({
  couriers: {
    type: Array,
    default: () => []
  }
})

const markers = {
  couriers: [],
  origins: [],
  destinations: [],
  statusPoints: [],
  infoWindow: null
}

let planItems = []
let tasksItems = []
let specificTaskItems = []

const activeCompany = computed(() => getters["auth/activeCompany"]);
const activeTaskTab = computed(() => getters["tabs/activeTaskTab"]);
const selectedCourier = computed(() => getters["operations/selectedCourier"])
const liveStream = computed(() => getters["tasks/liveStream"])
const selectedHub = computed(() => getters["operations/selectedHub"])
const showTasksOnMap = computed(() => getters["operations/showTasksOnMap"])
const showCourierTasksOnMap = computed(() => getters["operations/showCourierTasksOnMap"])
const showSpecificTaskOnMap = computed(() => getters["operations/showSpecificTaskOnMap"])
const showSpecificPlanOnMap = computed(() => getters["operations/showSpecificPlanOnMap"])

const filter = reactive({
  courier: {
    visible: true,
    ids: [],
  }
})

const filteredCourierIds = ref([])
const operationMap = ref()
const clusterVisible = ref(false)

onMounted(() => {

  getMap()
      .then(() => {

        createCourierMarkers()

        if (liveStream.value) listenChannel()

      })
      .catch(() => {

      })
})

watch([liveStream, selectedHub], () => {

  tasksItems.forEach((x) => {
    x.setMap(null)
  })
  tasksItems = []
  clusterVisible.value = false

  clearDestinationMarkerCluster()

  removeMapClusterControl()


  if (!liveStream.value && selectedHub.value) {

    if (clusterVisible.value) {
      createDestinationMarkerCluster()
    }

    addMapClusterControl()
    listenChannel()

  }

})


watch(liveStream, () => {


  if (activeTaskTab.value.name !== "TabTasks") return

  clearTaskMaskers()
  clearSpecificTaskMaskers()
  hidePlan()

}, {deep: true})

watch(activeTaskTab, () => {
  applyFilter()
}, {deep: true})

watch(selectedHub, () => {

  if (activeTaskTab.value.name !== "TabTasks") return

  clearTaskMaskers()
  clearSpecificTaskMaskers()
  hidePlan()

  const bounds = new google.maps.LatLngBounds()

  markers.couriers.forEach((marker) => {
    bounds.extend(marker.getPosition())
  })

  operationMap.value.fitBounds(bounds, 80);


}, {deep: true})

watch(selectedCourier, (next, prev) => {

  if (selectedCourier.value) return actSelectedCourier(selectedCourier.value)


  if (clusterVisible.value) {

    markers.couriers.forEach(marker => {
      if (marker.id === prev?.id) {
        marker.setVisible(false)
        marker.setAnimation(null);
      }
    })

    return

  }


  let visible = filteredCourierIds.value?.includes(prev?.id)

  markers.couriers.forEach(marker => {
    if (marker.id === prev?.id) {
      marker.setVisible(visible)
      marker.setAnimation(null);
    }
  })

})

watch(showCourierTasksOnMap, () => {

  if (activeTaskTab.value.name !== "TabTasks") return

  if (!!showCourierTasksOnMap.value?.length) return showCourierTasks()

  hideTasks()

  if (showSpecificTaskOnMap.value) toggleBounceSpecificTask()

}, {deep: true})

watch(showTasksOnMap, () => {

  if (activeTaskTab.value.name !== "TabTasks") return

  if (!!showTasksOnMap.value?.length
      && clusterVisible.value) return showCourierTasks()

  hideTasks()

  if (showSpecificTaskOnMap.value) toggleBounceSpecificTask()

}, {deep: true})

watch(showSpecificTaskOnMap, () => {

  toggleBounceSpecificTask()

}, {deep: true})

watch(showSpecificPlanOnMap, (next, prev) => {


  if (showSpecificPlanOnMap.value) showPlanOnMap()

  if (prev) return hidePlan(prev)

  hideTasks()

}, {deep: true})

const applyFilter = () => {
  return new Promise((resolve) => {

    if (showSpecificTaskOnMap.value && activeTaskTab.value.name === "TabTaskDetail") return

    if (clusterVisible.value) {
      markers.couriers.forEach((marker) => marker.setVisible(false))
      return
    }

    markerNames.forEach((name) => {
      markers[name + "s"].forEach((marker) => {
        if (filter[name].visible && filter[name].ids.includes(marker.id)) {
          marker.setVisible(true)
        } else {
          marker.setVisible(false)
        }
      })
    })
    resolve()
  })
}

import { loadGoogleMaps } from "@/views/Tracking/GoogleMapsLoaderService";

const getMap = () => {
  return new Promise(async (resolve) => {
    // Google Maps'in yüklenmesini bekle
    await loadGoogleMaps();
    await google.maps.importLibrary("geometry");
    const map = new google.maps.Map(document.getElementById("operationMap"), {
      minZoom: 1,
      zoom: 10,
      fullscreenControlOptions: {
        position: google.maps.ControlPosition.RIGHT_BOTTOM,
      },
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery", "roadmap", "satellite", "hybrid", "terrain"],
        position: google.maps.ControlPosition.TOP_RIGHT,
      },
      streetViewControl: false,
      rotateControl: false,
      fullscreenControl: true,
    })
    map.mapTypes.set(
        "qdelivery",
        new google.maps.StyledMapType(DefaultMapStyle, {name: "QDelivery"})
    );
    map.setMapTypeId("qdelivery");
    markers.infowindow = new google.maps.InfoWindow({
      content: ""
    });

    operationMap.value = map;


    if (!liveStream.value && selectedHub.value) {
      addMapClusterControl()
    }


    resolve();
  });


};

const addMapClusterControl = () => {

  if (!operationMap.value) return

  let leftControlDiv = document.createElement("div");

  const {createClusterControlBtn, clearClusterControlBtn} = createLeftControl(operationMap.value)

  leftControlDiv.appendChild(clearClusterControlBtn);
  leftControlDiv.appendChild(createClusterControlBtn);
  leftControlDiv.style.display = "flex";
  leftControlDiv.style.marginTop = "11px";
  leftControlDiv.style.flexDirection = "column";


  operationMap.value.controls[google.maps.ControlPosition.TOP_LEFT].push(leftControlDiv);
}

const removeMapClusterControl = () => {
  operationMap.value.controls[google.maps.ControlPosition.TOP_LEFT].clear()
}


window.getMap = getMap;

const createLeftControl = () => {


  const createClusterControlBtn = document.createElement("button");

  // Set CSS for the control.
  createClusterControlBtn.style.backgroundColor = "#fff";
  createClusterControlBtn.style.border = "2px solid #fff";
  createClusterControlBtn.style.borderRadius = "3px";
  createClusterControlBtn.style.boxShadow = "0 2px 6px rgba(0,0,0,.3)";
  createClusterControlBtn.style.color = "rgb(25,25,25)";
  createClusterControlBtn.style.cursor = "pointer";
  createClusterControlBtn.style.fontFamily = "Roboto,Arial,sans-serif";
  createClusterControlBtn.style.fontSize = "16px";
  createClusterControlBtn.style.lineHeight = "38px";
  createClusterControlBtn.style.margin = "10px 0 10px";
  createClusterControlBtn.style.padding = "0 5px";
  createClusterControlBtn.style.textAlign = "center";
  createClusterControlBtn.style.marginLeft = "17px"


  const image = document.createElement("img");

  image.src = BasketCluster
  // Replace this with the actual path to your PNG image
  image.style.width = "20px"; // Set the width of the image as needed
  image.style.height = "20px"
  createClusterControlBtn.appendChild(image);
  createClusterControlBtn.title = "Click to recenter the map";
  createClusterControlBtn.type = "button";

  // Setup the click event listeners: simply set the map to Chicago.


  const clearClusterControlBtn = document.createElement("button");

  // Set CSS for the control.
  clearClusterControlBtn.style.backgroundColor = "#fff";
  clearClusterControlBtn.style.border = "2px solid #fff";
  clearClusterControlBtn.style.borderRadius = "3px";
  clearClusterControlBtn.style.boxShadow = "0 2px 6px rgba(0,0,0,.3)";
  clearClusterControlBtn.style.color = "rgb(25,25,25)";
  clearClusterControlBtn.style.cursor = "pointer";
  clearClusterControlBtn.style.fontFamily = "Roboto,Arial,sans-serif";
  clearClusterControlBtn.style.fontSize = "16px";
  clearClusterControlBtn.style.lineHeight = "38px";

  clearClusterControlBtn.style.padding = "0 5px";
  clearClusterControlBtn.style.textAlign = "center";
  clearClusterControlBtn.style.marginLeft = "17px"


  const image2 = document.createElement("img");

  image2.src = PersonClusterBlue // Replace this with the actual path to your PNG image2
  image2.style.width = "20px"; // Set the width of the image2 as needed
  image2.style.height = "20px"
  clearClusterControlBtn.appendChild(image2);
  clearClusterControlBtn.title = "Click to recenter the map";
  clearClusterControlBtn.type = "button";


  createClusterControlBtn.addEventListener("click", () => {

    clusterVisible.value = true
    image.src = BasketClusterBlue
    image2.src = PersonCluster



    createDestinationMarkerCluster()

  });

  clearClusterControlBtn.addEventListener("click", () => {


    clusterVisible.value = false
    image.src = BasketCluster
    image2.src = PersonClusterBlue

    clearDestinationMarkerCluster()

  });

  return {createClusterControlBtn, clearClusterControlBtn};
}

const createDestinationMarkerCluster = async () => {
  if (cluster && cluster.markers.length > 0) return;

  tasksItems.forEach((x) => {
    x.setMap(null);
  });
  tasksItems = [];

  if (cluster) {
    cluster.onRemove();
    cluster.clearMarkers();
  }

  let data = showCourierTasksOnMap.value.length > 0 ? showCourierTasksOnMap.value : showTasksOnMap.value;

  await createTaskMarkers(data);

  let _markers = tasksItems.filter((x) => {
    if (x.type === "origin") {
      x.setMap(null);
    }
    if (x.type === 'destination') {
      x.setIcon(ClusterDestinationMarker);
      return x;
    }
  });

  markers.couriers.forEach((marker) => {
    marker.setVisible(false);
  });

  filter.courier.visible = false;

  tasksItems = tasksItems.filter((x) => {
    if (x.type !== 'polyline') return x;
    x.setMap(null);
  });


  if (cluster) {
    cluster.addMarkers(_markers);

    // Cluster sınırlarını belirleme
    const bounds = new google.maps.LatLngBounds();
    _markers.forEach((marker) => {
      bounds.extend(marker.getPosition());
    });
    cluster.getMap().fitBounds(bounds);

    cluster.onAdd();
    return;
  }

  cluster = new MarkerClusterer({
    markers: _markers,
    map: operationMap.value,
  });

  const bounds = new google.maps.LatLngBounds();
  _markers.forEach((marker) => {
    bounds.extend(marker.getPosition());
  });
  cluster.getMap().fitBounds(bounds);
};


// const createDestinationMarkerCluster = async () => {
//
//   if (cluster && cluster.markers.length > 0) return
//
// console.log(cluster)
//
//   // const bounds = new google.maps.LatLngBounds()
//   //
//   // bounds.extend()
//
//
//   tasksItems.forEach((x) => {
//     x.setMap(null)
//   })
//   tasksItems = []
//
//
//   if (cluster) {
//
//     cluster.onRemove()
//     cluster.clearMarkers()
//
//   }
//
//
//   let data = showCourierTasksOnMap.value.length > 0 ? showCourierTasksOnMap.value : showTasksOnMap.value
//
//   await createTaskMarkers(data)
//
//   let _markers = tasksItems.filter((x) => {
//     if (x.type === "origin") {
//       x.setMap(null)
//     }
//     if (x.type === 'destination') {
//       x.setIcon(ClusterDestinationMarker)
//       return x
//     }
//   })
//
//   markers.couriers.forEach((marker) => {
//     marker.setVisible(false)
//   })
//
//   filter.courier.visible = false
//
//   tasksItems = tasksItems.filter((x) => {
//     if (x.type !== 'polyline') return x
//     x.setMap(null)
//   })
//
//   if (cluster) {
//
//     cluster.addMarkers(_markers)
//     cluster.onAdd()
//     return;
//   }
//
//   cluster = new MarkerClusterer({
//     markers: _markers,
//     map: operationMap.value
//   });
//
// }

const clearDestinationMarkerCluster = () => {

  if (!cluster) return

  filter.courier.visible = true

  const bounds = new google.maps.LatLngBounds()
  markers.couriers.forEach((marker) => {
    if (filteredCourierIds.value.includes(marker.id)) {
      bounds.extend(marker.getPosition())
      marker.setVisible(true)

      return
    }

    marker.setVisible(false)
  })

  operationMap.value.fitBounds(bounds, 80);

  tasksItems.forEach((x) => {
    x.setMap(null)
  })

  cluster.onRemove()
  cluster.clearMarkers()

  tasksItems = []

  if (showCourierTasksOnMap.value.length > 0) {
    showCourierTasks()
  }

}

const createCourierMarkers = async () => {

  return new Promise((resolve, reject) => {


    if (props.couriers.length === 0) return reject()

    const bounds = new google.maps.LatLngBounds()

    props.couriers.forEach((courier) => {

      const marker = CreateMarker.forCourier(courier, operationMap.value, filter.courier.visible && filter.courier.ids.includes(courier.id))

      google.maps.event.addListener(marker, 'click', () => {
        dispatch("operations/setSelectedCourier", JSON.parse(JSON.stringify(courier)))
      });

      markers.couriers.push(marker);

      if (marker.visible) bounds.extend(marker.getPosition())

    })

    operationMap.value.fitBounds(bounds, 80);
    resolve()
  })
}


const courierLocationUpdated = ({courier, company}) => {

  if (company.id !== activeCompany?.value.id) return;


  const prevCourier = props.couriers.find(x => x.id === courier.id)


  emitter.emit(".courier.location.updated")

  props.couriers.forEach((c) => {
    if (c.id === courier.id && c.courier_location) {
      c.courier_location.is_online = courier.courier_location.is_online;
      c.courier_location.is_working = courier.courier_location.is_working;
      c.courier_location.lat = courier.courier_location.lat;
      c.courier_location.lng = courier.courier_location.lng;
    }
  });

  const marker = markers.couriers.find((c) => c.id === courier.id)

  if (marker) {
    marker.setPosition({
      lat: courier.courier_location.lat,
      lng: courier.courier_location.lng,
    });

    if (prevCourier.courier_location.is_working !== courier.courier_location.is_working) {
      marker.setIcon({
        url: getCourierMarkerIcon({
          isWorking: courier.courier_location.is_working,
          onDuty: courier.courier_location.on_duty,
        })
      });
    }
  }

  if (filteredCourierIds.value.includes(courier.id)) {

    if (!courier.courier_location.is_working) {

      filter.courier.ids.filter(x => x !== courier.id)

    } else {
      filter.courier.ids.push(courier.id)
    }
  }

  applyFilter()

}

const courierCapacityUpdated = (data) => {

  const duty = data.courier.courier_detail.capacity_info._available_capacity !== data.courier.courier_detail.capacity_info._capacity
  const prevCourier = props.couriers.find(x => x.id === data.courier.id)
  const marker = markers.couriers.find((c) => c.id === data.courier.id)

  if (!marker) return
  if (!prevCourier) return
  if (prevCourier.courier_location.on_duty === duty) return;

  prevCourier.courier_location.on_duty = duty

  marker.setIcon({
    url: getCourierMarkerIcon({
      isWorking: prevCourier.courier_location.is_working,
      onDuty: duty,
    })
  })
}

const courierMarkersReset = () => {

  if (clusterVisible.value) {
    markers.couriers.forEach((marker) => marker.setVisible(false))
    return
  }

  markers.couriers.forEach((marker) => {

    marker.setVisible(filter.courier.visible && filteredCourierIds.value.includes(marker.id))
  })

}

const couriersFiltered = ({courierIds, debounce}) => {


  filteredCourierIds.value = courierIds

  if (clusterVisible.value) return
  if (showSpecificTaskOnMap.value && activeTaskTab.value.name === "TabTaskDetail") return


  filter.courier.ids = courierIds

  markers.couriers.forEach((marker) => {
    if (filter.courier.visible && filter.courier.ids.includes(marker.id)) {
      marker.setVisible(true)
    } else {
      marker.setVisible(false)
    }
  })

}

const actSelectedCourier = () => {

  markers.couriers.forEach((marker) => {
    if (marker.id === selectedCourier.value.id) {
      marker.setAnimation(google.maps.Animation.BOUNCE);
      marker.setVisible(true)
      operationMap.value.setCenter(new google.maps.LatLng(marker.getPosition().lat(), marker.getPosition().lng()));
      marker.setVisible(true)
    } else {
      marker.setAnimation(null);
    }
  })

}

const createTaskPolyline = async (task) => {

  if (!task.path) return

  const polyline = await createPolyline({
    task,
    path: task.path,
    map: operationMap.value,
    selected: false,
    status: task.status_category_slug
  })

  polyline.forEach((x) => tasksItems.push(x))

}

const createMarkerIconUrl = ({to, routeOrder, type}) => {
  return pointMarkers[to][type][routeOrder]
}

const createTaskMarkers = async (params) => {

  params.forEach((task) => {


    if (!clusterVisible.value) createTaskPolyline(task)


    let originMarkerIconParams = {
      to: 'origin',
      routeOrder: '0',
      type: task.status_category_slug === 'completed' ? 'done' : 'default'
    }


    let destinationMarkerIconParams = {
      to: 'destination',
      routeOrder: '0',
      type: task.status_category_slug === 'completed' ? 'done' : 'default'
    }

    const originContentString = CreateInfoWindow.forOrigin({
      task,
      imgSrc: createMarkerIconUrl(originMarkerIconParams),
      type: originMarkerIconParams.type
    })

    const destinationContentString = CreateInfoWindow.forDestination({
      task,
      imgSrc: createMarkerIconUrl(destinationMarkerIconParams),
      type: destinationMarkerIconParams.type
    })

    if (!clusterVisible.value) {

      const originMarker = CreateMarker.forTask({
        id: task.id,
        map: operationMap.value,
        lat: task.origin_lat,
        lng: task.origin_lng,
        type: 'origin',
        iconUrl: createMarkerIconUrl(originMarkerIconParams)
      })

      google.maps.event.addListener(originMarker, 'click', function () {
        markers.infowindow.setContent(originContentString)
        markers.infowindow.open(operationMap.value, originMarker);
      });

      tasksItems.push(originMarker)

    }

    const destinationMarker = CreateMarker.forTask({
      id: task.id,
      map: operationMap.value,
      lat: task.destination_lat,
      lng: task.destination_lng,
      type: 'destination',
      iconUrl: createMarkerIconUrl(destinationMarkerIconParams)
    })

    google.maps.event.addListener(destinationMarker, 'click', function () {
      markers.infowindow.setContent(destinationContentString)
      markers.infowindow.open(operationMap.value, destinationMarker);
    });


    tasksItems.push(destinationMarker)
  })
}

const clearTaskMaskers = async () => {

  tasksItems.forEach((marker) => {
    marker.setMap(null)
  })

  if (cluster) {
    cluster.onRemove()
    cluster.clearMarkers()
  }

  tasksItems = []
}

const clearSpecificTaskMaskers = async () => {

  specificTaskItems.forEach((marker) => {
    marker.setMap(null)
  })

  specificTaskItems = []
}

const hideTasks = async () => {

  await clearTaskMaskers()
  await clearSpecificTaskMaskers()
  await applyFilter()
}

const showCourierTasks = async () => {

  await clearTaskMaskers()
  await clearSpecificTaskMaskers()


  if (clusterVisible.value) {
    await createDestinationMarkerCluster()
  } else {
    await createTaskMarkers(showCourierTasksOnMap.value)
  }

  const bounds = new google.maps.LatLngBounds()

  if (showCourierTasksOnMap.value.length > 0) {

    let findHasCourierId = showCourierTasksOnMap.value.find(x => x.courier_id).courier_id

    markers.couriers.forEach((marker) => {

      if (marker.id === findHasCourierId) {
        bounds.extend(marker.getPosition())
        return marker.setVisible(true)
      }

      if (selectedCourier.value && selectedCourier.value.id === marker.id) return

      marker.setVisible(false)

    })
  }


  tasksItems.forEach((item) => {
    if (item.type === "polyline") return
    bounds.extend(item.getPosition())
  })

  operationMap.value.fitBounds(bounds, 80);

}

const toggleBounceSpecificTask = async () => {

  await clearSpecificTaskMaskers()

  if (activeTaskTab.value.name === "RouteDetail") {

    planItems.forEach((item) => {

      if (item.type === "polyline") return

      if (item.id === showSpecificTaskOnMap.value?.id) {
        item.setVisible(false)
      } else {
        item.setVisible(true)
      }
      // item.setMap(null)
    })

  }

  if (!showSpecificTaskOnMap.value) {

    if (clusterVisible.value) createDestinationMarkerCluster()

    if (activeTaskTab.value.name !== "RouteDetail") courierMarkersReset()

    return

  }


  if (activeTaskTab.value.name === "TabTaskDetail") {

    tasksItems.forEach(x => x.setMap(null))
    tasksItems = []

    if (cluster) {

      cluster.onRemove()
      cluster.clearMarkers()

    }
  }


  const task = showSpecificTaskOnMap.value

  if (task.path && activeTaskTab.value.name !== "RouteDetail") {

    const polyline = await createPolyline({
      task,
      path: task.path,
      map: operationMap.value,
      selected: true,
      status: task.status_category_slug
    })

    polyline.forEach((x) => specificTaskItems.push(x))

  }

  let originMarkerIconParams = {
    to: 'origin',
    routeOrder: '0',
    type: "selected"
  }

  let destinationMarkerIconParams = {
    to: 'destination',
    routeOrder: activeTaskTab.value.name === "RouteDetail" && task.route_order ? String(task.route_order) : "0",
    type: "selected"
  }

  const originContentString = CreateInfoWindow.forOrigin({
    task,
    imgSrc: createMarkerIconUrl(originMarkerIconParams),
    type: originMarkerIconParams.type
  })

  const destinationContentString = CreateInfoWindow.forDestination({
    task,
    imgSrc: createMarkerIconUrl(destinationMarkerIconParams),
    type: destinationMarkerIconParams.type
  })


  if (activeTaskTab.value.name !== "RouteDetail") {

    const originMarker = CreateMarker.forTask({
      id: task.id,
      map: operationMap.value,
      lat: task.origin_lat,
      lng: task.origin_lng,
      iconUrl: createMarkerIconUrl(originMarkerIconParams)
    })

    google.maps.event.addListener(originMarker, 'click', function () {
      markers.infowindow.setContent(originContentString)
      markers.infowindow.open(operationMap.value, originMarker);
    });

    specificTaskItems.push(originMarker)

  }


  const destinationMarker = CreateMarker.forTask({
    id: task.id,
    map: operationMap.value,
    lat: task.destination_lat,
    lng: task.destination_lng,
    iconUrl: createMarkerIconUrl(destinationMarkerIconParams)
  })

  google.maps.event.addListener(destinationMarker, 'click', function () {
    markers.infowindow.setContent(destinationContentString)
    markers.infowindow.open(operationMap.value, destinationMarker);
  });

  specificTaskItems.push(destinationMarker)


  if (task.status_logs) {

    task.status_logs.forEach((status) => {

      if (!visibleStatusPointSlugs.includes(status.status_category_slug)) return

      const statusContentString = CreateInfoWindow.forStatusPoint({
            created_at: status.created_at,
            name: status.status.name
          }
      )

      const statusMarker = CreateMarker.forStatusPoint(status, operationMap.value)

      google.maps.event.addListener(statusMarker, 'click', function () {
        markers.infowindow.setContent(statusContentString)
        markers.infowindow.open(operationMap.value, statusMarker);
      });

      specificTaskItems.push(statusMarker)
    })

  }

  const bounds = new google.maps.LatLngBounds()

  specificTaskItems.forEach((item) => {

    if (item.type === "polyline") return
    if (item.type === "statusPoint") return

    bounds.extend(item.getPosition())

  })

  if (!showSpecificTaskOnMap.value.courier_id) {
    operationMap.value.fitBounds(bounds, 80);
    return courierMarkersReset()
  }

  markers.couriers.forEach((marker) => {

    if (marker.id === showSpecificTaskOnMap.value.courier_id) {
      bounds.extend(marker.getPosition())
      return marker.setVisible(true)
    }

    if (marker.id === selectedCourier.value?.id) {
      bounds.extend(marker.getPosition())
      return marker.setVisible(true)
    }

    marker.setVisible(false)

  })

  if (activeTaskTab.value.name !== "RouteDetail") {
    operationMap.value.fitBounds(bounds, 80);
  }

}

const hidePlan = () => {

  planItems.forEach((item) => {
    item.setVisible(false)
    item.setMap(null)
  })

  planItems = []

  courierMarkersReset()

}

const changeStatusUpdatedTaskMarkerIcon = ({param, task}) => {

  let destinationMarkerIconParams = {
    to: 'destination',
    routeOrder: String(task.route_order),
    type: param.status_category_slug === 'completed' ? "done" : "default"
  }

  planItems.forEach((marker) => {
    if (marker.id === task.id) {
      marker.setIcon(createMarkerIconUrl(destinationMarkerIconParams))
    }
  })

}

const showPlanOnMap = async () => {

  await clearTaskMaskers()

  const {tasks, hub, path} = showSpecificPlanOnMap.value

  const bounds = new google.maps.LatLngBounds()

  let type = tasks.find(x => x.status_category_slug !== "assigned") ? 'done' : 'default'

  let originMarkerIconParams = {
    to: 'origin',
    routeOrder: '0',
    type: type
  }

  const originMarker = CreateMarker.forTask({
    id: showSpecificPlanOnMap.value.id,
    map: operationMap.value,
    lat: hub.lat,
    lng: hub.lng,
    iconUrl: createMarkerIconUrl(originMarkerIconParams)
  })

  planItems.push(originMarker)

  bounds.extend(originMarker.getPosition())

  tasks.forEach((task) => {

    let destinationMarkerIconParams = {
      to: 'destination',
      routeOrder: String(task.route_order),
      type: task.status_category_slug === 'completed' ? "done" : "default"
    }

    const destinationContentString = CreateInfoWindow.forDestination({
      task,
      imgSrc: createMarkerIconUrl(destinationMarkerIconParams),
      type: destinationMarkerIconParams.type
    })

    const destinationMarker = CreateMarker.forTask({
      id: task.id,
      map: operationMap.value,
      lat: task.destination_lat,
      lng: task.destination_lng,
      iconUrl: createMarkerIconUrl(destinationMarkerIconParams)
    })

    google.maps.event.addListener(destinationMarker, 'click', function () {
      markers.infowindow.setContent(destinationContentString)
      markers.infowindow.open(operationMap.value, destinationMarker);
    });

    planItems.push(destinationMarker)
    bounds.extend(destinationMarker.getPosition())
  })


  markers.couriers.forEach((marker) => {

    if (marker.id === tasks[0].courier_id) {
      bounds.extend(marker.getPosition())
      return marker.setVisible(true)
    }

    if (selectedCourier.value && selectedCourier.value.id === marker.id) return

    marker.setVisible(false)

  })

  if (!path) return

  const polyline = await createPolyline({
    path: path,
    map: operationMap.value,
    selected: false,
    status: null
  })

  polyline.forEach((x) => planItems.push(x))

  planItems.forEach((item) => {
    if (item.type === "polyline") return
    bounds.extend(item.getPosition())
  })


  operationMap.value.fitBounds(bounds, 80);
}

emitter.on("filtered-courier", couriersFiltered)
emitter.on("update_destination_marker", changeStatusUpdatedTaskMarkerIcon)

// Throttled courier location update - 1 saniyede bir maksimum
const throttledCourierLocationUpdate = throttle(courierLocationUpdated, 1000);
const throttledCourierCapacityUpdate = throttle(courierCapacityUpdated, 2000);

const listenChannel = () => {
  Echo.channel("courier.location").listen(".courier.location.updated", throttledCourierLocationUpdate);
  Echo.private("company." + activeCompany.value.id).listen(".courier.capacity.updated", throttledCourierCapacityUpdate);
}

// const stopListening = () => {
//   // Echo.channel("courier.location").stopListening(".courier.location.updated")
//   // Echo.private("company." + activeCompany.value.id).stopListening(".courier.disconnected");
// }

</script>

<template>
  <div
      id="operationMap"
      class="w-full h-full grow relative"
  />

</template>
