<script setup>
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import dayjs from "dayjs";
import {onMounted, ref, inject} from "vue";
import {VehicleTypeIcon} from "@/class";
import TaskDetailMap from './TaskDetailMap.vue'

const api = inject("api")
const props = defineProps({
  task: {type: Object},
  taskId: {type: Number},
})


const loader = ref()
const _task = ref()

const getData = () => {
  loader.value.show()
  if (props.taskId) {
    api(`customer/tasks/${props.taskId}`)
        .then((r) => {
          _task.value = r
        })
        .finally(() => loader.value.hide())
  } else {
    _task.value = props.task
    loader.value.hide()
  }
}

onMounted(() => {
  getData()
})

</script>
<template>
  <LoadingBlock ref="loader"/>
  <div v-if="_task" class="w-full h-full relative">
    <div class="absolute inset-0 overflow-hidden flex-col flex">
      <div class="h-24 w-full flex justify-between items-center p-5 select-text">
        <div class="h-full flex-grow flex items-center">
          <div class="flex-col">
            <div class="font-bold flex items-center text-xl text-slate-700">
              <div>
                {{ _task.tracking_code }}
              </div>
              <div class="ml-1">
                <StatusTag :statusSlug="_task.status_category_slug">{{ _task.status_name }}</StatusTag>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-shrink">
          <!--          <div-->
          <!--              class="w-10 h-9 flex rounded-md border border-slate-300 justify-center items-center opacity-70 cursor-pointer"-->
          <!--          >-->
          <!--            <FontAwesomeIcon icon="edit"/>-->
          <!--          </div>-->
        </div>
      </div>
      <el-tabs class=" h-full overflow-auto delivery_detail_tab" type="border-card" tab-position="top">
        <el-tab-pane :label="$t('Task Detail')" class="h-full w-full">
          <div class="w-full h-full flex-col select-text">
            <div class="w-full bg-slate-50 border-t border-b border-slate-300 grid grid-cols-4 gap-4 px-5 py-4">
              <div class="col-span-2 md:col-span-1">
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Created Date') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ dayjs(_task.created_at).format('DD.MM.YYYY HH:mm') }}
                  </div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ `${$t('Distance')} (Km)` }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ (_task.distance / 1000).toFixed(1) + " km" }}
                  </div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Estimated Time Arrival') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ (_task.duration / 60).toFixed(1) + " " + $t('Min') }}
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1">
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Task') }} Id</div>
                  <div class="detail--information--label--text--desc">{{ _task.id }}</div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Quantity') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ _task.quantity }}
                  </div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Service Time') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ _task.service_time }} {{ $t('Min') }}
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1 block">

                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Start Date') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ dayjs(_task.starts_at).format('DD.MM.YYYY HH:mm') }}
                  </div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('End Date') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ dayjs(_task.ends_at).format('DD.MM.YYYY HH:mm') }}
                  </div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Task Channel') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ _task.channel === "ui" ? "interface" : _task.channel }}
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1">
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Delivery Type') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ _task.slot ? 'Slot' : $t('Single') }}
                  </div>
                </div>
                <div class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Task Owner') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ _task.hub_name }}
                  </div>
                </div>
                <div v-if="_task.amount" class="mb-1.5">
                  <div class="detail--information--label--text">{{ $t('Amount') }}</div>
                  <div class="detail--information--label--text--desc">
                    {{ _task.amount }}
                  </div>
                </div>
              </div>
            </div>
            <div class="w-full flex">
              <div class="w-full border-b border-slate-300 mt-2.5 flex-col text-slate-700 text-sm">
                <div class="text-lg font-bold mx-5">{{ $t('Origin') }}</div>
                <div class="grid grid-cols-2 gap-4 m-5">
                  <div class="col-span-2">
                    <div class="font-semibold">{{ _task.origin_name }}</div>
                    <div>{{ _task.origin_address }}</div>
                  </div>
                  <div class="col-span-2">
                    <div>{{ $t('Building No') }}: {{ _task.origin_address_building }}</div>
                    <div>{{ $t('Floor') }}: {{ _task.origin_address_floor }}</div>
                    <div>{{ $t('Apartment') }}: {{ _task.origin_address_apartment }}</div>
                  </div>
                </div>
              </div>
              <div class="w-full border-b border-slate-300 mt-2.5 flex-col text-slate-700 text-sm">
                <div class="text-lg font-bold mx-5">{{ $t('Destination') }}</div>
                <div class="grid grid-cols-2 gap-4 m-5">
                  <div class="col-span-2">
                    <div class="font-semibold">{{ _task.destination_name }}</div>
                    <div>{{ _task.destination_address }}</div>
                  </div>
                  <div class="col-span-2">
                    <div>{{ $t('Building No') }}: {{ _task.destination_address_building }}</div>
                    <div>{{ $t('Floor') }}: {{ _task.destination_address_floor }}</div>
                    <div>{{ $t('Apartment') }}: {{ _task.destination_address_apartment }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div
                class="w-full"
                style="height: 340px"
            >
              <TaskDetailMap :payload="_task"/>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('Operation History')" class="h-full w-full">
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-10 gap-y-4 p-5">
            <div class="col-span-1">
              <DetailCardContent>
                <template #title> {{ $t('Delivery Status') }}</template>
                <TaskDeliveryStatus :logs="_task.status_logs"/>
              </DetailCardContent>
            </div>
            <div class="col-span-1">
              <DetailCardContent>
                <template #title> {{ $t('Driver Information') }}</template>
                <div
                    class="group flex justify-between w-full items-center space-x-2"
                    style="max-width: 375px"
                >
                  <div class="flex items-center space-x-2">
                    <div class="flex relative items-center">
                      <!--                  <img-->
                      <!--                      class="rounded-full z-0"-->
                      <!--                      :src="courier.avatar_url ? courier.avatar_url : AvatarImg "-->
                      <!--                  />-->
                    </div>
                    <div class="flex-1 min-w-0">
                      <p
                          class="truncate flex items-center space-x-1 text-xs font-medium text-slate-700"
                      >
                        <span class="select-text">{{ _task.courier_name }}</span>
                      </p>
                      <p
                          class="text-xxs font-light text-slate-700"
                      >
                        <span class="select-text">{{ _task.courier_phone }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-2 items-center">
                    <div

                        class="flex flex-col items-end font-light text-xxs select-text"
                    >
                      {{ _task.courier_detail_vehicle_license_plate }}
                      <span class="inline-block items-center rounded-full">
          </span>
                    </div>
                    <div>
                      <img v-if="_task.courier_detail_vehicle_type_slug" class="w-7.5"
                           :src="VehicleTypeIcon[_task.courier_detail_vehicle_type_slug]"
                           alt="vehice-type"/>
                    </div>
                  </div>
                </div>
              </DetailCardContent>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
