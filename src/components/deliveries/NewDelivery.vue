<script setup>
import {
  ref,
  reactive,
  onMounted,
  nextTick,
  inject,
  computed,
  watch
} from "vue";
import dayjs from "dayjs";
import NavItem from "@/components/deliveries/components/NavItem.vue";
import { useScroll } from "@vueuse/core";
import AutomaticAssign from "@/components/deliveries/components/AutomaticAssign.vue";
import ManuelAssign from "@/components/deliveries/components/ManuelAssign.vue";
import EmbedMap from "@/components/ui/EmbedMap.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import PhoneInput from "@/components/ui/PhoneInput.vue";
import { Options } from "@/class";
import SlotInput from "@/components/ui/SlotInput.vue";
import { useI18n } from "vue-i18n";
import { useStore } from "vuex";
import { Loader } from "@googlemaps/js-api-loader";
import { useToast } from "vue-toastification";

const toast = useToast();
const { getters } = useStore();

const companyChannel = computed(
  () => "company." + getters["auth/activeCompany"].id
);
const { t } = useI18n();
const api = inject("api");
const emitter = inject("emitter");
const emit = defineEmits(["close"]);
const props = defineProps({
  visible: { type: Boolean }
});
const taskCountCheck = ref(false);
const taskCount = ref(0);

const disabled = ref(false);

const origin_address = reactive({
  apartment: null,
  floor: null,
  building: null,
  searchAddress: null,
  lat: null,
  lng: null,
  address: null,
  hub: null,
  description: null
});
const destination_address = reactive({
  apartment: null,
  floor: null,
  building: null,
  searchAddress: null,
  lat: null,
  lng: null,
  address: null,
  hub: null,
  description: null
});
const tabs = reactive({
  origin: "hubs",
  destination: "address",
  assign: "automatic"
});
const search = ref(null);
// const hub_id = ref(null);
const team_id = ref(null);
const sender_phone = reactive({
  country: "TR",
  phone: null
});
const receipent_phone = reactive({
  country: "TR",
  phone: null
});
const formValues = reactive({
  tags: [],
  owner: null,
  name: null,
  sender: null,
  hub: null,
  receipent: null,
  lastName: null,
  address: null,
  city: null,
  delivery_code: false,
  delivery_code_mandatory: false,
  proof_photo: false,
  proof_photo_mandatory: false,
  state: null,
  zip: null,
  destinationAddress: null,
  tracking: null,
  quantity: 1,
  service: null,
  unit_quantity: null,
  response: null,
  notes: null,
  startDate: null, //dayjs(),
  endDate: null, // dayjs().add(30, "minute"),
  slot: null,
  team: null,
  workingStatus: "all",
  onlineStatus: "all",
  obligation: "mandatory",
  courier: null,
  assigment: "capacity_aware",
  duty: "all",
  slot_starts_at: null,
  slot_ends_at: null,
  dont_ring_the_bell: false,
  contactless: false,
  recipient_notes: null,
  integration_id: null,
  locked: false,
  confirmed: true,
  actionType: "drop_off",
  priority: 0,
  distribution: "balanced",
  min_capacity: 4,
  team_id_by_polygon: false
});


const computed_origin_hub = computed(() => {
  return origin_address.hub
    ? hubs.value.find((c) => c.id === origin_address.hub).address
    : null;
});
const computed_destination_hub = computed(() => {
  return destination_address.hub
    ? hubs.value.find((c) => c.id === destination_address.hub).address
    : null;
});


const couriers = ref([]);
const hubs = ref([]);
const teams = ref([]);
const tags = ref([]);
const loader = ref(null);

const formContainer = ref(null);
const originEl = ref(null);
const destinationEl = ref(null);
const deliveryEl = ref(null);
const assignmentEl = ref(null);
let NAV_ITEMS = [
  {
    name: t("Origin"),
    ref: originEl,
    offset: { top: 0, bottom: 1 }
  },
  {
    name: t("Destination"),
    ref: destinationEl,
    offset: {}
  },
  {
    name: t("Assignment Options"),
    ref: assignmentEl,
    offset: {}
  },
  {
    name: t("Delivery Options"),
    ref: deliveryEl,
    offset: {}
  }
];

const { y: formContainerScrollPosition } = useScroll(formContainer);


const getElOffsets = (el) => {
  const top = el.offsetTop;
  const bottom = top + el.offsetHeight;
  return { top, bottom };
};

const goToSection = (position) => {
  formContainer.value.scrollTo({ top: position, behavior: "smooth" });
};

const getHubs = () => {
  return api("customer/hubs").then((res) => {
    hubs.value = res.data;
    if (res.data.length === 1) {
      formValues.owner = res.data[0].id;
      formValues.service = res.data[0].default_service_time;
      formValues.tags = [...res.data[0].tags];
    }
  });
};
const getTeams = () => {
  if (formValues.owner) {
    return api(`customer/teams?filter[hub_id]=${formValues.owner}`)
      .then((res) => (teams.value = res.data));
  } else {
    return api("customer/teams")
      .then((res) => (teams.value = res.data));
  }
};
const getTags = () => {
  return api("customer/tags")
    .then((res) => (tags.value = res.data));
};

const changedStartDate = (_date) => {
  let date = _date;
  formValues.endDate = date.getTime() + 2 * 60 * 60 * 1000;
};

const changedSlotStartDate = (_date) => {
  let date = _date;
  formValues.slot_ends_at = date.getTime() + 2 * 60 * 60 * 1000;
};

const activeCompany = computed(() => getters["auth/activeCompany"]);

onMounted(() => {
  // formValues.assigment = "capacity_aware";
  nextTick(() => {
    loader.value.show();
    const mappedNav = NAV_ITEMS.map((item) => {
      item.offset = getElOffsets(item.ref.value);
      return item;
    });
    NAV_ITEMS = mappedNav;
    gooleAutoComplete();
    formValues.locked = activeCompany.value.lock_tasks;
    emitter.on(".courier.location.updated", () => getFilteredCourierList());
    Promise.all([getTeams(), getHubs(), getTags()]).finally(() => loader.value.hide());
  });
});


const gooleAutoComplete = async () => {

  const { Autocomplete } = await google.maps.importLibrary("places");

  const address = new Autocomplete(
    document.getElementById("origin_address_search")
  );

  address.addListener("place_changed", () => {
    const place = address.getPlace();
    if (!place.geometry || !place.geometry.location) {
      window.alert("No details available for input: '" + place.name + "'");
      return;
    }
    const location = place.geometry.location;

    resolveAddressFromCoordinates(location.lat(), location.lng()).then(
      (address) => {
        origin_address.lat = location.lat();
        origin_address.lng = location.lng();
        origin_address.address = address;
      }
    );
  });
  const dest_address = new Autocomplete(
    document.getElementById("destination_address_search")
  );
  dest_address.addListener("place_changed", () => {
    const place = dest_address.getPlace();
    if (!place.geometry || !place.geometry.location) {
      window.alert("No details available for input: '" + place.name + "'");
      return;
    }
    const location = place.geometry.location;

    resolveAddressFromCoordinates(location.lat(), location.lng()).then(
      (address) => {
        destination_address.lat = location.lat();
        destination_address.lng = location.lng();
        destination_address.address = address;
      }
    );
  });
};

function resolveAddressFromCoordinates(lat, lng) {
  lat = parseFloat(lat.toFixed(8));
  lng = parseFloat(lng.toFixed(8));

  return new Promise((resolve) => {
    new google.maps.Geocoder().geocode(
      { location: { lat, lng } },
      (results, status) => {
        if (status === "OK") {
          if (results[0]) {
            resolve(results[0].formatted_address);
          }
        }
      }
    );
  });
}

// function setHubAsOrigin(hub_id) {
//   let hub =
//   if (hub) {
//     const location = new google.maps.LatLng(parseFloat(hub.lat), parseFloat(hub.lng))
//
//     resolveAddressFromCoordinates(location.lat(), location.lng()).then(address => {
//       originLatLng.value = location
//       form.origin_address = address
//       form.origin_lat = location.lat()
//       form.origin_lng = location.lng()
//       form.origin_name = hub.name
//     })
//   }
// }
//
// function setHubAsDestination(hub_id) {
//   if (hub) {
//     const location = new google.maps.LatLng(parseFloat(hub.lat), parseFloat(hub.lng))
//
//     resolveAddressFromCoordinates(location.lat(), location.lng()).then(address => {
//       destinationLatLng.value = location
//       form.destination_lat = location.lat()
//       form.destination_lng = location.lng()
//       form.destination_address = address
//       form.destination_name = hub.name
//     })
//   }
// }

const setSearch = (text) => {
  search.value = text;
};
const setAssigment = (slug) => {
  formValues.assigment = slug;
};
const setCourierId = (id) => {
  formValues.courier_id = id;
};
const selectOriginHub = (params) => {
};

const onCreate = (param) => {
  loader.value.show();
  disabled.value = true;
  canCreate()
    .then(() => {
      createTask(param);
    })
    .catch((err) => {
      toast.error(t(err));
      loader.value.hide();
      disabled.value = false;
    });
};
const canCreate = () => {
  return new Promise((resolve, reject) => {
    if (!formValues.owner) {
      return reject("Owner is empty");
    }
    if (tabs.origin === "hubs") {
      if (!origin_address.hub) {
        return reject("Origin address is empty");
      }
    } else {
      if (!origin_address.lat || !origin_address.lng) {
        return reject("Origin address is empty");
      } else if (!formValues.sender) {
        return reject("Sender is empty");
      }
    }
    if (tabs.destination === "hubs") {
      if (!destination_address.hub) {
        return reject("Destination address is empty");
      }
    } else {
      if (!destination_address.lat || !destination_address.lng) {
        return reject("Destination address is empty");
      } else if (!formValues.receipent) {
        return reject("Recipient is empty");
      }
    }
    if (tabs.assign === "manuel") {
      if (!formValues.courier) {
        return reject("Driver not selected");
      }
    } else {
      if (formValues.assigment === "slot" && !formValues.slot) {
        return reject("Slot is empty");
      }
    }
    if (!formValues.startDate) {
      return reject("Start date is empty");
    }
    if (!formValues.endDate) {
      return reject("End date is empty");
    }
    return resolve();
  });
};
const createTask = (param) => {
  const addProperty = (obj, key, value) => {
    if (value !== null && value !== undefined) {
      obj[key] = value;
    }
  };

  let body = {};
  addProperty(body, "notes", formValues.notes);
  addProperty(body, "recipient_notes", formValues.recipient_notes);
  addProperty(body, "tags", formValues.tags);
  addProperty(body, "starts_at", dayjs(formValues.startDate));
  addProperty(body, "ends_at", dayjs(formValues.endDate));
  addProperty(body, "slot", formValues.assigment === "slot" ? formValues.slot : null);
  addProperty(body, "slot_starts_at", formValues.assigment === "slot" ? dayjs(formValues.slot_starts_at) : null);
  addProperty(body, "slot_ends_at", formValues.assigment === "slot" ? dayjs(formValues.slot_ends_at) : null);
  addProperty(body, "quantity", formValues.quantity ? formValues.quantity : 0);
  addProperty(body, "priority", formValues.priority ? formValues.priority : 0);
  addProperty(body, "service_time", formValues.service ? formValues.service : 0);
  addProperty(body, "unit_quantity", formValues.unit_quantity ? Number(formValues.unit_quantity) : 0);
  addProperty(body, "response_time", formValues.response ? formValues.response : 0);
  addProperty(body, "locked", formValues.locked);
  addProperty(body, "confirmed", formValues.confirmed);
  addProperty(body, "action_slug", formValues.actionType);
  addProperty(body, "integration_id", formValues.integration_id);
  addProperty(body, "tracking_code", formValues.tracking);
  addProperty(body, "hub_id", formValues.owner);

  let requirements = {};
  addProperty(requirements, "dont_ring_the_bell", formValues.dont_ring_the_bell);
  addProperty(requirements, "contactless", formValues.contactless);
  addProperty(requirements, "delivery_code", formValues.delivery_code);
  addProperty(requirements, "delivery_code_mandatory", formValues.delivery_code ? formValues.delivery_code_mandatory : false);
  addProperty(requirements, "pod_photo", formValues.proof_photo);
  addProperty(requirements, "pod_photo_mandatory", formValues.proof_photo ? formValues.proof_photo_mandatory : false);
  body.requirements = requirements;

  let assignmentOptions = {};
  addProperty(assignmentOptions, "courier_duty_status", formValues.duty);
  addProperty(assignmentOptions, "team_id", team_id.value);
  addProperty(assignmentOptions, "courier_online_status", formValues.onlineStatus);
  addProperty(assignmentOptions, "courier_shift_status", formValues.workingStatus);
  addProperty(assignmentOptions, "obligation", formValues.obligation);
  addProperty(assignmentOptions, "distribution", formValues.distribution);
  addProperty(assignmentOptions, "team_id_by_polygon", formValues.team_id_by_polygon);

  if (formValues.distribution === "adaptive") {
    addProperty(assignmentOptions, "min_capacity", Number(formValues.min_capacity));
  }
  body.assignment_options = assignmentOptions;

  if (param === "draft") {
    body.assignment = {
      type: "draft",
      courier_id: null
    };
  } else if (tabs.assign === "manuel") {
    body.assignment = {
      type: "selected_courier",
      courier_id: formValues.courier.id
    };
  } else {
    body.assignment = {
      type: formValues.assigment
    };
  }

  if (tabs.origin === "hubs") {
    let hub = hubs.value.find((c) => c.id === origin_address.hub);
    body.origin = {
      hub_id: origin_address.hub,
      name: hub.name,
      address: hub?.address,
      address_building: hub?.building,
      address_floor: hub?.floor,
      address_apartment: hub?.apartment,
      lat: hub?.lat,
      lng: hub?.lng
    };
  } else {
    body.origin = {
      name: formValues.sender,
      phone_country: sender_phone.phone ? sender_phone.country : null,
      phone: sender_phone.phone,
      address: origin_address.address,
      address_building: origin_address.building,
      address_floor: origin_address.floor,
      address_apartment: origin_address.apartment,
      address_description: origin_address.description,
      lat: origin_address.lat.toFixed(6),
      lng: origin_address.lng.toFixed(6)
    };
  }

  if (tabs.destination === "hubs") {
    let hub = hubs.value.find((c) => c.id === destination_address.hub);
    body.destination = {
      hub_id: destination_address.hub,
      name: hub.name,
      address: hub?.address,
      address_building: hub?.building,
      address_floor: hub?.floor,
      address_apartment: hub?.apartment,
      lat: hub?.lat,
      lng: hub?.lng
    };
  } else {
    body.destination = {
      name: formValues.receipent,
      phone_country: receipent_phone.phone ? receipent_phone.country : null,
      phone: receipent_phone.phone,
      address: destination_address.address,
      address_building: destination_address.building,
      address_floor: destination_address.floor,
      address_apartment: destination_address.apartment,
      address_description: destination_address.description,
      lat: destination_address.lat.toFixed(6),
      lng: destination_address.lng.toFixed(6)
    };
  }

  if (!taskCountCheck.value) {
    api
      .post("customer/tasks", body)
      .then((r) => {
        emit("close", true);
        emitter.emit("refresh_tasks");
        toast.success(`${r.id} Numaralı görev oluşturuldu.`);
      })
      .catch((err) => toast.error(err.message))
      .finally(() => {
        loader.value.hide();
        disabled.value = false;
        taskCount.value = 0;
      });
  } else {
    api
      .post("customer/tasks", body)
      .then((r) => {
        taskCount.value = taskCount.value + 1;
        emitter.emit("refresh_tasks");
        toast.success(`${r.id} Numaralı görev oluşturuldu.`);
      })
      .catch((err) => toast.error(err.message))
      .finally(() => {
        loader.value.hide();
        disabled.value = false;
      });
  }
};


watch(
  [
    origin_address,
    tabs.assign,
    search,
    team_id,
    () => formValues.owner,
    () => formValues.onlineStatus,
    () => formValues.workingStatus
  ],
  () => {
    getFilteredCourierList();
  },
  { deep: true }
);

const getFilteredCourierList = () => {
  let latlng;
  if (tabs.origin === "hubs" && origin_address.hub) {
    let { lat, lng } = hubs.value.find((c) => c.id === origin_address.hub);
    latlng = lat + "," + lng;
  } else {
    if (
      !origin_address.searchAddress ||
      !origin_address.lat ||
      !origin_address.lng
    ) {
      return;
    }
    let { lat, lng } = origin_address;
    latlng = lat + "," + lng;
  }
  let body = {
    filter: {
      search: search.value ? search.value : null,
      online: formValues.onlineStatus,
      shift: formValues.workingStatus,
      latlng: latlng ? latlng : null,
      team_id: team_id.value ? team_id.value : null,
      duty: formValues.duty,
      hub_id: formValues.owner
    }
  };
  api
    .post(
      "components/compact-couriers-table",
      body
    )
    .then((response) => {
      couriers.value = response.data;
    })
    .catch(() => {
    })
    .finally(() => {
    });
};
const ownerChanged = (id) => {
  let selectedHub = hubs.value.find(hub => hub.id === id);
  formValues.tags = [...selectedHub.tags];
  formValues.service = selectedHub.default_service_time;
  getFilteredCourierList();
  getTeams();
};

const changedConfirmed = (param) => {
  if (param) {
    formValues.locked = false;
  }
};
const changedLocked = (param) => {
  if (param) {
    formValues.confirmed = false;
  }
};
watch(props, () => {
  if (props.visible) {
    emitter.on(".courier.location.updated", () => getFilteredCourierList());
  } else {
    emitter.off(".courier.location.updated");
  }
}, { deep: true });


watch(() => formValues.owner, () => {
  team_id.value = null;
});


const checkInput = (event) => {
  formValues.unit_quantity = formValues.unit_quantity.replace(/[^0-9]/g, "");
};

const serviceRules = (event) => {
  formValues.service = formValues.unit_quantity.replace(/[^0-9]/g, "");
};

const qantityRules = (event) => {
  formValues.quantity = formValues.quantity.replace(/[^0-9]/g, "");
};

const priorityRules = (event) => {
  formValues.priority = formValues.priority.replace(/[^0-9]/g, "");
};


</script>

<template>
  <div class="w-full h-full flex">
    <LoadingBlock ref="loader" />
    <div
      class="hidden sm:block w-56 bg-slate-50 border-slate-300 border-r py-5"
    >
      <NavItem
        v-for="item in NAV_ITEMS"
        :key="item.name"
        :active="
          formContainerScrollPosition >= item.offset.top &&
          formContainerScrollPosition < item.offset.bottom
        "
        @click="goToSection(item.offset.top)"
      >
        {{ item.name }}
      </NavItem>
    </div>
    <div class="flex-grow relative">
      <div
        ref="formContainer"
        class="absolute inset-0 overflow-y-auto px-2.5"
        style="padding-bottom: 100px"
      >
        <div ref="originEl">
          <FormSectionCard ref="originEl">
            <template #title>{{ t("Origin") }}</template>
            <el-form label-position="top" class="grid grid-cols-12 gap-4">
              <div v-if="hubs.length > 1" class="col-span-12">
                <el-form-item :label="t('Select Owner')" required>
                  <el-select
                    v-model="formValues.owner"
                    @change="ownerChanged"

                    :placeholder="t('Select Owner')"
                    filterable
                    class="w-full-important"
                  >
                    <el-option
                      v-for="item in hubs"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div v-if="tabs.origin === 'address'" class="col-span-12">
                <el-form-item :label="t('Sender')" required>
                  <el-input
                    v-model="formValues.sender"
                    :placeholder="t('Sender')"
                    required
                  />
                </el-form-item>
              </div>
              <div v-if="tabs.origin === 'address'" class="col-span-12">
                <PhoneInput v-model="sender_phone" />
              </div>
              <div class="col-span-12">
                <el-tabs v-model="tabs.origin" type="card">
                  <el-tab-pane :label="$t('Hubs')" name="hubs">
                    <template #label>
                      <FontAwesomeIcon class="mr-2" icon="store" />
                      <span>
                        {{ $t("Hubs") }}
                        <span v-if="tabs.origin === 'hubs'" class="text-red-500">
                          *
                        </span>
                      </span>
                    </template>
                    <el-select
                      v-model="origin_address.hub"
                      filterable
                      :placeholder="t('Select Hub')"
                      class="w-full-important"
                    >
                      <el-option
                        v-for="item in hubs"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                    <div
                      v-if="tabs.origin === 'hubs' && origin_address.hub"
                      class="bg-slate-100 text-slate-700 font-medium mt-1 rounded p-2 text-xs"
                    >
                      {{ computed_origin_hub }}
                    </div>
                  </el-tab-pane>
                  <el-tab-pane :label="t('Address')" name="address">
                    <template #label>
                      <FontAwesomeIcon class="mr-2" icon="map-marked-alt" />
                      <span>
                        {{ t("Address") }}
                        <span
                          v-if="tabs.origin === 'address'"
                          class="text-red-500">
                          *
                        </span>
                      </span>
                    </template>
                    <el-input
                      id="origin_address_search"
                      v-model="origin_address.searchAddress"
                      :placeholder="t('Address')"
                    />
                    <div class="grid grid-cols-12 gap-4">
                      <div class="col-span-12">
                        <EmbedMap
                          width="480"
                          height="140"
                          :lat="origin_address.lat"
                          :lng="origin_address.lng"
                        />
                      </div>
                      <div class="col-span-12 grid grid-cols-12 gap-4">
                        <div class="col-span-12 sm:col-span-4">
                          <el-form-item :label="t('Building Name/No')" required>
                            <el-input
                              v-model="origin_address.building"
                              :placeholder="t('Building Name/No')"
                            />
                          </el-form-item>
                        </div>
                        <div class="col-span-6 sm:col-span-4">
                          <el-form-item :label="t('Floor')">
                            <el-input
                              v-model="origin_address.floor"
                              :placeholder="t('Floor')"
                            />
                          </el-form-item>
                        </div>
                        <div class="col-span-6 sm:col-span-4">
                          <el-form-item :label="t('Apartment No')">
                            <el-input
                              v-model="origin_address.apartment"
                              :placeholder="t('Apartment No')"
                            />
                          </el-form-item>
                        </div>
                      </div>
                      <div class="col-span-12">
                        <el-form-item :label="t('Address Description')">
                          <el-input
                            v-model="origin_address.description"
                            :placeholder="t('Address Description')"
                          />
                        </el-form-item>
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-form>
          </FormSectionCard>
        </div>
        <div ref="destinationEl">
          <FormSectionCard>
            <template #title>{{ t("Destination") }}</template>
            <el-form label-position="top" class="grid grid-cols-12 gap-4">
              <div v-if="tabs.destination === 'address'" class="col-span-12">
                <el-form-item :label="t('Recipient')" required>
                  <el-input
                    v-model="formValues.receipent"
                    :placeholder="t('Recipient')"
                  />
                </el-form-item>
              </div>
              <div v-if="tabs.destination === 'address'" class="col-span-12">
                <PhoneInput v-model="receipent_phone" />
              </div>
              <div class="col-span-12">
                <el-tabs v-model="tabs.destination" type="card">
                  <el-tab-pane :label="t('Address')" name="address">
                    <template #label>
                      <FontAwesomeIcon class="mr-2" icon="map-marked-alt" />
                      <span>
                        {{ t("Address") }}
                        <span
                          v-if="tabs.destination === 'address'"
                          class="text-red-500">
                          *
                        </span>
                      </span>
                    </template>
                    <el-input
                      id="destination_address_search"
                      v-model="destination_address.searchAddress"
                      :placeholder="t('Address')"
                      required
                    />
                    <div class="grid grid-cols-12 gap-4">
                      <div class="col-span-12">
                        <EmbedMap
                          width="480"
                          height="140"
                          :lat="destination_address.lat"
                          :lng="destination_address.lng"
                        />
                      </div>
                      <div class="col-span-12 grid grid-cols-12 gap-4">
                        <div class="col-span-12 sm:col-span-4">
                          <el-form-item :label="t('Building Name/No')">
                            <el-input
                              v-model="destination_address.building"
                              :placeholder="t('Building Name/No')"
                            />
                          </el-form-item>
                        </div>
                        <div class="col-span-6 sm:col-span-4">
                          <el-form-item :label="t('Floor')">
                            <el-input
                              v-model="destination_address.floor"
                              :placeholder="t('Floor')"
                            />
                          </el-form-item>
                        </div>
                        <div class="col-span-6 sm:col-span-4">
                          <el-form-item :label="t('Apartment No')">
                            <el-input
                              v-model="destination_address.apartment"
                              :placeholder="t('Apartment No')"
                            />
                          </el-form-item>
                        </div>
                      </div>
                      <div class="col-span-12">
                        <el-form-item :label="t('Address Description')">
                          <el-input
                            v-model="destination_address.description"
                            :placeholder="t('Address Description')"
                          />
                        </el-form-item>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane :label="$t('Hubs')" name="hubs">
                    <template #label>
                      <FontAwesomeIcon class="mr-2" icon="store" />
                      <span>
                        {{ $t("Hubs") }}
                        <span
                          v-if="tabs.destination === 'hubs'"
                          class="text-red-500">
                          *
                        </span>
                      </span>
                    </template>
                    <el-select
                      v-model="destination_address.hub"
                      :placeholder="t('Select Hub')"
                      filterable
                      class="w-full-important"
                    >
                      <el-option
                        v-for="item in hubs"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                    <div
                      v-if="tabs.destination === 'hubs' && destination_address.hub"
                      class="bg-slate-100 text-slate-700 font-medium mt-1 rounded p-2 text-xs"
                    >
                      {{ computed_destination_hub }}
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-form>
          </FormSectionCard>
        </div>
        <div ref="assignmentEl">
          <FormSectionCard>
            <template #title>{{ t("Assignment Options") }}</template>
            <el-form label-position="top" class="grid grid-cols-12 gap-4">
              <div class="col-span-12">
                <el-form-item :label="t('Select Team')">
                  <el-select
                    v-model="team_id"
                    filterable
                    :placeholder="t('Select Team')"
                    class="w-full-important"
                    clearable
                    :disabled="!formValues.owner"
                  >
                    <el-option
                      v-for="item in teams"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item :label="t('Working Status')" required>
                  <el-select
                    v-model="formValues.workingStatus"
                    @change="getFilteredCourierList"
                    :placeholder="t('Working Status')"
                    class="w-full-important"
                  >
                    <el-option
                      v-for="item in Options.workingStatus"
                      :key="item.value"
                      :label="t(item.label)"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item :label="$t('Duty Status')">
                  <el-select
                    v-model="formValues.duty"
                    :placeholder="$t('Duty Status')"
                    clearable
                    filterable
                    class="w-full-important"
                  >
                    <el-option
                      v-for="item in Options.duty_type"
                      :key="item.value"
                      :label="$t(item.label)"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
<!--              <div class="col-span-6">-->
<!--                <el-form-item :label="t('Online Status')" required>-->
<!--                  <el-select-->
<!--                    v-model="formValues.onlineStatus"-->
<!--                    @change="getFilteredCourierList"-->
<!--                    :placeholder="t('Online Status')"-->
<!--                    class="w-full-important"-->
<!--                  >-->
<!--                    <el-option-->
<!--                      v-for="item in Options.status"-->
<!--                      :key="item.value"-->
<!--                      :label="t(item.label)"-->
<!--                      :value="item.value"-->
<!--                    >-->
<!--                    </el-option>-->
<!--                  </el-select>-->
<!--                </el-form-item>-->
<!--              </div>-->
              <div class="col-span-6">
                <el-form-item :label="t('Obligation')" required>
                  <el-select
                    v-model="formValues.obligation"
                    :placeholder="t('Obligation')"
                    class="w-full-important"
                  >
                    <el-option
                      v-for="item in Options.obligations"
                      :key="item.value"
                      :label="t(item.label)"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item :label="t('Distribution')" required>
                  <el-select
                    v-model="formValues.distribution"
                    :placeholder="t('Distribution')"
                    class="w-full-important"
                  >
                    <el-option
                      v-for="item in Options.distributions"
                      :key="item.value"
                      :label="t(item.label)"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item>
                  <el-checkbox v-model="formValues.team_id_by_polygon" size="small">
                <span class="text-sm text-slate-700">
                        {{ t("Takımı Otomatik Seç") }}
                      </span>
                  </el-checkbox>
                </el-form-item>

              </div>
              <div class="col-span-6">
                <el-form-item v-if="formValues.distribution === 'adaptive'" :label="t('Min Capacity')">
                  <el-input
                    v-model="formValues.min_capacity"
                    :placeholder="t('Min Capacity')"
                    type="number"
                  />
                </el-form-item>
              </div>
              <div class="col-span-12">
                <el-tabs v-model="tabs.assign" type="card">
                  <el-tab-pane name="automatic">
                    <template #label>
                      <FontAwesomeIcon class="mr-2" icon="magic" />
                      <span>{{ t("Automatic Assign") }}</span>
                    </template>
                    <AutomaticAssign v-model="formValues.assigment" />
                  </el-tab-pane>
                  <el-tab-pane name="manuel">
                    <template #label>
                      <FontAwesomeIcon class="mr-2" icon="exchange-alt" />
                      <span>{{ t("Manuel Assign") }}</span>
                    </template>
                    <ManuelAssign
                      v-model="formValues.courier"
                      @set-search="setSearch"
                      :couriers="couriers"
                    >
                      <el-input
                        v-model="search"
                        :placeholder="t('Driver Search (Name or Phone Number)')"
                      />
                    </ManuelAssign>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-form>
          </FormSectionCard>
        </div>
        <div ref="deliveryEl">
          <FormSectionCard>
            <template #title> {{ t("Delivery Options") }}</template>
            <el-form label-position="top" class="grid grid-cols-12 gap-4">

              <div class="col-span-6">
                <el-form-item :label="t('Integration Id')">
                  <el-input
                    v-model="formValues.integration_id"
                    :placeholder="t('Integration Id')"
                  />
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item :label="t('Action Type')">
                  <el-select
                    v-model="formValues.actionType"
                    :placeholder="t('Action Type')"
                    class="w-full-important"
                  >
                    <el-option
                      v-for="item in Options.actionType"
                      :key="item.value"
                      :label="t(item.label)"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="col-span-12 sm:col-span-6">
                <el-form-item :label="t('Start Date')" required>
                  <el-date-picker
                    v-model="formValues.startDate"
                    class="w-full-important"
                    type="datetime"
                    :placeholder="t('Start Date')"
                    format="DD.MM.YYYY HH:mm"
                    @change="changedStartDate"
                  />
                </el-form-item>
              </div>
              <div class="col-span-12 sm:col-span-6">
                <el-form-item :label="t('End Date')" required>
                  <el-date-picker
                    v-model="formValues.endDate"
                    class="w-full-important"
                    type="datetime"
                    :placeholder="t('End Date')"
                    format="DD.MM.YYYY HH:mm"
                  />
                </el-form-item>
              </div>
              <div v-if="formValues.assigment === 'slot'" class="col-span-12 sm:col-span-6">
                <el-form-item :label="t('Slot Start Date')" required>
                  <el-date-picker
                    v-model="formValues.slot_starts_at"
                    class="w-full-important"
                    type="datetime"
                    :placeholder="t('Start Date')"
                    format="DD.MM.YYYY HH:mm"
                    @change="changedSlotStartDate"
                  />
                </el-form-item>
              </div>
              <div v-if="formValues.assigment === 'slot'" class="col-span-12 sm:col-span-6">
                <el-form-item :label="t('Slot End Date')" required>
                  <el-date-picker
                    v-model="formValues.slot_ends_at"
                    class="w-full-important"
                    type="datetime"
                    :placeholder="t('End Date')"
                    format="DD.MM.YYYY HH:mm"
                  />
                </el-form-item>
              </div>
              <div v-if="formValues.assigment === 'slot'" class="col-span-12">
                <SlotInput
                  v-model="formValues.slot"
                  :startDate="formValues.slot_starts_at"
                  :endDate="formValues.slot_ends_at"
                  required
                />
              </div>
              <div class="col-span-2">
                <el-form-item :label="t('Priority')">
                  <el-input
                    v-model="formValues.priority"
                    :placeholder="t('Priority')"
                    @input="priorityRules"
                    type="number"
                  />
                </el-form-item>
              </div>
              <div class="col-span-2">
                <el-form-item :label="t('Quantity')">
                  <el-input
                    v-model="formValues.quantity"
                    :placeholder="t('Quantity')"
                    @input="qantityRules"
                    type="number"
                  />
                </el-form-item>
              </div>
              <div class="col-span-4">
                <el-form-item :label="t('Service Time')">
                  <el-input
                    v-model="formValues.service"
                    :placeholder="t('Service Time')"
                    @input="serviceRules"
                    type="number"
                  >
                    <template #suffix> {{ t("Min") }}</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="col-span-4">
                <el-form-item :label="t('Unit Capacity')">
                  <el-input
                    :step="1"
                    v-model="formValues.unit_quantity"
                    :placeholder="t('Unit Capacity')"
                    @input="checkInput"
                    type="number"
                  >

                  </el-input>
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item :label="t('Response Time')">
                  <el-input
                    v-model="formValues.response"
                    :placeholder="t('Response Time')"
                    type="number"
                  >
                    <template #suffix> {{ t("Min") }}</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="col-span-6">
                <el-form-item :label="t('Tracking Code')">
                  <el-input
                    v-model="formValues.tracking"
                    :placeholder="t('Tracking Code')"
                  />
                </el-form-item>
              </div>
              <div class="col-span-12">
                <el-form-item :label="t('Notes')">
                  <el-input
                    v-model="formValues.notes"
                    :placeholder="t('Notes')"
                  />
                </el-form-item>
              </div>
              <div class="col-span-12">
                <el-form-item :label="t('Recipient Notes')">
                  <el-input
                    v-model="formValues.recipient_notes"
                    :placeholder="t('Recipient Notes')"
                  />
                </el-form-item>
              </div>
              <div class="col-span-12">
                <div class="flex-col">
                  <div class="flex justify-between items-center">
                    <el-checkbox v-model="formValues.delivery_code" size="small">
                      <span class="text-sm font-bold text-slate-700">
                        {{ t("Delivery Code") }}
                      </span>
                    </el-checkbox>
                    <el-checkbox
                      v-if="formValues.delivery_code"
                      v-model="formValues.delivery_code_mandatory"
                      size="small"
                    >
                      <span class="text-xs font-bold text-slate-700">
                        {{ t("Mandatory") }}
                      </span>
                    </el-checkbox>
                  </div>
                  <div class="text-slate-400 text-xs mt-1">
                    {{
                      t("A code is sent to the recipient's phone and requested at the time of delivery.This requirement needs a mobile phone number.")
                    }}
                  </div>
                </div>

                <div class="flex-col mt-3">
                  <div class="flex justify-between items-center">
                    <el-checkbox v-model="formValues.proof_photo" size="small">
                      <span class="text-sm font-bold text-slate-700">
                        {{ t("Proof Of Delivery Photo") }}
                      </span>
                    </el-checkbox>
                    <el-checkbox
                      v-if="formValues.proof_photo"
                      v-model="formValues.proof_photo_mandatory"
                      size="small"
                    >
                      <span class="text-xs font-bold text-slate-700">
                        {{ t("Mandatory") }}
                      </span>
                    </el-checkbox>
                  </div>
                  <div class="text-slate-400 text-xs mt-1">
                    {{ t("Request a photo as proof of delivery(completed or failed) from the courier on duty.") }}
                  </div>
                </div>
              </div>
              <div class="col-span-3">
                <el-checkbox
                  v-model="formValues.dont_ring_the_bell"
                  size="small"
                >
                      <span class="text-xs font-bold text-slate-700">
                        {{ t("Don't Ring the Bell") }}
                      </span>
                </el-checkbox>
              </div>
              <div class="col-span-4">
                <el-checkbox
                  v-model="formValues.contactless"
                  size="small"
                >
                      <span class="text-xs font-bold text-slate-700">
                        {{ t("Contactless Delivery") }}
                      </span>
                </el-checkbox>
              </div>
              <div class="col-span-2">
                <el-checkbox
                  v-model="formValues.locked"
                  @change="changedLocked"
                  size="small"
                >
                      <span class="text-xs font-bold text-slate-700">
                       {{ t("Locked") }}
                      </span>
                </el-checkbox>
              </div>
              <div class="col-span-3">
                <el-checkbox
                  v-model="formValues.confirmed"
                  @change="changedConfirmed"
                  size="small"
                >
                      <span class="text-xs font-bold text-slate-700">
                        {{ t("Confirmed") }}
                      </span>
                </el-checkbox>
              </div>


              <div class="col-span-12">
                <el-form-item :label="t('Tags')">
                  <SelectBox
                    v-model="formValues.tags"
                    url="customer/tags"
                    value="name"
                    key="name"
                    label="name"
                    :multiple="true"
                    :filterable="true"
                    placeholder="Tags"
                  />
                </el-form-item>
              </div>
            </el-form>
          </FormSectionCard>
        </div>
      </div>
      <div
        class="flex justify-between items-center absolute bottom-0 z-20 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >
        <div class="flex item-center justify-center">
          <!--          <el-button @click="onCreate('draft')">{{ t('Save a Draft') }}</el-button>-->
          <el-checkbox v-if="!taskCountCheck" v-model="taskCountCheck">{{ t("Create another task") }}</el-checkbox>
          <el-checkbox v-if="taskCountCheck" v-model="taskCountCheck">{{ taskCount }}{{ t("task was created") }}
          </el-checkbox>
          <span class="ml-3 text-xxs">

          </span>

        </div>
        <div>
          <el-button @click="onCreate" type="primary" :disabled="disabled">
            <template #icon>
              <FontAwesomeIcon icon="check-square" />
            </template>
            {{ t("Create") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
