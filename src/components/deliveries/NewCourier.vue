<script setup>
import { computed, inject, onActivated, onMounted, reactive, ref, watch } from "vue";
import PhoneInput from "@/components/ui/PhoneInput.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { conversionFormData } from "@/class/helpers";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import WorkingHourPlanInput from "@/components/ui/WorkingHourPlanInput.vue";
import { useStore } from "vuex";

const toast = useToast();
const { t } = useI18n();

const api = inject("api");

const { dispatch, getters } = useStore();
const emit = defineEmits(["close"]);
const me = computed(() => getters["auth/me"]);

const loader = ref();
const teams = ref([]);
const tags = ref([]);
const courierCountCheck = ref(false);
const courierCount = ref(0);
const courierInvoiteCount = ref(0);
const invoiceVisible = ref(false);
const workingTemplates = ref([]);
const routingProfile = ref([]);
const courier_phone = reactive({
  country: "TR",
  phone: null
});
const formValues = reactive({
  tags: [],
  courierNameSurname: null,
  phone: null,
  email: null,
  driverLicense: null,
  validityDate: null,
  licenseType: null,
  vehicleType: "motorcycle",
  vehiclePlate: null,
  relatedTeam: [],
  courier_code: null,
  vehicleColor: null,
  vehicleDescription: null,
  unit_capacity: null,
  capacity: 1,
  pool_capacity: 1,
  working_hour_template_id: null,
  routing_profile_slug: null,
  allow_self_break: false,
});

const canCreate = () => {
  return new Promise((resolve, reject) => {
    if (!formValues.courierNameSurname) {
      return reject("Name is empty");
    } else if (!courier_phone.phone) {
      return reject("Phone is empty");
    } else if (!formValues.relatedTeam.length > 0) {
      return reject("Team is empty");
    } else if (formValues.capacity < 0 || formValues.pool_capacity < 0) {
      return reject("Capacity should be zero or greater");
    }
    return resolve();
  });
};


const getWorkingTemplates = () => {
  loader.value.show();
  return api("customer/working-hour-templates?filter[type]=courier")
    .then((res) => {
      workingTemplates.value = res.data;

      let defaultTemplate = res.data.find((x) => x.is_default);
      if (defaultTemplate) {
        formValues.working_hour_template_id = defaultTemplate.id;
      } else if (res.data.length > 0) {
        formValues.working_hour_template_id = res.data[0].id;
      }

    }).finally(() => loader.value.hide());
};

const onSubmit = (type) => {
  canCreate()
    .then(() => createOrInviteCourier(type))
    .catch((err) => {
      const errorMessage = typeof err === 'string' ? err : (err.message || 'An unexpected error occurred');
      toast.error(t(errorMessage));
    });
};

const inviteCourier = (body) => {
  if (!courierCountCheck.value) {
    api.post("customer/courier-invitations", body)
      .then(() => {
        emit("close", true);
        toast.success(`${body.name} Adlı Sürücü davet edildi.`);
      })
      .catch((err) => {
        const errorMessage = typeof err.message === 'string' ? err.message : 'An unexpected error occurred';
        toast.error(t(errorMessage));
      })
      .finally(() => {
        loader.value.hide();
        courierInvoiteCount.value = 0;
      });
  } else {
    api.post("customer/courier-invitations", body)
      .then(() => {
        courierInvoiteCount.value = courierInvoiteCount.value + 1;
        invoiceVisible.value = true;
        toast.success(`${body.name} Adlı Sürücü davet edildi.`);
      })
      .catch((err) => {
        const errorMessage = typeof err.message === 'string' ? err.message : 'An unexpected error occurred';
        toast.error(t(errorMessage));
      })
      .finally(() => loader.value.hide());
  }
};

const createCourier = (body, type) => {
  if (!courierCountCheck.value) {
    api.post(`customer/couriers`, conversionFormData(body))
      .then((r) => {

        emit("close", true);
        toast.success(`${r.name} adlı sürücü başarıyla oluşturuldu.`);

      })
      .catch((err) => toast.error(err.message))
      .finally(() => {
        loader.value.hide();
        courierCount.value = 0;
        // courierInvoiteCount.value = 0
      });
  } else {
    api.post(`customer/couriers`, conversionFormData(body))
      .then((r) => {

        courierCount.value = courierCount.value + 1;
        toast.success(`${r.name} adlı sürücü başarıyla oluşturuldu.`);
      })
      .catch((err) => toast.error(err.message))
      .finally(() => loader.value.hide());
  }

};


const createOrInviteCourier = (type) => {
  try {
    loader.value.show();
    let body = {
      allow_self_break: formValues.allow_self_break ? 1 : 0
    };

    if (formValues.courierNameSurname) body.name = formValues.courierNameSurname;
    if (formValues.email) body.email = formValues.email;
    if (courier_phone.country) body.phone_country = courier_phone.country;
    if (courier_phone.phone) body.phone = courier_phone.phone;
    if (formValues.courier_code) body.code = formValues.courier_code;
    if (formValues.vehicleType) body.vehicle_type_slug = formValues.vehicleType;
    if (formValues.vehicleDescription) body.vehicle_description = formValues.vehicleDescription;
    if (formValues.vehiclePlate) body.vehicle_license_plate = formValues.vehiclePlate;
    if (formValues.vehicleColor) body.vehicle_color = formValues.vehicleColor;
    if (formValues.capacity) body.capacity = formValues.capacity;
    if (formValues.pool_capacity) body.pool_capacity = formValues.pool_capacity;
    if (formValues.relatedTeam) body.team_ids = formValues.relatedTeam;
    if (formValues.tags) body.tags = formValues.tags;
    if (formValues.working_hour_template_id) body.working_hour_template_id = formValues.working_hour_template_id;
    if (formValues.unit_capacity) body.unit_capacity = formValues.unit_capacity;
    if (formValues.routing_profile_slug) body.routing_profile_slug = formValues.routing_profile_slug;

    type === "invite" ? inviteCourier(body, type) : createCourier(body, type);

  } catch (err) {
    loader.value.hide();
  }
};
const getRoutingProfile = () => {
  loader.value.show();
  return api.post("components/routing-profile-select")
    .then((res) => {
      routingProfile.value = res.data.filter(x => x.country === me.value.active_company.country_code).filter(x => x.vehicle_types.includes(formValues.vehicleType));

    }).finally(() => loader.value.hide());
};

onMounted(() => {
  getWorkingTemplates();
  getRoutingProfile();
});
onActivated(() => {
  getWorkingTemplates();
  getRoutingProfile();
});

const unit_capacityRules = (event) => {
  formValues.unit_capacity = formValues.unit_capacity.replace(/[^0-9]/g, "");
};

const capacityRules = (event) => {
  formValues.capacity = formValues.capacity.replace(/[^0-9]/g, "");
};

const poolRules = (event) => {
  formValues.pool_capacity = formValues.pool_capacity.replace(/[^0-9]/g, "");
};

watch(() => formValues.vehicleType, () => {
  getRoutingProfile();
});
</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex p-2">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16">
        <el-form label-position="top" class="grid grid-cols-12 gap-4">
          <div class="col-span-12">
            <el-form-item :label="t('Driver Name')" required>
              <el-input
                v-model="formValues.courierNameSurname"
                :placeholder="t('Driver Name')"
              />
            </el-form-item>
          </div>
          <div class="col-span-12">
            <PhoneInput v-model="courier_phone" />
          </div>
          <div class="col-span-12">
            <el-form-item label="Email">
              <el-input
                v-model="formValues.email"
                placeholder="Email"
              ></el-input>
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="$t('Code')">
              <el-input
                v-model="formValues.courier_code"
                :placeholder="$t('Code')"
              />
            </el-form-item>
          </div>
          <div class="col-span-12">
            <WorkingHourPlanInput
              :templates="workingTemplates"
              v-model="formValues.working_hour_template_id"
            />
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('Vehicle Type')">
              <SelectBox
                v-model="formValues.vehicleType"
                url="components/vehicle-type-select"
                method="post"
                placeholder="Vehicle Type"
                key="slug"
                label="name"
                value="slug"
              />
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('Routing Profile')">
              <el-select class="custom-routing-select" :placeholder="t('Routing Profile')" v-model="formValues.routing_profile_slug">
                <el-option
                  class="custom-routing-select"
                  v-for="item in routingProfile"
                  :key="item.profile"
                  :label="item.name"
                  :value="item.profile"
                >

                    <div >
                      <div  class="font-bold w-full">
                        {{item.name}}
                      </div>
                      <div class="text-xs">
                        {{ item.description }}
                      </div>

                    </div>

                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('License Plate')">
              <el-input
                v-model="formValues.vehiclePlate"
                :placeholder="t('License Plate')"
              />
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('Vehicle Color')">
              <el-input
                v-model="formValues.vehicleColor"
                :placeholder="t('Vehicle Color')"
              />
            </el-form-item>
          </div>
          <div class="col-span-12">
            <el-form-item :label="t('Vehicle Description')">
              <el-input
                v-model="formValues.vehicleDescription"
                :placeholder="t('Vehicle Description')"
              />
            </el-form-item>
          </div>
          <div class="col-span-12 flex grid grid-cols-3 gap-4">
            <el-form-item :label="t('Number Of Tasks')">
              <el-input
                v-model="formValues.capacity"
                :placeholder="t('Number Of Tasks')"
                @input="capacityRules"
                type="number"
              />
            </el-form-item>
            <el-form-item :label="t('Pool Capacity')">
              <el-input
                v-model="formValues.pool_capacity"
                :placeholder="t('Pool Capacity')"
                @input="poolRules"
                type="number"
              />
            </el-form-item>
            <el-form-item :label="t('Unit Capacity')">
              <el-input
                v-model="formValues.unit_capacity"
                @input="unit_capacityRules"
                :placeholder="t('Unit Capacity')"
                type="number"
              />
            </el-form-item>
          </div>

          <div class="col-span-12">
            <el-form-item :label="t('Related Team')" required>
              <SelectBox
                v-model="formValues.relatedTeam"
                url="customer/teams"
                :multiple="true"
                :filterable="true"
                placeholder="Related Team"
                key="id"
                label="name"
                value="id"
              />
            </el-form-item>

          </div>

          <div class="col-span-12">
            <el-form-item :label="t('Tags')">
              <SelectBox
                v-model="formValues.tags"
                url="customer/tags"
                value="name"
                key="name"
                label="name"
                :multiple="true"
                :filterable="true"
                placeholder="Tags"
              />
            </el-form-item>
          </div>
          <div class="col-span-12 flex items-center justify-between">
            <div class="text-sm text-slate-700 font-medium">{{ $t("Unapproved Break") }}</div>
            <div>
              <el-switch v-model="formValues.allow_self_break"/>
            </div>
          </div>
        </el-form>
      </div>
      <div
        class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0 0 5px #00000029"
      >

        <div class="flex item-center justify-center">
          <el-checkbox v-if="!courierCountCheck" v-model="courierCountCheck">{{ t("Invite / Create another driver") }}
          </el-checkbox>
          <el-checkbox v-if="courierCountCheck" v-model="courierCountCheck">{{ courierCount }} {{ t("was created")
            }}<span
              v-if="courierInvoiteCount">, {{ courierInvoiteCount }}{{ t("was invited") }}</span></el-checkbox>
          <span class="ml-3 text-xxs">

          </span>
        </div>
        <div>
          <el-button @click="onSubmit('invite')">
            <template #icon>
              <FontAwesomeIcon icon="envelope" />
            </template>
            {{ t("Invite") }}

          </el-button>
          <el-button @click="onSubmit('create')" type="primary">
            <template #icon>
              <FontAwesomeIcon icon="check-square" />
            </template>
            {{ t("Create") }}
          </el-button>

        </div>
      </div>
    </div>
  </div>
</template>
<style >
.el-select-dropdown__item{

}
</style>
