<template>
  <div
    :class="[
      active
        ? 'bg-white border-slate-300'
        : 'bg-transparent border-transparent hover:bg-slate-100',
    ]"
    class="cursor-pointer border-t border-b py-1 px-2 font-bold text-base"
  >
    <div class="flex justify-between">
      <div><FontAwesomeIcon v-if="active" icon="angle-double-right" /></div>
      <div><slot></slot></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NavItem",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  setup() {

  },
};
</script>
