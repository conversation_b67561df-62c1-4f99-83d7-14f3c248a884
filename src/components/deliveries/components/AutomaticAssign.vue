<script setup>
import {reactive} from "vue";
import {Options} from "@/class";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {type: [Number, String, Array, Date], default: ""},
});
let defaultList = Options.assignment_options
const list = reactive([...defaultList]);
const handleChange = (option) => {
  list.map((x) => {
    if (x.id === option.id) {
      x.selected = true;
      emit("update:modelValue", x.slug);
    } else {
      x.selected = false;
    }
  });
};
</script>

<template>
  <div v-for="option in list" v-show="option.id !== 5">
    <div
        @click="handleChange(option)"
        key="option.id"
        :class="[
        'flex w-full grid grid-cols-9 items-center cursor-pointer ',
        option.selected ? 'bg-indigo-600' : ' border border-slate-300',
      ]"
    >
      <div class="col-span-1 flex justify-center items-center">
        <FontAwesomeIcon
            icon="check-circle"
            :style="[
            option.selected
              ? 'color:white; width:24px; height:24px;'
              : 'color:rgb(51, 65, 85,0.7); width:24px; height:24px;',
          ]"
        />
      </div>
      <div class="col-span-8 flex-col py-4 pr-4">
        <div
            :class="[
            'text font-bold',
            option.selected ? 'text-slate-50' : 'text-slate-700',
          ]"
        >
          {{ $t(option.title) }}
        </div>
        <div
            :class="[
            'text-xxs font-medium text-opacity-70 mt-2',
            option.selected ? 'text-slate-50' : 'text-slate-700',
          ]"
        >
          {{ $t(option.description) }}
        </div>
      </div>
    </div>
  </div>
</template>
