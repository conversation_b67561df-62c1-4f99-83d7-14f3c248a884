<script setup>

import {ref, inject} from 'vue';

const api = inject("api")

const emit = defineEmits(["addProductToCart"])

const product = ref()
const products = ref([])
const loading = ref(false)

const searchProduct = (query) => {
  loading.value = true
  api(`customer/company-products?filter[search]=${query}`)
      .then((res) => {
        products.value = res.data
      })
      .finally(() => loading.value = false)
}

const addProduct = (param) => {
  let productToBeAdd = products.value.find(x => x.id === param)
  emit("addProductToCart", {...productToBeAdd, unitPrice: productToBeAdd.amount})
  product.value = null
}


</script>

<template>
  <el-form-item
      :label="$t('Search Product')"
  >
    <el-select
        class="w-full-important"
        :placeholder="$t('Search Product')"
        v-model="product"
        clearable
        filterable
        remote
        reserve-keyword
        :remote-method="searchProduct"
        :loading="loading"
        @change="addProduct"
    >
      <el-option
          v-for="item in products"
          :key="item.id"
          :label="item.name"
          :value="item.id"
      >
      </el-option>
    </el-select>
  </el-form-item>
</template>
