<script setup>
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const emit = defineEmits(["removeProductFromCart"])
const props = defineProps({
  modelValue: {type: Array, default: () => []}
})

const onRemove = (id) => {
  emit("removeProductFromCart", id)
}

</script>

<template>
  <div class="w-full grid grid-cols-12 gap-2 text-slate-700">
    <div class="col-span-8 font-bold ">
      {{ $t('Order Information') }}
    </div>
    <div class="col-span-4 text-right text-sm">

    </div>
    <div class="col-span-12 h-px bg-slate-300"/>
    <template v-for="product in modelValue">
      <div class="col-span-9 center-text-left pb-1">
        <div
            @click="onRemove(product.id)"
            class="text-red-500 hover:text-red-700 cursor-pointer mr-2">
          <FontAwesomeIcon icon="circle-minus"/>
        </div>
        <div>
          {{ product.name }}
        </div>
      </div>
      <div class="col-span-3 center font-bold">
        <el-input
            v-model="product.unitPrice"
            type="number"
            :placeholder="$t('Price')"
            size="small"
        >
          <template #suffix>
            <div class="center border-l border-slate-300 pl-2 font-bold text-slate-700">
              <FontAwesomeIcon icon="turkish-lira-sign"/>
            </div>
          </template>
        </el-input>

      </div>
      <div class="col-span-8">
        <el-input
            v-model="product.description"
            :placeholder="$t('Description')"
            size="small"
        />
      </div>
      <div class="col-span-4 text-right">
        <el-input-number
            v-model="product.quantity"
            :min="1"
            size="small"
            :placeholder="$t('Quantity')"
            controls-position="right"
        />
      </div>
    </template>
  </div>
</template>

<style scoped>

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-text-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

</style>

