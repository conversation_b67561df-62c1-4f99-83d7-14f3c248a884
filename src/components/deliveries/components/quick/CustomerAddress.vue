<script setup>
import {inject, nextTick, onMounted, ref} from "vue";
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import {Loader} from "@googlemaps/js-api-loader";
import PhoneInput from "@/components/ui/PhoneInput.vue";
import DefaultMapStyle from "@/map-styles/default.json";

const api = inject("api")

const props = defineProps({
  modelValue: {type: Object}
});

const showAddressDetail = ref(true)
const searchCustomerPhoneVisible = ref(true)
const loading = ref(false)
const customers = ref([])
const customer = ref()
const pinmap = ref()
const map = ref()
const marker = ref()


onMounted(() => {
  nextTick(() => {
    mapPromise()
  });
});

const mapPromise = async () => {

  const {Autocomplete} = await google.maps.importLibrary("places");

  const address = new Autocomplete(
      document.getElementById("address_search")
  );
  address.addListener("place_changed", () => {
    const place = address.getPlace();
    if (!place.geometry || !place.geometry.location) {
      return;
    }
    const location = place.geometry.location;
    changeAddress(location.lat(), location.lng())
  });

  return new Promise((resolve) => {
    map.value = new google.maps.Map(pinmap.value, {
      center: {
        lat: props.modelValue.lat ? props.modelValue.lat : 41.013028158816425,
        lng: props.modelValue.lng ? props.modelValue.lng : 28.99737372063146,
      },
      zoom: 14,
      mapTypeControl: false,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery"],
      },
    });
    map.value.mapTypes.set(
        "qdelivery",
        new google.maps.StyledMapType(DefaultMapStyle, {name: "QDelivery"})
    );
    map.value.setMapTypeId("qdelivery");

    marker.value = new google.maps.Marker({
      map: map.value,
      draggable: true,
      position: props.modelValue.lat
          ? {lat: Number(props.modelValue.lat), lng: Number(props.modelValue.lng)}
          : {lat: 41.013028158816425, lng: 28.99737372063146},
    });

    map.value.addListener("click", (mapsMouseEvent) => {
      const {lat, lng} = mapsMouseEvent.latLng.toJSON()
      changeAddress(lat, lng)
    });

    marker.value.addListener("dragend", (markerDragEvent) => {
      const {lat, lng} = markerDragEvent.latLng.toJSON()
      changeAddress(lat, lng)
    });


    google.maps.event.addListenerOnce(map, "tilesloaded", function () {
      resolve(map);
    });
  });
}

const changeAddress = (lat, lng) => {
  resolveAddressFromCoordinates(lat, lng).then(
      (address) => {
        props.modelValue.lat = lat
        props.modelValue.lng = lng
        props.modelValue.address = address
        marker.value.setPosition({
          lat,
          lng,
        });
        map.value?.panTo(marker.value.getPosition())
      }
  );
}

const resolveAddressFromCoordinates = (lat, lng) => {
  lat = parseFloat(lat.toFixed(8));
  lng = parseFloat(lng.toFixed(8));

  return new Promise((resolve) => {

    new google.maps.Geocoder().geocode(
        {location: {lat, lng}},
        (results, status) => {
          if (status === "OK") {
            if (results[0]) {
              resolve(results[0].formatted_address);
            }
          }
        }
    );
  });
}

const toggleSearchCustomerPhone = () => {
  searchCustomerPhoneVisible.value = !searchCustomerPhoneVisible.value
}

const toggleAddressDetail = () => {
  showAddressDetail.value = !showAddressDetail.value
}

const searchCustomer = (query) => {
  loading.value = true
  api(`customer/company-customers?filter[search]=${query}`)
      .then((res) => customers.value = res.data)
      .finally(() => loading.value = false)
}

const onChangeCustomer = (param) => {
  let data = customers.value.find(x => x.phone === param)
  props.modelValue.name = data.name
  props.modelValue.address = data.address
  props.modelValue.address_floor = data.address_floor
  props.modelValue.address_building = data.address_building
  props.modelValue.address_apartment = data.address_apartment
  props.modelValue.lat = data.lat
  props.modelValue.lng = data.lng
  props.modelValue.country = data.phone_country

  if (marker.value) {
    marker.value.setPosition({
      lat: Number(data.lat),
      lng: Number(data.lng),
    });
    map.value?.panTo(marker.value.getPosition())
  }
}

</script>

<template>
  <div class="grid grid-cols-12 gap-4 ">
    <div class="col-span-12">
      <el-form-item
          label="Müşteri Bilgileri"
      >
        <div class="w-full h-full flex items-center justify-between">
          <el-select
              v-if="searchCustomerPhoneVisible"
              class="w-full-important"
              :placeholder="$t('Customer search')"
              v-model="modelValue.phone"
              clearable
              filterable
              remote
              reserve-keyword
              :remote-method="searchCustomer"
              :loading="loading"
              @change="onChangeCustomer"
          >
            <el-option
                v-for="item in customers"
                :key="item.phone"
                :label="item.phone"
                :value="item.phone"
            >
              <template #default>
                <div class="flex items-center justify-between">
                  <div>
                    {{ item.name }}
                  </div>
                  <div>
                    {{ item.phone }}
                  </div>
                </div>
              </template>
            </el-option>
          </el-select>
          <div v-if="!searchCustomerPhoneVisible" class="w-full">
            <PhoneInput
                :modelValue="modelValue"
                @update:modelValue="modelValue = $event"
                :visibleLabel="false"
            />
          </div>
          <div
              @click="toggleSearchCustomerPhone"
              class="w-10 ml-2 h-8 flex border-slate-300 rounded bg-white border items-center justify-center cursor-pointer"
              :class="[searchCustomerPhoneVisible ? 'text-indigo-600' : 'text-slate-300' ]"
          >
            <FontAwesomeIcon icon="user-magnifying-glass"/>
          </div>
        </div>
      </el-form-item>
    </div>
    <div class="col-span-12 flex items-center justify-between">
      <el-input
          :placeholder="$t('Customer Name')"
          v-model="modelValue.name"
      />
      <div
          @click="toggleAddressDetail"
          class="w-10 ml-2 h-8 flex items-center justify-center cursor-pointer font-bold text-slate-700">
        <FontAwesomeIcon :icon="showAddressDetail ? 'chevron-up' : 'chevron-down'"/>
      </div>
    </div>
    <template v-if="showAddressDetail">
      <div v-show="searchCustomerPhoneVisible" class="col-span-12 flex items-center justify-between">
        <el-input
            :placeholder="$t('Address')"
            v-model="modelValue.address"
        />
      </div>
      <div v-show="!searchCustomerPhoneVisible" class="col-span-12">
        <div class="flex flex-col h-60 w-full">
          <div class="flex items-center justify-between">
            <el-input
                id="address_search"
                :placeholder="$t('Search Address')"
                v-model="modelValue.address"
            >
              <template #prefix>
                      <span>
                        <FontAwesomeIcon icon="search"/>
                      </span>
              </template>
            </el-input>
          </div>
          <div ref="pinmap" class="h-full w-full"/>
        </div>
      </div>
      <div class="col-span-4">
        <el-input
            :placeholder="$t('Building')"
            v-model="modelValue.address_building"
        />
      </div>
      <div class="col-span-4">
        <el-input
            :placeholder="$t('Apartment')"
            v-model="modelValue.address_apartment"
        />
      </div>
      <div class="col-span-4">
        <el-input
            :placeholder="$t('Floor')"
            v-model="modelValue.address_floor"
        />
      </div>
    </template>
    <div class="col-span-12">
      <el-input
          :placeholder="$t('Address Description')"
          v-model="modelValue.address_description"
      />
    </div>
  </div>
</template>
