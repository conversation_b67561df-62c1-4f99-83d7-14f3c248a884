<script setup>
import { reactive, inject, ref ,defineProps,watch} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";

const toast = useToast();
const { t } = useI18n();

const api = inject("api");

const emit = defineEmits(["close"]);
const visible = ref(false);

const loader = ref();

const props = defineProps({
  courier: {
    type: Object, default: () => {
    }
  }
});


const disablePastDates = (time) => {
  return time < new Date(new Date().setHours(0, 0, 0, 0));
};

const formValues = reactive({
  startDate: null,
  breakDuration: null,
  bufferDuration: null
});

watch(visible, () => {
  if (!visible.value) {
    formValues.startDate = null;
  }
})

const onSubmit = () => {
  loader.value.show()
  let body = {};

  if (formValues.startDate !== null && formValues.startDate !== undefined && formValues.startDate !== '') {
    body.starts_at = formValues.startDate;
  }

  if (formValues.breakDuration !== null && formValues.breakDuration !== undefined && formValues.breakDuration !== '') {
    body.break_duration = formValues.breakDuration;
  }

  if (formValues.bufferDuration !== null && formValues.bufferDuration !== undefined && formValues.bufferDuration !== '') {
    body.buffer_duration = formValues.bufferDuration;
  }

  api.post(`customer/couriers/${props.courier.id}/break`, body)
    .then(() => {
      toast.success(t("Break Created Successfully"));
      emit("close", true)
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => {
      loader.value.hide()
    })
};

</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
            label-position="top"
            class="grid grid-cols-12 gap-4"
          >

            <div class="col-span-6">
              <el-form-item :label="t('Break Duration')">
                <el-input
                  v-model="formValues.breakDuration"
                  :placeholder="t('Break Duration')"
                  type="number"
                >
                  <template #suffix> {{ t("Min") }}</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-6">
              <el-form-item :label="t('Buffer Duration')">
                <el-input
                  v-model="formValues.bufferDuration"
                  :placeholder="t('Buffer Duration')"
                  type="number"
                >
                  <template #suffix> {{ t("Min") }}</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t('Future Break') }}</div>
              <div>
                <el-switch v-model="visible"/>
              </div>
            </div>
            <div v-if="visible" class="col-span-12">
              <el-form-item :label="t('Start Date')" required>
                <el-date-picker
                  v-model="formValues.startDate"
                  class="w-full-important"
                  type="datetime"
                  :placeholder="t('Start Date')"
                  format="DD.MM.YYYY HH:mm"
                  :disabled-date="disablePastDates"
                />
              </el-form-item>
            </div>

          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0px 0px 5px #00000029"
      >
        <el-button @click="onSubmit" type="primary">
          <FontAwesomeIcon class="mr-2" icon="check-square" />
          {{ t("Create") }}
        </el-button>
      </div>
    </div>
  </div>
</template>
