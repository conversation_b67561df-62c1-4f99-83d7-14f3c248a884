<script setup>
import { ref, computed } from "vue";
import AvatarImg from "@/assets/images/avatar.png";
import { useStore } from "vuex";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  couriers:{type:Array, default:()=>[]},
  modelValue: { type: [Number, String, Array, Date, Object], default: "" },
  secondary:{type:Boolean, default:false}
});
const assignModel = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});
const { dispatch, getters } = useStore();
const active_company = computed(()=>getters["auth/me"].active_company)


</script>

<template>
  <slot></slot>
  <div class="flex w-full h-10 bg-white border-l border-r border-slate-300">
    <div class="group flex justify-between w-full items-center p-2">
      <div class="flex text-slate-700 opacity-60">{{ $t('Driver') }}</div>
      <div class="flex justify-between items-center space-x-20">
        <div class="text-slate-700 opacity-60 cursor-pointer">
          {{ $t('Origin Distance') }}
        </div>
        <div class="text-slate-700 opacity-60 cursor-pointer">{{$t('Capacity')}}</div>
      </div>
    </div>
  </div>
  <div class="w-full border border-slate-300"
      :class="[
          secondary ? 'h-full' : 'h-96'
      ]"
  >
    <div class="w-full h-full overflow-y-auto">
      <el-radio-group
        class="w-full divide-y divide-slate-300"
        v-model="assignModel"
      >
        <div
          v-for="i in couriers"
          :key="i.id"
          class="group flex items-center h-14 bg-white w-full"
        >
          <div
            class="group flex justify-between w-full items-center p-2 manuelAssignCourierItem"
          >
            <div class="flex items-center">
              <div class="flex items-center">
                <el-radio :label="i" name=""></el-radio>
                <img
                  class="rounded-full z-0 h-10 w-10 ml-1"
                  :src="AvatarImg"
                />
              </div>
              <div class="flex-col items-center ml-4">
                <p
                  class="truncate flex items-start space-x-1 text-sm font-bold text-slate-700"
                >
                  <span>{{ i.name }}</span>
                </p>
                <p
                  class="text-xxxs font-medium text-gray-500"
                >
                  <span>{{ i.phone }}</span>
                </p>
              </div>
            </div>
            <div class="flex items-center">
              <div class="flex text-sm font-medium text-gray-500">
                {{ parseDistance(i.distance, active_company.unit_slug) }}
              </div>
            </div>
            <div class="flex items-center">
              <div class="flex text-sm font-medium text-gray-500">
                {{ i.current_tasks_of_active_company_count }}/{{ i.courier_detail.capacity }}
              </div>
            </div>
          </div>
        </div>
      </el-radio-group>
    </div>
  </div>
</template>
