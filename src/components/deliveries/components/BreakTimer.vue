<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useI18n } from "vue-i18n";
import dayjs from 'dayjs';

const { t } = useI18n();
const props = defineProps({
  breakInfo: {
    type: Object,
    required: true
  }
});

const remainingTime = ref('');
const isExpired = ref(false);
const timer = ref(null);
const timePercentage = ref(0);

const calculateDurationInMinutes = () => {
  const startTime = dayjs(props.breakInfo.break_buffer_started_at);
  const endTime = dayjs(props.breakInfo.ended_at);
  const durationInMinutes = endTime.diff(startTime, 'minute');
  return durationInMinutes;
};

const calculateRemainingTime = () => {
  const now = dayjs();
  const endTime = dayjs(props.breakInfo.ended_at);

  // Eğer bitiş zamanı geçmişse
  if (now.isAfter(endTime)) {
    remainingTime.value = t('Break time has ended');
    isExpired.value = true;
    timePercentage.value = 100;
    if (timer.value) {
      clearInterval(timer.value);
    }
    return;
  }

  // Kalan süreyi hesapla
  const diff = endTime.diff(now);
  const minutes = Math.floor(diff / 60000);
  const seconds = Math.floor((diff % 60000) / 1000);

  remainingTime.value = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

  // İlerleme yüzdesini hesapla
  const startTime = dayjs(props.breakInfo.break_buffer_started_at);
  const totalDuration = endTime.diff(startTime);
  const elapsedDuration = now.diff(startTime);

  // Eğer başlangıç zamanı gelecekteyse
  if (elapsedDuration <= 0) {
    timePercentage.value = 0;
  } else {

    if (now.isAfter(endTime)) {
      timePercentage.value = 100;
    } else {
      timePercentage.value = Math.min(Math.max((elapsedDuration / totalDuration) * 100, 0), 100);
    }
  }
};

onMounted(() => {
  if (props.breakInfo) {
    calculateRemainingTime();
    timer.value = setInterval(calculateRemainingTime, 1000);
  }
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>
<template>
  <div class="bg-slate-100 border border-slate-200 rounded-lg p-4 shadow-sm">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center">
        <div>
          <h3 class="text-lg font-medium text-gray-800">{{ t('Break Information') }}</h3>
          <p class="text-sm text-gray-600 mt-4">
            {{ t('Total Break Duration') }}: {{ calculateDurationInMinutes() }} {{ t('Min') }} - Başlangış saati: {{dayjs(props.breakInfo.break_buffer_started_at).format("HH:mm")}}
          </p>
        </div>
      </div>

    </div>
<div class="flex items-center justify-center">
  <div class="w-full flex  bg-gray-200 rounded-full h-2.5 mb-1">
    <div class="bg-blue-600 h-2.5 rounded-full " :style="{ width: timePercentage + '%' }">     </div>

  </div>


  <div :class="['text-lg font-bold ml-2',
              isExpired ? 'text-red-500' :
              timePercentage === 0 ? 'text-gray-500' : 'text-blue-700']">
    {{ remainingTime }}
  </div>
</div>


    <div class=" text-sm text-gray-700 ">
      <span v-if="isExpired" class="text-red-600 font-medium">
        {{ t('Break time has ended') }}!
      </span>
    </div>
  </div>
</template>

