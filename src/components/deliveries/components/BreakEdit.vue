<script setup>
import { reactive, inject, ref, defineProps, onMounted, watch } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import BreakTimer from "./BreakTimer.vue"; // BreakTimer bileşenini import edin

const toast = useToast();
const { t } = useI18n();

const api = inject("api");

const emit = defineEmits(["close"]);

const loader = ref();

const breakInfo = ref(null);

const props = defineProps({
  courier: {
    type: Object, default: () => {
    },
    CourierBreakInfo:{
      type: Object, default: () => {
      }
    }
  }
});

const disablePastDates = (time) => {
  return time < new Date(new Date().setHours(0, 0, 0, 0));
};

const formValues = reactive({
  startDate: null,
  breakDuration: null,
  bufferDuration: null
});


onMounted(() => {
  getCourierBreakInfo();
  console.log('1')
});

const getCourierBreakInfo = () => {
  api(`customer/couriers/${props.courier.id}/break`)
    .then((r) => {
      breakInfo.value = r;
    });
};

const onSubmit = () => {
  loader.value.show();

  let body = {};

  if (formValues.startDate !== null && formValues.startDate !== undefined) {
    body.started_at = formValues.startDate;
  }

  if (formValues.breakDuration !== null && formValues.breakDuration !== undefined) {
    body.break_duration = formValues.breakDuration;
  }

  if (formValues.bufferDuration && formValues.bufferDuration > 0) {
    body.buffer_duration = formValues.bufferDuration;
  }

  api.patch(`customer/couriers/${props.courier.id}/break`, body)
    .then(() => {
      toast.success(t("Break Edited Successfully"));
      emit("close", true);
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => {
      loader.value.hide();
    });
};

</script>

<template>
  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form
            label-position="top"
            class="grid grid-cols-12 gap-4"
          >
            <div class="col-span-12">
              <!-- Mola bilgisi ve timer bileşeni burada render edilecek -->
              <BreakTimer v-if="breakInfo" :breakInfo="breakInfo" />
              <div v-else class="text-center py-4 text-gray-500">
                {{ t('No active break information') }}
              </div>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Start Date')" required>
                <el-date-picker
                  v-model="formValues.startDate"
                  class="w-full-important"
                  type="datetime"
                  :placeholder="t('Start Date')"
                  format="DD.MM.YYYY HH:mm"
                  :disabled-date="disablePastDates"
                />
              </el-form-item>
            </div>
            <div class="col-span-6">
              <el-form-item :label="t('Break Duration')">
                <el-input
                  v-model="formValues.breakDuration"
                  :placeholder="t('Break Duration')"
                  type="number"
                >
                  <template #suffix> {{ t("Min") }}</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="col-span-6">
              <el-form-item :label="t('Buffer Duration')">
                <el-input
                  v-model="formValues.bufferDuration"
                  :placeholder="t('Buffer Duration')"
                  type="number"
                >
                  <template #suffix> {{ t("Min") }}</template>
                </el-input>
              </el-form-item>
            </div>


          </el-form>
        </div>
      </div>
      <div
        class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
        style="box-shadow: 0px 0px 5px #00000029"
      >
        <el-button @click="onSubmit" type="primary">
          <FontAwesomeIcon icon="check-square" class="mr-1" />
          {{ t("Edit") }}
        </el-button>
      </div>
    </div>
  </div>
</template>