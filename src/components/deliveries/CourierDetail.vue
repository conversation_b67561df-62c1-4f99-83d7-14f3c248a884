<script setup>
import dayjs from "dayjs";
import AvatargImg from "@/assets/images/avatar.png";
import { inject, ref } from "vue";
import { Chart, registerables } from "chart.js";
import EmbedMap from "@/components/ui/EmbedMap.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";
import * as err from "autoprefixer";
import { useToast } from "vue-toastification";

Chart.register(...registerables);

const emitter = inject("emitter");
const api = inject("api");

const props = defineProps({
  selectedId: { type: Number },
  courier: { type: Object }
});
const emit = defineEmits(["showEdit", "close", "showDetail"]);

const loader = ref(null);
const lastFeedBack = ref([]);
const update = ref(false);
const toast = useToast();

const editClick = () => {
  emit("showEdit");
};

const courierSupportTeamJson = props.courier.support_teams.map((r) => r.name);
const courierSupportTeam = courierSupportTeamJson.join(",");


const getFeedBacks = () => {
  return new Promise((resolve, reject) => {
    api.get(`customer/couriers/feedbacks?filter[courier_id]=${props.courier.id}&per_page=1&[has_comment]=1`)
      .then((r) => {
        lastFeedBack.value = r.data;
        resolve();
      })
      .catch(() => reject());

  });
};



// const getChartData = () => {
//   return new Promise((resolve, reject) => {
//     try {
//       let date = new Date()
//       date.setDate(date.getDate() - 7);
//       let labels = []
//       let nums = []
//       for (let i = 0; i < 7; i++) {
//         let _date = date.setDate(date.getDate() + 1);
//         api(`customer/report/task/count?filter[date]=${dateRangeFilter(_date)}&filter[courier_id]=${props.courier.id}`)
//             .then((r) => {
//               labels.push(dayjs(_date).format("DD.MM.YYYY"))
//               nums.push(r.count)
//               if (labels.length === 7 && nums.length === 7) {
//                 let canvas = document.getElementById("courier-weekly-stats");
//                 new Chart(canvas, {
//                   type: "bar",
//                   data: {
//                     labels: labels,
//                     datasets: [
//                       {
//                         data: nums,
//                         backgroundColor: "#4F46E6",
//                       },
//                     ],
//                   },
//                   options: {
//                     responsive: true,
//                     plugins: {
//                       legend: {
//                         display: false,
//                       },
//                     },
//                     scales: {
//                       y: {
//                         beginAtZero: true,
//                       },
//                     },
//                   },
//                 });
//                 resolve()
//               }
//             })
//       }
//     } catch {
//       reject()
//     }
//
//   })
// }

const workingOnOff = () => {
  loader.value.show();
  api.post(`customer/couriers/${props.courier.id}/shifts:toggle`)
    .then(() => {
      {
        emit("close", true);
        emitter.emit("fetch_couriers");
      }
    })
    .catch((err) => {
      toast.error(err.message);
    })
    .finally(() => {
      loader.value.hide();
    });
};

</script>

<template>

  <LoadingBlock ref="loader" />
  <div class="w-full h-full flex select-text">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto">
        <div class="h-24 w-full flex justify-between items-center px-5">
          <div class="h-full flex-grow flex items-center">
            <div class="flex relative items-center ">
              <slot></slot>
              <div
                v-if="courier?.active_shift"
                class="w-4 h-4 absolute z-10 right-0 bottom-0 border-2 border-white rounded-full bg-white overflow-hidden"
              >
                <div class="w-4 h-4" :class="[
                courier?.active_shift ? 'bg-green-500': 'bg-amber-500'
                ]">

                </div>
              </div>
              <img
                class="rounded-full z-0 h-16 w-16"
                :src="courier?.avatar_url ? courier?.avatar_url : AvatargImg"
                alt="profile"
              />
            </div>

            <div class="flex-col ml-5">
              <div class="font-bold text-xl text-slate-700">
                {{ courier?.name }}
              </div>
              <div class="text-xxs text-slate-700 opacity-80 mb-0.5">
                {{ $t("Join at") }} {{ dayjs(courier?.crated_at).format("DD.MM.YYYY") }}.
              </div>
            </div>
          </div>
          <div class="flex-shrink flex items-center">
            <el-button
              @click="workingOnOff"
              class="mr-2 h-10"
              :type="courier?.active_shift ? '' : 'primary'">
              {{ courier?.active_shift ? $t("End Shift") : $t("Start Shift") }}
            </el-button>
            <ButtonToolTip :tooltipText="$t('Edit')" position="bottom">
              <div
                @click="editClick"
                class="w-10 h-10 flex rounded-md border border-slate-300 justify-center items-center opacity-70 cursor-pointer"
              >
                <FontAwesomeIcon icon="edit" />
              </div>
            </ButtonToolTip>

          </div>
        </div>
        <div
          class="w-full bg-slate-50 border-t border-b border-slate-300 grid grid-cols-4 gap-4 px-5 py-4"
        >
          <div class="col-span-2 md:col-span-1">
            <div class="mb-1.5">
              <div class="detail--information--label--text">{{ $t("Driver") }} ID</div>
              <div class="detail--information--label--text--desc">
                #{{ courier?.id }}
              </div>
            </div>
            <div class="mb-1.5">
              <div class="detail--information--label--text">{{ $t("Phone") }}</div>
              <div class="detail--information--label--text--desc">
                {{ courier?.phone }}
              </div>
            </div>
          </div>
          <div class="col-span-2 md:col-span-1">
            <div class="mb-1.5">
              <div class="detail--information--label--text">{{ $t("Vehicle Type") }}</div>
              <div class="detail--information--label--text--desc">
                {{ courier?.courier_detail?.vehicle_type }}
              </div>
            </div>
          </div>
          <div class="col-span-2 md:col-span-1 block">
            <div class="mb-1.5">
              <div class="detail--information--label--text">{{ $t("License Plate") }}</div>
              <div class="detail--information--label--text--desc">
                {{ courier?.courier_detail?.vehicle_license_plate }}
              </div>
            </div>
            <div class="">
              <div class="detail--information--label--text">{{ $t("Capacity") }}</div>
              <div class="detail--information--label--text--desc">{{ courier?.courier_detail?.capacity }}</div>
            </div>
          </div>
          <div class="col-span-2 md:col-span-1">
            <div class="detail--information--label--text">{{ $t("Tag") }}</div>
            <div class="flex flex-wrap space-x-0.5 space-y-1">
              <span></span>
              <span class="custom--badge--text" v-for="tag in courier.tags"> {{ tag }} </span>
            </div>

            <div class="mt-1">
              <div class="detail--information--label--text">{{ $t("Unit Capacity") }}</div>
              <div class="detail--information--label--text--desc">{{ courier?.courier_detail?.unit_capacity }}</div>
            </div>
          </div>

        </div>
        <!--        <div class="grid grid-cols-1 gap-4 p-5">-->
        <!--          <div class="col-span-1">-->
        <!--            <div class="flex justify-between items-center mb-2.5">-->
        <!--              <div-->
        <!--                  class="block font-bold text-md text-slate-700 mb-2.5 items-center"-->
        <!--              >-->
        <!--                {{ $t('Statistic') }}<span class="text-xxs"> / {{ $t('Last 7 Days') }}</span>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--            <div class="w-full border border-slate-300 bg-slate-50 h-52">-->
        <!--              <canvas-->
        <!--                  style="height: 200px;width: 700px"-->
        <!--                  id="courier-weekly-stats"-->
        <!--              >-->
        <!--              </canvas>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="grid grid-cols-2 gap-4 p-5">
          <div class="col-span-2 md:col-span-1">
            <div
              class="block font-bold text-md text-slate-700 mb-2.5 items-center"
            >
              {{ $t("Last Activity") }}
              <span class="text-xxs">
                /
                {{
                  courier?.courier_location?.last_seen_at &&
                  dayjs(courier.courier_location.last_seen_at).format(
                    "DD.MM.YYYY HH:mm"
                  )
                }}</span
              >
            </div>
            <div class="flex-grow">
              <EmbedMap
                :lat="courier?.courier_location?.lat"
                :lng="courier?.courier_location?.lng"
              />
            </div>
          </div>
          <div class="flex-col col-span-2 md:col-span-1">
            <div class="flex-grow">
              <div class="flex justify-between items-center mb-2.5">
                <div class="font-bold text-md text-slate-700">
                  {{ $t("Delivery Score") }}
                </div>
                <!--                <el-button :text="true" size="small"> All Feedback</el-button>-->
              </div>
              <div class="flex h-24 border border-slate-300 bg-slate-50 select-text">
                <div class="px-9 my-4 border-r border-slate-300">
                  <div class="flex flex-col justify-center items-center">
                    <span class="text-4xl font-bold text-slate-700">{{
                        courier?.rating
                      }}</span>
                    <span class="w-16 text-xxxs mt-2 text-slate-700">
                      {{ courier?.scored_tasks_count }} {{ $t("Delivery") }}
                    </span>
                  </div>
                </div>
                <div v-if="lastFeedBack.length > 0" class="flex-grow p-4">
                  <div class="text-xs text-slate-700">{{ $t("Last Feedback") }}</div>
                  <div class="text-xxxs text-slate-700 my-0.5">
                    {{ lastFeedBack[0].comment }}
                  </div>
                  <div class="text-xxxxs text-slate-700">{{ lastFeedBack[0].destination_name }}
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-1">
              <div class="flex justify-between items-center">
                <div class="font-bold text-md text-slate-700">{{ $t("Teams") }}</div>
                <!--                <el-button :text="true" size="small"> Updated Team</el-button>-->
              </div>
              <div
                class="flex flex-grow flex flex-wrap text-xs text-slate-700 border border-slate-300 bg-slate-50 p-2.5"
              >
                {{ courier.teams }}
              </div>
            </div>
            <div class="mt-1" v-if="courierSupportTeam">
              <div class="flex justify-between items-center">
                <div class="font-bold text-md text-slate-700">{{ $t("Support Teams") }}</div>
                <!--                <el-button :text="true" size="small"> Updated Team</el-button>-->
              </div>
              <div
                class="flex flex-grow flex flex-wrap text-xs text-slate-700 border border-slate-300 bg-slate-50 p-2.5"
              >
                {{ courierSupportTeam }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>
