<script setup>
import { inject, reactive, onMounted, ref, onActivated, defineEmits, computed, watch } from "vue";
import FormSectionCard from "@/components/general/FormSectionCard.vue";
import PhoneInput from "@/components/ui/PhoneInput.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { conversionFormData } from "@/class/helpers";
import PickImage from "@/components/ui/PickImage.vue";
import AvatargImg from "@/assets/images/avatar.png";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import WorkingHourPlanInput from "@/components/ui/WorkingHourPlanInput.vue";
import SelectBox from "@/components/ui/SelectBox.vue";
import { useStore } from "vuex";

const toast = useToast();
const { t } = useI18n();
const api = inject("api");

const emit = defineEmits(["close", "showDetail", "showEdit", "getData","cancelDrawer"]);
const { dispatch, getters } = useStore();
const props = defineProps({
  courier: { type: Object, default: {} },
  courierId: { type: Number, default: null },
  visible: { type: Boolean, default: true },
});
const upload = ref();
const avatarMode = ref(false);
const dialogVisible = ref(false);
const loader = ref();
const teams = ref([]);
const tags = ref([]);
const workingTemplates = ref([]);
const supportTeamList = ref();
const me = computed(() => getters["auth/me"]);
const routingProfile = ref([]);
const courier = ref();
const id = ref();

const courier_phone = reactive({
  country: "TR",
  phone: null
});


const formValues = reactive({
  tags: [],
  courierNameSurname: null,
  email: null,
  driverLicense: null,
  licenseType: null,
  vehicleType: null,
  vehiclePlate: null,
  relatedTeam: [],
  courier_code: null,
  vehicleColor: null,
  vehicleDescription: null,
  capacity: 0,
  pool_capacity: 0,
  avatar: null,
  working_hour_template_id: null,
  supportTeam: [],
  is_active: true,
  unit_capacity: null,
  routing_profile_slug: null,
  allow_self_break: false
});

const setFields = () => {

  courier_phone.country = props.courier.phone_country;
  courier_phone.phone = props.courier.phone;

  formValues.courierNameSurname = props.courier.name;
  formValues.allow_self_break = props.courier.allow_self_break;
  formValues.email = props.courier.email ? props.courier.email : "";
  formValues.avatar = props.courier.avatar_url;
  formValues.relatedTeam = props.courier.team_ids;
  formValues.tags = props.courier.tags ? [...props.courier.tags] : [];

  if (props.courier.courier_detail) {
    formValues.vehicleType = props.courier.courier_detail.vehicle_type_slug;
    formValues.vehiclePlate = props.courier.courier_detail.vehicle_license_plate;
    formValues.courier_code = props.courier.courier_detail.code;
    formValues.vehicleColor = props.courier.courier_detail.vehicle_color;
    formValues.vehicleDescription = props.courier.courier_detail.vehicle_description;
    formValues.capacity = props.courier.courier_detail.capacity;
    formValues.pool_capacity = props.courier.courier_detail.pool_capacity;
    formValues.working_hour_template_id = props.courier.courier_detail.working_hour_template_id;
    formValues.unit_capacity = props.courier.courier_detail.unit_capacity;
    formValues.routing_profile_slug = props.courier.courier_detail.routing_profile_slug;
    formValues.allow_self_break = props.courier.courier_detail.allow_self_break
  }

  formValues.supportTeam = props.courier.support_team_ids;
  formValues.is_active = props.courier.courier_detail ? props.courier.courier_detail.is_active : true;


};
const setFieldsApi = (param) => {

  courier_phone.country = param.phone_country;
  courier_phone.phone = param.phone;
  formValues.courierNameSurname = param.name;
  formValues.email = param.email ? param.email : "";
  formValues.avatar = param.avatar_url;
  formValues.relatedTeam = param.team_ids;
  formValues.vehicleType = param.courier.courier_detail.vehicle_type_slug;
  formValues.tags = [...param.tags];
  formValues.vehiclePlate = param.courier.courier_detail.vehicle_license_plate;
  formValues.courier_code = param.courier.courier_detail.code;
  formValues.vehicleColor = param.courier.courier_detail.vehicle_color;
  formValues.vehicleDescription = param.courier.courier_detail.vehicle_description;
  formValues.capacity = param.courier.courier_detail.capacity;
  formValues.pool_capacity = param.courier.courier_detail.pool_capacity;
  formValues.working_hour_template_id = param.courier.courier_detail.working_hour_template_id;
  formValues.supportTeam = param.support_team_ids;
  formValues.is_active = param.courier.courier_detail.is_active;
  formValues.unit_capacity = param.courier.courier_detail.unit_capacity;
  formValues.routing_profile_slug = param.courier.courier_detail?.routing_profile_slug;
  formValues.allow_self_break = param.courier.courier_detail?.allow_self_break;

};


const canCreate = () => {
  return new Promise((resolve, reject) => {
    if (!formValues.courierNameSurname) {
      return reject("Name is empty");
    } else if (!courier_phone.phone) {
      return reject("Phone is empty");
    } else if (!formValues.relatedTeam.length > 0) {
      return reject("Team is empty");
    }
    return resolve();
  });
};

const getCourier = () => {
  if (props.courierId) {
    loader.value.show();
    api(`customer/couriers/${props.courierId}`)
      .then((r) => {
        id.value = r.data.id;
        setFieldsApi(r.data);
      }).finally(() => loader.value.hide());
  }
};

const getWorkingTemplates = () => {
  if (loader.value) loader.value.show();
  return api("customer/working-hour-templates?filter[type]=courier")
    .then((res) => {
      workingTemplates.value = res.data;
    }).finally(() => {
      if (loader.value) loader.value.hide();
    });
};

const getSupportTeamSellect = () => {
  loader.value.show();
  let body = {
    courier_id: props.courier.id
  };
  return api.post("/components/support-team-select", body)
    .then((r) => {
      supportTeamList.value = r.data;
    }).finally(() => loader.value.hide());
};

const onSubmit = () => {
  canCreate()
    .then(() => updateCourier())
    .catch((err) => toast.error(t(err)));
};

const updateCourier = () => {
  loader.value.show();
  let body = {
    allow_self_break: formValues.allow_self_break ? 1 : 0
  };

  if (formValues.courierNameSurname) body.name = formValues.courierNameSurname;
  if (formValues.email) body.email = formValues.email;
  if (courier_phone.country) body.phone_country = courier_phone.country;
  if (courier_phone.phone) body.phone = courier_phone.phone;
  if (formValues.courier_code) body.code = formValues.courier_code;
  if (formValues.vehicleType) body.vehicle_type_slug = formValues.vehicleType;
  if (formValues.vehicleDescription) body.vehicle_description = formValues.vehicleDescription;
  if (formValues.vehiclePlate) body.vehicle_license_plate = formValues.vehiclePlate;
  if (formValues.vehicleColor) body.vehicle_color = formValues.vehicleColor;
  if (formValues.capacity) body.capacity = formValues.capacity;
  if (formValues.pool_capacity) body.pool_capacity = formValues.pool_capacity;
  if (upload.value) body.avatar = upload.value;
  if (formValues.tags) body.tags = formValues.tags;
  if (formValues.relatedTeam) body.team_ids = formValues.relatedTeam;
  if (formValues.working_hour_template_id) body.working_hour_template_id = formValues.working_hour_template_id;
  if (formValues.supportTeam) body.support_team_ids = formValues.supportTeam;
  if (formValues.is_active !== null) body.is_active = formValues.is_active ? 1 : 0;
  if (formValues.unit_capacity) body.unit_capacity = formValues.unit_capacity;
  if (formValues.routing_profile_slug) body.routing_profile_slug = formValues.routing_profile_slug;

  body._method = "PATCH";

  let formData = conversionFormData(body);
  api.post(`customer/couriers/${props.courier.id ? props.courier.id : id.value}`, formData)
    .then(() => {
      emit("close");
      emit("getData", true);
    })
    .catch((err) => toast.error(err.message))
    .finally(() => loader.value.hide());
};
const getRoutingProfile = () => {
  loader.value.show();
  return api.post("components/routing-profile-select")
    .then((res) => {
      routingProfile.value = res.data.filter(x => x.country === me.value.active_company.country_code).filter(x => x.vehicle_types.includes(formValues.vehicleType));
    }).finally(() => loader.value.hide());
};


const terminate = () => {
  dialogVisible.value = false;
  loader.value.show();
  api.delete(`customer/couriers/${props.courier.id}`)
    .then(() => emit("close"))
    .catch((err) => toast.error(err.message))
    .finally(() => loader.value.hide());
};

const onCancel = () => {
  emit("cancelDrawer");
  emit("showDetail");

};

onMounted(() => {
  if (props.courierId) {
    getCourier();
    getWorkingTemplates();
    getRoutingProfile();
  }
  if (props.courier) {
    setFields();
    getWorkingTemplates();
    getRoutingProfile();
  }
});

onActivated(() => {
  setFields();
  getWorkingTemplates();
  getSupportTeamSellect();
  getRoutingProfile();

});
const closeDialog = () => {
  dialogVisible.value = false;
};


const unit_capacityRules = (event) => {
  formValues.unit_capacity = formValues.unit_capacity.replace(/[^0-9]/g, "");
};

const capacityRules = (event) => {
  formValues.capacity = formValues.capacity.replace(/[^0-9]/g, "");
};

const poolRules = (event) => {
  formValues.pool_capacity = formValues.pool_capacity.replace(/[^0-9]/g, "");
};
watch(() => formValues.vehicleType, () => {
  getRoutingProfile();

});

</script>

<template>
  <div class="w-full h-full flex">
    <LoadingBlock ref="loader" />
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16">
        <FormSectionCard>
          <el-form label-position="top" class="grid grid-cols-12 gap-4">
            <div class="col-span-12 flex items-center justify-between">
              <div v-if="avatarMode" class="ml-4">
                <PickImage v-model="upload" />

              </div>
              <div v-else class="flex ml-4 ">
                <img class="h-20 w-20" :src="formValues.avatar? formValues.avatar : AvatargImg" alt="" />
              </div>
              <el-button @click="avatarMode = !avatarMode">{{
                  avatarMode ? t("Cancel") : t("Update")
                }}
              </el-button>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Driver Name')" required>
                <el-input
                  v-model="formValues.courierNameSurname"
                  :placeholder="t('Driver Name')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <PhoneInput v-model="courier_phone" />
            </div>
            <div class="col-span-12">
              <el-form-item label="Email">
                <el-input
                  v-model="formValues.email"
                  placeholder="Email"
                ></el-input>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Code')">
                <el-input
                  v-model="formValues.courier_code"
                  :placeholder="t('Code')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <WorkingHourPlanInput
                :templates="workingTemplates"
                v-model="formValues.working_hour_template_id"
              />
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Vehicle Type')">
                <SelectBox
                  v-model="formValues.vehicleType"
                  url="components/vehicle-type-select"
                  method="post"
                  placeholder="Vehicle Type"
                  key="slug"
                  label="name"
                  value="slug"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :placeholder="t('Routing Profile')" :label="t('Routing Profile')">
                <el-select class="custom-routing-select" :placeholder="t('Routing Profile')"
                           v-model="formValues.routing_profile_slug">
                  <el-option
                    class="custom-routing-select"
                    v-for="item in routingProfile"
                    :key="item.profile"
                    :label="item.name"
                    :value="item.profile"
                  >

                    <div>
                      <div class="font-bold w-full">
                        {{ item.name }}
                      </div>
                      <div class="text-xs">
                        {{ item.description }}
                      </div>

                    </div>

                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('License Plate')">
                <el-input
                  v-model="formValues.vehiclePlate"
                  :placeholder="t('License Plate')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Vehicle Color')">
                <el-input
                  v-model="formValues.vehicleColor"
                  :placeholder="t('Vehicle Color')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Vehicle Description')">
                <el-input
                  v-model="formValues.vehicleDescription"
                  :placeholder="t('Vehicle Description')"
                />
              </el-form-item>
            </div>
            <div class="col-span-12 flex grid grid-cols-3 gap-4">
              <el-form-item :label="t('Number Of Tasks')">
                <el-input
                  v-model="formValues.capacity"
                  :placeholder="t('Number Of Tasks')"
                  @input="capacityRules"
                  type="number"
                />
              </el-form-item>
              <el-form-item :label="t('Pool Capacity')">
                <el-input
                  v-model="formValues.pool_capacity"
                  :placeholder="t('Pool Capacity')"
                  @input="poolRules"
                  type="number"
                />
              </el-form-item>
              <el-form-item :label="t('Unit Capacity')">
                <el-input
                  v-model="formValues.unit_capacity"
                  @input="unit_capacityRules"
                  :placeholder="t('Unit Capacity')"
                  type="number"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Related Team')" required>
                <SelectBox
                  v-model="formValues.relatedTeam"
                  url="customer/teams"
                  :multiple="true"
                  :filterable="true"
                  placeholder="Related Team"
                  key="id"
                  label="name"
                  value="id"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="t('Support Teams')">
                <el-select
                  v-model="formValues.supportTeam"
                  class="w-full"
                  filterable
                  size="small"
                  multiple
                  :placeholder="t('Support Teams')"
                >
                  <el-option
                    v-for="item in supportTeamList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="col-span-12">
              <el-form-item :label="t('Tags')">
                <SelectBox
                  v-model="formValues.tags"
                  url="customer/tags"
                  value="name"
                  key="name"
                  label="name"
                  :multiple="true"
                  :filterable="true"
                  placeholder="Tags"
                />
              </el-form-item>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Unapproved Break") }}</div>
              <div>
                <el-switch v-model="formValues.allow_self_break" />
              </div>
            </div>
            <div class="col-span-12 flex items-center justify-between">
              <div class="text-sm text-slate-700 font-medium">{{ $t("Status") }}</div>
              <div>
                <el-switch v-model="formValues.is_active" />
              </div>
            </div>
          </el-form>
        </FormSectionCard>


        <div class="text-right px-2.5 pt-5 ">
          <span @click="dialogVisible = true" class="cursor-pointer text-sm text-red-600 font-medium">
            {{ t("Terminate Contract") }}
          </span>
        </div>
      </div>
      <div
        class="flex justify-between items-center absolute bottom-0 w-full bg-white px-5 py-2 z-20"
        style="box-shadow: 0 0 5px #00000029"
      >
        <div>
          <el-button @click="onCancel">{{ t("Cancel") }}</el-button>
        </div>
        <div>
          <el-button @click="onSubmit" type="primary">
            <template #icon>
              <FontAwesomeIcon icon="check-square" />
            </template>
            {{ t("Save") }}
          </el-button>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" width="30%" center>
      <div class="text-center text-lg text-slate-700 font-bold">
        {{ t("Are you sure?") }}
      </div>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">{{ t("Cancel") }}</el-button>
        <el-button @click="terminate" type="danger">
          {{ t("Terminate") }}
        </el-button>
      </span>
      </template>
    </el-dialog>
  </div>
</template>
