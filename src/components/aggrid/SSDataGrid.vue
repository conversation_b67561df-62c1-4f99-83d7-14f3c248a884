<script>
import { AgGridVue } from "ag-grid-vue3";
import CustomDateInput from "@/components/aggrid/CustomDateInput";
import { computed, inject, defineComponent, ref, reactive, watch, onActivated, onDeactivated } from "vue";
import { AG_GRID_LOCALE_EN } from "@/locales/agGrid/en";
import { AG_GRID_LOCALE_TR } from "@/locales/agGrid/tr";
import { i18n } from "@/plugins/vuei18n";
import dayjs from "dayjs";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import debounce from "lodash.debounce";
import { useToast } from "vue-toastification";
import { useI18n } from "vue-i18n";

// Bu componente ref verip içerisindeki metodlara ulaşabiliriz

// PROPS

// "optionalFilter" ile column'da belirlenen field keylerinin BE tarafında filtre karşılıkları
// olmadığı zaman keyleri değiştirip içerisinde ekstra alan ekleyebiliyoruz.

// "setDataSourceMiddleware" bu değer "true" geldiği zaman "setDataSource" eventi tektiklenir
// ve parentta manipüle edilerek ag-grid'e satırlar eklenebilir.


export default defineComponent({
  components: {
    FontAwesomeIcon,
    AgGridVue,
    agDateInput: CustomDateInput
  },
  props: {
    columns: {
      type: Array, default: () => {
      }
    },
    filter: {
      type: [Object, Boolean], default: false
    },
    exportUrl: { type: String },
    exportedFileName: { type: String, default: "export" },
    columnStateSlug: { type: String, default: "" },
    restoreColumnStateEnabled: { type: Boolean, default: false },
    optionalFilter: { type: Array, default: () => [] },
    rowKey: { type: String, default: "id" },
    url: { type: String },
    excelExportType: { type: String, default: "" },
    setDataSourceMiddleware: { type: Boolean, default: false },
    autoSizeColumn: { type: Boolean, default: true },
    fitColumnMaxSize: { type: Number, default: 840 },
    pagination: { type: Boolean, default: true },
    sideBar: { type: [Object, Array], default: false },
    modelValue: { default: "" },
    rowSelection: { type: String, default: "multiple" },
    cursorEnabled: { type: Boolean, default: false },
    paginationPageSize: { type: Number, default: 100 },
    suppressContextMenu: { type: Boolean, default: true },
    waitFilters: { type: Boolean, default: false },
    isEnabledCurrentPage: { type: Boolean, default: true },
    rowLength: { type: Number, default: null },
    numberFilterFormatters: { type: Array, default: () => [] }

  },
  emits: ["update:modelValue", "onGridReady", "rowClicked", "onFilterChanged", "rowDoubleClicked", "handleSelectionChanged", "setDataSource", "rowDataUpdated"],
  setup: (props, { emit }) => {

    const { t } = useI18n();
    const toast = useToast();
    const api = inject("api");


    const localeText = computed(() => i18n.global.locale.value === "tr" ? AG_GRID_LOCALE_TR : AG_GRID_LOCALE_EN);

    const defaultColDef = {
      resizable: true,
      menuTabs: [],
      sortable: false,
      floatingFilter: true,
      filterParams: {
        buttons: ["reset", "apply"],
        closeOnApply: true
      }
    };

    const excelStyles = [
      {
        id: "numberType",
        numberFormat: {
          format: "0"
        }
      },
      {
        id: "currencyFormat",
        numberFormat: {
          format: "#,##0.00 €"
        }
      },
      {
        id: "negativeInBrackets",
        numberFormat: {
          format: "$[blue] #,##0;$ [red](#,##0)"
        }
      },
      {
        id: "booleanType",
        dataType: "Boolean"
      },
      {
        id: "stringType",
        dataType: "String"
      },
      {
        id: "dateType",
        dataType: "DateTime",
        numberFormat: {
          format: "yyyy-mm-ddThh:MM:ss.mmm:"
        }
      }
    ];

    const grid = reactive({
      next: true,
      prev: null,
      last: null,
      first: null,
      total: null,
      from: null,
      to: null,
      next_cursor: null,
      prev_cursor: null
    });

    const columnsDef = ref([]);
    const columnApi = ref();
    const gridApi = ref();
    const loading = ref(true);
    const currentPage = ref(1);
    const exportExcelDrawerVisible = ref(false);
    const activated = ref(true);
    const cursorValue = ref();
    const pageCount = ref(1);
    const nextButonVisible = ref(false)
    const prevButtonVisible = ref(false)

    onActivated(() => {
      activated.value = true;

    });

    onDeactivated(() => {
      activated.value = false;
    });


    watch(currentPage, () => {
      props.isEnabledCurrentPage && getData();
    });


    watch(cursorValue, () => {
      props.cursorEnabled && getData();
    });


    watch(() => props.filter, () => {

      if (currentPage.value === 1) {
        props.isEnabledCurrentPage && getData();
      } else {
        currentPage.value = 1;
      }


    }, { deep: true });

    const onGridReady = (params) => {
      columnsDef.value = props.columns.map(x => {
          return {
            ...x,
            cellClass: "stringType"
          };
        }
      );
      columnApi.value = params.columnApi;

      setTimeout(() => {
        emit("update:modelValue", params.api);
        emit("onGridReady");
      }, 150);

      if (!props.waitFilters) {
        getData();
      }
      const savedColumnStateStr = localStorage.getItem(props.columnStateSlug);
      if (savedColumnStateStr) {
        try {
          // Try to parse the saved column state
          const savedColumnState = JSON.parse(savedColumnStateStr);
          if (savedColumnState) {
            params.columnApi.applyColumnState({
              state: savedColumnState,
              applyOrder: true
            });
          }
        } catch (e) {
          console.error("Error parsing saved column state:", e);
          // Remove the invalid column state from localStorage to prevent future errors
          localStorage.removeItem(props.columnStateSlug);
        }
      }
      if (props.onGridReadyVisible) {
        params.api.addEventListener("columnVisible", onColumnVisible);
      }

      // gridApi set ediliyor
      gridApi.value = params.api;
      // gridApi tanımlandıktan sonra closeToolPanel çağrılıyor
      gridApi.value.closeToolPanel();
    };


    const onColumnVisible = (params) => {
      const columnState = params.columnApi.getColumnState();
      localStorage.setItem(props.columnStateSlug, JSON.stringify(columnState));

    };

    const createSortRequestModel = (params) => {
      return params.map(x => {
        let y;
        if (x.sort === "desc") {
          y = "-" + x.colId;
        } else {
          y = x.colId;
        }
        return y;
      })
        .join(",");
    };

    const createFilterRequestModel = () => {

      let filterModel = JSON.parse(JSON.stringify(gridApi.value.getFilterModel()));


      if (props.numberFilterFormatters.length > 0) {

        Object.keys(filterModel).forEach((x) => {

          if (props.numberFilterFormatters.map(y => y.key).includes(x)) {

            filterModel[x].filter = props.numberFilterFormatters.find(item => item.key === x).method(filterModel[x].filter);

          }
        });
      }

      Object.values(filterModel).map(x => {
        if (x.filterType === "set") {
          x.filter = x.values;
          x.filterType = "text";
          x.type = "set";
          delete x.values;
        }
        if (x.filterType === "date" && x.type === "inRange") {
          x.filter = [dayjs(x.dateFrom).toISOString(), dayjs(x.dateTo).toISOString()];
          delete x.dateFrom;
          delete x.dateTo;
        }
        return x;
      });

      let isOptionalFilter = false;

      Object.keys(filterModel).map(x => {
        if (props.optionalFilter.map(x => x.old).includes(x)) {
          isOptionalFilter = true;
        }
        return x;
      });
      if (isOptionalFilter) {
        props.optionalFilter.forEach(x => {
          if (filterModel[x.old]) {

            if (x.newValue) {
              x.newValue.forEach((a) => {
                filterModel[x.old][Object.keys(a)[0]] = Object.values(a)[0];
              });
            }

            Object.defineProperty(filterModel, x.new, Object.getOwnPropertyDescriptor(filterModel, x.old));
            delete filterModel[x.old];
          }
        });
      }

      return JSON.stringify(filterModel).replaceAll(",", ";");
    };

    const fetchData = (type) => {
      if (!gridApi.value) {
        console.error("Grid API is not initialized yet");
        return;
      }
      gridApi.value.showLoadingOverlay();
      loading.value = true;

      if (!columnApi.value) {
        console.error("Column API is not initialized yet");
        return;
      }
      let sort = createSortRequestModel(columnApi.value.getColumnState().filter(s => s.sort !== null));

      let params = {
        sort,
        "filter[grid]": createFilterRequestModel()
      };

      if (props.filter && Object.keys(props.filter).length !== 0) {
        const { "filter[grid]": _, ...restFilters } = props.filter;
        params = {
          ...params,
          ...restFilters
        };
      }


      let url = !props?.cursorEnabled
        ? `${props.url}?page=${currentPage.value}&per_page=${props.paginationPageSize}`
        : `${props.url}?per_page=${props.paginationPageSize}${cursorValue.value ? `&cursor=${cursorValue.value}` : ''}`;

      if (type === "next") {
        url = grid.next;
      }

      if (type === "prev") {
        url = grid.prev;
      }


      api(url, { params })
        .then((r) => {
          gridApi.value.hideOverlay();

          if (r.meta) {
            grid.total = r.meta?.total;
            grid.from = r.meta?.from;
            grid.to = r.meta?.to;
            grid.next_cursor = r.meta?.next_cursor;
            grid.prev_cursor = r.meta?.prev_cursor;
          }

          if(props.cursorEnabled){
            if (r.meta?.next_cursor){
              nextButonVisible.value = true
            }
          }
          if(props.cursorEnabled){
            if (!r.meta?.next_cursor){
              nextButonVisible.value = false
            }
          }
          if(props.cursorEnabled){
            if (r.meta?.prev_cursor){
              prevButtonVisible.value = true
            }
          }
          if(props.cursorEnabled){
            if (!r.meta?.prev_cursor){
              prevButtonVisible.value = false
            }
          }


          if (props.pagination) {
            grid.next = r.links.next;
            grid.prev = r.links.prev;
          }

          if (props.setDataSourceMiddleware) {
            emit("setDataSource", r.data);
          } else {
            gridApi.value.setRowData(r.data);
            gridApi.value.refreshCells({ force: true });
          }
        })
        .catch((err) => {
          if (props.setDataSourceMiddleware) {
            emit("setDataSource", []);
          } else {
            gridApi.value.setRowData([]);
          }

          toast.error(err.message);
        })
        .finally(() => {
          gridApi.value.hideOverlay();
          loading.value = false;
        });
    };

    const getData = debounce(fetchData, 300);

    const rowClicked = (param) => {
      emit("rowClicked", param);
    };

    const rowDoubleClicked = (param) => {
      emit("rowDoubleClicked", param);
    };

    const handleSelectionChanged = () => {
      emit("handleSelectionChanged");
    };

    const onFirst = () => {

      if (!grid.prev) return;

      if (props.isEnabledCurrentPage) {
        currentPage.value = 1;
      } else {
        getData();
      }

    };

    const onNext = () => {
      if (grid.next) {

        if (props.isEnabledCurrentPage) {
          currentPage.value += 1;
        } else {
          getData("next");
        }
      }
    };

    const onNextCursor = () => {
      if (grid.next_cursor) {

        if (props.cursorEnabled) {
          cursorValue.value = grid.next_cursor;
          pageCount.value += 1;
        } else {
          getData("next");
        }
      }
    };


    const onPrevious = () => {
      if (grid.prev) {
        if (props.isEnabledCurrentPage) {
          currentPage.value -= 1;
        } else {
          getData("prev");
        }
      }
    };
    const onPreviousCursor = () => {
      if (grid.prev_cursor) {
        if (props.cursorEnabled) {
          cursorValue.value = grid.prev_cursor;
          pageCount.value -= 1;
        } else {
          getData("prev");
        }
      }
    };


    const onSortChanged = () => {
      if (currentPage.value === 1) {
        getData();
      } else {
        currentPage.value = 1;
      }
    };

    const onFilterChanged = (param) => {
      emit("onFilterChanged", param);
      if (param.afterDataChange) return;

      if (currentPage.value === 1) {
        let emptyFilter = false;
        let filterModel = JSON.parse(JSON.stringify(gridApi.value.getFilterModel()));
        Object.values(filterModel).map(x => {
          if (x.filterType === "set" && x.values.length === 0) {
            emptyFilter = true;
          }
        });

        if (!emptyFilter) {

          getData();
        }
      } else {
        currentPage.value = 1;
      }
    };

    const getRowId = (params) => {
      return params.data[props.rowKey];
    };

    const refresh = () => {
      getData();
    };

    const onFirstDataRendered = (params) => {

      if (props.autoSizeColumn) {

        if (!activated.value) return;

        params.columnApi.autoSizeAllColumns(true);

      } else {
        params.api.sizeColumnsToFit();
        // window.onresize = () => {
        //   if (window.innerWidth < props.fitColumnMaxSize) {
        //     params.columnApi.autoSizeAllColumns(true)
        //   } else {
        //     console.log("testtt")
        //     params.api.sizeColumnsToFit()
        //   }
        // }
      }
      restoreColumnState();
    };

    const openExportExcelDrawer = () => {
      exportExcelDrawerVisible.value = true;
    };

    const exportAsCsv = () => {
      return new Promise((resolve, reject) => {

        let type = "csv";
        if (!props.exportUrl) return reject();

        let sort = createSortRequestModel(columnApi.value.getColumnState().filter(s => s.sort !== null));

        let params = {
          format: type,
          sort,
          "filter[grid]": createFilterRequestModel()
        };

        if (Object.keys(props.filter).length !== 0) {
          params = {
            ...props.filter, ...params
          };
        }

        api
          .get(props.exportUrl, {
            params,
            responseType: "arraybuffer"
          })
          .then((response) => {

            let blob;
            if (type === "csv") {
              blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), response], {
                type: "text.csv"
              });
            } else {
              blob = new Blob([response], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              });
            }

            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = `${props.exportedFileName}.${type}`;
            link.click();
            window.URL.revokeObjectURL(link.href);
            resolve();
          })
          .catch((err) => reject(err));

      });
    };

    const exportAsExcel = (params) => {
      return new Promise((resolve, reject) => {
        exportExcelDrawerVisible.value = false;

        if (!props.excelExportType) return reject();

        let sort = createSortRequestModel(columnApi.value.getColumnState().filter(s => s.sort !== null));

        let filter = {
          grid: createFilterRequestModel()
        };

        if (props.filter && Object.keys(props.filter).length !== 0) {
          Object.keys(props.filter).forEach(key => {
            const cleanKey = key.replace(/^filter\[(.*)\]$/, '$1');
            filter[cleanKey] = props.filter[key];
          });
        }

        let query = {
          sort,
          filter
        };

        let body = {
          filetype: "xlsx",
          filename: params.fileName,
          email: params.email,
          description: params.description,
          query
        };

        api
          .post(`customer/exports/${props.excelExportType}`, body)
          .then(() => toast(t("Export started")))
          .catch((err) => {
            if (err.type) {
              return toast.error(err.message);
            }
            reject(err);
          })
          .finally(() => resolve());
      });
    };

    const restoreColumnState = (params) => {

      if (!props.columnStateSlug || !props.restoreColumnStateEnabled) return;

      let localColumnState = localStorage.getItem(props.columnStateSlug);

      if (localColumnState) {
        columnApi.value.applyColumnState({
          state: JSON.parse(localColumnState),
          applyOrder: true
        });
      }
    };

    const dragStopped = () => {
      if (!props.columnStateSlug) return;

      let cols = columnApi.value.getColumnState();
      cols.map(x => {
        delete x.sort;
        delete x.sortIndex;
        return x;
      });
      localStorage.setItem(props.columnStateSlug, JSON.stringify(cols));
    };

    const resetColumnState = () => {
      if (!props.columnStateSlug) return;
      columnApi.value.resetColumnState();
    };

    const rowDataUpdated = (params) => {
      emit("rowDataUpdated", params);
    };

    const testButton = () => {

      let filterModel = JSON.parse(JSON.stringify(gridApi.value.getSideBar()));

      // function getSideBar() {
      //   var sideBar = gridApi.getSideBar();
      //   alert(JSON.stringify(sideBar));
      //   console.log(sideBar);
      // }

    };


    const overlayLoadingTemplate = `<img src="/loading.svg" class="h-4 w-full" alt />`;

    return {
      onFirst,
      rowDataUpdated,
      exportAsCsv,
      openExportExcelDrawer,
      testButton,
      resetColumnState,
      restoreColumnState,
      dragStopped,
      exportAsExcel,
      onFirstDataRendered,
      columnsDef,
      defaultColDef,
      excelStyles,
      localeText,
      overlayLoadingTemplate,
      onGridReady,
      onColumnVisible,
      onFilterChanged,
      rowClicked,
      rowDoubleClicked,
      handleSelectionChanged,
      onSortChanged,
      loading,
      pageCount,
      prevButtonVisible,
      onPrevious,
      onPreviousCursor,
      onNextCursor,
      exportExcelDrawerVisible,
      nextButonVisible,
      currentPage,
      onNext,
      grid,
      refresh,
      getRowId
    };
  }
});
</script>

<template>
  <div class="flex flex-col w-full h-full overflow-hidden">
    <ag-grid-vue
      class="w-full h-full ag-theme-balham overflow-auto"
      groupDisplayType="groupRows"
      :column-defs="columnsDef"
      :default-col-def="defaultColDef"
      :row-selection="rowSelection"
      :localeText="localeText"
      :sideBar="sideBar"
      :excelStyles="excelStyles"
      :overlayLoadingTemplate="overlayLoadingTemplate"
      :paginationPageSize="paginationPageSize"
      :cacheBlockSize="paginationPageSize"
      :animateRows="true"
      :suppressCopyRowsToClipboard="true"
      :getRowId="getRowId"
      :suppressContextMenu="suppressContextMenu"
      :suppressDragLeaveHidesColumns=true
      @grid-ready="onGridReady"
      @column-visible="onColumnVisible"
      @dragStopped="dragStopped"
      @filter-changed="onFilterChanged"
      @row-clicked="rowClicked"
      @row-double-clicked="rowDoubleClicked"
      @selection-changed="handleSelectionChanged"
      @sort-changed="onSortChanged"
      @first-data-rendered="onFirstDataRendered"
      @rowDataUpdated="rowDataUpdated"
    />
    <div v-if="pagination"
         class="w-full flex items-center justify-between px-2 py-2 text-slate-700 border-t border-slate-300 relative">
      <div v-if="loading" class="absolute inset-0  bg-white  bg-opacity-75" />
      <div class="flex items-center justify-start">
        <div
          v-if="grid.total"
          class="text-xxs flex items-center"
        >
          <strong class="custom-primary-color">
            {{ grid.total }}
          </strong>
          <span v-if="grid.from && grid.to" class="ml-3">
            {{ grid.from }}
            <FontAwesomeIcon icon="fa-thin fa-arrows-left-right-to-line" class="mx-0.5" />
            {{ grid.to }}
          </span>
        </div>
        <div class="ml-3 text-xxs" v-if="$props.rowLength">
          {{ $props.rowLength }} {{ $t("to task selected") }}
        </div>
      </div>
      <div v-if="!cursorEnabled" class="flex items-center justify-end font-semibold">

        <div
          @click="onFirst"
          class="cursor-pointer  text-xxs py-0.5 px-2"
          :class="[
                grid.prev ? 'text-slate-700' : 'text-slate-300'
            ]"
        >
          {{ $t("First") }}
        </div>
        <div
          @click="onPrevious"
          class="cursor-pointer  text-xxs py-0.5 px-2"
          :class="[
                 grid.prev ? 'text-slate-700' : 'text-slate-300'
            ]"
        >
          {{ $t("Previous") }}
        </div>
        <div v-if="isEnabledCurrentPage" class="text-xxs py-0.5 px-2 custom-primary-color font-bold">
          {{ currentPage }}
        </div>
        <div
          @click="onNext"
          class="cursor-pointer  text-xxs py-0.5 px-2"
          :class="[
                  grid.next ? 'text-slate-700' : 'text-slate-300'
              ]"
        >
          {{ $t("Next") }}
        </div>
      </div>
      <div v-if="cursorEnabled" class="flex items-center justify-end font-semibold">

        <!--        <div-->
        <!--          @click="onFirst"-->
        <!--          class="cursor-pointer  text-xxs py-0.5 px-2"-->
        <!--          :class="[-->
        <!--                grid.prev_cursor ? 'text-slate-700' : 'text-slate-300'-->
        <!--            ]"-->
        <!--        >-->
        <!--          {{ $t("First") }}-->
        <!--        </div>-->
        <div
          @click="onPreviousCursor"
          class="cursor-pointer  text-xxs py-0.5 px-2"
          :class="[
                prevButtonVisible || grid.prev_cursor ? 'text-slate-700' : 'text-slate-300'
            ]"
        >
          {{ $t("Previous") }}
        </div>
        <div v-if="cursorEnabled" class="text-xxs py-0.5 px-2 custom-primary-color font-bold">
          {{pageCount}}
        </div>
        <div
          @click="onNextCursor"
          class="cursor-pointer  text-xxs py-0.5 px-2"
          :class="[
                  nextButonVisible || grid.next_cursor ? 'text-slate-700' : 'text-slate-300'
              ]"
        >
          {{ $t("Next") }}
        </div>
      </div>
    </div>
    <el-drawer
      v-model="exportExcelDrawerVisible"
      class="customized-drawer"
      :title="$t('Export')"
      append-to-body
      destroy-on-close
    >
      <ExcelExportDrawer @export="exportAsExcel" />
    </el-drawer>
  </div>
</template>
