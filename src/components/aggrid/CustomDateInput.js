import flatpickr from 'flatpickr'
import 'flatpickr/dist/flatpickr.min.css'
import 'flatpickr/dist/themes/light.css'
import {Turkish} from "flatpickr/dist/l10n/tr"

export default {
    template: `
      <div class="ag-input-wrapper custom-aggrid-date-input " role="presentation" ref="flatpickr">
      <input type="text" ref="eInput" data-input style="width: 100%;"/>
      <a class="input-button" title="clear" data-clear>
        <i class="fa fa-times"></i>
      </a>
      </div>
    `,
    data: function () {
        return {
            date: null,
        };
    },
    mounted() {
        let lang = this.$i18n.locale
        this.picker = flatpickr(this.$refs['flatpickr'], {
            locale: lang === "tr" ? Turkish : null,
            onChange: this.onDateChanged.bind(this),
            dateFormat: 'd.m.Y H:i',
            wrap: true,
            enableTime: true,
            disableMobile: true
        });

        this.eInput = this.$refs['eInput'];
        this.picker.calendarContainer.classList.add('ag-custom-component-popup');
    },
    methods: {
        onDateChanged(selectedDates) {
            this.date = selectedDates[0] || null;
            this.params.onDateChanged();
        },

        getDate() {
            return this.date;
        },

        setDate(date) {
            this.picker.setDate(date);
            this.date = date || null;
        },

        setInputPlaceholder(placeholder) {
            this.eInput.setAttribute('placeholder', placeholder);
        },

        setInputAriaLabel(label) {
            this.eInput.setAttribute('aria-label', label);
        },
    },
};
