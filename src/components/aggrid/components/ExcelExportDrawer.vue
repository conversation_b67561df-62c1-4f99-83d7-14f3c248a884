<script setup>
import {reactive} from "vue";
import {useToast} from "vue-toastification";
import {useI18n} from "vue-i18n";

const toast = useToast()
const {t} = useI18n()

const emits = defineEmits(["export"])

const form = reactive({
  fileName: null,
  description: null,
  email: null,
})

const onSubmit = () => {
  if (!form.fileName) {
    toast.error(t("Filename is required"))
    return
  }
  emits("export", form)
}

</script>

<template>
  <LoadingBlock ref="loader"/>
  <div class="w-full h-full flex">
    <div class="flex-grow relative">
      <div class="absolute inset-0 overflow-y-auto px-2.5 pb-16 mt-3">
        <div class="px-2.5 pb-5">
          <el-form label-position="top" class="grid grid-cols-12 gap-4">

            <div class="col-span-12">
              <el-form-item :label="$t('File Name')" required>
                <el-input
                    v-model="form.fileName"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Email')">
                <el-input
                    v-model="form.email"
                />
              </el-form-item>
            </div>
            <div class="col-span-12">
              <el-form-item :label="$t('Description')">
                <el-input
                    v-model="form.description"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <div
          class="flex justify-end absolute bottom-0 w-full bg-white px-5 py-2"
          style="box-shadow: 0px 0px 5px #00000029"
      >
        <el-button @click="onSubmit" type="primary">
          <template #icon>
            <FontAwesomeIcon icon="check-square"/>
          </template>

          {{ $t('Export') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
