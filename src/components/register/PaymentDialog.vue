<script setup>
import { onMounted, ref, inject, watch, computed, defineProps } from "vue";
import { loadStripe } from "@stripe/stripe-js";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { useStore } from "vuex";
import router from "@/router";
import { useToast } from "vue-toastification";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import blackLogo from "@/assets/images/black-logo.png";
import formatter from "@/class/formatter";


const emit = defineEmits(["getData"]);
const props = defineProps({
  plan: {
    type: Object, default: () => ({})
  },
  companyForm: {
    type: Object, default: () => ({})
  },
  planIdes: {
    type: Array, default: () => ([])
  }
});


const api = inject("api");
const key = ref();
const route = useRoute();
const { t } = useI18n();
const submitVisible = ref(false);
const { dispatch, getters } = useStore();
const me = computed(() => getters["auth/me"]);
const toast = useToast();
const loader = ref();
const prices = ref();
const price_id = computed(() => {
  const price = prices.value.find((i) => i.priceable_id === props.plan.id);

  return price;
});



const getSecretKey = () => {

  api.post("customer/subscriptions/start-payment")
    .then((r) => {

      key.value = r.client_secret;
    });
};

onMounted(() => {
  getSecretKey();
  boot();
  getCarts();
  getPlanPrices();
});

const getCarts = () => {
  api.get("customer/subscriptions/payment-methods")
    .then((r) => {

    });
};


const getPlanPrices = () => {


  const planIdsString = props.planIdes.join(",");

  api(`customer/prices/plan?filter[priceable_id]=${planIdsString}&filter[country_code]=${props.companyForm.country}&filter[currency_unit]=USD`)
    .then((r) => {
      prices.value = r.data;
    });
};


const boot = async () => {
  var stripe = await loadStripe("pk_test_51NyrivAr7IBxBDyQp4oYtaJ2kbfaurTtI2nbjbbQt3VXnpjNjTJMnSyE208LDKBdTvNyXl7X3R4HdBRg7jumLPDa00ybyl63vq");
  var elements = stripe.elements();

  const style = {
    base: {
      color: "#32325D",
      fontFamily: "\"Helvetica Neue\", Helvetica, sans-serif",
      fontSmoothing: "antialiased",
      fontSize: "16px",
      "::placeholder": {
        color: "#AAB7C4",
        content: "FGYEYEFYEFGY"
      },
      border: "1px solid ",
      borderColor:"#FA755A"

    },
    invalid: {
      color: "#FA755A",
      iconColor: "#FA755A",

      border: "1px solid ",
      borderColor:"#FA755A"
    }
  };
  var card = elements.create("card", {
    hidePostalCode: true,
    style: style
  });
  card.mount("#card-element", { style: style });
  card.addEventListener("change", function(event) {
    var displayError = document.getElementById("card-errors");
    if (event.error) {
      displayError.textContent = event.error.message;
    } else {
      displayError.textContent = "";
    }
  });

  const cardHolderName = document.getElementById("card-holder-name");
  const cardButton = document.getElementById("card-button");

  cardButton.addEventListener("click", async (e) => {
    e.preventDefault();
    const { setupIntent, error } = await stripe.confirmCardSetup(
      key.value, {
        payment_method: {
          card: card,
          billing_details: { name: cardHolderName.value }
        }
      }
    );
    if (error) {
      var errorElement = document.getElementById("card-errors");
      errorElement.textContent = error.message;
    } else {
      loader.value.show();

      if (!price_id.value) {
        console.error("price_id is null or undefined");
        return; // stop execution if price_id is null
      }
      api.post("customer/subscriptions/subscribe", {
        payment_method: setupIntent.payment_method,
        plan_id: props.plan.id,
        plan_price_id: price_id.value.id
      })
        .then((r) => {

          router.push({ name: "Home" });

        }).catch((e) => {
        toast.error(e.message);
      }).finally(() => {
        loader.value.hide();
      });
    }
  });
};


</script>
<template>
  <LoadingBlock ref="loader" />

  <form method="GET" id="subscribe-form">
    <div class="flex justify-start items-center mx-4 mb-6">
      <div>
        <img :src="blackLogo" style="width: 35px;height: 30px">
      </div>
      <div class="ml-5 "
           v-if="props.plan.renewal_period_slug ==='yearly'">
        {{ t("Yearly") }} {{ formatter.currency(props.plan.price, props.plan.currency_unit) }} {{ t("By paying") }}
        {{ props.plan.name }}
        {{ t("you will subscribe to the package and your subscription will be immediately applied to your account") }}
      </div>
      <div class="ml-5 "
           v-if="props.plan.renewal_period_slug ==='monthly'">
        {{ t("Monthly") }} {{ formatter.currency(props.plan.price, props.plan.currency_unit) }} {{ t("By paying") }}
        {{ props.plan.name }}
        {{ t("you will subscribe to the package and your subscription will be immediately applied to your account") }}
      </div>
    </div>
    <label class="mx-4 " for="card-holder-name form-control">{{ $t("Card Holder") }}</label> <br>
    <div class="w-full px-4 input-container">
      <input id="card-holder-name" type="text" style="height: 30px; padding-left: 8px;" class="input"
             :placeholder="t('Name Surname')">
    </div>

    <div class="form-row px-4 pt-6">
      <label for="card-element">{{ $t("Card Information") }}</label>
      <div id="card-element" style="border: 1px solid #AAB7C4" class="form-control">
      </div>
      <!-- Used to display form errors. -->
      <div id="card-errors" role="alert"></div>
    </div>
    <div class="pt-9 px-4">{{ $t("In all payment transactions") }} <span class="text-indigo-600">Stripe</span>
      {{ $t("infrastructure is used") }}
    </div>
    <div class="stripe-errors"></div>
    <div class="flex items-center justify-start mt-3 mb-4 px-4">

      <button id="card-button" :data-secret="key" class="border py-1 px-2 rounded bg-indigo-600 text-white">
        {{ $t("Start Subscription") }}
      </button>
    </div>
  </form>
</template>

<style scoped>
.StripeElement {
  background-color: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  box-shadow: 0 1px 3px 0 #E6EBF1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 #CFD7DF;
}

.StripeElement--invalid {
  border-color: #FA755A;
}

.StripeElement--webkit-autofill {
  background-color: #FEFDE5 !important;
}

.input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #AAB7C4;
  box-shadow: 0 1px 3px 0 #E6EBF1;
  transition: box-shadow 150ms ease;
  padding-left: 30px;

}

</style>
