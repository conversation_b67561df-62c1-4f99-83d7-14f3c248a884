<script setup>
import { defineProps } from 'vue';

const props = defineProps({
    isActive: Boolean,
    title: String
});

</script>

<template>
    <div v-show="isActive" class="bg-white flex flex-1 w-full flex-col px-8 lg:px-28 pt-8 lg:pt-5 tabs-wrapper">
        <div class="border-b text-indigo-600 font-semibold text-lg pb-4 mb-2">
            {{ title }}
        </div>
        <slot></slot>
    </div>
</template>


<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');

.tabs-wrapper * {
    font-family: 'Inter';
}
</style>