<script setup>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { computed } from 'vue';

const props = defineProps({
  item: { type: Object },
  activeTab: String
});

const isActive = computed(() => { return props.activeTab === props.item.title });

const isCompleted = computed(() => {
  const order = props.activeTab.slice(-1);
  const current = props.item.title.slice(-1);

  return Number(current) < Number(order);
});

const iconColor = computed(() => {
  return isCompleted.value ? 'blue' : isActive.value ? 'black' : '#cbd5e1'
});

</script>

<template>
  <div class="flex flex-row align-center flex-1 sticky top-0">
    <div class="flex-1 items-center justify-center flex flex-col lg:px-6 grey-bg border-r border-slate-300 ">
      <span class="w-full mt-auto" />
      <div class="px-4 py-2 lg:py-8">
        <el-icon
          :size="36"
          :color="iconColor"
        >
          <FontAwesomeIcon :icon="item.icon" />
        </el-icon>
      </div>

      <span class="border-b border-slate-300 w-full mt-auto" />
    </div>
    <div class="lg:w-8 self-center hidden lg:flex">
      <el-icon
        v-if="isActive"
        :size="34"
        color="#f0f0f0"
        class="-ml-2"
      >
        <FontAwesomeIcon icon="fa-solid fa-play m-0" />
      </el-icon>
    </div>
  </div>
</template>

<style scoped>
.grey-bg {

  background-color: #F0F0F0;
}</style>
