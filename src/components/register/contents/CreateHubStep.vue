<script setup>
import PinMap from "@/components/ui/PinMap.vue";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { Loader } from "@googlemaps/js-api-loader";
import { reactive, ref, onMounted, nextTick, inject, computed } from "vue";
import { generate3RandomNumbers } from "@/plugins/helpers";
import router from "@/router";
import { useStore } from "vuex";

const { t } = useI18n();

const mapVisible = ref(false);
const { dispatch, getters } = useStore();

const toast = useToast();
const emit = defineEmits(['openNextTab', 'showLoader', 'hideLoader'])
const api = inject('api');
const me = computed(() => getters["auth/me"]);

const showMap = () => {
    mapVisible.value = true;
}

const location = reactive({
    searchAddress: '',
    lat: null,
    lng: null,
    area_json: null
});

const hubForm = reactive({
    hubName: null,
    relatedTeam: null,
    integration_id: null,
    addressNote: null,
    searchAddress: null,
    building: null,
    flor: null,
    apartment: null,
    lat: null,
    lng: null,
    address: null,
    default_service_time: 1,
    tags: [],
    working_hour_template_id: null,
    max_distance: 0,
    is_active: true,
    default_preparation_time: 5
});

const onSubmit = () => {
    emit('showLoader');

    if (!location.lat || !location.lng) {
        toast.warning('Konum seçilmeli');
        return emit('hideLoader')
    }

    if (!hubForm?.hubName || hubForm.hubName?.length < 3) {
        toast.warning('Lokasyon adı çok kısa');
        return emit('hideLoader')
    };

    resolveAddressFromCoordinates(location.lat, location.lng).then(
        (address) => {
            const formObject = {
                ...hubForm,
                ...location,
                searchAddress: address
            };

            const generatedHubId = formObject.hubName.slice(0, 2) + generate3RandomNumbers();

          let body = {
            step: "hub",
            integration_id: generatedHubId,
            name: formObject.hubName,
            lat: formObject.lat.toFixed(6),
            lng: formObject.lng.toFixed(6)
          };

          if (formObject.address) body.address = formObject.address;
          if (formObject.building) body.address_building = formObject.building;
          if (formObject.apartment) body.address_apartment = formObject.apartment;
          if (formObject.floor) body.address_floor = formObject.floor;
          if (formObject.tags) body.tags = formObject.tags;
          if (formObject.default_service_time !== null && formObject.default_service_time !== undefined) body.default_service_time = Number(formObject.default_service_time);
          if (formObject.working_hour_template_id) body.working_hour_template_id = formObject.working_hour_template_id;
          if (formObject.max_distance) body.max_distance = formObject.max_distance;
          if (formObject.is_active !== null && formObject.is_active !== undefined) body.is_active = formObject.is_active;
          if (formObject.default_preparation_time !== null && formObject.default_preparation_time !== undefined) body.default_preparation_time = formObject.default_preparation_time;


            api
              .patch("customer/register/log", body)
                .then((hubResponse) => {
                    localStorage.setItem('created_hub_id', hubResponse.id);
                    toast.success(t('Hub created'));
                     router.push({ name: "RegisterStep3" });
                    emit('openNextTab')
                })
                .catch((err) => toast.error(err.data.message))
                .finally(() => emit('hideLoader'));
        }
    );
}

function resolveAddressFromCoordinates(lat, lng) {
    lat = parseFloat(lat.toFixed(8));
    lng = parseFloat(lng.toFixed(8));
    return new Promise((resolve) => {
        new google.maps.Geocoder().geocode(
            { location: { lat, lng } },
            (results, status) => {
                if (status === "OK") {
                    if (results[0]) {
                        resolve(results[0].formatted_address);
                    }
                }
            }
        );
    });
}

const setfileldHub = () => {
  if (me.value && me.value.register_log && me.value.register_log.data && me.value.register_log.data.hub) {
   hubForm.hubName = me.value.register_log.data.hub.name;
    location.lat = Number(me.value.register_log.data.hub.lat);
    location.lng = Number(me.value.register_log.data.hub.lng);
    location.searchAddress = me.value.register_log.data.hub.address

  }
};

const mapLoader = new Loader({
  apiKey: import.meta.env.VITE_GOOGLE_API_KEY,
  version: "beta",
  libraries: ["geometry"],
});

onMounted(() => {
  setfileldHub()
    mapLoader.load().then(() =>{
      showMap()
    } );
});


const rules = reactive({
    hubName: [
        {
            required: true,
            message: t('Required'),
            trigger: "blur",
        },
    ],
    integration_id: [
        {
            required: true,
            message: t('Required'),
            trigger: "blur",
        },
    ],
    searchAddress: [
        {
            required: true,
            message: t('Required'),
            trigger: "blur",
        },
    ],
});

</script>

<template>
    <div>
        <el-form
            label-position="top"
            class="mt-7 space-y-6"
            :rules="rules"
        >
            <div class="flex flex-row w-full gap-6">
                <el-form-item
                    :label="t('Hub Name')"
                    required
                    class="w-1/2"
                >
                    <el-input
                        v-model="hubForm.hubName"
                        :placeholder="t('Name')"
                    />
                </el-form-item>
            </div>
        <PinMap
            v-show="mapVisible"
            v-model="location"
            :poligonVisible="false"
        />
    </el-form>
</div>
<div class="w-full align-end justify-end flex mt-10">
    <el-button
        @click="onSubmit"
        type="primary"
    >
        {{ t('Next') }}
        <FontAwesomeIcon
            icon="chevron-right"
            class="ml-2"
        />
    </el-button>
</div></template>