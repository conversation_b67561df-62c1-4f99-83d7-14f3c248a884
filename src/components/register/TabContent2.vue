<script setup>
import { reactive, ref, inject, watch, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import TabTemplate from "./TabTemplate.vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useToast } from "vue-toastification";
import router from "@/router";
import CreateHubStep from "./contents/CreateHubStep.vue";
import PhoneInputExtended from "../ui/PhoneInputExtended.vue";
import VehicleSelectBox from "../ui/VehicleSelectBox.vue";
import { useStore } from "vuex";
import PaymentDialog from "@/components/register/PaymentDialog.vue";
import formatter from "@/class/formatter";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import CreateHubStep2 from "@/components/register/contents/CreateHubStep2.vue";


const { dispatch, getters } = useStore();

const props = defineProps(["activeTab", "lang"]);
const emit = defineEmits(["setTab"]);
const { t } = useI18n();
const api = inject("api");
const loaderRef = ref();
const toast = useToast();
const loader = ref();
const paymentDialogVisible = ref(false);
const customMValue = ref();
const sectorList = ref([]);


const courierCountList = [
  { value: 1, label: "1" },
  { value: 2, label: "2-5" },
  { value: 3, label: "6-15" },
  { value: 4, label: "15-30" },
  { value: 5, label: "30-60" },
  { value: 6, label: "60+" }
];


const plan = ref();

const startM = ref();
const startY = ref();
const scaleM = ref();
const scaleY = ref();
const customM = ref();
const customY = ref();
const planIdes = ref();


const timeZones = ref([]);
const countryList = ref();
const currencyList = ref();
const measurementList = ref();
const dateFormatList = ref();
const radio1 = ref("first");

const me = computed(() => getters["auth/me"]);

const showLoader = () => loaderRef.value.show();
const hideLoader = () => loaderRef.value.hide();

const companyForm = reactive({
  country: null,
  timeZone: null,
  measurementUnit: "metric",
  legalName: null,
  shortName: null,
  address: null,
  email: me.email || null,
  dateFormat: "european",
  currencyUnit: "TRY",
  courier_count: 0,
  sector: null
});

const newTeamName = ref("");
const driverSectionVisible = ref(false);

onMounted(() => {
  customMValue.value = localStorage.getItem("plan");
  getCurrencyUnitSelect();
  getMeasurementSelect();
  getCountrySelect();
  getDateFormat();
  getSector();
});

const getSector = () => {
  return api.post('components/industry-type-select')
    .then((r)=>{
      sectorList.value = r
    })
};


const getCountrySelect = () => {
  loaderRef.value.show();
  api.post("components/country-select", { lang: props.lang })
    .then((r) => {
      countryList.value = r.data;

    })
    .finally(() => loaderRef.value.hide());
};

const getDateFormat = () => {
  loaderRef.value.show();
  api.post("components/date-format-select", { lang: props.lang })
    .then((r) => {
      dateFormatList.value = r.data;
    }).finally(() => loaderRef.value.hide());
};

const getMeasurementSelect = () => {
  loaderRef.value.show();
  api.post("components/measurement-select", { lang: props.lang })
    .then((r) => {
      measurementList.value = r.data;
    }).finally(() => loaderRef.value.hide());
};

const getCurrencyUnitSelect = () => {
  loaderRef.value.show();
  api.post("components/currency-units-select", { lang: props.lang })
    .then((r) => {
      currencyList.value = r.data;
    }).finally(() => loaderRef.value.hide());
};


watch(() => companyForm.country, () => {
  timeZones.value = countryList.value.find((c) => c.code === companyForm.country)?.timezones;

  companyForm.timeZone = timeZones.value?.[0]?.name;
});


watch(() => props.activeTab, () => {

  if (props.activeTab === "step4") {
    getPlan();

  }
});

watch(() => props.lang, () => {
  getCurrencyUnitSelect();
  getMeasurementSelect();
  getCountrySelect();
  getDateFormat();
  if (props.activeTab === "step4") {
    getPlan();
  }


});


const onTeamSubmit = () => {
  loaderRef.value.show();
  const hub_id = localStorage.getItem("created_hub_id");
  api
    .post("customer/teams", { name: newTeamName.value, hub_id })
    .then((teamResponse) => {
      localStorage.setItem("created_team_id", teamResponse.id);
      localStorage.removeItem("created_hub_id");
      toast.success(t("Team created"));
      driverSectionVisible.value = true;
      emit("setTab", "step4");
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => loaderRef.value.hide());
};


watch(() => companyForm.legalName, () => {
  if (companyForm.legalName == "") {
    companyForm.shortName = "";
  } else if (companyForm.legalName?.split(" ")?.[0]) {
    companyForm.shortName = companyForm.legalName?.split(" ")?.[0];
  }
});

const onCompanySubmit = () => {
  loaderRef.value.show();

  let formData = new FormData();
  formData.append("name", companyForm.legalName);
  formData.append("short_name", companyForm.shortName);
  formData.append("email", companyForm.email);
  formData.append("timezone", companyForm.timeZone);
  formData.append("unit_slug", companyForm.measurementUnit);
  formData.append("country_code", companyForm.country);
  formData.append("date_format_slug", companyForm.dateFormat);
  formData.append("currency_unit", companyForm.currencyUnit);
  formData.append("sector", companyForm.sector);
  formData.append("courier_count", companyForm.courier_count);

  api
    .post("customer/companies", formData)
    .then((companyResponse) => {
      const successMessage = t("Workspace created");
      // console.log(' CREATE RES : ', companyResponse);
      const newCompanyId = companyResponse.id;
      //
      api.put("customer/active-companies/" + newCompanyId).then((activeRes) => {
        // console.log(' active res : ', activeRes);
        toast.success(successMessage);
        emit("setTab", "step2");
      }).catch((err) => {
        toast.warning(err);
      });
      //

    })
    .catch((err) => toast.error(err.data?.message))
    .finally(() => (
      loaderRef.value.hide()
    ));
};


const driver_phone = reactive({
  country: "TR",
  phone: null
});

const filterMethod = (searchText, isCountry = false) => {
  if (isCountry) {
    countryList.value = countryList.value?.map((tz) => {
      if (tz.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1) {
        return { ...tz, hide: false };
      } else {
        return { ...tz, hide: true };
      }
    }) || [];
  } else {
    timeZones.value = timeZones.value?.map((tz) => {
      if (tz.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1) {
        return { ...tz, hide: false };
      } else {
        return { ...tz, hide: true };
      }
    }) || [];
  }
};

const getPlan = () => {
  loaderRef.value.show();

  api("customer/plans?filter[plan_no]=start_m,scale_m,custom_m,start_y,scale_y,custom_y")
    .then((r) => {
      planIdes.value = r.data.map((i) => i.id);

      const customMValue = localStorage.getItem("plan");


      startM.value = r.data.find((i) => i.plan_no === "start_m");
      startY.value = r.data.find((i) => i.plan_no === "start_y");
      scaleM.value = r.data.find((i) => i.plan_no === "scale_m");
      scaleY.value = r.data.find((i) => i.plan_no === "scale_y");
      customM.value = r.data.find((i) => i.plan_no === "custom_m");
      customY.value = r.data.find((i) => i.plan_no === "custom_y");

    })
    .catch((err) => {
      toast.error(err.data.message);
    })
    .finally(() => loaderRef.value.hide());
};

const openPaymentDialog = (item) => {

  plan.value = item;
  paymentDialogVisible.value = true;
};

</script>
<template>
  <LoadingBlock ref="loaderRef" />
  <TabTemplate :title="t('Company Information')"
               :isActive="activeTab === 'step1'">
    <div>
      <el-form label-position="top"
               class="mt-7 space-y-12">
        <div class="flex flex-col lg:flex-row w-full gap-6">
          <el-form-item :label="t('Company Brand Name')"
                        required
                        class="w-full-important">
            <el-input v-model="companyForm.legalName"
                      :placeholder="t('Enter your company brand name')" />
          </el-form-item>
          <el-form-item :label="t('Company Short Name')"
                        required
                        class="w-full-important">
            <el-input v-model="companyForm.shortName"
                      :placeholder="t('Enter your company short name')"></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-col lg:flex-row w-full gap-6">
          <el-form-item :label="$t('Country')"
                        required
                        class="w-full-important">
            <el-select v-model="companyForm.country"
                       filterable
                       :placeholder="t('Country')"
                       class="w-full-important"
                       :filterMethod="(st) => filterMethod(st, true)">
              <el-option v-for="item in countryList?.filter((tz) => !tz.hide)"
                         :key="item.code"
                         :label="item.name"
                         :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="t('Company Corporate Email')"
                        required
                        class="w-full-important">
            <el-input v-model="companyForm.email"
                      type="email"
                      placeholder="Email"></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-col lg:flex-row w-full gap-6">
          <el-form-item :label="$t('Sector')"
                        required
                        class="w-full-important">
            <el-select v-model="companyForm.sector"
                       filterable
                       :placeholder="t('Sector')"
                       class="w-full-important"
            >
              <el-option v-for="item in sectorList"
                         :key="item.slug"
                         :label="item.name"
                         :value="item.slug">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="t('Driver Count')"
                        required
                        class="w-full-important">
            <el-select v-model="companyForm.courier_count"
                       filterable
                       :placeholder="t('Driver Count')"
                       class="w-full-important"
            >
              <el-option v-for="item in courierCountList"
                         :key="item.value"
                         :label="item.label"
                         :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex flex-col lg:flex-row w-full gap-6">
          <div class="flex w-full justify-between gap-2">
            <el-form-item class="w-full" :label="$t('Time Zone')"
                          required>
              <el-select v-model="companyForm.timeZone"
                         filterable
                         :placeholder="t('Time Zone')"

                         :filterMethod="filterMethod">
                <el-option v-for="item in timeZones?.filter((tz) => !tz.hide)"
                           :key="item.slug"
                           :label="`${item.name} (${item.utc_offset})`"
                           :value="item.slug">
                </el-option>

              </el-select>

            </el-form-item>
            <el-form-item class="w-full" :label="$t('Date Format')"
                          required>
              <el-select v-model="companyForm.dateFormat"
                         filterable
                         :placeholder="t('Date Format')">

                <el-option v-for="item in dateFormatList"
                           :key="item.slug"
                           :label="item.format"
                           :value="item.slug">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="flex flex-row w-full justify-between gap-2">
            <el-form-item class="w-full" :label="$t('Distance Unit')"
                          required>
              <el-select v-model="companyForm.measurementUnit"
                         filterable
                         :placeholder="t('Distance Unit')">

                <el-option v-for="item in measurementList"
                           :key="item.slug"
                           :label="item.name"
                           :value="item.slug">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item class="w-full" :label="$t('Currency Unit')"
                          required>
              <el-select v-model="companyForm.currencyUnit"
                         filterable
                         :placeholder="t('Currency Unit')">

                <el-option v-for="item in currencyList"
                           :key="item.unit"
                           :label="item.description"
                           :value="item.unit">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>

      <div class="w-full align-end justify-end flex mt-20">
        <el-button @click="onCompanySubmit"
                   type="primary">
          {{ t("Next") }}
          <FontAwesomeIcon icon="chevron-right"
                           class="ml-2" />
        </el-button>
      </div>
    </div>
  </TabTemplate>

  <TabTemplate :title="t('Create Hub')"
               :isActive="activeTab === 'step2'">
    <CreateHubStep2
      @showLoader="showLoader"
      @hideLoader="hideLoader"
      @openNextTab="() => emit('setTab', 'step3')"
    />

  </TabTemplate>

  <TabTemplate :title="t('Create Team')"
               :isActive="activeTab === 'step3'">
    <div>
      <el-form label-position="top"
               class="mt-7 space-y-6">
        <div class="flex flex-row w-full gap-6">
          <el-form-item :label="t('Team Name')"
                        required
                        class="w-full-important">
            <el-input v-model="newTeamName"
                      :placeholder="t('Team Name')"
                      :disabled="driverSectionVisible" />
          </el-form-item>
          <div class="w-full align-end justify-end flex">
            <el-button type="primary"
                       class="mt-auto"
                       @click="onTeamSubmit"
                       v-show="!driverSectionVisible">
              {{ t("Create Team") }}
              <FontAwesomeIcon icon="fa-grid-2-plus"
                               class="ml-3" />
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
<!--    <div v-show="driverSectionVisible">-->
<!--      <div class="border-t text-indigo-600 font-semibold text-lg pb-4 mb-2 mt-8 pt-6">-->
<!--        {{ t("Invite Driver") }}-->
<!--      </div>-->
<!--      <el-form label-position="top"-->
<!--               class="mt-7 space-y-6">-->
<!--        <div class="flex flex-row w-full gap-6">-->
<!--          <el-form-item :label="t('Driver Name Surname')"-->
<!--                        required-->
<!--                        class="w-full-important">-->
<!--            <el-input v-model="courierForm.courierNameSurname"-->
<!--                      :placeholder="t('Enter driver name surname')" />-->
<!--          </el-form-item>-->
<!--          <el-form-item :label="t('Vehicle Type')"-->
<!--                        class="w-full-important">-->
<!--            <VehicleSelectBox v-model="courierForm.vehicleType"-->
<!--                              url="components/vehicle-type-select"-->
<!--                              method="post"-->
<!--                              :body="true"-->
<!--                              placeholder="Vehicle Type"-->
<!--                              key="slug"-->
<!--                              :lang="props.lang"-->
<!--                              value="slug" />-->
<!--          </el-form-item>-->
<!--        </div>-->
<!--        <PhoneInputExtended v-model="driver_phone"-->
<!--                            class="w-full lg:w-1/2 pr-4" />-->

<!--      </el-form>-->
<!--    </div>-->
<!--    <div v-show="driverSectionVisible"-->
<!--         class="w-full align-end justify-end flex mt-20">-->
<!--      <el-button type="primary"-->
<!--                 @click="inviteCourier">-->
<!--        {{ t("Next") }}-->
<!--        <FontAwesomeIcon icon="chevron-right"-->
<!--                         class="ml-2" />-->
<!--      </el-button>-->
<!--      <el-button type="secondary"-->
<!--                 @click="skipInviteCourier">-->
<!--        {{ t("Skip for now") }}-->
<!--        <FontAwesomeIcon icon="chevron-right"-->
<!--                         class="ml-2" />-->
<!--      </el-button>-->
<!--    </div>-->
  </TabTemplate>

  <TabTemplate :title="t('Start your trial period now')" :isActive="activeTab === 'step4'">
    <div class="flex flex-1 w-full flex-col h-full ">
      <div class="text-slate-700 my-3 font-light">
        <span style="font-size: 17px">
          {{ t(`during_14_trial`) }}
        </span>
        <div class="mt-5" v-if="companyForm.country ==='TR'">
          {{ t(`for_questions_about`) }}
        </div>
        <div v-if="companyForm.country ==='TR'" class="w-full align-end justify-end flex mt-16">
          <el-button type="primary" @click="() => router.push({ name: 'Home' })">
            {{ t("Start QDelivery") }}
            <FontAwesomeIcon icon="chevron-right" class="ml-2" />
          </el-button>
        </div>
      </div>
      <div class="w-full flex items-center justify-center py-3" v-if="companyForm.country !=='TR'">
        <el-tabs v-model="radio1" type="card" class="custom-register-tabs demo-tabs w-full">
          <el-tab-pane :label="t('Monthly')" name="first">
            <div v-if="companyForm.country !=='TR'"
                 class="container mx-auto p-4 pt-4 w-full flex justify-center items-center h-full">
              <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-4 h-full">
                <div class="h-full">
                  <div :class="customMValue === 'start_m' ? 'border-indigo-600 border-2' : ''"
                       class="border rounded-xl mb-1.5 p-6 shadow-md h-60 flex flex-col items-start justify-between">
                    <div class="text-md" style="color: #00DC78">{{ startM?.name }}</div>
                    <div class="mt-4 text-2xl">
                      {{ formatter.currency(startM?.price, startM?.currency_unit) }}/<span class="text-xl">{{ t("mo")
                      }}</span>
                    </div>
                    <div class="ml-2 text-gray-600 text-sm" style="color:#A0A1A2"> ({{ t("per driver") }})</div>
                    <div class="mt-2 flex-grow" style="color:#A0A1A2">
                      {{ t("All necessary features in one package") }} {{ t("For first timer") }}
                    </div>
                    <div class="w-full justify-center item-center px-7">
                      <button @click="openPaymentDialog(startM)"
                              class="w-full text-white py-2 px-4 rounded hover:bg-green-600 rounded-full"
                              style="background: #00DC78">{{ t("Get Started") }}
                      </button>
                    </div>
                  </div>
                </div>
                <div class="h-full">
                  <div :class="customMValue === 'scale_m' ? 'border-indigo-600 border-2' : ''"
                       class="border rounded-xl mb-1.5 p-6 shadow-md h-60 flex flex-col items-start justify-between">
                    <div class="text-md" style="color:#8684F5">{{ scaleM?.name }}</div>
                    <div class="mt-4 text-2xl">
                      {{ formatter.currency(scaleM?.price, scaleM?.currency_unit) }}/<span class="text-xl">{{ t("mo")
                      }}</span>
                    </div>
                    <div class="ml-2 text-gray-600 text-sm" style="color:#A0A1A2"> ({{ t("per driver") }})</div>
                    <div class="mt-2 flex-grow" style="color:#A0A1A2">
                      {{ t("Advanced feature-set for scaling your business") }}
                    </div>
                    <div class="w-full justify-center item-center px-7">
                      <button @click="openPaymentDialog(scaleM)"
                              class="w-full text-white py-2 px-4 rounded hover:bg-green-600 rounded-full"
                              style="background: #8684F5">{{ t(`Get Started`) }}
                      </button>
                    </div>
                  </div>
                  <!-- ... other code ... -->
                </div>
                <div class="h-full">
                  <div :class="customMValue === 'custom_m' ? 'border-indigo-600 border-2' : ''"
                       class="border rounded-xl mb-1.5 p-6 shadow-md h-60 flex flex-col items-start justify-between">
                    <div class="text-md" style="color: #132D95">{{ customM?.name }}</div>
                    <div class="mt-4 text-2xl mb-5">Custom</div>
                    <div class="mt-2 flex-grow" style="color:#A0A1A2">
                      {{ t("Custom-tailored solutions for established distribution operations") }}
                    </div>
                    <div class="w-full justify-center item-center px-7">
                      <button @click="openPaymentDialog(customM)"
                              class="w-full text-white py-2 px-4 rounded hover:bg-green-600 rounded-full"
                              style="background: #132D95">{{ t(`Get Started`) }}
                      </button>
                    </div>
                  </div>
                  <!-- ... other code ... -->
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="t('Yearly')" name="second">
            <div v-if="companyForm.country !=='TR'"
                 class="container mx-auto p-4 pt-4 w-full flex justify-center items-center">
              <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-4 h-full">
                <div class="h-full">
                  <div :class="customMValue === 'start_y' ? 'border-indigo-600 border-2' : ''"
                       class="border rounded-xl p-6 shadow-md h-60 flex flex-col items-start justify-between mb-1.5">
                    <div class="text-md" style="color: #00DC78">{{ startY?.name }}</div>
                    <div class="mt-4 text-2xl">
                      {{ formatter.currency(startY?.price / 12, startY?.currency_unit) }}/<span
                      class="text-xl">{{ t("mo") }}</span>
                    </div>
                    <div class="ml-2 text-gray-600 text-sm" style="color:#A0A1A2"> ({{ t("per driver") }})</div>
                    <div class="mt-2 flex-grow" style="color:#A0A1A2">
                      {{ t("All necessary features in one package") }} {{ t("For first timer") }}
                    </div>
                    <div class="w-full justify-center item-center px-7">
                      <button @click="openPaymentDialog(startY)"
                              class="w-full text-white py-2 px-4 rounded hover:bg-green-600 rounded-full"
                              style="background: #00DC78">{{ t(`Get Started`) }}
                      </button>
                    </div>
                  </div>
                  <!-- ... other code ... -->
                </div>
                <div class="h-full">
                  <div :class="customMValue === 'scale_y' ? 'border-indigo-600 border-2' : ''"
                       class="border rounded-xl p-6 shadow-md h-60 flex flex-col items-start justify-between mb-1.5">
                    <div class="text-md" style="color:#8684F5">{{ scaleY?.name }}</div>
                    <div class="mt-4 text-2xl">
                      {{ formatter.currency(scaleY?.price / 12, scaleY?.currency_unit) }}/<span
                      class="text-xl">{{ t("mo") }}</span>
                    </div>
                    <div class="ml-2 text-gray-600 text-sm" style="color:#A0A1A2"> ({{ t("per driver") }})</div>
                    <div class="mt-2 flex-grow" style="color:#A0A1A2">
                      {{ t("Advanced feature-set for scaling your business") }}
                    </div>
                    <div class="w-full justify-center item-center px-7">
                      <button @click="openPaymentDialog(scaleY)"
                              class="w-full text-white py-2 px-4 rounded hover:bg-green-600 rounded-full"
                              style="background: #8684F5">{{ t(`Get Started`) }}
                      </button>
                    </div>
                  </div>
                  <!-- ... other code ... -->
                </div>
                <div class="h-full">
                  <div :class="customMValue === 'custom_y' ? 'border-indigo-600 border-2' : ''"
                       class="border rounded-xl p-6 shadow-md h-60 flex flex-col items-start justify-between mb-1.5">
                    <div class="text-md" style="color: #132D95">{{ customY?.name }}</div>
                    <div class="mt-4 text-2xl mb-5">Custom</div>
                    <div class="mt-2 flex-grow" style="color:#A0A1A2">
                      {{ t("Custom-tailored solutions for established distribution operations") }}
                    </div>
                    <div class="w-full justify-center item-center px-7">
                      <button @click="openPaymentDialog(customY)"
                              class="w-full text-white py-2 px-4 rounded hover:bg-green-600 rounded-full"
                              style="background: #132D95">{{ t(`Get Started`) }}
                      </button>
                    </div>
                  </div>
                  <!-- ... other code ... -->
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <el-dialog v-model="paymentDialogVisible" class="el-custom-dialog full_screen" :title="t('Payment Entry')"
               width="659" destroy-on-close>
      <PaymentDialog :planIdes="planIdes" :companyForm="companyForm" :plan="plan" />
    </el-dialog>
  </TabTemplate>

</template>

<style scoped>
.text-indigo-600 {
  padding-bottom: 12px;
}

.container {
  max-width: 1200px;
}

.grid > div {
  min-height: 200px;
}


.el-tabs__content {
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>