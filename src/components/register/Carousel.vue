
<script setup>
import { Carousel, Pagination, Slide } from 'vue3-carousel'
import slider1 from '@/assets/carousel/slider_1.gif';
import slider2 from '@/assets/carousel/slider_2.gif';
import slider3 from '@/assets/carousel/slider_3.gif';
import { useI18n } from 'vue-i18n';
import 'vue3-carousel/dist/carousel.css';

const { t } = useI18n()

const items = [slider1, slider2, slider3
];

const textList = [
    "_Flexible Connection",
    "_Real-Time Tracking",
    "_Autopilot Mode",
];

</script>
<template>
    <div class="hidden lg:flex lg:w-1/2 bg-indigo-600 h-auto justify-center align-center relative">
        <img
            src="@/assets/images/thunder1.png"
            class="w-4/6 object-contain absolute -right-24 -top-4"
        />
        <img
            src="@/assets/images/poly3.png"
            style="width:100px; left: -24px;bottom:0;"
            class="object-contain absolute bottom-2"
        />
        <div class="flex flex-col items-center align-center justify-center w-full pb-12">
            <Carousel
                style="width:510px; height:464px; z-index: 20;"
                :autoplay="4000"
                :pauseAutoplayOnHover="true"
                :wrapAround="true"
            >
                <Slide
                    style="height: 100%; width: 100%"
                    v-for="slide in 3"
                    :key="slide"
                >
                    <div class="flex flex-col w-full h-full">

                        <div class="carousel__item text-white">
                            <img
                                :src="items[slide - 1]"
                                class="w-full h-full flex flex-1"
                            />
                        </div>

                        <div class="text-xl text-white mb mt-4">
                            <span class="text-xl text-white text-bold">
                                {{ t(textList[slide - 1]) }}
                            </span>
                        </div>
                    </div>

                </Slide>

                <template #addons>
                    <!-- <Navigation /> -->
                    <Pagination />
                </template>
            </Carousel>
        </div>
    </div>
</template>
  
<style>
.carousel__item {
    height: 100%;
    width: 100%;
    background-color: var(--vc-clr-primary);
    color: var(--vc-clr-white);
    font-size: 20px;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.carousel__viewport,
.carousel__slide,
.carousel__track {
    height: 100%;
}

.carousel__pagination-item .carousel__pagination-button {
    width: 24px !important;
    height: 24px !important;
    background-color: #0C3FD5 !important;
    display: block;
    border-radius: 8px;
    margin-left: 5px;
    margin-top: 12px;
}

.carousel__pagination-item .carousel__pagination-button--active {
    width: 50px !important;
}

.carousel__pagination-button::after {
    visibility: hidden;
}
</style>
  
