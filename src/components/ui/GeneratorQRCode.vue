<script setup>
import QRCode from "qrcode";
import {nextTick, onMounted} from "vue";


const props = defineProps({
  integrationId: {type: String}
})

const createQRCode = () => {
  QRCode.toCanvas(document.getElementById('canvas'), props.integrationId, function (err, url) {})
}

onMounted(() => {
  nextTick(()=>{
    createQRCode()
  })
})

</script>

<template>
  <div class="flex items-center justify-center">
    <canvas id="canvas"></canvas>
  </div>
</template>
