<template>
  <transition
    enter-active-class="ease-out duration-300"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="ease-in duration-200"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isProcessing"
      class="absolute left-0 top-0 z-30 w-full h-full flex items-center justify-center bg-white bg-opacity-75"
    >
      <img src="../../assets/images/loading.svg" class="h-4" alt />
    </div>
  </transition>
</template>

<script>
import { defineComponent, ref } from "vue";

export default defineComponent({
  props: {
    processing: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const isProcessing = ref(props.processing);

    function show() {
      isProcessing.value = true;
    }

    function hide() {
      isProcessing.value = false;
    }

    return {
      show,
      hide,
      isProcessing,
    };
  },
});
</script>
