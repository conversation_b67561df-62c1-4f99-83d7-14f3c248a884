<script setup>
import {inject} from "vue";
import {useStore} from "vuex";

const props = defineProps({
  courier: {
    type: Object,
    default: {},
  },
  removeButtonVisible: {type: Boolean, default: true}
});
const emit = defineEmits(["clearSelectedCourier"]);
const {dispatch, getters} = useStore();
const emitter = inject("emitter")

const clearCourier = () => {
  emit("clearSelectedCourier");
  dispatch("tasks/resetTasksFilter")
  // Clear selected courier from tabs
  // emitter.emit("clear_destination_marker")
};
</script>

<template>
  <div
      class="flex items-center text-slate-700 boder-l  border-r border-slate-300 px-2 py-1 bg-white"
  >
    <div>
      <img
          class="rounded-full z-0 h-7 w-7 mx-2"
          alt="CourierAvatar"
          :src="courier.avatar_url"
      />
    </div>
    <div class="flex flex-col">
      <div class="text-xs">{{ courier.name }}</div>
      <div class="text-xxs">{{ courier.current_task_capacity_status }}</div>
    </div>
    <span
        v-show="removeButtonVisible"
        @click="clearCourier"
        aria-label="Kapat"
        class="flex ml-4 transition cursor-pointer"
        type="button"
    >
        <FontAwesomeIcon class="text-slate-300 text-lg" icon="times"/>
      </span>
  </div>
</template>
