<script setup>
import {inject, ref} from "vue";
import Toaster from "@/components/ui/Toaster.vue";

const messages = ref([]);
const style = ref();

const emitter = inject("emitter");
// emitter.on("toaster", (param) => {
//   let id = Math.random()
//   let _message = {
//     style: param.type === "danger" ? "bg-red-600" : "bg-green-600",
//     message: param.message,
//     id,
//   }
//   messages.value.push(_message)
// });

const onRemove = (id) => {
  messages.value = messages.value.filter((c) => c.id !== id)
}
</script>

<template>
  <div v-if="messages.length > 0" v-for="(item,index) in messages">
    <Toaster :item="item" :index="index" @remove="onRemove"/>
  </div>
</template>
