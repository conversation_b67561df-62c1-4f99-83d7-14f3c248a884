<script setup>
import { inject, computed,watch} from "vue";
import { useGetData } from "@/composables/useGetData";
import { VehicleTypeIcon } from "@/class";

const api = inject("api")
// to do : should be simpler acc. to need
const emit = defineEmits(["update:modelValue", "select", "change"]);
const props = defineProps({
  url: { type: String },
  key: { type: String, default: "id" },
  lang:{ type: [Number, String, Array, Date], default: null},
  value: { type: String, default: "id" },
  label: { type: String, default: "name" },
  method: { type: String, default: "get" },
  placeholder: { type: String, default: "" },
  filterable: { type: Boolean, default: false },
  multiple: { type: Boolean, default: false },
  multipleLimit: { type: Number, default: 0 },
  modelValue: { type: [Number, String, Array, Date], default: "" },
  body:{type:<PERSON>olean,default:false}
})

watch(()=>props.lang,()=>{
  getData()
})

const { data, loading } = useGetData(props.url, props.method)

const val = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const getData = () => {
  if (!props.body){
    loading.value = true
    api.post(props.url)
      .then((response) => data.value = response.data)
      .finally(() => loading.value = false)
  }
  if (props.body){
    loading.value = true
    api.post(props.url,{lang:props.lang})
      .then((response) => data.value = response.data)
      .finally(() => loading.value = false)
  }
}

const onChange = (data) => {
  emit("change", data)
  props.multiple && emit("select", data.find(x => x[props.value] === data))
};
</script>

<template>
  <el-select
    v-model="val"
    :placeholder="$t(placeholder)"
    :filterable="filterable"
    :multiple="multiple"
    :loading="loading"
    :loading-text="$t('Loading')"
    class="w-full-important"
    @change="onChange"
    :multiple-limit="multipleLimit"
  >
    <el-option
      v-for="item in data"
    :key="item[key]"
    :value="item[value]"
    :label="item[label]"
  >
    <div class="flex flex-row align-center">
      <img
        :src="VehicleTypeIcon[item[value]]"
        class="mr-4"
        style="object-fit: contain; width: 8%;"
      />
      <div class="align-center">{{ item[label] }}</div>
    </div>
  </el-option>
</el-select></template>
