<script setup>
import {computed, ref} from 'vue';
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import dayjs from "dayjs";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {type: [Number, String, Array, Date, Object], default: ""},
});

const date = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const datePicker = ref()

const onFocus = () => {
  datePicker.value.focus()
}

</script>

<template>
  <div class="w-full h-full flex flex-col items-end justify-start">
    <el-date-picker
        style="visibility: hidden; top:30px;height: 0!important;  width: 90px!important;"
        ref="datePicker"
        v-model="date"
        size="small"
        :clearable="false"
        :editable="false"
        format="DD MMM"
    />
    <el-button @click="onFocus" class="text-slate-700">
      <template #icon>
        <FontAwesomeIcon icon="calendar-days"/>
      </template>
      <span class="ml-2 ">
        {{ dayjs(date).format("DD MMM") }}
      </span>
    </el-button>
  </div>
</template>
