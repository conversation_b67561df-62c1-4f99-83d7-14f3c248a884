<template>
  <transition
    enter-active-class="ease-out duration-300"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="ease-in duration-200"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="show && message"
      :class="{
        'bg-green-500': style === 'success',
        'bg-red-700': style === 'danger',
      }"
    >
      <div class="max-w-screen-xl mx-auto py-1 px-4">
        <div class="flex items-center justify-between flex-wrap">
          <div class="w-0 flex-1 flex items-center min-w-0">
            <span class="flex">
              <FontAwesomeIcon
                v-if="style === 'success'"
                class="text-white"
                icon="check-circle"
              />
              <FontAwesomeIcon
                v-if="style === 'danger'"
                class="text-white"
                icon="exclamation-triangle"
              />
            </span>

            <p class="ml-3 font-medium text-sm text-white truncate">
              {{ message }}
            </p>
          </div>

          <div class="flex-shrink-0 sm:ml-3">
            <span
              aria-label="Kapat"
              :class="{
                'hover:bg-green-600 focus:bg-green-600': style === 'success',
                'hover:bg-red-600 focus:bg-red-600': style === 'danger',
              }"
              class="-mr-1 flex p-2 rounded focus:outline-none sm:-mr-2 transition cursor-pointer"
              type="button"
              @click.prevent="show = false"
            >
              <FontAwesomeIcon class="text-white" icon="times" />
            </span>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { defineComponent, ref, inject } from "vue";

export default defineComponent({
  setup() {
    const emitter = inject("emitter");
    const show = ref(false);
    const style = ref(null);
    const message = ref(null);

    emitter.on("message", (data) => {
      style.value = data.type;
      message.value = data.message;
      show.value = true;
    });

    return {
      show,
      style,
      message,
    };
  },
});
</script>