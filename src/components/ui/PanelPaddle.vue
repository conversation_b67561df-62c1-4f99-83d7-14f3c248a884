<template>
  <div
      class="flex items-end cursor-pointer"

  >

    <img class="h-7.5 w-6" v-if="$props.position === 'left' && !$props.isPanelOpen" src="../../assets/images/right-up.png"/>
    <img class="h-7.5 w-6"  v-if="$props.position === 'left' && $props.isPanelOpen" src="../../assets/images/right-down.png"/>
    <img class="h-7.5 w-6"  v-if="$props.position === 'right' && $props.isPanelOpen" src="../../assets/images/left-down.png"/>
    <img class="h-7.5 w-6"  v-if="$props.position === 'right' && !$props.isPanelOpen" src="../../assets/images/left-up.png"/>
    <img class="h-6 w-7.5"  v-if="$props.position === 'ground' && $props.isPanelOpen" src="../../assets/images/ground-down.png"/>
    <img class="h-6 w-7.5"  v-if="$props.position === 'ground' && !$props.isPanelOpen" src="../../assets/images/ground-up.png"/>

  </div>
</template>

<script>
export default {
  name: "PanelPaddle",
  props: {
    isPanelOpen: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: "up",
    },
    secondary: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    return {};
  },
};
</script>
