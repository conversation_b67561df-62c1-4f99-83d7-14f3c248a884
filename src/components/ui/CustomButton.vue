<script setup>


const emit = defineEmits(["click"])
const props = defineProps({
  title: {type: String},
  size: {type: String, default: "medium"}
})

const getSize = () => {
  let sizeClass = ""

  if (props.size === "medium") {
    sizeClass = 'h-8 px-3 text-sm'
  }

  if (props.size === "small") {
    sizeClass = 'h-7 px-3 py-2 text-xs'
  }
  if (props.size === "mini") {
    sizeClass = 'h-6 px-2 py-1.5 text-xxs'
  }

  return sizeClass
}

</script>

<template>
  <div
      @click="emit('click')"
      class="custom-button__color flex items-center justify-between rounded cursor-pointer"
      :class="[getSize()]"
  >
    <slot/>
    <div class="text-white font-bold ml-2">
      {{ title }}
    </div>
  </div>
</template>
