<script setup>
import {ref } from "vue";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: { type: [Number, String, Array, Date], default: "" },
});
const fileSelect = document.getElementById("fileSelect")
const file = ref()
const img = ref()
const addFile = () => {
  console.log("addFile")
  if (fileElem) {
    fileElem.click();
  }
}
const selectedFiles = (e) => {
  file.value = e.target.files[0]
  img.value = URL.createObjectURL(e.target.files[0]);
  emit("update:modelValue", file.value);

}
const test = () => {
  file.value = null
  img.value = null
  document.getElementById('fileElem').value = "";
  emit("update:modelValue", file.value);

}
</script>

<template>
  <input @change="selectedFiles" type="file" id="fileElem" multiple accept="image/*" style="display:none">
  <div v-if="!file" class="flex items-center justify-center h-20 w-20 border border-slate-300 rounded cursor-pointer text-slate-700" @click="addFile" >
    <FontAwesomeIcon icon="plus"/>
  </div>
  <div v-else class="flex items-center justify-center h-20 w-20 border border-slate-300 rounded relative" >
    <img :src="img">
    <div class="absolute cursor-pointer pick-image-remove-button text-slate-700" @click="test">
      <FontAwesomeIcon icon="times-circle"/>
    </div>
  </div>
</template>
