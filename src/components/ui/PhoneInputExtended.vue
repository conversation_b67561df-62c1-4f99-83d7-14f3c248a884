<script setup>
import { computed } from "vue";
import { countryList } from "@/class/countryList";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: { type: [Number, String, Array, Date, Object] },
  visibleLabel: { type: Boolean, default: true },
  disabled: { type: Boolean, default: false },
  hasSpace: { type: Boolean }
});


const form = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

</script>


<template>
  <div class="flex">
    <div
      style="width: 7rem!important"
      class="left_cont"
    >
      <el-form-item
        :label="$t('Country')"
        required
        style="border-radius: 0 !important;"
      >
        <el-select
          :disabled="props.disabled"
          v-model="form.country"
          :placeholder="$t('Country')"
          class="w-full-important"
          style="border-radius: 0 !important;"
        >
          <el-option
            v-for="item in countryList"
            :key="item.code"
            :label="`${item.emoji} ${item.dial_code}`"
            :value="item.code"
            style="border-radius: 0 !important;"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </div>
    <div :class="hasSpace ? 'flex-grow ml-4 right_cont' : 'flex-grow right_cont'">
      <el-form-item
        :label="$t('Phone')"
        required
      >
        <el-input
          :disabled="props.disabled"
          v-model="form.phone"
          :placeholder="$t('Enter your phone number')"
        />
      </el-form-item>
    </div>
  </div>
</template>

<style>
.left_cont .el-input__wrapper {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.right_cont .el-input__wrapper {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
</style>
