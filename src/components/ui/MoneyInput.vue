<template>
  <el-form-item :label="label" class="money-input">
    <el-input :disabled="disabled" ref="inputRef" v-model="formattedValue" :placeholder="placeholder"/>
  </el-form-item>
</template>

<script>
import {useCurrencyInput} from "vue-currency-input";
import {useStore} from "vuex";
import {computed, ref} from "vue";

export default {
  name: "MoneyInput",
  props: {
    modelValue: [Number , String],
    options: Object,
    label: String,
    placeholder: String,
    min: {type: Number, default: 500},
    disabled: {type: Boolean, default: false}
  },
  setup(props) {
    const {getters} = useStore();

    const test = ref();

    const profile = computed(() => getters["auth/me"]);
    const locale = profile.value && profile.value.locale ? profile.value.locale : "tr";

    const {inputRef, formattedValue} = useCurrencyInput({
      ...props.options,
      locale: locale,
      valueRange: {
        min: props.min,
      },
    });

    return {inputRef, formattedValue, test};
  },
};
</script>

