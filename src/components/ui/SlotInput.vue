<script setup>
import {computed, ref} from 'vue';
import formatter from "@/class/formatter";

const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {type: String},
  startDate: {type: [Date, String]},
  endDate: {type: [Date, String]},
  required: {type: Boolean, default: false},
  label: {type: String, default: "Slot"},
})

const slot = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const createdSlot = computed(() => formatter.slot(props.startDate, props.endDate))

const displayDateRange = ref()

const setSlot = () => {
  displayDateRange.value = formatter.displaySlot(props.startDate, props.endDate)
  slot.value = formatter.slot(props.startDate, props.endDate)
}

</script>

<template>
  <div class="flex flex-col">
    <div>
      <div class="flex items-center justify-between">
        <label class="el-form-item__label">{{ label }}
          <label v-show="required" style="color:#EF4444;">
            *
          </label>
        </label>
        <small v-if="createdSlot===slot" class="text-slate-400" style="font-size: 9px">
          {{ displayDateRange }}
        </small>
        <small v-else-if="startDate && endDate" @click="setSlot" class="text-slate-400 cursor-pointer underline "
               style="font-size: 9px">
          {{ $t('Create From Slot Dates') }}
        </small>
      </div>
    </div>
    <el-input
        v-model="slot"
        :placeholder="$t('Example 2022030109001100')"
    />
  </div>

</template>

