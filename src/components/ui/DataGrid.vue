<script>
import {AgGridVue} from "ag-grid-vue3";

export default {
  components: {
    AgGridVue,
  },
};
</script>
<script setup>
import {computed, onActivated, onDeactivated, onUnmounted, ref, watch} from "vue";
import {AG_GRID_LOCALE_EN} from "@/locales/agGrid/en";
import {AG_GRID_LOCALE_TR} from "@/locales/agGrid/tr";
import {i18n} from "@/plugins/vuei18n";

const emit = defineEmits(["update:modelValue", "onGridReady", "onFilterChanged", "rowClicked", "rowDoubleClicked", "handleSelectionChanged", "rowDataUpdated", "modelUpdated"]);
const props = defineProps({
  columns: {
    type: Array, default: () => {
    }
  },
  columnStateSlug: {type: String, default: ""},
  restoreColumnStateEnabled: {type: Boolean, default: false},
  autoSizeColumn: {type: Boolean, default: false},
  pagination: {type: Boolean, default: false},
  statusBarEnabled: {type: Boolean, default: false},
  dataSource: {type: Array},
  modelValue: {default: ""},
  initialGroupOrderComparator: {type: Function},
  rowSelection: {type: String, default: "multiple"},
  fitColumnMaxSize: {type: Number, default: 840},
  sideBar: { type: [Object, Array], default: false },
})

const localeText = computed(() => {
  return i18n.global.locale.value === 'tr' ? AG_GRID_LOCALE_TR : AG_GRID_LOCALE_EN
})

const columnsDef = ref([])
const columnApi = ref()
const gridApi = ref()
const activated = ref(true)
const defaultColDef = ref({
  resizable: true,
  menuTabs: [],
  sortable: true,
  floatingFilter: true,
  filterParams: {
    buttons: ['reset', 'apply'],
    closeOnApply: true,
  },
});

const statusBar = {
  statusPanels: [
    {statusPanel: 'agTotalRowCountComponent', align: 'left'},
    {statusPanel: 'agFilteredRowCountComponent', align: 'left'},
    {statusPanel: 'agSelectedRowCountComponent'},
    {statusPanel: 'agAggregationComponent'},
  ],
}

const excelStyles = [
  {
    id: 'numberType',
    numberFormat: {
      format: '0',
    },
  },
  {
    id: 'currencyFormat',
    numberFormat: {
      format: '#,##0.00 €',
    },
  },
  {
    id: 'negativeInBrackets',
    numberFormat: {
      format: '$[blue] #,##0;$ [red](#,##0)',
    },
  },
  {
    id: 'booleanType',
    dataType: 'Boolean',
  },
  {
    id: 'stringType',
    dataType: 'String',
  },
  {
    id: 'dateType',
    dataType: 'DateTime',
  },
];

onUnmounted(() => {
  // window.onresize = null
})

watch(() => props.dataSource, () => {
  if (props.dataSource.length === 0) {
    columnApi.value && columnApi.value.autoSizeAllColumns(false);
  } else {
    if (!props.autoSizeColumn) {
      gridApi.value && gridApi.value.sizeColumnsToFit();
    } else {
      columnApi.value && columnApi.value.autoSizeAllColumns(false);
      // window.onresize = () => {
      //   // TODO burada pixele göre size column ayarlanacak
      //   // console.log("param:",window.innerWidth)
      //   columnApi.value && columnApi.value.autoSizeAllColumns(false);
      // }
    }
  }
})

onActivated(() => {
  activated.value = true
})

onDeactivated(() => {
  activated.value = false
})


function onGridReady(params) {

  columnsDef.value = props.columns.map(x => {
        return {
          ...x,
          cellClass: 'stringType'
        }
      }
  )

  if (!props.autoSizeColumn) {

    params.api.sizeColumnsToFit();

  } else {

    if (!activated.value) return

    params.columnApi.autoSizeAllColumns(true);
    // window.onresize = () => {
    //   params.columnApi.autoSizeAllColumns(true);
    // }
  }
  columnApi.value = params.columnApi
  gridApi.value = params.api
  emit("update:modelValue", params.api);
  emit("onGridReady");
}

const rowClicked = (param) => {
  emit("rowClicked", param)
}

const rowDoubleClicked = (param) => {
  emit("rowDoubleClicked", param)
}

const handleSelectionChanged = () => {
  emit("handleSelectionChanged")
}

const rowDataUpdated = (params) => {
  emit("rowDataUpdated", params)
}
const modelUpdated = (params) => {
  emit("modelUpdated", params)
}

const isFullWidthRow = () => {
  return true
};

function onFirstDataRendered(params) {


  if (props.autoSizeColumn) {

    if (!activated.value) return

    params.columnApi.autoSizeAllColumns(true)

  } else {
    params.api.sizeColumnsToFit()
    // window.onresize = () => {
    //   if (window.innerWidth < props.fitColumnMaxSize) {
    //     params.columnApi.autoSizeAllColumns(true)
    //   } else {
    //     console.log("testtt datagrid")
    //     params.api.sizeColumnsToFit()
    //   }
    // }
  }
  restoreColumnState()
}


const restoreColumnState = () => {
  if (!props.columnStateSlug || !props.restoreColumnStateEnabled) return;

  let localColumnState = localStorage.getItem(props.columnStateSlug)

  if (localColumnState) {
    columnApi.value.applyColumnState({
      state: JSON.parse(localColumnState),
      applyOrder: true,
    });
  }
}

const dragStopped = () => {
  if (!props.columnStateSlug) return

  let cols = columnApi.value.getColumnState();
  cols.map(x => {
    delete x.sort
    delete x.sortIndex
    return x
  })
  localStorage.setItem(props.columnStateSlug, JSON.stringify(cols))
}

const onFilterChanged = (params) => {
  emit("onFilterChanged", params)
}

let groupDisplayType = "groupRows"


const overlayLoadingTemplate = `<img src="/loading.svg" class="h-4 w-full" alt />`

</script>
<template>
  <ag-grid-vue
      class="w-full h-full ag-theme-balham"
      :column-defs="columnsDef"
      :suppressCopyRowsToClipboard="true"
      :default-col-def="defaultColDef"
      :row-data="dataSource"
      :pagination="pagination"
      :groupDisplayType="groupDisplayType"
      :row-selection="rowSelection"
      :suppressDragLeaveHidesColumns=true
      :excelStyles="excelStyles"
      :localeText="localeText"
      :sideBar="sideBar"
      :statusBar="statusBarEnabled ? statusBar : null"
      :overlayLoadingTemplate="overlayLoadingTemplate"
      @grid-ready="onGridReady"
      @row-clicked="rowClicked"
      @row-double-clicked="rowDoubleClicked"
      @selection-changed="handleSelectionChanged"
      @first-data-rendered="onFirstDataRendered"
      @dragStopped="dragStopped"
      @rowDataUpdated="rowDataUpdated"
      @filter-changed="onFilterChanged"
      @modelUpdated="modelUpdated"
  />
</template>

