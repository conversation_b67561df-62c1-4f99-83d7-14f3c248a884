<script setup>
import {inject, computed} from "vue";
import {useGetData} from "@/composables/useGetData";


const api = inject("api")

const emit = defineEmits(["update:modelValue", "select", "change"]);
const props = defineProps({
  url: {type: String},
  key: {type: String, default: "id"},
  value: {type: String, default: "id"},
  label: {type: String, default: "name"},
  method: {type: String, default: "get"},
  placeholder: {type: String, default: ""},
  filterable: {type: Boolean, default: false},
  multiple: {type: Boolean, default: false},
  multipleLimit: {type: Number, default: 0},
  modelValue: {type: [Number, String, Array, Date], default: ""},
})

const {data, loading} = useGetData(props.url,props.method)

const val = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const getData = () => {
  loading.value = true
  api(props.url)
      .then((response) => data.value = response.data)
      .finally(() => loading.value = false)
}

const onChange = (data) => {
  emit("change", data)
  props.multiple && emit("select", data.find(x => x[props.value] === data))

}
</script>

<template>
  <el-select
      v-model="val"
      :placeholder="$t(placeholder)"
      :filterable="filterable"
      :multiple="multiple"
      :loading="loading"
      :loading-text="$t('Loading')"
      class="w-full-important"
      @change="onChange"
      :multiple-limit="multipleLimit"
  >
    <el-option
        v-for="item in data"
        :key="item[key]"
        :label="item[label]"
        :value="item[value]"
    >
    </el-option>
  </el-select>
</template>
