<script setup>
import axios from "axios";
import {onMounted, ref} from "vue";
import {useI18n} from "vue-i18n";

const {t} = useI18n()

const emit = defineEmits(["update:modelValue", "setAreaJson"])
const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
})

const polygon = ref([])
const _polygon = ref([])
const loading = ref(false)
const polygons = ref([])

onMounted(() => {
  convertFeatureToPolygon()

})

const searchPolygons = (text) => {

  loading.value = true
  axios(`https://nominatim.openstreetmap.org/search.php?q=${text}&polygon_geojson=1&format=jsonv2`)
      .then((res) => {
        polygons.value = res.data.filter(x => x.category === 'boundary')
      })
      .finally(() => loading.value = false)

}


const updateValue = (params) => {

  _polygon.value = [..._polygon.value, polygons.value.find(x => x.place_id === params)]

  let featureCollection = convertFeatureCollection()
  emit("update:modelValue", JSON.stringify(featureCollection));

  polygon.value = null
}


const createFeature = ({place_id, display_name, coordinates}) => {
  return {
    place_id,
    display_name,
    type: "Feature",
    properties: {},
    geometry: {
      coordinates: coordinates,
      type: "Polygon"
    }
  }
}

const convertFeatureToPolygon = () => {

  if (!props?.modelValue) {
    return
  }

  let parseModelValue = JSON.parse(props?.modelValue)

  let {features} = parseModelValue
  let newPolygons = []
  features.forEach(feature => {

    newPolygons.push({
      display_name: feature.display_name,
      place_id: feature.place_id,
      geojson: {
        coordinates: feature.geometry.coordinates,
      }
    })

  })

  _polygon.value = newPolygons
}

const convertFeatureCollection = () => {

  let collection = {
    type: "FeatureCollection",
    features: []
  }

  _polygon.value.forEach(x => {
    collection.features.push(createFeature({
      coordinates: x.geojson.coordinates,
      display_name: x.display_name,
      place_id: x.place_id
    }))
  })

  return collection
}

const removePolygon = (itemId) => {

  _polygon.value = _polygon.value.filter(x => x.place_id !== itemId)

  let featureCollection = convertFeatureCollection()
  emit("update:modelValue", JSON.stringify(featureCollection));
}


</script>

<template>
  <div>
    <el-select
        class="w-full-important"
        :placeholder="t('Search Polygon')"
        v-model="polygon"
        filterable
        remote
        reserve-keyword
        :remote-method="searchPolygons"
        :loading="loading"
        @change="updateValue"
    >
      <el-option
          v-for="item in polygons"
          :key="item.place_id"
          :label="item.display_name"
          :value="item.place_id"
      >
      </el-option>
    </el-select>
    <div
        v-for="item in _polygon"
        class="flex items-center justify-between space-y-1.5 mt-2"
    >
      <div class="text-white px-2 p-0.5 bg-slate-700 rounded mr-2">
        {{ item.display_name }}
      </div>

      <div
          @click="removePolygon(item.place_id)"
          class="text-red-500 hover:text-red-700 cursor-pointer mx-2">
        <FontAwesomeIcon icon="circle-minus" size="lg"/>
      </div>
    </div>
  </div>
</template>
