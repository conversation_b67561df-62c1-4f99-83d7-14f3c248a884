<script setup>
import { ref} from "vue";


const props = defineProps({
  modelValue: {type: [Number, String, Array, Date], default: ""},
  list_type: {type: String, default: "picture-card"}
});
const emit = defineEmits(["update:modelValue"]);
const file = ref()
const img = ref()


const addFile = () => {
  fileElem && fileElem.click()
}

const selectedFiles = (e) => {

  file.value = e.target.files[0]
  if (props.list_type === "picture-card") {
    img.value = URL.createObjectURL(e.target.files[0]);
  }
  emit("update:modelValue",  e.target.files[0]);

}
const test = () => {
  file.value = null
  img.value = null
  document.getElementById('fileElem').value = "";
  emit("update:modelValue", file.value);
}
const clearFile = () => {
  fileElem.value = null
  file.value = null
  emit("update:modelValue", null);
}
</script>

<template>
  <input @change="selectedFiles" type="file" id="fileElem"
         :accept=" list_type === 'picture-card' ? 'image/*'  : '.xlsx'"
         style="display:none">
  <div v-if="list_type === 'picture-card'">
    <div v-if="!file"
         class="flex items-center justify-center h-20 w-20 border border-slate-300 rounded cursor-pointer text-slate-700"
         @click="addFile">
      <FontAwesomeIcon icon="plus"/>
    </div>
    <div v-else class="flex items-center justify-center h-20 w-20 border border-slate-300 rounded relative">
      <img :src="img">
      <div class="absolute cursor-pointer pick-image-remove-button text-slate-700" @click="test">
        <FontAwesomeIcon icon="times-circle"/>
      </div>
    </div>
  </div>
  <div v-else>
    <div
        v-if="!file"
        class="flex cursor-pointer items-center justify-center border font-bold border-slate-300 text-indigo-600 h-10"
        @click="addFile"
    >
      <div class="mr-2">
        {{$t('Upload File')}}
      </div>
      <div class="mr-2">
        <FontAwesomeIcon icon="plus"/>
      </div>
    </div>
    <div v-else class="flex items-center justify-between">
      <div class="text-slate-700">
        {{ file.name }}
      </div>
      <div @click="clearFile" class="text-red-600 cursor-pointer">
        <FontAwesomeIcon icon="times-circle"/>
      </div>
    </div>
  </div>
</template>
