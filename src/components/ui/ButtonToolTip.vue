<script setup>

const props = defineProps({
  text: {type: String, default: ''},
  tooltipText: {type: String, default: ''},
  position: {type: String, default: 'top'},
  loading: {type: Boolean, default: false}
})

</script>

<template>
  <div class="tooltip">
    <template v-if="loading">
      <el-button
          size="small"
          :disabled="loading"
      >
        <div class="flex items-center">
              <span v-show="loading">
                <img src="@/assets/images/spin.svg" class="h-2" alt/>
              </span>
        </div>
      </el-button>
    </template>
    <template v-else>
      <slot/>
    </template>
    <span
        class="bg-slate-700 tooltiptext z-50 text-xxs"
        :class="[position]"
    >
      {{ tooltipText }}
    </span>
  </div>
</template>

<style>
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  position: absolute;
  z-index: 99999;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  border-width: 5px;
  border-style: solid;
}


.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.tooltip .top {
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
}

.tooltip .bottom {
  top: 125%;
  left: 50%;
  margin-left: -60px;
}

.tooltip .left {
  right: 125%;
  margin-left: -60px;
}

.tooltip .right {
  left: 125%;
}

.tooltip .top::after {
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-color: #555 transparent transparent transparent;
}

.tooltip .bottom::after {
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-color: transparent transparent #555 transparent;
}

.tooltip .left::after {
  left: 100%;
  top: 50%;
  margin-top: -5px;
  border-color: transparent transparent transparent #555;
}

.tooltip .right::after {
  right: 100%;
  top: 50%;
  margin-top: -5px;
  border-color: transparent #555 transparent transparent;
}


</style>
