<script setup>
import {computed} from "vue";

const props = defineProps({
  height: {default: 200},
  width: {default: "100%"},
  lat: {default: 40.971435},
  lng: {default: 29.09444}
})

const src = computed(() => {
  return `https://maps.google.com/maps?q=${props.lat}, ${props.lng}&z=13&output=embed`;
});

</script>

<template>
  <iframe
      :width="width"
      :height="height"
      style="border: 1px solid #cbd5e1"
      :src="src"
  >
  </iframe>
</template>
