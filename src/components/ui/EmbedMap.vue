<script setup>
import {computed, ref, watch} from "vue";

const props = defineProps({
  height: {default: 200},
  width: {default: "100%"},
  lat: {default: 40.971435},
  lng: {default: 29.09444}
})

// Memoized src - sadece koordinatlar değiştiğinde yeniden hesapla
const cachedSrc = ref('');
const lastCoords = ref({ lat: null, lng: null });

const src = computed(() => {
  const currentLat = parseFloat(props.lat).toFixed(6);
  const currentLng = parseFloat(props.lng).toFixed(6);

  // Koordinatlar değişmediyse cache'den döndür
  if (lastCoords.value.lat === currentLat && lastCoords.value.lng === currentLng && cachedSrc.value) {
    return cachedSrc.value;
  }

  // Yeni URL oluştur ve cache'le
  const newSrc = `https://maps.google.com/maps?q=${currentLat}, ${currentLng}&z=13&output=embed`;
  cachedSrc.value = newSrc;
  lastCoords.value = { lat: currentLat, lng: currentLng };

  return newSrc;
});

</script>

<template>
  <iframe
      :width="width"
      :height="height"
      style="border: 1px solid #cbd5e1"
      :src="src"
  >
  </iframe>
</template>
