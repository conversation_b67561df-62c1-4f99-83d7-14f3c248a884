<script setup>
import { onMounted, ref, nextTick, computed } from 'vue';
import DefaultMapStyle from "@/map-styles/default.json";
import { loadGoogleMaps, throttle } from "@/views/Tracking/GoogleMapsLoaderService";

const emit = defineEmits(["update:modelValue", "setAreaJson"])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
    }
  },
  hiddenPlaceHolder: { type: Boolean, default: false },
  poligonVisible: { type: Boolean, default: true },
  visible: { type: Boolean, default: false },
  customHeight: { type: String }
})


const formValues = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const newJsonArea = ref()
const pinmap = ref()
const map = ref()
const marker = ref()
const geoJson = ref()
const loadingDefaultPolygon = ref(true)



// Throttled address change - aşırı geocoding API çağrısını önler
const throttledChangeAddress = throttle((lat, lng) => {
  changeAddress(lat, lng);
}, 500);

const mapPromise = async () => {
  // Google Maps'in yüklendiğinden emin ol
  await loadGoogleMaps();

  const { Autocomplete } = await google.maps.importLibrary("places");

  const address = new Autocomplete(
    document.getElementById("address_search")
  );

  address.addListener("place_changed", () => {
    const place = address.getPlace();

    if (!place.geometry || !place.geometry.location) {
      return;
    }

    const location = place.geometry.location;
    throttledChangeAddress(location.lat(), location.lng())
  });

  return new Promise((resolve) => {
    map.value = new google.maps.Map(pinmap.value, {
      center: {
        lat: props.modelValue.lat ? props.modelValue.lat : 41.013028,
        lng: props.modelValue.lng ? props.modelValue.lng : 28.997373,
      },
      zoom: 14,
      mapTypeControl: false,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery"],
      },
    });
    map.value.mapTypes.set(
      "qdelivery",
      new google.maps.StyledMapType(DefaultMapStyle, { name: "QDelivery" })
    );
    map.value.setMapTypeId("qdelivery");


    geoJson.value = new google.maps.Data();
    geoJson.value.setMap(map.value);
    geoJson.value.setStyle({
      fillColor: "green",
      strokeWeight: 1,
    });

    marker.value = new google.maps.Marker({
      map: map.value,
      draggable: true,
      position: {
        lat: props.modelValue.lat ? props.modelValue.lat : 41.013028,
        lng: props.modelValue.lng ? props.modelValue.lng : 28.997373,
      },
      visible: true
    });

    pinChange()

    google.maps.event.addListenerOnce(map, "tilesloaded", function () {
      resolve(map);
    });

    if (props.modelValue.area_json) {
      newJsonArea.value = props.modelValue.area_json
      createPolygon(JSON.parse(props.modelValue.area_json))
    }
    loadingDefaultPolygon.value = false
  });
}

const changeAddress = (lat, lng) => {
  resolveAddressFromCoordinates(lat, lng).then(
    (address) => {

      formValues.value.lat = lat
      formValues.value.lng = lng;
      formValues.value.address = address;
      let newValue = {
        lat,
        lng,
        address
      }
      emit("update:modelValue", newValue);

      marker.value.setPosition({
        lat,
        lng,
      });
      map.value?.panTo(marker.value.getPosition())

    }
  );
}

const pinChange = () => {
  map.value.addListener("click", (mapsMouseEvent) => {
    const { lat, lng } = mapsMouseEvent.latLng.toJSON()
    throttledChangeAddress(lat, lng)
  });

  marker.value.addListener("dragend", (markerDragEvent) => {
    const { lat, lng } = markerDragEvent.latLng.toJSON()
    throttledChangeAddress(lat, lng)
  });
}

const resolveAddressFromCoordinates = (lat, lng) => {
  lat = parseFloat(lat.toFixed(8));
  lng = parseFloat(lng.toFixed(8));

  return new Promise((resolve) => {

    new google.maps.Geocoder().geocode(
      { location: { lat, lng } },
      (results, status) => {
        if (status === "OK") {
          if (results[0]) {
            resolve(results[0].formatted_address);
          }
        }
      }
    );
  });
}

const clearPolygon = () => {
  geoJson.value.forEach(function (feature) {
    geoJson.value.remove(feature);

  });
}

const createPolygon = (featureCollection) => {

  geoJson.value.forEach(function (feature) {
    geoJson.value.remove(feature);
  });

  geoJson.value.addGeoJson(featureCollection);

  let bounds = new google.maps.LatLngBounds();

  geoJson.value.forEach(function (feature) {

    feature.getGeometry().forEachLatLng(function (latlng) {
      bounds.extend(latlng);
    });
  });

  if (marker.value) {
    bounds.extend(marker.value.getPosition());
  }

  map.value.fitBounds(bounds);
}


onMounted(() => {
  setTimeout(() => {
    nextTick(() => {
      mapPromise()
    })
  }, 750)



});


const newPoligon = () => {
  emit("setAreaJson", newJsonArea.value);
  createPolygon(JSON.parse(newJsonArea.value))
}

</script>

<template>
  <div class="col-span-12">
    <div :class="['flex flex-col w-full', customHeight || 'h-96']">
      <div class="flex items-center">
        <div class="grow">
          <div v-if="hiddenPlaceHolder">
            <el-input
              id="address_search"
              v-model="formValues.searchAddress"
              :placeholder="$t('Address')"
            />
          </div>
          <div v-else>
            <el-form-item :label="$t('Address')">
              <el-input
                id="address_search"
                v-model="formValues.searchAddress"
                :placeholder="$t('Address')"
              />
            </el-form-item>
          </div>
        </div>
        <slot name="suffix" />
      </div>
      <div
        ref="pinmap"
        class="h-full w-full"
      ></div>
      <div
        v-if="formValues.address"
        class="bg-slate-100 text-slate-700 font-medium mt-1 rounded p-2 text-xs "
      >
        {{ formValues.address }}
      </div>
    </div>

  </div>
  <div
    v-if="!loadingDefaultPolygon && poligonVisible"
    class="col-span-12"
  >
    <SearchPolygons v-model="newJsonArea" />
  </div>
  <div class="col-span-12">
    <div
      v-if="poligonVisible"
      class="col-span-12"
    >
      <el-form-item>
        <template #label>
          <div style="display: flex; align-items: center; justify-content: space-between">

            {{ $t('Poligon Json') }}
            <div class="flex items-center justify-end space-x-2">
              <small
                @click="clearPolygon"
                class="text-slate-400 cursor-pointer underline "
                style="font-size: 9px"
              >
                {{ $t('Remove') }}
              </small>

              <small
                @click="newPoligon"
                class="text-slate-700 cursor-pointer underline "
                style="font-size: 9px"
              >
                {{ $t('On Map') }}
              </small>
            </div>
          </div>
        </template>
        <el-input
          v-model="newJsonArea"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </div>
  </div>
</template>
