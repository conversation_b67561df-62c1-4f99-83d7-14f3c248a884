<script setup>

import {ref, onMounted} from 'vue';


const emits = defineEmits(["click"])

const props = defineProps({
  count: {type: Number},
  label: {type: String},
  type: {type: String}
})


const hovered = ref(false)
const color = ref("#EE7B7D")
const bgColorHover = ref('#EE7B7D')

onMounted(() => {
  getColours()
})

const getColours = () => {

  if (props.type === "danger") {
    color.value = "#E53935"
    bgColorHover.value = "#FFE3E2"
  }

  if (props.type === "warning") {
    color.value = "#FCC419"
    bgColorHover.value = "#FFF0C0"
  }

  if (props.type === "success") {
    color.value = "#6BE3A2"
    bgColorHover.value = "#CEFFE5"
  }

}


const onClick = () => {
  emits("click")
}

const hover = () => {
  hovered.value = true
}

</script>


<template>
  <div
      @click="onClick"
      class="flex items-center justify-between border rounded text-xs font-bold  mx-2 cursor-pointer border-gray-400 hover:bg-slate-200"
      id="#button"
  >
    <div class="px-1.5" v-if="props.type==='warning'">
      <img class="h-4" src="../../assets/basket.png">
    </div>
    <div class="px-1.5" v-if="props.type==='danger'">
      <img class="h-4" src="../../assets/alarm.png">
    </div>
    <div class="px-1.5" v-if="props.type==='success'">
      <img class="h-4" src="../../assets/trolley.png">
    </div>
    <div
        class="py-1 px-1"

    >
      {{ label }}
    </div>

    <div
        class="py-1 px-1.5"
    >
      {{ count }}
    </div>
  </div>
</template>
<style scoped>

</style>
