<script setup>
import {computed,onMounted} from 'vue';
import ShowWorkingHourPlan from "@/components/ui/ShowWorkingHourPlan.vue";

const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {type: [String, Number]},
  templates: {type: Array, default: () => []},
})


const plan = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

const currentWorkingPlan = computed(() => {
  return props.templates.find((template) => template.id === plan.value)
})

</script>

<template>

  <el-form-item>
    <template #label>
      <div class="flex items-center justify-between relative">
        {{ $t('Working Plan') }}
        <div v-if="currentWorkingPlan">
          <el-popover
              trigger="click"
              :width="currentWorkingPlan?.type === 'courier' ? 510 : 400"
          >
            <template #default>
              <ShowWorkingHourPlan :currentWorkingPlan="currentWorkingPlan"/>
            </template>
            <template #reference>
              <small
                  v-if="plan"
                  class="text-indigo-500 font-semibold cursor-pointer underline"
                  style="font-size: 11px"
              >
                {{ $t('Show') }}
              </small>
            </template>
          </el-popover>
        </div>
      </div>

    </template>

    <el-select
        v-model="plan"
        :placeholder="$t('Working Plan')"
        class="w-full-important"
    >
      <el-option
          v-for="template in templates"
          :key="template.id"
          :value="template.id"
          :label="template.name"
      />
    </el-select>
  </el-form-item>


</template>

