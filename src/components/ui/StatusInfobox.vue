<script setup>

const props = defineProps({
  name: {type: String, default: ''},
  color: {type: String, default: ''}
})

</script>

<template>
  <div class="flex items-center px-1 py-0.5">
    <span class="hidden sm:block"> {{ name }}:</span>
    <el-popover
        placement="top-start"
        trigger="hover"
        :content="name"
    >
      <template #reference>
        <div class="w-2 h-2 ml-1" :class="color"/>
      </template>
    </el-popover>
  </div>
</template>
