<template>
  <el-tooltip placement="top" content="Toggle layout">
    <button
      type="button"
      class="text-indigo-200 hover:text-white"
      @click="toggleOperationLayout"
    >
      <FontAwesomeIcon icon="columns" size="1x" />
    </button>
  </el-tooltip>
</template>

<script>
import { useStore } from "vuex";

export default {
  name: "ToggleOperationLayout",
  setup() {
    const { dispatch } = useStore();
    const toggleOperationLayout = () => dispatch("ui/toggleOperationLayout");

    return { toggleOperationLayout };
  },
};
</script>
