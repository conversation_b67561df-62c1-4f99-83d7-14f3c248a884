<script>
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import ButtonToolTip from "@/components/ui/ButtonToolTip.vue";

export default {
  components: {ButtonToolTip, FontAwesomeIcon},
  emits: ["onClick"],
  props: ["params"],
  setup() {
    return {}
  }
};
</script>

<template>
  <div class="container flex items-center justify-between">
    <span class="text-md">{{ params?.message }}</span>
    <div
        v-if="!params.actionIcon"
        class="action cursor-pointer"
        @click.stop="$emit('onClick')"
    >
      {{ params?.actionName }}
    </div>
    <button
        @click.stop="$emit('onClick')"
        class="action cursor-pointer py-1 px-2 rounded ml-2"
        style="background-color: #3c8b3f"
    >
      <FontAwesomeIcon :icon="params.actionIcon"/>
    </button>
  </div>
</template>
