<script setup>
import formatter from "@/class/formatter";
import { onMounted, ref, watch } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const props = defineProps({
  currentWorkingPlan: {
    type: Array, default: () => {
    }
  }
});
const planList = ref([]);

const getData = () => {
  // Eğer props.currentWorkingPlan boş veya undefined ise fonksiyondan çık
  if (!props.currentWorkingPlan.working_hours || !Array.isArray(props.currentWorkingPlan.working_hours)) {
    console.error("currentWorkingPlan verisi yok veya yanlış formatta");
    return;
  }

  let arr = [];

  // currentWorkingPlan içindeki her bir öğeyi map ile dön
  let _list = props.currentWorkingPlan.working_hours.map((x) => {
    return {
      ...x,
      starts_at: x.starts_at ? x.starts_at.split(":").slice(0, 2).join(":") : null,
      ends_at: x.ends_at ? x.ends_at.split(":").slice(0, 2).join(":") : null,
      is_closed: x.is_open || false,  // is_open boşsa varsayılan olarak false
      break_time: x.break_time || 0,   // break_time boşsa varsayılan olarak 0
      auto_working_on: x.auto_working_on || false,  // Varsayılan değer
      auto_working_off: x.auto_working_off || false  // Varsayılan değer
    };
  });

  // Haftanın günlerine göre sırala
  arr.push(_list.find((x) => x.day_name === "monday"));
  arr.push(_list.find((x) => x.day_name === "tuesday"));
  arr.push(_list.find((x) => x.day_name === "wednesday"));
  arr.push(_list.find((x) => x.day_name === "thursday"));
  arr.push(_list.find((x) => x.day_name === "friday"));
  arr.push(_list.find((x) => x.day_name === "saturday"));
  arr.push(_list.find((x) => x.day_name === "sunday"));

  // Günleri planList'e ata
  planList.value = [...arr];
};

onMounted(() => {


    if (props.currentWorkingPlan && props.currentWorkingPlan.working_hours.length > 0) {
      getData();
    } else {
      console.warn("currentWorkingPlan verisi yok");
    }


});

watch(
  () => props.currentWorkingPlan.working_hours,
  (newVal) => {

    if (newVal && newVal.length > 0) {
      getData();
    } else {
      console.warn("currentWorkingPlan verisi boş veya undefined");
    }
  },
  { deep: true }
);

</script>

<template>
  <div v-for="item in planList"
       class="border-b border-slate-300"
  >
    <div class="grid grid-cols-5 gap-2  px-2 py-1 text-xs">
      <div class="col-span-1 flex items-center justify-end font-medium">
        {{ $t(formatter.capitalizeFirstLetter(item.day_name)) }}
      </div>
      <div class="col-span-3 grid-cols-3 flex items-center justify-evenly">
        <div class="px-5">
          {{ item.starts_at }}
        </div>
        <div class="grow flex items-center justify-center">
          <FontAwesomeIcon icon="right-left" />
        </div>
        <div class="px-5">
          {{ item.ends_at }}
        </div>
      </div>
      <div v-if="currentWorkingPlan.type === 'courier'" class="col-span-1 flex items-center justify-center ">
        <el-checkbox size="small" v-model="item.is_closed" :disabled="true">
        <span class="text-xs">
          {{ $t("Working") }}
        </span>
        </el-checkbox>

      </div>
      <div v-if="currentWorkingPlan.type === 'hub'" class="col-span-1 flex items-center justify-center ">
        <el-checkbox size="small" v-model="item.is_closed" :disabled="true">
        <span class="text-xs">
          {{ $t("Open") }}
        </span>
        </el-checkbox>

      </div>
    </div>

    <div v-if="currentWorkingPlan.type === 'courier'" class="w-full grid grid-cols-10 px-2 gap-2 py-1 text-xs">

      <div class="col-span-2 flex items-center justify-end font-medium pr-4">
        {{ $t("Break time") }}
      </div>
      <div class="col-span-2 flex items-center">
        {{ item.break_time }} - DK
      </div>

      <div class="col-span-3 flex items-center justify-center pr-2 mr-5">
        <el-checkbox size="small" v-model="item.auto_working_on" :disabled="true">
        <span class="text-xs ">
          {{ $t("Automatic Online") }}
        </span>
        </el-checkbox>

      </div>
      <div class="col-span-3 flex items-center justify-center ">
        <el-checkbox size="small" v-model="item.auto_working_off" :disabled="true">
        <span class="text-xs">
          {{ $t("Automatic Offline") }}
        </span>
        </el-checkbox>

      </div>

    </div>

  </div>
</template>
