<script setup>

import {onMounted, ref} from "vue";

const emit = defineEmits(["remove"])

const timer = ref(5)
const hover = ref(false)
const props = defineProps({
  item: {type: Object},
  index: {type: Number}
})

const onRemove = () => {
  emit("remove", props.item.id)
}

const onMouseOver = (id) => {
  hover.value = true
  timer.value = 5
}
const onTimer = () => {
  setTimeout(() => {
    if (!hover.value) {
      timer.value = timer.value - 1
    }
    if (timer.value === 0) {
      onRemove()
    } else {
      onTimer()
    }
  }, 1000)
}
onMounted(() => {
  onTimer()
})

</script>


<template>

  <div
      @mouseover="onMouseOver"
      @mouseleave="hover = false"
      class="max-w-xs sm:max-w-sm w-96 rounded-md mb-2 custom-toaster absolute" :class="[
          item.style,
          timer === 0 && 'custom-toaster-out'
          ]"
      :style="{top:((index*50)+10 )+ 'px'}"
  >
    <div class="flex items-center justify-between px-4 py-2 text-white">
      <div :class="[item.message.length > 255 ? 'truncate':null]">{{ item.message }}</div>
      <div @click="onRemove">
        <FontAwesomeIcon icon="times" class="cursor-pointer"/>
      </div>
    </div>
  </div>
</template>
