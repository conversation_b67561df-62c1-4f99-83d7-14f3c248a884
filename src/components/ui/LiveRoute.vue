<template>
  <div
    class="fixed flex items-center inset-0 w-full h-full z-90 p-4"
  >
    <div
       class="fixed inset-0 w-full h-full bg-black opacity-50 z-0"
    ></div>
    <div
      class="relative flex flex-col w-full max-w-7xl h-4/5 m-auto bg-white shadow-lg z-10 rounded overflow-hidden"
    >
      <iframe
        :src="src"
        class="block relative w-full h-full z-10 bg-slate-200"
        allowfullscreen
      />
      <div class="flex w-full items-center justify-between px-2 py-1">
        <div>
          <span class="text-sm font-bold">#{{ data.taskId }}</span>
        </div>
        <div>
          <button type="button" @click="close">
            <FontAwesomeIcon icon="times" fixed-width />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed} from "vue";


export default {
  props:{
   data:{
     type:Object,
   },
    close:{
     type:Function
    }
  },
  setup(props) {
    const src = computed(() => {
      return `https://www.google.com/maps/embed/v1/directions?origin=${props.data.origin}&destination=${props.data.destination}&key=${import.meta.env.VITE_GOOGLE_API_KEY}`;
    });

    return {
      src,
    };
  },
};
</script>
