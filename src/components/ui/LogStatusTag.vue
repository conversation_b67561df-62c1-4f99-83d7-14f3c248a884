<template>
  <span
      :class="[
      'text-xxxs font-medium truncate rounded px-3 py-1 h-5',
      'text-xxxs font-medium truncate rounded',
      big ? 'px-5 py-2' : 'px-3 py-1',
      statusClass,
    ]"
  ><slot></slot
  ></span>
</template>

<script>
import {computed} from "vue";

export default {
  name: "StatusTag",
  props: {
    value: {
      type: Boolean,
      default: "assigned",
    },
    big: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const statusClass = computed(() => {
      if (props.value) {
        return "bg-green-50 text-green-500";
      } else {
        return "bg-rose-200 text-rose-500";
      }
    });

    return {statusClass};
  },
};
</script>
