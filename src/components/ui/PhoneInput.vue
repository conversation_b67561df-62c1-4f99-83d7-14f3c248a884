<script setup>
import {computed} from "vue";
import {Options} from "@/class"

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {type: [Number, String, Array, Date, Object]},
  visibleLabel: {type: Boolean, default: true},
  disabled: {type: Boolean, default: false},
  requiredVisible :{type: Boolean, default: false},
});


const form = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});

</script>

<template>
  <div v-if="visibleLabel" class="flex">
    <div style="width: 6rem!important">
      <el-form-item :label="$t('Country')">
        <el-select
            :disabled="props.disabled"
            v-model="form.country"
            :placeholder="$t('Country')"
            class="w-full-important"
        >
          <el-option
              v-for="item in Options.countries"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </div>
    <div class="flex-grow">
      <el-form-item :label="$t('Phone')">
        <el-input
            :disabled="props.disabled"
            v-model="form.phone"
            :placeholder="$t('Phone')"
        />
      </el-form-item>
    </div>
  </div>
  <div v-else="visibleLabel" class="flex">
    <div style="width: 6rem!important">
      <el-select
          v-model="form.country"
          :placeholder="$t('Country')"
          class="w-full-important"
          :disabled="props.disabled"
      >
        <el-option
            v-for="item in Options.countries"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        >
        </el-option>
      </el-select>
    </div>
    <div class="flex-grow">
      <el-input
          v-model="form.phone"
          :placeholder="$t('Phone')"
      />
    </div>
  </div>
</template>
