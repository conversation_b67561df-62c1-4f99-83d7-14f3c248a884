<script setup>
import {nextTick, ref, computed, onMounted} from 'vue'

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  disabled:{type:Boolean},
  modelValue: {type: Array, default: []},
  limit:{type:Number, default: 10},
});

const tags = ref([])


const inputValue = ref('')
const closable = computed(()=>!props.disabled)
const dynamicTags = computed({
  get() {
    return props.modelValue;
  },
  set(newValue) {
    emit("update:modelValue", newValue);
  },
});
const inputVisible = ref(false)
const InputRef = ref()

const handleClose = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
}
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value.input.focus()
  })
}
const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const getData = () => {
  api("customer/tags")
      .then((res)=>{
        tags.value = res.data
      })
}

onMounted(()=>{getData()})


</script>

<template>
  <el-select
      v-model="dynamicTags"
      filterable
      :placeholder="t('Select Team')"
      class="w-full-important"
      clearable
  >
<!--    <el-option-->
<!--        v-for="item in tags"-->
<!--        :key="item.id"-->
<!--        :label="item.name"-->
<!--        :value="item.id"-->
<!--    >-->
<!--    </el-option>-->
  </el-select>
<!--  <el-tag-->
<!--      v-for="tag in dynamicTags"-->
<!--      :key="tag"-->
<!--      class="mx-1"-->
<!--      :closable="closable"-->
<!--      :disable-transitions="false"-->
<!--      @close="handleClose(tag)"-->
<!--  >-->
<!--    {{ tag }}-->
<!--  </el-tag>-->
<!--  <el-input-->
<!--      v-if="inputVisible"-->
<!--      ref="InputRef"-->
<!--      v-model="inputValue"-->
<!--      class="ml-1 w-20"-->
<!--      size="small"-->
<!--      @keyup.enter="handleInputConfirm"-->
<!--      @blur="handleInputConfirm"-->
<!--  />-->
<!--  <el-button v-else-if="closable" :disabled="dynamicTags.length >= limit" class="button-new-tag ml-1" size="small" @click="showInput">-->
<!--    + {{$t('New Tag')}}-->
<!--  </el-button>-->
</template>
