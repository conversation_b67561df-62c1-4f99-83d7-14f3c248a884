<script setup>
import dayjs from "dayjs";
import en from 'element-plus/es/locale/lang/en'
import tr from 'element-plus/es/locale/lang/tr'
import { useStore } from "vuex";
import { onMounted, computed, watch, ref } from "vue";
import { pusher } from "@/socket";

const { dispatch, getters } = useStore();

const loading = computed(() => getters["ui/loading"]);
const me = computed(() => getters["auth/me"])
const activeCompany = computed(() => getters["auth/activeCompany"]);

const root = ref(document.documentElement)

const setAuthToken = (token) => {
  dispatch("auth/setAuthToken", token);
};

const developmentListener = () => {
  let channel = pusher.subscribe("development")
  channel.bind("deploy", () => {
    setTimeout(() => {
      window.location.reload()
    }, 5000)
  })
}

onMounted(() => {
  const strToken = localStorage.getItem("authToken");
  setAuthToken(strToken);
  developmentListener()
});

watch(me, () => {
  if (me.value) {
    dayjs.locale(me.value.locale ? me.value.locale : 'en')
  }
})

watch(activeCompany, () => {
  if (activeCompany.value.primary_color) {
    root.value.style.setProperty("--custom-primary-color", activeCompany.value.primary_color)
  } else {
    let color = activeCompany.value.company_product_type === "qd" ? '#4f46e5' : '#84cc16'
    root.value.style.setProperty("--custom-primary-color", color)
  }
})

</script>

<template>
  <el-config-provider :locale="$i18n.locale === 'tr' ? tr : en">
    <div class="relative h-full w-full overflow-x-hidden md:overflow-x-auto">
      <transition
        enter-active-class="ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="ease-in duration-200"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <div
          v-if="loading"
          class="absolute left-0 top-0 z-50 w-full h-full flex items-center justify-center bg-white bg-opacity-75"
        >
          <img
            src="./assets/images/loading.svg"
            class="h-4"
            alt
          />
        </div>
      </transition>
      <router-view />
    </div>
  </el-config-provider>
</template>

