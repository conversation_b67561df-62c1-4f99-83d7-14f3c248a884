export default {
  install: (app, options) => {
    function parseDistance(distance, unit = 'metric') {
      if (isNaN(distance) || !distance) {
        return null
      }

      let appendix = 'km'

      distance = distance / 1000

      if (unit !== 'metric') {
        distance = distance * 0.621371192
        appendix = 'mi'
      }

      return `${distance.toFixed(2)} ${appendix}`
    }

    app.provide('parseDistance', parseDistance)

    app.mixin({
      methods: {
        parseDistance,
      }
    })
  }
}

export const _debounce = (func, timeout = 300) => {
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, 3000);
  };
}

export const generate3RandomNumbers = () => {
  return Math.floor(Math.random()*(999-100+1)+100);
}
