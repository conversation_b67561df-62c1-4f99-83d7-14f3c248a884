import { library, dom } from "@fortawesome/fontawesome-svg-core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { faCcMastercard} from "@fortawesome/free-brands-svg-icons";
import {
  faTrashSlash,
  faPenToSquare,
  faSquareArrowDown,
  faSquareArrowUp,
  faStar,
  faForward,
  faPlay as fasPlay,
  faGrid2Plus,
  faLocationExclamation,
  faArrowLeft,
  faLocationPin,
  faHouse,
  faMugHot,
  faPersonRunningFast,
  faMugTea,
  faWifiExclamation,
  faCalendarPen,
  faBatteryThreeQuarters,
  faLocationXmark,
  faClockTwelve,
  faClockRotateLeft,
  faCircleInfo,
  faArrowRight,
  faArrowsRotate,
  faBell,
  faBellSlash,
  faBriefcase,
  faCheckCircle,
  faExclamationTriangle,
  faLock,
  faMap,
  faMapPin,
  faHouseChimneyUser,
  faCircleCheck,
  faPen,
  faRunning,
  faSearch,
  faMessageBot,
  faSignOutAlt,
  faTachometerAlt,
  faTasks,
  faTimes,
  faTrash,
  faUserCircle,
  faUserEdit,
  faUserFriends,
  faUsers,
  faSignInAlt,
  faTrashRestore,
  faUserTie,
  faChevronDown,
  faChevronUp,
  faEarthEurope,
  faPlus,
  faMagic,
  faHouseUser,
  faMapMarker,
  faMapMarkerAlt,
  faUserSlash,
  faFilter,
  faEnvelope,
  faWrench,
  faBoxes,
  faColumns,
  faUser,
  faCaretSquareDown,
  faCaretSquareLeft,
  faCaretSquareRight,
  faCaretSquareUp,
  faEllipsisV,
  faHistory,
  faCaretDown,
  faCaretUp,
  faExternalLinkAlt,
  faReceipt,
  faVial,
  faMapMarked,
  faFileExcel,
  faMinus,
  faMinusCircle,
  faSortAlphaDown,
  faSortAlphaUp,
  faSortAmountDown,
  faSortAmountUp,
  faSync,
  faExternalLinkSquareAlt,
  faInfo,
  faFileAlt,
  faAngleDoubleRight,
  faStore,
  faMapMarkedAlt,
  faExchangeAlt,
  faCheckSquare,
  faEdit,
  faChevronLeft,
  faCalendarLinesPen,
  faChevronRight,
  faPlug,
  faShoppingBag,
  faCircleExclamation,
  faCalendarDay,
  faCalendar,
  faCog,
  faCashRegister,
  faMotorcycle,
  faBatteryFull,
  faBatteryHalf,
  faSyncAlt,
  faPrint,
  faBan,
  faBusinessTime,
  faRoad,
  faTimesCircle,
  faFileCsv,
  faCalendarCheck,
  faExpandArrowsAlt,
  faLocationDot,
  faLocationDotSlash,
  faPeopleCarry,
  faShoppingBasket,
  faHourglass,
  faDiamondTurnRight,
  faHourglassEnd,
  faFolderOpen,
  faXmark,
  faBoxOpen,
  faDownload,
  faSearchPlus,
  faCircleQuestion,
  faCircle,
  faFlag,
  faBatteryQuarter,
  faClock,
  faCheck,
  faUnlock,
  faLockOpen,
  faCopy,
  faQrcode,
  faEllipsis,
  faWallet,
  faCoins,
  faArrowsLeftRight,
  faEllipsisVertical,
  faRightLeft,
  faTimesSquare,
  faPhone,
  faPhoneSquareAlt,
  faLocationArrow,
  faNoteSticky,
  faBuilding,
  faBars,
  faCalendarDays,
  faBoxOpenFull,
  faBatteryLow,
  faCartPlus,
  faRoute,
  faUserMagnifyingGlass,
  faCircleMinus,
  faTurkishLiraSign,
  faUpload,
  faSigma,
  faFilterSlash,
  faFilterCircleXmark,
  faEye,
  faEyeSlash,
  faClipboardUser,
  faUp,
  faDown,
  faMaximize,
  faMagnifyingGlass,
  faLink,
  faArrowsTurnRight,
  faArrowsSplitUpAndLeft,
  faHandshakeSlash,
  faGifts,
  faSquareInfo,
  faClockDesk,
  faSpinner,
  faUserHeadset,
  faTriangleExclamation,
  faGear,
  faNote,
  faMapLocationDot,
  faBoltLightning,
  faPersonWalkingArrowLoopLeft,
  faBuildings,
  faOctagonPlus,
  faCircleUser,
  faIdCard
} from "@fortawesome/pro-solid-svg-icons";

import { faWhatsapp } from "@fortawesome/free-brands-svg-icons";

import {
  faBars as faBarsThin,
  faArrowsLeftRightToLine,
  faPlay
} from "@fortawesome/pro-thin-svg-icons";

import {
  faEye as faEyeLight,
  faClock as faClockLight,
  faBell as faBellLight
} from "@fortawesome/pro-light-svg-icons";

import {
  faBuilding as farBuilding,
  faPlay as farPlay,
  faWarehouse as farWarehouse,
  faPeopleGroup as farPeopleGroup,
  faHouseCircleCheck as farHouseCircleCheck
} from "@fortawesome/pro-regular-svg-icons";

library.add(
  faTrashSlash,
  faForward,
  faPenToSquare,
  fasPlay,
  faPlay,
  farPlay,
  faGrid2Plus,
  faPersonRunningFast,
  faEllipsisVertical,
  farBuilding,
  farPeopleGroup,
  faFileExcel,
  faBatteryHalf,
  faXmark,
  farHouseCircleCheck,
  faBatteryThreeQuarters,
  faEllipsis,
  farWarehouse,
  faClockLight,
  faHouse,
  faBellLight,
  faEyeLight,
  faHouseChimneyUser,
  faTriangleExclamation,
  faCalendarLinesPen,
  faHouseUser,
  faIdCard,
  faCircleCheck,
  faCircleUser,
  faOctagonPlus,
  faClockRotateLeft,
  faCalendarPen,
  faStar,
  faCircleInfo,
  faBuildings,
  faMugHot,
  faMugTea,
  faPersonWalkingArrowLoopLeft,
  faBoltLightning,
  faBatteryFull,
  faNote,
  faWhatsapp,
  faSpinner,
  faClockDesk,
  faSquareInfo,
  faGifts,
  faUserHeadset,
  faHandshakeSlash,
  faSquareArrowUp,
  faArrowsSplitUpAndLeft,
  faArrowsTurnRight,
  faLink,
  faMinus,
  faMaximize,
  faSquareArrowDown,
  faUp,
  faDown,
  faWifiExclamation,
  faClipboardUser,
  faLocationExclamation,
  faEye,
  faEyeSlash,
  faCircleExclamation,
  faFilterCircleXmark,
  faFilterSlash,
  faArrowsLeftRightToLine,
  faSigma,
  faClockTwelve,
  faUpload,
  faTurkishLiraSign,
  faCircleMinus,
  faBatteryQuarter,
  faLocationXmark,
  faBatteryLow,
  faLocationDot,
  faLocationDotSlash,
  faUserMagnifyingGlass,
  faRoute,
  faCalendar,
  faLocationPin,
  faCartPlus,
  faBoxOpenFull,
  faArrowsRotate,
  faCalendarDays,
  faBarsThin,
  faBars,
  faBuilding,
  faNoteSticky,
  faLocationArrow,
  faPhoneSquareAlt,
  faPhone,
  faTimesSquare,
  faRightLeft,
  faArrowsLeftRight,
  faCoins,
  faWallet,
  faCircle,
  faQrcode,
  faCopy,
  faLockOpen,
  faCheck,
  faUnlock,
  faClock,
  faFlag,
  faCircleQuestion,
  faSearchPlus,
  faDownload,
  faBoxOpen,
  faFolderOpen,
  faDiamondTurnRight,
  faHourglassEnd,
  faHourglass,
  faShoppingBasket,
  faPeopleCarry,
  faExpandArrowsAlt,
  faArrowLeft,
  faArrowRight,
  faBriefcase,
  faCheckCircle,
  faExclamationTriangle,
  faLock,
  faMap,
  faMapPin,
  faRunning,
  faSignOutAlt,
  faTachometerAlt,
  faTasks,
  faTimes,
  faUserEdit,
  faUserFriends,
  faUsers,
  faUserCircle,
  faCcMastercard,
  faBell,
  faGear,
  faBellSlash,
  faSearch,
  faPen,
  faTrash,
  faSignInAlt,
  faTrashRestore,
  faEarthEurope,
  faUserTie,
  faChevronDown,
  faChevronUp,
  faPlus,
  faMagic,
  faMapMarker,
  faMapMarkerAlt,
  faUserSlash,
  faFilter,
  faEnvelope,
  faWrench,
  faBoxes,
  faColumns,
  faUser,
  faCaretSquareDown,
  faCaretSquareLeft,
  faCaretSquareRight,
  faCaretSquareUp,
  faEllipsisV,
  faHistory,
  faCaretDown,
  faCaretUp,
  faExternalLinkAlt,
  faReceipt,
  faVial,
  faMapMarked,

  faFileCsv,
  faMinusCircle,
  faSortAlphaDown,
  faSortAlphaUp,
  faSortAmountDown,
  faSortAmountUp,
  faMessageBot,
  faSync,
  faExternalLinkSquareAlt,
  faInfo,
  faFileAlt,
  faAngleDoubleRight,
  faStore,
  faMapMarkedAlt,
  faExchangeAlt,
  faCheckSquare,
  faEdit,
  faPlug,
  faChevronLeft,
  faChevronRight,
  faShoppingBag,
  faCalendarDay,
  faCalendarCheck,
  faCog,
  faCashRegister,
  faMotorcycle,
  faSyncAlt,
  faPrint,
  faBan,
  faBusinessTime,
  faRoad,
  faTimesCircle,
  faMagnifyingGlass,
  faMapLocationDot
);

export default {
  install: (app) => {
    dom.watch();
    app.component("FontAwesomeIcon", FontAwesomeIcon);
  }
};
