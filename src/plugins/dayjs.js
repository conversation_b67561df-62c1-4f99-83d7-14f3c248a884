import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import localizedFormat from "dayjs/plugin/localizedFormat";
import localData from "dayjs/plugin/localeData";
import utc from "dayjs/plugin/utc";
import "dayjs/locale/tr";
import weekday from "dayjs/plugin/weekday";
import isBetween from "dayjs/plugin/isBetween";

export default {
  install: (app, options) => {
    dayjs.locale((options && options.locale) || document.documentElement.lang);
    dayjs.extend(relativeTime);
    dayjs.extend(localizedFormat);
    dayjs.extend(localData);
    dayjs.extend(utc)
    dayjs.extend(weekday)
    dayjs.extend(isBetween)


    app.config.globalProperties.$dayjs = dayjs;
    app.provide("dayjs", dayjs);
  },
};
