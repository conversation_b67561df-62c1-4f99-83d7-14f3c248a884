import AppLayout from "@/layouts/AppLayout.vue";
import SectionLayout from "@/layouts/SectionLayout.vue";
import FormCard from "@/components/general/FormCard.vue";
import FormSectionCard from "@/components/general/FormSectionCard.vue";
import FormSectionCardButtons from "@/components/general/FormSectionCardButtons.vue";
import PageLayout from "@/layouts/PageLayout.vue";
import DetailCard from "@/components/general/DetailCard.vue";
import SelectBox from "@/components/ui/SelectBox.vue";
import DetailCardContent from "@/components/general/DetailCardContent.vue";
import MultiTabDetailPageLayout from "@/components/settings/drawers/detail/MultiTabDetailPageLayout.vue"
import AuthPageLayout from "@/layouts/AuthPageLayout.vue"
export default {
  install: (app) => {
    app.component("AppLayout", AppLayout);
    app.component("PageLayout", PageLayout);
    app.component("MultiTabDetailPageLayout",MultiTabDetailPageLayout)
    app.component("SectionLayout", SectionLayout);
    app.component("FormCard", FormCard);
    app.component("FormSectionCard", FormSectionCard);
    app.component("FormSectionCardButtons", FormSectionCardButtons);
    app.component("DetailCard", DetailCard);
    app.component("DetailCardContent", DetailCardContent);
    app.component("AuthPageLayout",AuthPageLayout)
    app.component("SelectBox",SelectBox)
  },
};
