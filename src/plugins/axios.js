import axios from "axios";
import store from "@/store";

const api = axios.create({
    baseURL: import.meta.env.VITE_APP_API,
});

api.interceptors.request.use(
    (config) => {
        let token = localStorage.getItem("authToken");
        let headers = {
            "X-Requested-With": "XMLHttpRequest",
        };
        if (token) {
            headers["Authorization"] = `Bearer ${token}`;
        }
        config.withCredentials = true
        config.headers = headers;
        return config;
    },
    (error) => Promise.reject(error)
);

api.interceptors.response.use(
    (response) => {
        return response.data;
    },
    async (error) => {
        if (error.response && error.response.status === 401) {
            localStorage.removeItem("authToken");
            store.dispatch("auth/setActiveCompany", null)
            store.dispatch("auth/setAuthToken", null)
            window.location.href = "/"
        }
        if (error.response && error.response.status === 422) {
        }
        let _error = {
            error: JSON.stringify(error),
            message: error.response.data.message,
            status: error.response.status,
            data: error.response.data,
            type: 1,
        };

        return Promise.reject(_error);
    }
);

export default {
    install: (app) => {
        app.config.globalProperties.$api = api;
        app.provide("api", api);
    },
};
