<script setup>
import { ref, inject, onMounted } from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import { useRoute } from "vue-router";
import router from "@/router";
import { useStore } from "vuex";


const loader = ref();
const api = inject("api");
const route = useRoute();
const token =ref()
const { dispatch, getters } = useStore();

onMounted(() => {
token.value = route.query.session_id
  getToken();
});
const getToken = () => {
  loader.value.show();
  api.post("kuik/customers/impersonation/token",{session_id:token.value})
    .then((res) => {

      localStorage.setItem("authToken", res.token);
      dispatch("auth/setAuthToken", res.token)
      router.push({ name: "<PERSON>ikR<PERSON>" });
    })
  .catch((e) => {
    }).finally(() => {
      loader.value.hide();
    });
};


</script>

<template>
  <LoadingBlock ref="loader" />
</template>

<style scoped>

</style>