<script lang="ts" setup>
import { ref, onMounted, watch, computed, inject } from "vue";
import { i18n } from "@/plugins/vuei18n";
import LogoBlue from "@/assets/images/logo-blue.png";
import { Options } from "@/class";
import TabButton from "@/components/register/TabButton.vue";
import TabContent from "@/components/register/TabContent.vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";

const { getters, dispatch } = useStore();
const lang = ref();
const api: any = inject("api");
const activeStep = ref();

const me = computed(() => getters["auth/me"]);
const router = useRouter();
const tabs = [
  { title: "step1", icon: "fa-regular fa-building" }, { title: "step2", icon: "fa-regular fa-warehouse" },
  { title: "step3", icon: "fa-regular fa-people-group" }, { title: "step4", icon: "fa-regular fa-house-circle-check" }
];


onMounted(() => {
  if (me.value) {
    lang.value = me.value.locale;
  }
  if (me.value.register_log.data.team) {
    activeTab.value = "step4";
    router.push({ name: `RegisterStep4` });
  }
  if(router.currentRoute.value.name === "RegisterStep1") activeTab.value = "step1";
  if(router.currentRoute.value.name === "RegisterStep2") activeTab.value = "step2";
  if(router.currentRoute.value.name === "RegisterStep3") activeTab.value = "step3";
  if(router.currentRoute.value.name === "RegisterStep4") activeTab.value = "step4";
});


watch(lang, () => {
  if (!me.value.active_company) {

    const body = {
      locale: lang.value
    };
    api.put(`customer/profile`, body)
      .then((res) => {
        // dispatch("auth/setMe", res);
      })
      .catch(() => {

      });
  }
  sessionStorage.setItem("lang", lang.value);
  i18n.global.locale.value = lang.value;
});


const activeTab = ref("step1");

const selectTab = (ind) => {
  activeTab.value = `step${ind}`;
  router.push({ name: `RegisterStep${ind}` });
};

const handleTabClick = (tab) => {
  activeStep.value = me.value?.register_log?.active_step;
  if (!me.value.register_log) {
    router.push({ path: `/create-company/step1}` });
  } else {
    if (activeStep.value === "company" && ["step1"].includes(tab.title)) {
      activeTab.value = tab.title;
      router.push({ path: `/create-company/${tab.title}` });
    } else if (activeStep.value === "hub" && ["step1", "step2"].includes(tab.title)) {
      activeTab.value = tab.title;
      router.push({ name: `RegisterStep${tab.title.charAt(4)}` });
    } else if (activeStep.value === "team" && ["step1", "step2", "step3"].includes(tab.title)) {
      activeTab.value = tab.title;
      router.push({ name: `RegisterStep${tab.title.charAt(4)}` });
    } else if (me.value.register_log.completed_at) {
      activeTab.value = "step4";
      router.push({ name: `RegisterStep${tab.title.charAt(4)}` });
    } else {
      console.warn("Navigation blocked!");
    }
  }
};


// watch(() => me.value, (newValue, oldValue) => {
//   if (newValue !== oldValue) {
//     handleTabClick({ title: activeTab.value });
//   }
// });

const isTabDisabled = (tab) => {
  const currentStep = me.value.register_log.active_step;
  const allowedTabs = {
    company: ["step1", "step2"],
    hub: ["step1", "step2", "step3"],
    team: ["step1", "step2", "step3", "step4"]
  };

  return !allowedTabs[currentStep]?.includes(tab.title);
};

const isStepEnabled = (tabTitle) => {
  activeStep.value = me.value.register_log.active_step;
  if (!me.value.register_log) return "step1" === tabTitle;
  if (activeStep.value === "company") return ["step1"].includes(tabTitle);
  if (activeStep.value === "hub") return ["step1", "step2"].includes(tabTitle);
  if (activeStep.value === "team") return ["step1", "step2", "step3",'step4'].includes(tabTitle);
  if (me.value.register_log.completed_at) return ["step4"].includes(tabTitle);
  return false;
};

// TODO BURAYA ROUTE BAKARAK ACTİVE TAB I DEĞİŞTİR WATCH İLE

watch(() => router.currentRoute.value.name, (newValue, oldValue) => {
if(router.currentRoute.value.name === "RegisterStep1") activeTab.value = "step1";
if(router.currentRoute.value.name === "RegisterStep2") activeTab.value = "step2";
if(router.currentRoute.value.name === "RegisterStep3") activeTab.value = "step3";
if(router.currentRoute.value.name === "RegisterStep4") activeTab.value = "step4";
});


</script>
<template>
  <div class="flex flex-col  h-screen">
    <div class="flex flex-row justify-between p-8 border-b border-slate-300 w-full sticky top-0 bg-white z-10">
      <img
        :src="LogoBlue"
        style="object-fit: contain;"
        class="w-4/12 lg:w-1/12 cursor-pointer"
        @click="() => router.push({ name: 'Home' })"
      >
      <div class="w-2/6 lg:w-1/6">
        <el-select
          v-model="lang"
          :placeholder="$t('Language')"
        >
          <el-option
            v-for="item in Options.lang_list"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="flex flex-1 w-full flex-col lg:flex-row lg:h-full">
      <div class="w-full lg:w-40 flex flex-row lg:flex-col  top-16 lg:top-0 lg:sticky">
        <TabButton
          v-for="(tab, index) in tabs"
          :key="tab.title"
          :item="tab"
          :disabled="!isStepEnabled(tab.title)"
          :activeTab="activeTab"
          @click="handleTabClick(tab)"

        />
        <div class="hidden lg:flex flex-row h-full flex-1 w-full">
          <div class="reg-navbar flex flex-1 border-r border-slate-300">
          </div>
          <div class="w-8 h-full flex" />
        </div>
      </div>
      <div class="flex-1 overflow-y-auto">
        <TabContent
          :activeTab="activeTab"
          @setTab="selectTab"
          :lang="lang"
          class="tab-content"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: red;
  font-size: 32px;
  font-weight: 600;
}

.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
  height: 100%;
}

.reg-navbar {
  background-color: #F0F0F0;
  height: 100%;
}

.tab-content {
  flex: 1;
  padding: 32px; /* Adjust the padding as needed */
}

.sticky {
  position: sticky;
  background-color: white; /* Ensures background remains consistent during scroll */
  z-index: 10; /* Ensures sticky elements remain on top */
}

.top-0 {
  top: 0;
}

.top-16 {
  top: 16px;
}
</style>

