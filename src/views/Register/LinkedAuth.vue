<script setup>
import {useRoute} from "vue-router";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {ref, onMounted, inject} from "vue";
import router from "@/router";
import {useToast} from "vue-toastification";

const api = inject("api");

const toast = useToast()
const route = useRoute();

const loader = ref();

onMounted(() => {
  localStorage.removeItem("authToken")
  loader.value.show();
  api.get(`auth/linkedin/callback${route.fullPath.split("linkedin-auth")[1]}`)
      .then((res) => {
        localStorage.setItem("authToken", res.token);
        router.push({name: "SelectActiveCompany"});
      })
      .catch((err) => {
        toast.error(err.data.message);
        router.push({name: "Login"});
      })
      .finally(() => {
        loader.value.hide();
      });
});
</script>

<template>
  <div class="h-full w-full">
    <LoadingBlock ref="loader"/>
  </div>
</template>
