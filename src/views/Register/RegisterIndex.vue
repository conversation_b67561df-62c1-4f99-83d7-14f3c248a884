<script setup>
import router from "@/router";
import { inject, reactive, ref, watch, onMounted } from "vue";
import { useRoute } from "vue-router"; // useRoute'u import et
import PhoneInputExtended from "@/components/ui/PhoneInputExtended.vue";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import LogoBlue from "../../assets/images/logo-blue.png";
import { Options } from "@/class";
import { i18n } from "@/plugins/vuei18n";
import Carousel from "@/components/register/Carousel.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const route = useRoute(); // Route'ı kullan
const toast = useToast()
const { t } = useI18n()
const api = inject("api");

const loading = ref(false);
const lang = ref();
const isPasswordHidden = ref();

const user_phone = reactive({
  country: "TR",
  phone: null,
});

const form = reactive({
  email: null,
  password: null,
  password_confirmation: null,
  nameSurname: null,
  rememberMe: false,
});

onMounted(() => {



  if (route.query.plan) {
    localStorage.setItem("plan", route.query.plan);
  }

  let localLang = sessionStorage.getItem("lang");
  const userLang = Intl?.DateTimeFormat()?.resolvedOptions()?.locale?.split('-')?.[0] || navigator.language || navigator.userLanguage;
  lang.value = localLang ? localLang : userLang.split("-")[0];
});

watch(lang, () => {
  sessionStorage.setItem("lang", lang.value);
  i18n.global.locale.value = lang.value;
});

const goToLogin = () => {
  localStorage.removeItem("authToken");
  router.push({ name: "Login" });
};

const createAcc = () => {
  loading.value = true;
  let body = {
    name: form.nameSurname,
    email: form.email,
    password: form.password,
    password_confirmation: form.password_confirmation || form.password,
    phone_country: user_phone.country,
    phone: user_phone.phone,
    locale: "tr",
  };

  api
    .post("customer/register", body)
    .then((res) => {
      let message = t('A verification link has been sent to the e-mail address', { message: form.email });
      toast.success(message);
      router.push({ name: "Login" });
    })
    .catch((err) => {
      toast.error(err.message);
    })
    .finally(() => {
      loading.value = false;
    });
};

const withGoogle = () => {
  api("auth/google")
    .then((res) => {
      window.location.href = res.url;
    });
};

const setPasswordHidden = () => {
  isPasswordHidden.value = !isPasswordHidden.value;
};
</script>

<template>
  <div class="flex flex-row w-full register-wrapper overflow-x-hidden">
    <div class="h-full w-full lg:w-1/2 px-12 lg:px-20 pb-8 lg:pb-8 pt-6 lg:pt-16">
      <div class="flex flex-row justify-between mb-8">
        <img
          :src="LogoBlue"
          style="width: 30%; object-fit: contain;"
        >

        <div class="w-28">
          <el-select
            v-model="lang"
            :placeholder="$t('Language')"
          >
            <el-option
              v-for="item in Options.lang_list"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </div>

      </div>
      <div class="text-2xl font-semibold">{{ t('Move your business to QDelivery now') }}</div>
      <div class="text-lg font-medium opacity-50 ">{{ t('Start your free 14 day trial') }}</div>
      <div class=" flex justify-start items-center py-5">
        <div class="font-light mr-3 opacity-60 ">
          {{ t('Already have an account') }}
        </div>
        <div
          class="text-indigo-600 font-semibold cursor-pointer mr-3 "
          @click="goToLogin"
        >{{ t('Login') }}</div>
      </div>

      <el-form
        label-position="top"
        class=" space-y-6 w-full lg:w-3/5"
      >
        <el-form-item
          :label="t('Name Surname')"
          required
        >
          <el-input
            v-model="form.nameSurname"
            :placeholder="t('Name')"
          />
        </el-form-item>
        <el-form-item
          :label="t('Email')"
          required
        >
          <el-input
            v-model="form.email"
            type="email"
            :placeholder="t('Email')"
          ></el-input>
        </el-form-item>
        <PhoneInputExtended v-model="user_phone" />

        <el-form-item
          :label="t('Password')"
          required
        >
          <el-input
            v-model="form.password"
            :type="isPasswordHidden ? 'password' : 'text'"
            :placeholder="t('Password')"
          >
            <template #prefix>
              <span
                class="ml-auto absolute right-0 cursor-pointer"
                @click="setPasswordHidden"
              >
                <FontAwesomeIcon
                  class="text-indigo-600"
                  :icon="isPasswordHidden ? 'eye' : 'eye-slash'"
                />
              </span>
            </template>
          </el-input>
        </el-form-item>
        <el-button
          @click="createAcc"
          type="primary"
          class="w-full mt-6 font-semibold"
          style="height: 40px;"
        >
          {{ t('Get Started') }}
        </el-button>
        <div
          class="flex justify-center items-center font-light"
          style="margin-top: 10px;"
        >
          {{ t('or') }}
        </div>
        <div
          @click="withGoogle"
          class="flex justify-center items-center border-2 border-gray-300 cursor-pointer"
          style="margin-top: 10px; height: 40px;"
        >
          <img
            class="h-6"
            src="../../assets/images/googleLogo.png"
            alt="logo"
          />
          <div class="text-l text-slate-700 font-medium py-1 ml-1">{{ t('Sign up with Google') }}</div>
        </div>
      </el-form>
    </div>
    <Carousel />
  </div>
</template>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');

.register-wrapper * {
  font-family: 'Inter';
}

@media (max-aspect-ratio: 70/30) {
  .register-wrapper {
    height: inherit;
  }
}

@media (min-aspect-ratio: 70/30) {
  .register-wrapper {
    height: auto;
  }

  .register-wrapper>div {
    padding-top: 50px;
  }
}
</style>
