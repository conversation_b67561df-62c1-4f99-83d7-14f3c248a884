<script lang="ts" setup>
import { ref, onMounted, watch, computed, inject } from "vue";
import { i18n } from "@/plugins/vuei18n";
//@ts-ignore
import LogoBlue from "@/assets/images/logo-blue.png";
import { Options } from "@/class";
import { useStore } from "vuex";
import router from "@/router";
import TabButton2 from "@/components/register/TabButton2.vue";
import TabContent2 from "@/components/register/TabContent2.vue";

const { getters, dispatch } = useStore();
const lang = ref();
const api: any = inject("api");

const me = computed(() => getters["auth/me"]);

onMounted(() => {
  if (me.value) {
    lang.value = me.value.locale;
  }
});

watch(lang, () => {
  if (me.value) {

    const body = {
      locale: lang.value
    };
    api.put(`customer/profile`, body)
      .then((res) => {
        dispatch("auth/setMe", res);
      })
      .catch((err) => {

      });
  }
  sessionStorage.setItem("lang", lang.value);
  i18n.global.locale.value = lang.value;
});

const tabs = [{ title: "step1", icon: "fa-regular fa-building" }, { title: "step2", icon: "fa-regular fa-warehouse" },
  { title: "step3", icon: "fa-regular fa-people-group" }, { title: "step4", icon: "fa-regular fa-house-circle-check" }];

const activeTab = ref("step1");

const selectTab = (ind) => {
  activeTab.value = ind;
};



</script>
<template>
  <div class="flex flex-col  h-screen">
    <div class="flex flex-row justify-between p-8 border-b border-slate-300 w-full sticky top-0 bg-white z-10">
      <img
        :src="LogoBlue"
        style="object-fit: contain;"
        class="w-4/12 lg:w-1/12 cursor-pointer"
        @click="() => router.push({ name: 'Home' })"
      >
      <div class="w-2/6 lg:w-1/6">
        <el-select
          v-model="lang"
          :placeholder="$t('Language')"
        >
          <el-option
            v-for="item in Options.lang_list"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="flex flex-1 w-full flex-col lg:flex-row lg:h-full">
      <div class="w-full lg:w-40 flex flex-row lg:flex-col  top-16 lg:top-0 lg:sticky">
        <TabButton2
          v-for="(tab, index) in tabs"
          :key="tab.title"
          :item="tab"
          :activeTab="activeTab"
        />



        <div class="hidden lg:flex flex-row h-full flex-1 w-full">
          <div class="reg-navbar flex flex-1 border-r border-slate-300">
          </div>
          <div class="w-8 h-full flex" />
        </div>
      </div>
      <div class="flex-1 overflow-y-auto">
        <TabContent2
          :activeTab="activeTab"
          @setTab="selectTab"
          :lang="lang"
          class="tab-content"
       />

      </div>
    </div>
  </div>
</template>

<style scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: red;
  font-size: 32px;
  font-weight: 600;
}

.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
  height: 100%;
}

.reg-navbar {
  background-color: #F0F0F0;
  height: 100%;
}

.tab-content {
  flex: 1;
  padding: 32px; /* Adjust the padding as needed */
}

.sticky {
  position: sticky;
  background-color: white; /* Ensures background remains consistent during scroll */
  z-index: 10; /* Ensures sticky elements remain on top */
}

.top-0 {
  top: 0;
}

.top-16 {
  top: 16px;
}
</style>

