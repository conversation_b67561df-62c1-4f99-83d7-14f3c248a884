<script setup>
import router from "@/router";
import { inject, onMounted, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import LogoBlue from "../../assets/images/logo-blue.png";
import { useToast } from "vue-toastification";
import { Options } from "@/class";
import { i18n } from "@/plugins/vuei18n";
import { useI18n } from "vue-i18n";
import PhoneInputExtended from '@/components/ui/PhoneInputExtended.vue';

const toast = useToast()
const { t } = useI18n();
const api = inject("api")

// const api = inject("api");
const lang = ref();
const phone = reactive({
  country: "TR",
  phone: null,
});
const { getters, dispatch } = useStore();


onMounted(() => {
  let localLang = sessionStorage.getItem("lang")
  let userLang = navigator.language || navigator.userLanguage;
  lang.value = localLang ? localLang : userLang.split("-")[0]
});

watch(lang, () => {
  sessionStorage.setItem("lang", lang.value)
  i18n.global.locale.value = lang.value
});

const handlePhoneSave = () => {
  const body = {
    phone_country: phone.country,
    phone: phone.phone
  };
  api.put(`customer/profile`, body)
    .then((res) => {
      toast.success(t('Düzenleme işleminiz başarılı.'));
      dispatch("auth/setMe", res)
      setTimeout(() => {
        //   router.push({ name: "CreateCompany" })
        router.push({ name: "SelectActiveCompany" });
      }, 1200);
    })
    .catch((err) => {

      toast.error(err?.message);
    });
}

</script>

<template>
  <div class="flex flex-row justify-between p-8 border-b border-slatery-300 tabs-wrapper">
    <img
      :src="LogoBlue"
      style="width: 13%; object-fit: contain;"
    >
    <div class="w-1/6">
      <el-select
        v-model="lang"
        :placeholder="$t('Language')"
      >
        <el-option
          v-for="item in Options.lang_list"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>
    </div>
  </div>
  <div class="px-64 pt-16 tabs-wrapper">
    <div class="border-b text-indigo-600 font-semibold text-lg pb-4 mb-2">
      {{ t('Account Verification') }}
    </div>
    <!-- <div class="flex flex-1 w-full flex-col h-full text-xl pr-20"> -->
    <div>
      <el-form label-position="top">
        <el-form-item class="phone_wrapper flex flex-col">
          <PhoneInputExtended
            :has-space="true"
            v-model="phone"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="w-full align-end justify-end flex mt-12">
      <el-button
        type="primary"
        @click="handlePhoneSave"
      >
        {{ t('Next') }}
        <FontAwesomeIcon
          icon="chevron-right"
          class="ml-2"
        />
      </el-button>
    </div>
    <!-- </div> -->
  </div>
</template>


<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');

.tabs-wrapper * {
  font-family: 'Inter';
}
</style>
