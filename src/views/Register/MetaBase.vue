<script setup>
import { KJ<PERSON> } from 'jsrsasign';
import { computed , onMounted} from "vue";
import { useStore } from "vuex";


const { dispatch, getters } = useStore();
const me = computed(() => getters["auth/me"])


const METABASE_SITE_URL = import.meta.env.VITE_METABASE_SITE_URL;
const METABASE_SECRET_KEY = import.meta.env.VITE_METABASE_SECRET_KEY;
const METABASE_ENABLE = import.meta.env.VITE_METABASE_ENABLE;

const header = { alg: "HS256", typ: "JWT" };
var payload = {
  resource: { question: 1125 },
  params: {
    "company_id": me.value.active_company_id,
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};
var secretKey = METABASE_SECRET_KEY;

var sHeader = JSON.stringify(header);
var sPayload = JSON.stringify(payload);

var token = KJUR.jws.JWS.sign("HS256", sHeader, sPayload, { utf8: secretKey });

var iframeUrl = METABASE_SITE_URL + "/embed/question/" + token + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";







var payload2 = {
  resource: { question: 1128 },
  params: {
    "company_id": me.value.active_company_id,
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};

var sHeader2 = JSON.stringify(header);
var sPayload2= JSON.stringify(payload2);

var token2 = KJUR.jws.JWS.sign("HS256", sHeader2, sPayload2, { utf8: secretKey });

var iframeUrl2 = METABASE_SITE_URL + "/embed/question/" + token2 + "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";




var payload3 = {
  resource: { question: 1129 },
  params: {
    "company_id": me.value.active_company_id,
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};

var sHeader3 = JSON.stringify(header);
var sPayload3= JSON.stringify(payload3);

var token3 = KJUR.jws.JWS.sign("HS256", sHeader3, sPayload3, { utf8: secretKey });

var iframeUrl3 = METABASE_SITE_URL + "/embed/question/" + token3 +  "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";






var payload4 = {
  resource: { question: 1126 },
  params: {
    "company_id":me.value.active_company_id,
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};

var sHeader4 = JSON.stringify(header);
var sPayload4= JSON.stringify(payload4);

var token4 = KJUR.jws.JWS.sign("HS256", sHeader4, sPayload4, { utf8: secretKey });

var iframeUrl4 = METABASE_SITE_URL + "/embed/question/" + token4 +    "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";





var payload5 = {
  resource: { question: 1127 },
  params: {
    "company_id": me.value.active_company_id,
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};

var sHeader5 = JSON.stringify(header);
var sPayload5= JSON.stringify(payload5);

var token5 = KJUR.jws.JWS.sign("HS256", sHeader5, sPayload5, { utf8: secretKey });

var iframeUrl5 = METABASE_SITE_URL + "/embed/question/" + token5 +  "#font=Inter&theme=transparent&bordered=false&titled=false&hide_download_button=true";












</script>

<template>
  <div class="iframe-container" v-if="METABASE_ENABLE">
    <div class="iframe-row">

      <iframe :src="iframeUrl" class="full-size-iframe rounded-lg shadow-2xl"></iframe>
      <iframe :src="iframeUrl2" class="full-size-iframe rounded-lg shadow-2xl"></iframe>
    </div>
    <div class="iframe-row">
      <iframe :src="iframeUrl3" class="full-size-iframe rounded-lg shadow-2xl"></iframe>
      <iframe :src="iframeUrl4" class="full-size-iframe rounded-lg shadow-2xl"></iframe>
    </div>
    <div class="iframe-row">
      <iframe :src="iframeUrl5" class="full-size-iframe rounded-lg shadow-2xl"></iframe>
      <div>

      </div>
    </div>

  </div>
</template>

<style scoped>
.full-size-iframe {
  flex: 1;
  width: 400px;
  height: 300px;
  border: none;
  transform: scale(0.70);

}

.iframe-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 400px;
  height: 300px;
  border-radius: 0.5rem; /* 8px */
}

.iframe-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* Ekran genişliği 768px'in altına düştüğünde */
@media (max-width: 768px) {
  .full-size-iframe {
    width: 100%; /* İframe'in genişliğini %100 yap */
    height: auto; /* İframe'in yüksekliğini otomatik yap */
  }

  .iframe-row {
    flex-direction: column; /* İframe'leri dikey bir sıraya koy */
    width: 100%; /* İframe satırının genişliğini %100 yap */
    height: auto; /* İframe satırının yüksekliğini otomatik yap */
  }
}
</style>