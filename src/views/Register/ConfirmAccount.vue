<script setup>
import router from "@/router";
import { inject, onMounted, computed, ref, watch } from "vue";
import { useStore } from "vuex";
import LogoBlue from "../../assets/images/logo-blue.png";
import { useToast } from "vue-toastification";
import { Options } from "@/class";
import { i18n } from "@/plugins/vuei18n";
import { useI18n } from "vue-i18n";

const { getters } = useStore();
const { t } = useI18n();

const lang = ref();

const loading = ref(false)

const profile = computed(() => getters["auth/me"]);

onMounted(() => {
  let localLang = sessionStorage.getItem("lang")
  let userLang = navigator.language || navigator.userLanguage;
  lang.value = localLang ? localLang : userLang.split("-")[0]
});

watch(lang, () => {
  sessionStorage.setItem("lang", lang.value)
  i18n.global.locale.value = lang.value
})

onMounted(() => {
  loading.value = true
  if (profile.value.email_verified_at) {
    router.push({ name: "SelectActiveCompany" });
  }
  loading.value = false
});

</script>

<template>
  <div class="flex flex-row justify-between p-8 border-b border-slatery-300 tabs-wrapper">
    <img
      :src="LogoBlue"
      style="width: 13%; object-fit: contain;"
    >
    <div class="w-1/6">
      <el-select
        v-model="lang"
        :placeholder="$t('Language')"
      >
        <el-option
          v-for="item in Options.lang_list"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>
    </div>
  </div>
  <div class="px-64 pt-16 tabs-wrapper">
    <div class="border-b text-indigo-600 font-semibold text-lg pb-4 mb-2">
      {{ t('Account Verification') }}
    </div>
    <div class="flex flex-1 w-full flex-col h-full text-xl pr-20">
      <div class="text-slate-700 my-8 font-semibold">
        {{ t('We have successfully received your registration.') }}
      </div>
      <div class="font-light">
        <p class="text-slate-600">
          {{ t('To activate your QDelivery account,') }}
          <span>{{ t("please follow the account verification link sent to your email address.")}}</span>
        </p>
      </div>
    </div>
  </div>
</template>


<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');

.tabs-wrapper * {
  font-family: 'Inter';
}
</style>
