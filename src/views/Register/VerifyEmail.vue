<script setup>
import {useRoute} from "vue-router";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {ref, onMounted, inject} from "vue";
import router from "@/router";
import {useI18n} from "vue-i18n";
import {useToast} from "vue-toastification";

const toast = useToast()

const {t} = useI18n()
const api = inject("api");
const route = useRoute();
const loader = ref();

onMounted(() => {
  loader.value.show();
  api(`verify-email/${route.query.id}/${route.query.hash}?expires=${route.query.expires}&signature=${route.query.signature}`)
      .then(() => toast.success(t('Your account has been verified')))
      .catch((err) => toast.error(err.data.message))
      .finally(() => {
        loader.value.hide();
        router.push({name: "Lo<PERSON>"});
      });
});

</script>

<template>
  <div class="h-full w-full">
    <LoadingBlock ref="loader"/>
  </div>
</template>
