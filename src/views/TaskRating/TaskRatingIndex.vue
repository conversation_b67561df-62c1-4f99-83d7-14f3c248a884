<script setup>
import { onMounted, ref } from "vue";
import axios from "axios";
import { useRoute } from "vue-router";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";

const route = useRoute();
const task = ref();
const loader = ref();
const comment = ref()
const rate = ref()
onMounted(() => {
  getTrackingData();
});

const getTrackingData = () => {
  loader.value.show();
  axios
    .get(`${import.meta.env.VITE_APP_BASE_API}tracking?code=${route.query.code}`)
    .then((response) => {
      task.value = response.data;
    })
    .finally(() => loader.value.hide());
};

//TODO
const onSave = () => {
  // let body = {
  //   code:route.query.code,
  //   rating: rate.value * 2,
  //   comment:comment.value,
  // }
}
</script>

<template>
  <div class="h-full w-full">
    <LoadingBlock ref="loader" />

    <div
      v-if="task"
      class="min-h-screen flex items-center justify-center bg-slate-50 py-12 px-4 sm:px-6 lg:px-8"
    >
      <div class="max-w-md w-full drop-shadow-2xldrop-shadow-2xl">
        <div
          class="flex-col items-center space-y-4 bg-white p-5 rounded-lg"
        >

            <div class="flex items-center">
              <img
                class="inline-block border-2 border-gray-400 h-14 w-14 rounded-full"
                :src="task.courier.avatar_url"
                alt="Avatar"
              />
              <div class="ml-3">
                <p class="text-xs font-medium text-gray-400">Sürücü</p>
                <p class="text-lg font-semibold text-black">
                  {{ task?.courier?.name }}
                </p>
              </div>
            </div>

          <div class="text-right">
            <el-rate v-model="rate" allow-half />
          </div>
          <div>
            <el-input
              v-model="comment"
              maxlength="500"
              placeholder="Please input"
              show-word-limit
              type="textarea"
            />
          </div>
          <div class="text-right">
            <el-button @click="onSave" type="primary" size="small" :disabled="!rate">
               Gönder
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
