<script setup>
import { onMounted, ref } from "vue";
import TrackingDetail from "@/views/Tracking/TrackingDetail.vue";
import TrackingMap from "@/views/Tracking/TrackingMap.vue";
import { useRoute } from "vue-router";
import axios from "axios";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";

const trackingData = ref();
const detailVisible = ref(false);
const statusLog = ref(0);
const comment = ref();
const rate = ref();
const loader = ref();
const loading = ref(true);
const hiderating = ref(false);

const showDetail = () => {
  detailVisible.value = !detailVisible.value;
};
const route = useRoute();

onMounted(() => {
  loader.value.show();
  getTrackingData();
});

const getTrackingData = () => {
  axios
    .get(`${import.meta.env.VITE_APP_API}tracking?code=${route.query.code}`)
    .then((response) => {
      trackingData.value = response.data;
      if (response.data?.status.category_slug === "created" || response.data?.status.category_slug === "assigned") {
        statusLog.value = 0;
      } else if (response.data?.status.category_slug === "in_progress") {
        statusLog.value = 33;
      }
      else if (response.data?.status.category_slug === "on_delivery") {
        statusLog.value = 66;
      } else if (response.data?.status.category_slug === "completed") {
        statusLog.value = 100;
      }
    })
    .finally(() => {
      loader.value.hide();
      loading.value = false;
    });
  setTimeout(() => {
    getTrackingData();
  }, 30000);
};

const onSave = () => {
  let body = {
    code: route.query.code,
    rating: rate.value * 2,
    comment: comment.value
  };
  axios.post(`${import.meta.env.VITE_APP_BASE_API}task-ratings`, body).then(() => {
    hiderating.value = true;
  });
};
</script>

<template>
  <!--
			Gri durum çubuğu her adımda sabit
			Yeşil durum çubuğu
				adım 1: w-0
				adım 2: w-1/2
				adım 3: w-full

			Stepler aktif olduğunda
				arkaplan: bg-green-500 text: text-black
			Stepler pasif iken
				arkaplan: bg-gray-200 text: text-gray-200
			-->
  <LoadingBlock ref="loader" />
  <div v-if="trackingData" class="w-full h-full flex flex-col">
    <header
      class="h-12 md:h-16 flex flex-shrink-0 items-center justify-between px-6 lg:px-5"
      :style="{ background: trackingData?.company?.primary_color ? trackingData?.company?.primary_color : '#1450FF' }"
    >
      <div class="flex items-center">
        <img
          v-if="trackingData?.company?.brand_logo"
          :src="trackingData?.company?.brand_logo"
          class="w-10 h-auto"
          alt="QDelivery Logo"
        />
        <div class="hidden md:block text-lg text-white font-medium ml-2">
          Merhaba, {{ trackingData?.destination_name }}
        </div>
      </div>
      <div><h1 class="text-white">{{ $t("Canlı Sipariş Takibi") }}</h1></div>
    </header>
    <div class="md:hidden text-2xl text-black opacity-90 font-medium my-2 mx-5">
      Merhaba, {{ trackingData?.destination_name }}
    </div>
    <div class="flex flex-col md:flex-row bg-white flex-grow">
      <div class="flex-shrink md:w-full md:h-full">
        <div v-if="trackingData.courier" class="flex items-center pb-4 px-5 py-4">
          <div>
            <img
              class="inline-block border-2 border-gray-400 h-12 w-12 rounded-full"
              :src="trackingData.courier.avatar_url"
              alt="Avatar"
            />
          </div>
          <div class="ml-3">
            <p class="text-xs font-medium text-gray-400">{{ $t("Driver") }}</p>
            <p class="text-lg font-semibold text-black">
              {{ trackingData?.courier?.name }}
            </p>
          </div>
        </div>
        <div class="border-t border-gray-200 py-5 px-8">
          <div class="text-2xl text-red-600" v-if="trackingData.status.category_slug === 'cancelled'">
            Siparişiniz İptal Edildi
          </div>
          <div class="text-2xl text-red-600" v-if="trackingData.status.category_slug === 'failed'">
            Teslimat Başarısız Oldu
          </div>
          <div class="relative"
               v-if="trackingData.status.category_slug !== 'failed' && trackingData.status.category_slug !== 'cancelled'">
            <div
              class="absolute top-2 w-full h-1.5 bg-gray-100 z-0"
            ></div>
            <div
              class="absolute top-2 left-6  h-1.5 bg-green-500 z-0"
              :class="[
                          statusLog === 0 ? 'w-0' : '',
                          statusLog === 33 ? 'w-1/3' : '',
                          statusLog === 66 ? 'w-2/3' : '',
                          statusLog === 99 ? 'w-full' : '',
                        ]"
            ></div>
            <div class="flex items-center justify-between z-10 relative">
              <div class="flex flex-col items-center relative">
                <div class="absolute bg-white w-1/2 h-full left-0 z-0"></div>
                <div class="w-6 h-6 rounded-full bg-green-500 z-10"></div>
                <div class="text-xs font-semibold mt-2 z-10 text-black">
                  Sipariş Alındı
                </div>
              </div>
              <div class="flex flex-col items-center relative">
                <div
                  class="w-6 h-6 rounded-full z-10"
                  :class="[statusLog >= 33 ? 'bg-green-500' : 'bg-gray-200']"
                />
                <div
                  class="text-xs font-semibold mt-2 z-10"
                  :class="[statusLog >= 33 ? 'text-black' : 'text-gray-200']"
                >
                  Hazırlanıyor
                </div>
              </div>
              <div class="flex flex-col items-center relative">
                <div
                  class="w-6 h-6 rounded-full z-10"
                  :class="[statusLog >= 66 ? 'bg-green-500' : 'bg-gray-200']"
                />
                <div
                  class="text-xs font-semibold mt-2 z-10"
                  :class="[statusLog >= 66 ? 'text-black' : 'text-gray-200']"
                >
                  Yola Çıktı
                </div>
              </div>
              <div class="flex flex-col items-center relative">
                <div class="absolute bg-white w-1/2 h-full right-0 z-0"></div>
                <div
                  class="w-6 h-6 rounded-full z-10"
                  :class="[statusLog >= 100 ? 'bg-green-500' : 'bg-gray-200']"
                />
                <div
                  class="text-xs font-semibold mt-2 z-10"
                  :class="[statusLog >= 100 ? 'text-black' : 'text-gray-200']"
                >
                  Teslim Edildi
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="hidden md:block">
          <h3
            class="h-12 px-5 bg-gray-100 flex items-center justify-between border-t border-b border-gray-200"
          >
            <div class="flex items-center">
              <!--
								Sipariş Detayı açıkken icon için class
								"transform -rotate-90"
								-->
              <FontAwesomeIcon :icon="['fas', 'receipt']" class="h-4 w-4" />
              <span class="ml-1 text-base">Sipariş Detayı</span>
            </div>
            <div>
              <FontAwesomeIcon
                :icon="['fas', 'chevron-down']"
                class="h-4 w-4"
              />
            </div>
          </h3>
          <TrackingDetail :trackingData="trackingData" />
        </div>
      </div>
      <div class="md:hidden">
        <h3
          class="h-12 px-5 bg-gray-100 flex items-center justify-between border-t border-b border-gray-200"
          @click="showDetail"
        >
          <div class="flex items-center">
            <!--
							Sipariş Detayı açıkken icon için class
							"transform -rotate-90"
							-->
            <FontAwesomeIcon :icon="['fas', 'receipt']" class="h-4 w-4" />
            <span class="ml-1 text-base">Sipariş Detayı</span>
          </div>
          <div>
            <FontAwesomeIcon
              :icon="['fas', detailVisible ? 'chevron-up' : 'chevron-down']"
              class="h-4 w-4"
            />
          </div>
        </h3>
      </div>
      <div class="w-full h-full relative">
        <!-- :lat="data.lat" :lng="data.lng" -->
        <div class="w-full h-full z-30">
          <template v-if="statusLog !== 100">
            <TrackingMap :trackingData="trackingData" />
          </template>
          <template v-else>
            <div
              v-if="hiderating"
              class="text-slate-700 text-lg text-center mt-12"
            >
              Görüşlerinizi bizimle paylaştığınız için teşekkür ederiz.
            </div>
<!--            <div-->
<!--              v-else-->
<!--              class="flex-col items-center space-y-4 bg-white p-5 rounded-lg"-->
<!--            >-->
<!--              <div class="text-slate-700 text-center text-lg">-->
<!--                Teslimat ile ilgili görüşlerinizi bizimle paylaşmak ister-->
<!--                misiniz?-->
<!--              </div>-->
<!--              <div class="flex items-center">-->
<!--                <div class="text-slate-700 text-lg">Sürücü Memuniyeti:</div>-->
<!--                <el-rate class="ml-2" v-model="rate" allow-half />-->
<!--              </div>-->
<!--              <div>-->
<!--                <el-input-->
<!--                  v-model="comment"-->
<!--                  maxlength="500"-->
<!--                  placeholder="Yorum"-->
<!--                  show-word-limit-->
<!--                  type="textarea"-->
<!--                />-->
<!--              </div>-->
<!--              <div class="text-right">-->
<!--                <el-button @click="onSave" :disabled="!rate">-->
<!--                  Gönder-->
<!--                </el-button>-->
<!--              </div>-->
<!--            </div>-->
          </template>
        </div>
        <div
          class="absolute inset-0 md:hidden"
          :class="[detailVisible ? 'bg-white' : '-z-10']"
        >
          <div :class="[detailVisible ? '' : 'hidden']">
            <TrackingDetail :trackingData="trackingData" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else-if="!loading" class="w-full h-full flex flex-col">
    <header
      class="h-12 md:h-16 flex flex-shrink-0 items-center justify-between bg-indigo-600 px-6 lg:px-5"
    >
      <div><h1 class="text-white">Canlı Sipariş Takibi</h1></div>
    </header>
    <div class="flex-grow flex items-center justify-center">
      <div class="text-4xl font-bold text-slate-700 text-center">
        Herhangi bir kayıda ulaşılamamıştır.
      </div>
    </div>
  </div>
</template>
