<script setup>
import dayjs from "dayjs";
const props = defineProps({
  trackingData: { type: Object },
});
</script>
<template>
  <div class="px-5">
    <div class="divide-y divide-gray-200">
      <div class="py-5">
        <h4 class="text-lg text-gray-400 font-bold mb-1"><PERSON><PERSON><PERSON><PERSON>du</h4>
        <div class="font-semibold">{{ trackingData?.tracking_code }}</div>
      </div>
      <div
        class="grid grid-cols-1 gap-4 py-5 divide-y divide-gray-200 lg:grid-cols-2 lg:divide-y-0"
      >
        <div>
          <h4 class="text-lg text-gray-400 font-bold mb-1"><PERSON><PERSON><PERSON><PERSON></h4>
          <div>
            <div class="font-semibold text-md">{{trackingData?.origin_name}}</div>
<!--            <div class="font-semibold text-md">Bağdat Caddesi Şubesi</div>-->
            <div class="text-sm mt-1">
             {{trackingData?.origin_address}}
            </div>
          </div>
        </div>
        <div class="pt-5 lg:pt-0">
          <h4 class="text-lg text-gray-400 font-bold mb-1">Alıcı</h4>
          <div>
            <div class="font-semibold text-md mb-1">{{trackingData?.destination_name}}</div>
            <div class="text-sm">
              {{trackingData?.destination_address}}
            </div>
            <div class="font-semibold text-md"> {{trackingData?.destination_phone}}</div>
          </div>
        </div>
      </div>
      <div class="py-5" v-if="trackingData.status.category_slug !== 'failed' && trackingData.status.category_slug !== 'cancelled'">
        <h4 class="text-lg text-gray-400 font-bold mb-1">Teslimat Detayı</h4>
        <div class="text-md font-semibold">
          <h5>Teslimat Aralığı</h5>
          <div v-if="trackingData.starts_at">
            <span class="inline-block w-auto md:w-28"> En Erken </span>:
            <span class="ml-2 font-normal text-sm"> {{
                dayjs(trackingData.starts_at).format('DD.MM.YYYY HH:mm')
              }} </span>
          </div>
          <div>
            <span class="inline-block w-auto md:w-28"> En Geç </span>
            :
            <span class="ml-2 font-normal text-sm"> {{ dayjs(trackingData.ends_at).format('DD.MM.YYYY HH:mm')}} </span>
          </div>
<!--          <div>-->
<!--            <span class="inline-block w-auto md:w-28">Teslimat Kodu</span>-->
<!--            :-->
<!--            <span class="ml-2 font-normal text-sm"> 6536 </span>-->
<!--          </div>-->
        </div>
        <div class="mt-3">
          <div
            class="inline-block bg-yellow-50 p-3 w-auto border border-yellow-100"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <FontAwesomeIcon
                  icon="circle-info"
                  class="h-4 w-4 text-yellow-500"
                />
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-500">
                  Sürücü sizden teslimat kodu talep edebilir.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
