
import { Loader } from "@googlemaps/js-api-loader";

let loader;
let loadPromise;
let isLoaded = false;

export function getGoogleMapsLoader() {
  if (!loader) {
    loader = new Loader({
      apiKey: import.meta.env.VITE_GOOGLE_API_KEY,
      version: "beta",
      libraries: ["geometry", "places"],
    });
  }

  return loader;
}

// Singleton pattern ile Google Maps'i sadece bir kez yükle
export function loadGoogleMaps() {
  if (isLoaded) {
    return Promise.resolve();
  }

  if (loadPromise) {
    return loadPromise;
  }

  loadPromise = getGoogleMapsLoader()
    .load()
    .then(() => {
      isLoaded = true;
      return Promise.resolve();
    })
    .catch((error) => {
      console.error('Google Maps loading failed:', error);
      loadPromise = null; // Reset promise to allow retry
      throw error;
    });

  return loadPromise;
}

// Google Maps'in yüklenip yüklenmediğini kontrol et
export function isGoogleMapsLoaded() {
  return isLoaded;
}

// Throttle utility function
export function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}