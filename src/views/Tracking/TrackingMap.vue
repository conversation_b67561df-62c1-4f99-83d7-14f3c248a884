<template>
  <div id="map" ref="trackingMapRef" class="w-full h-full"></div>
</template>

<script setup>
/* eslint-disable no-undef */
import {ref, onMounted, reactive, watch} from "vue";

import DefaultMapStyle from "../../map-styles/default.json";
import courierPin from "../../assets/images/map/marker/duty_courier.png";
import DestinationSelected from "../../assets/images/map/marker/Destination_Selected.png"
import DestinationSelectedStart from "../../assets/images/map/marker/Destination_Selected_Start.png"
import {createPolyline} from "@/components/operations/helper";
import { loadGoogleMaps, isGoogleMapsLoaded } from "@/views/Tracking/GoogleMapsLoaderService";

const props = defineProps({
  trackingData: {type: Object},
});
const trackingMapRef = ref(null);
const trackingMap = ref(null);
const markers = reactive({
  courier: null,
  destination: null,
  origin: null,
});

const mapPromise = async () => {
  await google.maps.importLibrary("geometry");
  return new Promise((resolve) => {

    const map = new google.maps.Map(trackingMapRef.value, {
      center: {
        lat: 41.013028158816425,
        lng: 28.99737372063146,
      },
      zoom: 10,
      mapTypeControl: true,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        mapTypeIds: ["qdelivery", "roadmap", "satellite", "hybrid", "terrain"],
      },
    });
    map.mapTypes.set(
        "qdelivery",
        new google.maps.StyledMapType(DefaultMapStyle, {name: "QDelivery"})
    );
    map.setMapTypeId("qdelivery");
    google.maps.event.addListenerOnce(map, "tilesloaded", function () {
      resolve(map);
    });
  });
};

watch(() => props.trackingData, (curr, prev) => {

  if (markers.courier) {
     if (curr.courier?.courier_location.lat){
       markers.courier.setPosition({
         lat: curr.courier.courier_location.lat,
         lng: curr.courier.courier_location.lng
       })
     }

    // markers.destination.setPosition({
    //   lat: curr.destination_lat,
    //   lng: curr.destination_lng
    // })
    // markers.origin.setPosition({
    //   lat: curr.origin_lat,
    //   lng: curr.origin_lng
    // })
  } else {

    markers.courier = new google.maps.Marker({
      position: {
        lat: props.trackingData.courier?.courier_location.lat,
        lng: props.trackingData.courier?.courier_location.lng,
      },
      icon: {
        url: courierPin,
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(14, 36),
      },
      map: trackingMap.value,
    });

  }
}, {deep: true})

const loadMap = async () => {

  try {
    mapPromise().then((map) => {
      if (props.trackingData.courier) {

        markers.courier = new google.maps.Marker({
          position: {
            lat: props.trackingData.courier.courier_location.lat,
            lng: props.trackingData.courier.courier_location.lng,
          },
          icon: {
            url: courierPin,
            origin: new google.maps.Point(0, 0),
            anchor: new google.maps.Point(14, 36),
          },
          map,
        });
      }

      markers.origin = new google.maps.Marker({
        position: {
          lat: props.trackingData.origin_lat,
          lng: props.trackingData.origin_lng,
        },
        icon: {
          url: DestinationSelectedStart,
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(14, 36),
        },
        map,
      });


      markers.destination = new google.maps.Marker({
        position: {
          lat: props.trackingData.destination_lat,
          lng: props.trackingData.destination_lng,
        },
        icon: {
          url: DestinationSelected,
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(14, 36),
        },

        map,
      });

      if (props.trackingData.path){


        const data = props.trackingData.path

        createTaskPolyline(data, map)

      }


      let bounds = new google.maps.LatLngBounds();
      if (markers.courier){
        bounds.extend(markers.courier.getPosition());
      }
      if (markers.destination){
        bounds.extend(markers.destination.getPosition());
      }
      if (markers.origin){
        bounds.extend(markers.origin.getPosition());
      }
      map.fitBounds(bounds);
      trackingMap.value = map
    });
  } catch (ex) {
  }
};
const createTaskPolyline = async (data, map) => {



  const polyline = await createPolyline({
    path:data,
    map: map,
    selected: true,
    status: null,

  })
  return polyline

}

onMounted(() => {
  loader.load().then(() => loadMap())
})
</script>
