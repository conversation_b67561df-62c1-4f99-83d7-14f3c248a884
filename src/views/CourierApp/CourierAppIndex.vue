<script setup>
import LogoSvg from "../../assets/images/qdelivery-logo-with-text.svg";
import PlayStore from "../../assets/images/play_store.png";
import AppStore from "../../assets/images/app_store.png";
import {onMounted, ref} from "vue";


const device = ref()
const ios_url = "https://apps.apple.com/tr/app/qdelivery-kurye/id1608898909"
const android_url = "https://play.google.com/store/apps/details?id=com.qdelivery.app"
const env = import.meta.env.MODE

onMounted(() => {
  device.value = getMobileOperatingSystem()
})
const getMobileOperatingSystem = () => {
  let userAgent = navigator.userAgent || navigator.vendor || window.opera;

  if (/android/i.test(userAgent)) {
    if (env !== 'development') {
      window.location.href = android_url
    }
    return "Android";
  }
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    if (env !== 'development') {
      window.location.href = ios_url
    }
    return "iOS";
  }

  return "unknown";
}

const onAppStore = () => {
  window.open(ios_url);
}
const onPlayStore = () => {
  window.open(android_url);
}
</script>

<template>

  <div class="w-full h-full flex flex-col items-center justify-center">
    <img :src="LogoSvg" class="h-56 w-56">
    <div v-if="env !== 'development'">
      <template v-if="device==='iOS'">
        <div class=" h-10 mt-5 flex justify-center items-center" style="width: 425px;">
          <img @click="onAppStore" :src="AppStore" style="width: 50%">
        </div>
      </template>
      <template v-else-if="device==='Android'">
        <div class=" h-10 mt-5 flex justify-center items-center" style="width: 425px;">
          <img @click="onPlayStore" :src="PlayStore" style="width: 50%">
        </div>
      </template>

      <div v-else class=" h-10 mt-5 flex justify-between items-center" style="width: 425px;">
        <img @click="onPlayStore" :src="PlayStore" style="width: 50%;">
        <img @click="onAppStore" :src="AppStore" style="width: 50%; height: 55px;">
      </div>
    </div>
    <div v-else>
      <span class="text-slate-700 text-2xl font-bold">Download Android App</span>
      <div class=" h-10 mt-5 rounded-lg bg-slate-500 flex justify-center items-center">
        <a href="app-release.apk" download="" class="text-white text-xl px-2.5 ">
          Test Apk
        </a>
      </div>
    </div>

  </div>

</template>
