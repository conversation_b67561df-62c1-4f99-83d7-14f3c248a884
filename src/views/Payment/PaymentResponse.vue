<script setup>

import {useRoute} from "vue-router";
import {inject, onMounted, ref} from "vue";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";

const emitter = inject("emitter")
const route = useRoute()
const loader = ref()

onMounted(() => {
  loader.value.show()
  loader.value.hide()
})
</script>

<template>
  <div class="w-full h-full">
    <LoadingBlock ref="loader"/>
  </div>
</template>
