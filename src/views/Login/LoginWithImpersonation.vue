<script setup>
import {inject, onMounted, ref} from "vue";
import {useRoute} from "vue-router";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import router from "@/router";
import {useStore} from "vuex";
import {useToast} from "vue-toastification";

const toast = useToast()
const {dispatch} = useStore()
const route = useRoute();

const api = inject("api");

const loader = ref();

onMounted(() => {
  loginWithImpersonation()
})

const loginWithImpersonation = () => {
  loader.value.show()
  api
      .post(`customer/login-with-impersonation/${route.query.id}?expires=${route.query.expires}&signature=${route.query.signature}`)
      .then((res) => {
        localStorage.setItem("authToken", res.token);
        dispatch("auth/setAuthToken", res.token)
        router.push({name: "SelectActiveCompany"});
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() => {
        loader.value.hide()
      })
}

</script>

<template>
  <div class="w-full h-full">
    <LoadingBlock ref="loader"/>
  </div>
</template>
