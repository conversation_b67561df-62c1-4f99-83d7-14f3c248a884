<script setup>
import router from "@/router";
import { inject, reactive, ref, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useToast } from "vue-toastification";
import LogoBlue from "../../assets/images/logo-blue.png";
import { Options } from "@/class";
import { i18n } from "@/plugins/vuei18n";
import { useStore } from "vuex";

const toast = useToast()
const { t } = useI18n()
const api = inject("api");
const { dispatch, getters } = useStore();

const loading = ref(false);
const lang = ref();
const buttonLoading = ref(false);

const form = reactive({
  email: "",
  password: "",
  rememberMe: true,
});


const withGoogle = () => {
  api("auth/google")
    .then((res) => {
      window.location.href = res.url
    })
};

onMounted(() => {
  let localLang = sessionStorage.getItem("lang")
  const userLang = Intl?.DateTimeFormat()?.resolvedOptions()?.locale?.split('-')?.[0] || navigator.language || navigator.userLanguage;
  lang.value = localLang ? localLang : userLang.split("-")[0]
});

watch(lang, () => {
  sessionStorage.setItem("lang", lang.value)
  i18n.global.locale.value = lang.value
})

const goToRegister = () => {
  router.push({ name: "Register" });
};

const handleLogin = () => {
  buttonLoading.value = true;
  loading.value = true;
  api(`${import.meta.env.VITE_APP_BASE_API}sanctum/csrf-cookie`).then(() => {
    api
      .post("customer/login", form)
      .then((res) => {
        localStorage.setItem("authToken", res.token);
        dispatch("auth/setAuthToken", res.token)
        router.push({ name: "Home" });
      })
      .catch((err) => toast.error(err.data.message))
      .finally(() =>{
        loading.value = false
        buttonLoading.value = false
      } );
  });
};

const requestPasswordReset = () => {
  router.push({ name: "RequestPasswordReset" });
}

</script>

<template>
  <div class="flex flex-row w-full flex-1 h-full register-wrapper overflow-x-hidden">
    <div class="w-full lg:w-1/2 px-12 lg:px-20 pb-8 lg:pb-8 pt-6 lg:pt-16">
      <div class="flex flex-row justify-between mb-8">
        <img
          :src="LogoBlue"
          style="width: 30%; object-fit: contain;"
        >
        <div class="w-28">
          <el-select
            v-model="lang"
            :placeholder="$t('Language')"
          >
            <el-option
              v-for="item in Options.lang_list"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="mb text-2xl font-semibold">{{ t('Welcome') }}</div>
      <div class="text-lg font-medium opacity-50 ">{{ t('Log in to Platform') }}</div>
      <div class=" flex justify-start items-center py-5 font-light">
        <div class="opacity-60 mr-2">
          {{ t("Don't have an account yet?") }}
        </div>
        <div
          class="text-indigo-600 font-semibold cursor-pointer"
          @click="goToRegister"
        >{{ t('Sign up for a free trial') }}</div>
      </div>

      <el-form
        label-position="top"
        class=" space-y-6 w-full lg:w-3/5"
      >
        <el-form-item
          :label="t('Email')"
          required
        >
          <el-input
            v-model="form.email"
            type="email"
            :placeholder="t('Enter your email')"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="t('Password')"
          required
        >
          <el-input
            v-model="form.password"
            type="password"
            :placeholder="t('Enter your password')"
          />
        </el-form-item>
        <div
          class="flex flex-row justify-start"
          style="margin-top: 16px; margin-bottom: 8px;"
        >
          <span
            @click="requestPasswordReset"
            class="opacity-60 hover:text-indigo-600 font-medium cursor-pointer"
          >
            {{ t('Forgot Password') }}
          </span>
        </div>
        <el-button
          @click="handleLogin"
          type="primary"
          class="w-full mt-6 font-semibold"
          :loading="buttonLoading"
          style="height: 40px; margin-top: 8px;"
        >
          {{ t('Sign In') }}
        </el-button>
        <div
          class="flex justify-center items-center text-md font-light"
          style="margin-top: 20px; margin-bottom: 20px;"
        >
          {{ t('or') }}
        </div>
        <div
          @click="withGoogle"
          class="flex justify-center items-center border-2 border-gray-300 cursor-pointer"
          style="margin-top: 10px; height:40px;"
        >
          <img
            class="h-6"
            src="../../assets/images/googleLogo.png"
            alt="logo"
          />
          <div class="text-slate-700 font-medium py-1 ml-1">{{ t('Log in with Google') }}</div>
        </div>
      </el-form>
      <div class="font-light ">

      </div>
    </div>
    <Carousel />
  </div>
</template>


<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');

.register-wrapper * {
  font-family: 'Inter';
}

.register-wrapper {
  height: inherit;
}
</style>

