<script setup>
import { onMounted, ref, inject, computed, watch } from "vue";
import { useStore } from "vuex";
import router from "@/router";
import { useToast } from "vue-toastification";
import { i18n } from "@/plugins/vuei18n";
//@ts-ignore
import LogoBlue from "@/assets/images/logo-blue.png";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { Options } from "@/class";
import { useI18n } from 'vue-i18n';

const toast = useToast();
const { dispatch, getters } = useStore();

const api = inject("api");

const companies = computed(() => getters["auth/companies"]);
const me = computed(() => getters["auth/me"]);

const { t } = useI18n();
const loading = ref(false);

const lang = ref();

onMounted(() => {
  if (me.value) {
    lang.value = me.value.locale;
  }
});

watch(lang, (tor) => {
  if (me.value) {
    const body = {
      locale: lang.value,
    };
    api.put(`customer/profile`, body)
      .then((res) => {
        dispatch("auth/setMe", res);
      })
      .catch((err) => {
        // Handle error
      });
  }
  sessionStorage.setItem("lang", lang.value);
  i18n.global.locale.value = lang.value;
});

onMounted(() => {
  loading.value = true;
  api("customer/active-companies")
    .then((r) => dispatch("auth/setCompanies", r.data))
    .catch((err) => toast.error(err.data.message))
    .finally(() => loading.value = false);
});

const selectActiveCompany = (company) => {
  if (!company.is_active) return;
  loading.value = true;
  api
    .put(`customer/active-companies/${company.id}`)
    .then((_company) => {
      dispatch("auth/setActiveCompany", _company);
      router.push({ name: "Home" });
    })
    .finally(() => loading.value = false);
};

const logout = () => {
  api.delete("customer/logout").finally(() => {
    localStorage.removeItem("authToken");
    window.location.reload();
  });
};

const goToCreateCompany = () => {
  router.push({ name: "Register2" });
};
</script>
<template>
  <!-- <AuthPageLayout :loading="loading"> -->
  <div class="flex flex-row justify-between p-8 border-b border-slate-300">
    <img
      :src="LogoBlue"
      style="object-fit: contain;"
      class="logo_blue"
    >
    <div class="w-3/6 lg:w-1/6 flex-row flex items-center justify-end">
      <el-select
        v-model="lang"
        :placeholder="$t('Language')"
      >
        <el-option
          v-for="item in Options.lang_list"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>

      <span
        @click="logout"
        class="ml-6 font-medium cursor-pointer"
      >
        {{ $t('Logout') }}
      </span>
    </div>
  </div>

  <div class="px-8 lg:px-64 pt-16 tabs-wrapper">
    <div class="border-b text-indigo-600 font-semibold text-lg pb-8 mb-8">
      {{ t('Workspace Select') }}
    </div>
    <div class="flex flex-1 w-full flex-col h-full text-xl grid-cols-2 grid pr-4 gap-8">
      <!-- // -->
      <div
        v-for="company in companies"
        :key="company.id"
        @click="selectActiveCompany(company)"
        :class="[company.is_active ? 'cursor-pointer hover:border-indigo-600 hover:border-2' : 'opacity-50', 'border border-gray-100 p-8 flex flex-row items-center ']"
        style="border-radius: 4px;"
      >
        <FontAwesomeIcon
          icon="fa-regular fa-building"
          style="width: 25px; height:30px;"
          :class="[company.is_active ? 'text-indigo-600' : 'text-gray-300']"
        />
        <div class="flex flex-col ml-8 text-sm">
          <div class="font-semibold">{{ company.short_name }}</div>
          <div class="font-light">{{ '' }}</div>
        </div>
      </div>
      <!-- // -->

    </div>
    <div class="font-light mt-12 text-gray-500 text-sm">
      {{ t('Do you want to create a new company?') }} <span
        class="text-indigo-600 font-medium cursor-pointer"
        @click="goToCreateCompany"
      >{{ t('Start Now') }}</span>
    </div>
  </div>
</template>

<style scoped>
.logo_blue {
  width: 40%;
}

@media screen and (min-width: 600px) {
  .logo_blue {
    width: 13%;
  }
}
</style>
