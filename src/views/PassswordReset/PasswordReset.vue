<script setup>
import {inject, reactive, ref} from 'vue';
import {useI18n} from "vue-i18n";
import {useRoute} from "vue-router";
import router from "@/router";
import {useToast} from "vue-toastification";

const toast = useToast()
const {t} = useI18n()
const route = useRoute();
const api = inject("api")
const loading = ref(false)

const form = reactive({
  password: null,
  password_confirmation: null
})


const resetPassword = () => {
  loading.value = true;

  const prepareData = (data) => {
    const filteredData = {};
    for (const key in data) {
      if (data[key] !== null && data[key] !== '') {
        filteredData[key] = data[key];
      }
    }
    return filteredData;
  };

  let body = prepareData({
    email: route.query.email,
    token: route.query.token,
    password: form.password,
    password_confirmation: form.password_confirmation
  });

  api.post("reset-password", body)
    .then(() => {
      //TODO locale
      toast.success("<PERSON><PERSON><PERSON><PERSON>, ye<PERSON> <PERSON><PERSON><PERSON><PERSON> ile giri<PERSON> ya<PERSON>bil<PERSON>z");
      goToLogin();
    })
    .catch((err) => toast.error(err.data.message))
    .finally(() => loading.value = false);
};

const goToLogin = () => {
  router.push({name: "Login"})
}

</script>

<template>
  <AuthPageLayout :loading="loading">
    <template #header>

      <div
          class="grid grid-cols-1 md:grid-cols-2 gap-1 items-center text-white text-md z-10"
      >
        <div class="grid-span-2">
          <span @click="goToLogin" class="ml-0.5 font-medium cursor-pointer">
            {{ $t('Login') }}
          </span>
        </div>
      </div>

    </template>
    <template #content-text>
      <div class="text-2xl font-bold">
        {{ route.query.email }} adresinin şifresini sıfırlayın
      </div>
    </template>
    <template #content>
      <el-form label-position="top" class="mt-7 space-y-6">

        <el-form-item :label="t('Password')" required>
          <el-input
              v-model="form.password"
              type="password"
              :placeholder="t('Password')"
          />
        </el-form-item>
        <el-form-item :label="t('Password Confirmation')" required>
          <el-input
              v-model="form.password_confirmation"
              type="password"
              :placeholder="t('Password Confirmation')"
          />
        </el-form-item>
        <div>
          <el-button @click="resetPassword" id="button" type="primary" class="w-full">
            {{ $t('Reset Your Password') }}
          </el-button>
        </div>
      </el-form>
    </template>
  </AuthPageLayout>
</template>
