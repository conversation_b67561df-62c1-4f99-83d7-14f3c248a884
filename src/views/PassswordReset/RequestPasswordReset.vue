<script setup>
import { inject, reactive, ref, onMounted, watch } from 'vue';
import router from "@/router";
import { useToast } from "vue-toastification";
import { useI18n } from "vue-i18n";
import { i18n } from "@/plugins/vuei18n";
import { Options } from "@/class";
import LogoBlue from "@/assets/images/logo-blue.png";

const toast = useToast()
const { t } = useI18n()

const api = inject("api");

const lang = ref();

onMounted(() => {
  let localLang = sessionStorage.getItem("lang")
  const userLang = Intl?.DateTimeFormat()?.resolvedOptions()?.locale?.split('-')?.[0] || navigator.language || navigator.userLanguage;
  lang.value = localLang ? localLang : userLang.split("-")[0]
});

watch(lang, () => {
  sessionStorage.setItem("lang", lang.value)
  i18n.global.locale.value = lang.value
})

const loading = ref(false)

const form = reactive({
  email: null
})

const requestResetPassword = () => {
  loading.value = true
  api.post("forgot-password", { email: form.email })
    .then(() => toast.success())
    .catch((err) => toast.error(err.data.message))
    .finally(() => loading.value = false)
}

const goToLogin = () => {
  router.push({ name: "Login" })
}

</script>

<template>
  <div class="flex flex-row justify-between p-8 border-b border-slate-300">
    <img
      class="w-3/12 lg:w-1/12"
      :src="LogoBlue"
      style="object-fit: contain;"
    >
    <div class="w-3/6 lg:w-2/6 flex-row flex items-center justify-end">
      <el-select
        v-model="lang"
        :placeholder="$t('Language')"
      >
        <el-option
          v-for="item in Options.lang_list"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>

      <span
        @click="goToLogin"
        class="ml-6 font-medium cursor-pointer"
      >
        {{ $t('Login') }}
      </span>
    </div>
  </div>

  <div class="px-8 lg:px-96 pt-16 tabs-wrapper">
    <div class="border-b text-indigo-600 font-semibold text-lg pb-8 mb-8">
      {{ $t('Forgot your password') }}
    </div>
    <el-form-item>
      <el-input
        v-model="form.email"
        placeholder="Email"
        type="email"
      ></el-input>
    </el-form-item>
    <el-button
      @click="requestResetPassword"
      type="primary"
      class="w-full font-semibold"
    >
      {{ t('Reset Your Password') }}
    </el-button>
  </div>
</template>
