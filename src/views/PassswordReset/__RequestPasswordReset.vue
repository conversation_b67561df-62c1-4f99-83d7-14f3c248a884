<script setup>
import {inject, reactive, ref} from 'vue';
import router from "@/router";
import {useToast} from "vue-toastification";
import {useI18n} from "vue-i18n";

const toast = useToast()
const {t} = useI18n()

const api = inject("api")

const loading = ref(false)

const form = reactive({
  email: null
})

const requestResetPassword = () => {
  loading.value = true
  api.post("forgot-password", {email: form.email})
      .then(() => toast.success())
      .catch((err) => toast.error(err.data.message))
      .finally(() => loading.value = false)
}

const goToLogin = () => {
  router.push({name: "Login"})
}

</script>

<template>
  <AuthPageLayout :loading="loading">
    <template #header>
      <div
          class="grid grid-cols-1 md:grid-cols-2 gap-1 items-center text-white text-md z-10"
      >
        <div class="grid-span-2">
          <span @click="goToLogin" class="ml-0.5 font-medium cursor-pointer">
            {{ $t('Login') }}
          </span>
        </div>
      </div>
    </template>
    <template #content-text>
      <div class="text-2xl font-bold">
        {{ $t('Forgot your password') }}
      </div>
    </template>
    <template #content>
      <el-form label-position="top" class="mt-7 space-y-6">
        <div class="mb-2.5">
          <el-form-item label="Email">
            <el-input v-model="form.email" placeholder="Email" type="email"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-button @click="requestResetPassword" id="button" type="primary" class="w-full">
            {{ $t('Reset Your Password') }}
          </el-button>
        </div>
      </el-form>
    </template>
  </AuthPageLayout>
</template>
