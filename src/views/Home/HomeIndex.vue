<script>
import {computed, defineAsyncComponent, onMounted, onUnmounted} from "vue";
import {useStore} from "vuex";
import MainTabs from "@/components/mainTabs/MainTabs.vue";
import {useToast} from "vue-toastification";
import CustomToast from "@/components/ui/CustomToast.vue";
import {useI18n} from "vue-i18n";

export default {
  name: "HomeIndex",
  components: {
    MainTabs,
    TabDashboard: defineAsyncComponent(() => import("./MainTabs/TabDashboard.vue")),
    TabConnect: defineAsyncComponent(() => import("./MainTabs/TabConnect.vue")),
    TabOperation: defineAsyncComponent(() =>
        import("./MainTabs/TabOperation.vue")
    ),
    TabCouriers: defineAsyncComponent(() =>
        import("./MainTabs/TabCouriers.vue")
    ),
    TabAnalyzes: defineAsyncComponent(() => import("./MainTabs/TabAnalyzes.vue")),
    TabDeliveries: defineAsyncComponent(() => import("./MainTabs/TabDeliveries.vue")),
    TabStreaming: defineAsyncComponent(() => import("./MainTabs/TabStreaming.vue")),
    TabApplications: defineAsyncComponent(() => import("./MainTabs/TabApplications.vue")),
    TabSettings: defineAsyncComponent(() =>
        import("./MainTabs/TabSettings.vue")
    ),
  },
  setup() {

    const {t} = useI18n()
    const toast = useToast()
    const {getters} = useStore();

    const profile = computed(() => getters["auth/me"]);
    const activeMainTab = computed(() => getters["maintabs/activeTab"]);
    const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);

    onMounted(() => {
      listener()
    })

    onUnmounted(() => {
      stopListener()
    })







    const listener = () => {
      if (companyChannel.value) {
        Echo.private(companyChannel.value).listen(".export.completed", exportCompleted)
        Echo.private(companyChannel.value).listen(".export.failed", exportFailed)
      }
    }

    const stopListener = () => {
      if (companyChannel.value) {
        Echo.private(companyChannel.value).stopListening(".export.completed")
        Echo.private(companyChannel.value).stopListening(".export.failed")
      }
    }

    const exportCompleted = (data) => {
      if (data.export.user_id === profile.value.id) {
        exportCompletedToaster(data.export)
      }
    }

    const downloadCreatedExcelFile = (url) => {
      window.open(url, "_blank")
    }

    const exportCompletedToaster = (data) => {
      toast.success({
            component: CustomToast,
            listeners: {
              onClick: () => downloadCreatedExcelFile(data.url)
            },
            props: {
              params: {
                message: t('Export completed'),
                actionIcon: "download"
              },
            }
          },
      )
    }

    const exportFailed = (data) => {
      //TODO
      // console.log("exportFailed:", data)
    }


    return {activeMainTab};
  },
};
</script>

<template>
  <AppLayout>
    <div class="h-full flex flex-col">
      <div class="h-full relative overflow-hidden">
<!--        <keep-alive>-->
<!--          <router-view/>-->
<!--&lt;!&ndash;          <component&ndash;&gt;-->
<!--&lt;!&ndash;              :is="activeMainTab.name"&ndash;&gt;-->
<!--&lt;!&ndash;              :payload="activeMainTab.data"&ndash;&gt;-->
<!--&lt;!&ndash;          ></component>&ndash;&gt;-->
<!--        </keep-alive>-->

        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component"/>
          </keep-alive>
        </router-view>
      </div>
    </div>
  </AppLayout>
</template>

