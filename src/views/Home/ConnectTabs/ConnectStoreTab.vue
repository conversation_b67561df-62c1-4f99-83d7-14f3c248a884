<script>
import StatusRenderer from "@/renderers/StatusRenderer.vue";
import StatusUpdateButton from "@/renderers/StatusUpdateButton.vue";

export default {
  components: {
    StatusRenderer,
    StatusUpdateButton
  },
};
</script>
<script setup>

import {computed, inject, onActivated, onDeactivated, reactive, ref, watch} from "vue";
import formatter from "@/class/formatter";
import {useStore} from "vuex";
import LoadingBlock from "@/components/ui/LoadingBlock.vue";
import {useI18n} from "vue-i18n";
import UpdateStatusDrawer
  from "@/components/operations/components/taskTabs/subTabTask/components/UpdateStatusDrawer.vue";
import UpdateStatus from "@/components/qconnect/orderDetail/components/UpdateStatus.vue";
import {conversionFormData} from "@/class/helpers";
import SSDataGrid from "@/components/aggrid/SSDataGrid.vue";

const {t} = useI18n()
const {getters, dispatch} = useStore()

const api = inject("api")

const selectedHub = computed(() => getters["operations/selectedHub"].id);
const companyChannel = computed(() => "company." + getters["auth/activeCompany"].id);

const grid = ref();
const gridApi = ref(null);
const loader = ref(null);
const updateStatusVisible = ref(false);
const cancelOrderDialogVisible = ref(false);
const selectedTask = ref([]);
const data = ref([])
const selectedStatusCategory = ref("completed")

const filter = reactive({
  "filter[hub_id]": selectedHub.value,
  "filter[status_category_slug]": "completed,failed,cancelled",
})

const statues = {
  failed: "Failed",
  completed: "Completed",
  cancelled: "Cancelled",
}

const columnDefs = ref([
  {
    field: "integration_id",
    headerName: t("Order No"),
    filter: 'agTextColumnFilter',
    width: 90,
    sortable: true,
  },
  {
    field: "status_category_slug",
    headerName: t("Status"),
    filter: 'agSetColumnFilter',
    sortable: true,
    width: 120,
    cellRenderer: "StatusRenderer",
    valueFormatter: (params) => params.data.status_name,
    filterParams: {
      values: function (params) {
        params.success(Object.keys(statues))
      },
      valueFormatter: (params) => t(statues[params.value])
    }
  },
  {
    field: "amount",
    headerName: t("Amount"),
  },
  {
    field: "origin_name",
    headerName: t("Origin Name"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "destination_name",
    headerName: t("Destination Name"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "distance",
    headerName: `${t('Distance')} (Km)`,
    valueFormatter: (param) => (param.value / 1000).toFixed(1) + " km",
    keyCreator: (param) => (param.value / 1000).toFixed(1),
    sortable: true,
  },
  {
    field: "duration",
    headerName: `${t('Duration')} (${t('Min')})`,
    valueFormatter: (param) => (param.value / 60).toFixed(1) + " " + t('Min'),
    keyCreator: (param) => (param.value / 60).toFixed(1),
    sortable: true,
  },
  {
    field: "starts_at",
    headerName: t("Start Date"),
    filter: 'agDateColumnFilter',
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "ends_at",
    headerName: t("End Date"),
    filter: 'agDateColumnFilter',
    sortable: true,
    filterParams: {
      inRangeInclusive: true,
      filterOptions: ["inRange"],
      suppressAndOrCondition: true
    },
    valueFormatter: (params) => formatter.date(params),
  },
  {
    field: "origin_address",
    headerName: t("Origin Address"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "origin_lat",
    headerName: t("Origin Latitude"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "origin_lng",
    headerName: t("Origin Longitude"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "destination_address",
    headerName: t("Destination Address"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "destination_lat",
    headerName: t("Destination Latitude"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "destination_lng",
    headerName: t("Destination Longitude"),
    filter: 'agTextColumnFilter',
    sortable: true,
  },
  {
    field: "id",
    headerName: "",
    pinned: "right",
    minWidth: 90,
    cellStyle: {textAlign: "center"},
    cellRenderer: "StatusUpdateButton",
    cellRendererParams: {
      onComplete: (params) => {
        selectedStatusCategory.value = "completed"
        openUpdateStatusDrawer(params);
      },
      onCancel: (params) => {
        if (params.channel === "qconnect") {
          openCancelStatusDiaolog(params)
        } else {
          selectedStatusCategory.value = "cancelled"
          openUpdateStatusDrawer(params.id);
        }
      }
    },
  },
]);

onActivated(() => {
  refresh()
  listener()
})

onDeactivated(() => {
  stopListener()
})

watch(selectedHub, () => {
  filter["filter[hub_id]"] = selectedHub.value
})

const openCancelStatusDiaolog = (params) => {
  selectedTask.value = params
  cancelOrderDialogVisible.value = true
}

const openUpdateStatusDrawer = (params) => {
  selectedTask.value = {id: params}
  updateStatusVisible.value = true
}

const closeUpdateStatusDrawer = (params) => {
  selectedTask.value = null
  updateStatusVisible.value = false
  if (params) {
    refresh()
  }
}

const onCancel = (data) => {
  loader.value.show()
  cancelOrderDialogVisible.value = false
  let formData = conversionFormData(data)
  api.post(`customer/qconnect/${selectedTask.value.id}/reject`, formData)
      .then(() => getData())
      .finally(() => {
        selectedTask.value = null
        loader.value.hide()
      })
}

const refresh = () => {
  if (grid.value && !grid.value.loading) {
    grid.value.refresh()
  }
}

const listener = () => {
  Echo.private(companyChannel.value).listen('.task.status.updated', refresh)
}

const stopListener = () => {
  Echo.private(companyChannel.value).stopListening('.task.status.updated')
}


</script>

<template>
  <div class="w-full h-full flex flex-col text-slate-700 overflow-hidden">
    <LoadingBlock ref="loader"/>
    <div class="h-12 border-b border-slate-300 bg-slate-50 flex items-center px-2">
      <div class="text-lg font-bold">
        {{ t('Completed Orders') }}
      </div>
    </div>
    <div class="h-full w-full overflow-auto">
      <SSDataGrid
          ref="grid"
          v-model="gridApi"
          :columns="columnDefs"
          :filter="filter"
          columnStateSlug="completed_orders"
          :restore-column-state-enabled="true"
          url="customer/kuik-tasks"
      />
    </div>
    <el-dialog
        class="kuik-customized-dialog"
        v-model="cancelOrderDialogVisible"
        :title="t('Order Cancellation')"
        destroy-on-close
    >
      <UpdateStatus
          @onSubmit="onCancel"
          :order="selectedTask"
      />
    </el-dialog>
    <el-drawer
        v-model="updateStatusVisible"
        class="customized-drawer"
        :title="t('Update Status')"
        append-to-body
        destroy-on-close
    >
      <UpdateStatusDrawer
          :selectedTask="selectedTask"
          @close="closeUpdateStatusDrawer"
          :selectedStatusCategory="selectedStatusCategory"
      />
    </el-drawer>
  </div>

</template>
