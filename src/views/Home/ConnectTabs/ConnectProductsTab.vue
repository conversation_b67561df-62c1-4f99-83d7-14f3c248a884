<script setup>

import {inject, onMounted, ref} from "vue";
import ProductList from "@/components/qconnect/product/ProductList.vue";
import AddProduct from "@/components/qconnect/product/AddProduct.vue";
import {useI18n} from "vue-i18n";
import EditProduct from "@/components/qconnect/product/EditProduct.vue";

const {t} = useI18n()

const api = inject("api")

const loader = ref()
const products = ref([]);
const selectedProduct = ref()
const newProductDrawerVisible = ref(false)
const editProductDrawerVisible = ref(false)

onMounted(() => {
  getProducts()
})

const getProducts = () => {
  loader.value.show()
  api("customer/company-products")
      .then((res) => products.value = res.data)
      .finally(() => loader.value.hide())
}

const showNewProductDrawer = () => {
  newProductDrawerVisible.value = true
}

const closeNewProductDrawer = (param) => {
  newProductDrawerVisible.value = false

  if (param) {
    getProducts()
  }
}
const showEditProductDrawer = () => {
  editProductDrawerVisible.value = true
}

const closeEditProductDrawer = (param) => {
  editProductDrawerVisible.value = false
  selectedProduct.value = null
  if (param) {
    getProducts()
  }
}

const openProductEditDrawer = (product) => {
  selectedProduct.value = product
  showEditProductDrawer()
}

</script>

<template>
  <div class="w-full h-full relative">
    <div class="w-full h-full flex flex-col text-slate-700 overflow-hidden">
      <LoadingBlock ref="loader"/>
      <div class="h-12 border-b border-slate-300 bg-slate-50 flex items-center justify-between px-3">
        <div class="text-lg font-bold">
          {{ t('Product List') }}
        </div>
        <CustomButton
            @click="showNewProductDrawer"
            :title="t('New Product')"
            size="small"
        >
          <div class="text-white text-sm">
            <FontAwesomeIcon icon="plus"/>
          </div>
        </CustomButton>
      </div>

      <div class="h-full w-full overflow-auto">
        <ProductList
            :products="products"
            @openProductEditDrawer="openProductEditDrawer"
        />
      </div>
    </div>

    <el-drawer
        v-model="newProductDrawerVisible"
        class="customized-drawer"
        :title="$t('New Product')"
        append-to-body
        destroy-on-close
    >
      <AddProduct @close="closeNewProductDrawer"/>
    </el-drawer>
    <el-drawer
        v-model="editProductDrawerVisible"
        class="customized-drawer"
        :title="$t('New Product')"
        append-to-body
        destroy-on-close
    >
      <EditProduct :product="selectedProduct" @close="closeEditProductDrawer"/>
    </el-drawer>
  </div>
</template>
