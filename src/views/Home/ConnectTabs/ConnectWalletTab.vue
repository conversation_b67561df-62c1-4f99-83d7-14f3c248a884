<script setup>

import {ref} from "vue";
import Payment from "@/components/qconnect/wallet/Payment.vue";
import Transactions from "@/components/qconnect/wallet/Transactions.vue";

const isOrdersPanelOpen = ref(true);
const hideOrdersPanel = () => {
  isOrdersPanelOpen.value = false;
};

const toggleOrdersPanel = () => {
  isOrdersPanelOpen.value = !isOrdersPanelOpen.value;
};
</script>

<template>
  <div class="w-full h-full relative">
    <div class="flex absolute inset-0">

      <div class="w-full bg-white relative">
        <div
            v-if="isOrdersPanelOpen"
            class="absolute h-full w-full bg-black bg-opacity-50 z-10 block lg:hidden"
            @click="hideOrdersPanel"
        />
       <Transactions/>
      </div>
      <div
          :class="[
          isOrdersPanelOpen
            ? 'w-96 xl:w-110 2xl:w-120 min-w-96 xl:min-w-110 2xl:min-w-120 border-r border-slate-300'
            : 'w-0',
        ]"

          class="h-full z-20 absolute bg-white right-0 lg:relative"
      >
        <Payment/>
        <div class="absolute bottom-3 -left-6">
          <PanelPaddle
              position="left"
              :is-panel-open="isOrdersPanelOpen"
              @click="toggleOrdersPanel"
          />
        </div>
      </div>
    </div>
  </div>
</template>
