@forward "element-plus/theme-chalk/src/common/var.scss" with (
  $colors: (
    "primary": (
      "base": #1450FF,
    ),
    "success":(
      "base":#26EB73
    ),
    "danger":(
    "base":#dc2626
    )
  )
);

// Custom Form Styles

.money-input {
  .el-form-item {
    &__label {

    }
  }

  .el-input {
    &__inner {
      text-align: right;
      border-radius: 3px !important;
      @apply border-slate-300 text-slate-700;
    }

    .el-input__suffix {
      padding: 0 5px;
    }
  }
}

.el-input {
  &__inner {
    border-radius: 3px !important;
    @apply border-slate-300 text-slate-700;
  }

  .el-input__suffix {
    padding: 0 5px;
  }
}

.el-form {
  .el-form-item {
    @apply mb-0;
    &__label {
      margin-bottom: 2px !important;
      @apply font-medium text-xs text-slate-700;
    }

    &.is-required {
      .el-form-item__label {
        &::before {
          content: "";
          margin: 0;
        }

        //&::after {
        //  content: "*";
        //  @apply text-red-500;
        //  margin-left: 2px;
        //}
      }
    }
  }

  .el-tabs {
    .el-tabs__header {
      margin-bottom: 1px;
      @apply border-0;
      .el-tabs__item {
        @apply border-0 bg-white text-slate-400 transition-none px-4;
        &.is-active {
          @apply bg-slate-700 text-slate-50;
        }
      }

      .el-tabs__nav {
        @apply border-0;
      }
    }
  }
}

.uncontrols-number-input {
  .el-input-number {
    width: 100% !important;
    text-align: left !important;

    .el-input {
      &__inner {
        text-align: left !important;
      }
    }
  }
}

// Custom Utility
.rounded-none .el-input__inner {
  border-radius: 0;
}

.border-0 .el-input__inner {
  border-width: 0;
}

.working-hour-plan {
  .el-checkbox {
    flex-direction: row-reverse;
  }

  .el-checkbox__label {
    font-size: 10px !important;
    margin-right: 5px;
    padding-left: 0 !important;
  }

}

.custom-el-progress-bar{
  //.el-progress-bar{
  //  border-radius: 0px !important;
  //
  //}
  .el-progress-bar__outer{
    border-radius: 4px !important;
  }
  .el-progress-bar__inner{
    background-color: #334155 !important;
    border-radius: 4px !important;
  }

}


.custom-popover-preparation-time {

  .el-popper {
    width: 70px !important;
    min-width: 70px !important;
  }
}
