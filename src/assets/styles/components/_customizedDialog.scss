.customized-header__dialog {
  padding: 0px !important;
  .el-dialog {

    &__header {
      background-color: var(--custom-primary-color);
      padding-top: 8px !important;
      padding-bottom: 8px !important;
      @apply mb-0 py-3 px-4  text-2xl font-bold;
    }

    &__title {
      @apply text-white;
    }

    &__body {
      @apply pb-0;

    }

    &__close {
      color: white !important;

      &:hover i {
        @apply text-white;
      }
    }

  }
  .el-dialog__footer{
    padding-bottom: 15px !important;
  }
}

.customized-dialog {
  width: 530px !important;
  padding: 0px !important;

  &.customized-dialog--big {
    width: 760px !important;
  }

  &.customized-dialog--small {
    width: 430px !important;
  }

  &.customized-dialog--medium {
    width: 550px !important;
  }

  .el-dialog {
    padding: 0px !important;
    &__header {
      background-color: var(--custom-primary-color);
      padding-top: 8px !important;
      padding-left: 8px !important;
      padding-bottom: 8px !important;
      @apply mb-0 py-3 px-4  text-2xl font-bold;
    }

    &__title {
      @apply text-white;
    }

    &__body {
      @apply p-0;
    }

    &__close {
      color: white !important;

      &:hover i {
        @apply text-white;
      }
    }
  }
}

@media (max-width: 550px) {
  .customized-dialog {
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .customized-dialog.customized-dialog--big {
    width: 100% !important;
  }
}

.kuik-customized-dialog {
  width: 530px !important;
  padding: 0px !important;

  &.customized-dialog--big {
    width: 760px !important;
  }

  .el-dialog {
    padding: 5px !important;
    &__header {
      background-color: var(--custom-primary-color);
      padding-top: 8px !important;
      padding-left: 8px !important;
      padding-bottom: 8px!important;;
      @apply mb-0 py-3 px-4  text-2xl font-bold;

    }

    &__title {
      @apply text-white;
    }

    &__body {
      @apply p-0;
    }

    &__close {
      color: white !important;

      &:hover i {
        @apply text-white;
      }
    }
  }
}

@media (max-width: 550px) {
  .customized-dialog {
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .customized-dialog.customized-dialog--big {
    width: 100% !important;
  }
}


.el-dialog__body {
  word-break: break-word !important;
}

.map-customized-dialog {
  padding: 0px !important;
  .el-dialog__body {
    margin: 0;
    padding: 0;

  }

  .el-dialog__header {
  padding-top: 7px;
    text-align: center !important;

    background-color: #e1e2e3;
  }
}
.el-repicent-dialog{
  .el-dialog__body {
    padding: 0 !important;
    margin: 0!important;
  }
}

.el-custom-dialog {
  .el-dialog__header {
    height: 0;
    width: 0;
    padding: 0 ;
    margin: 0;
  }

  .el-dialog__title {
    @apply text-white;
  }

  .el-dialog__body {
    @apply p-0;
    height: 100%;
  }

  .el-dialog__close {
    color: white !important;

    &:hover i {
      @apply text-white;
    }
  }
}

.el-custom-tabs {
  //.el-tabs__nav-scroll{
  //  height: 60px !important;
  //}
  .el-tabs__header {
    background-color: #BCC0C6 !important;

  }

  .el-tabs__item {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    color: #FFFFFF;

  }

  .el-tabs__item:hover {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    color: #3E4B5E !important;

  }

  .is-active {
    background-color: #FFFFFF !important;
    padding: 20px;
    color: #3E4B5E !important;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;

  }

  .el-tabs__active-bar {
    height: 0 !important;
  }


  .el-tabs__nav-scroll {
    padding-top: 12px;
    padding-left: 16px;
  }
}
