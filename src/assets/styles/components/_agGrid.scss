@import "ag-grid-community/src/styles/ag-grid.scss";
@import "ag-grid-community/src/styles/ag-theme-balham/sass/ag-theme-balham-mixin.scss";

.ag-theme-balham {
  // https://www.ag-grid.com/vue-data-grid/themes-customising/

  @include ag-theme-balham(
                  (
                          balham-active-color: $indigo-300,
                          header-background-color: $slate-50,
                          border-color: $slate-300,
                          row-border-color: $slate-300,
                          odd-row-background-color: $slate-50,
                          selected-row-background-color: $indigo-100,
                          row-hover-color: $slate-100,
                          checkbox-border-radius: 3px,
                          font-family: (
                                  "Inter var",
                                  ui-sans-serif,
                                  system-ui,
                                  -apple-system,
                                  sans-serif,
                          ),
                  )
  );

  .ag-root-wrapper {
    border: none;
  }

  .ag-row {
    border-top-width: 0;
  }

  .ag-checkbox-input-wrapper:after {
    color: $slate-300;
  }

  .ag-ltr {

    .ag-filter-apply-panel {
      padding: 8px 8px !important;
    }

    .ag-filter-apply-panel-button {
      padding: 1px 4px;
      border-radius: 4px;
      font-weight: bold;
      font-size: 11px;
      color: #334155;
      border: 1px solid #cbcdce;
      background: var(--ag-header-background-color, #f8fafc);
    }
  }
}
