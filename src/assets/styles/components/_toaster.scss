.Vue-Toastification__toast {
  min-height: 40px !important;
  align-items: center !important;
  max-width: 375px!important;
  min-width: 375px!important;
}

.custom-toaster {
  right: 10px;
  z-index: 99999999999;
  animation-name: example;
  animation-duration: 0.5s;
}

.custom-toaster-out {
  right: -400px;
  z-index: 99999999999;
  animation-name: out;
  animation-duration: 0.5s;
}

@keyframes out {
  0% {
    right: 10px
  }
  100% {
    right: -400px;
  }
}

@keyframes example {
  0% {
    right: -400px
  }
  100% {
    right: 10px;
  }
}
