.customized-drawer {
  width: 530px !important;

  &.customized-drawer--big {
    width: 760px !important;
  }

  .el-drawer {
    &__header {
      background-color: var(--custom-primary-color,#2563eb);
      padding-top: 8px !important;
      padding-bottom: 8px!important;;
      @apply mb-0 py-3 px-4 text-slate-50 text-xl font-bold;
    }
    &__body {
      @apply p-0;
    }
    &__close {
      color: white !important;
      &:hover i {
        @apply text-white;
      }
    }
  }
}

@media (max-width: 550px) {
  .customized-drawer {
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .customized-drawer.customized-drawer--big {
    width: 100% !important;
  }
}
