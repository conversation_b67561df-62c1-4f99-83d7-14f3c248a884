.el-menu-vertical-demo:not(.el-menu--collapse) {
  min-width: 256px !important;
  width: 256px !important;
}


.el-custom-menu {
  //@apply bg-indigo-50;
  //.el-menu-item {
  //  height: 32px !important;
  //  line-height: 32px !important;
  //  @apply text-slate-700;
  //}
  //
  //.el-menu {
  //  @apply bg-white;
  //}
  //
  //.el-menu-item.is-active {
  //
  //  background-color: #E6EFFC !important;
  //  color: #0052CC !important;
  //  border-right-color: #0052CC !important;
  //  @apply w-full font-medium  h-full  items-center border-r-4
  //}
  //
  //.el-menu-item.is-active:hover {
  //  @apply bg-rose-300 text-slate-700 font-medium
  //}
  //
  //
  //.el-menu-item {
  //
  //  @apply text-gray-500;
  //  @apply bg-white
  //
  //}
  //
  //.el-sub-menu__title {
  //  height: 40px !important;
  //  line-height: 40px !important;
  //  @apply text-gray-500;
  //;
  //  @apply bg-white
  //}
  //
  //
  //
  //.el-sub-menu.is-active>.el-sub-menu__title {
  //  color: #0052CC !important;
  //}
  //
  //.el-menu-item:hover {
  //  color: #0052CC !important;
  //  background-color: #E6EFFC !important;
  //
  //}
  //
  //.el-sub-menu__title:hover {
  //  color: #0052CC !important;
  //  background-color: #E6EFFC !important;
  //}
  .el-menu-item {
    padding-left: 14px !important;
    color: #3E4B5E !important;
    height: 32px !important;
  }

  .el-sub-menu .el-menu-item:hover{
    background-color: #e6effc !important;

  }
  .el-sub-menu .el-menu-item.is-active:hover{
    background-color: white !important;
  }
  //.el-sub-menu .el-menu-item{
  //  height: 27px;
  //}

  .el-menu-item.is-active.bg-white {
    padding-left: 14px !important;
    color: #0B46F4 !important;


  }

  .el-sub-menu__title {
    height: 30px !important;
    padding-left: 14px !important;
    border-top:none !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 600 !important; // SemiBold
    font-size: 16px !important;
    color: #CBD5E1 !important;
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .el-sub-menu__title:hover {
    background-color: white !important;
  }

  .el-sub-menu {
    border-bottom:1px solid #EAEAEA !important;
  }


  .el-sub-menu__title.is-active {
    padding-left: 14px !important;
    border-top-width: 1px !important;
    border-top-color: #EAEAEA !important;
    border-bottom: none !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 600 !important; // SemiBold
    font-size: 16px !important;
  }
  .el-sub-menu__title.is-active:hover {
    padding-left: 14px !important;
    border-top-width: 1px !important;
    border-top-color: #EAEAEA !important;
    border-bottom: none !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 600 !important; // SemiBold
    font-size: 16px !important;
  }
  .el-sub-menu.is-active .el-sub-menu__title {
    //border-bottom: none !important;

  }

  .el-sub-menu.is-active .el-sub-menu__title {
    //border-bottom: none !important;
    color: #3E4B5E !important;
  }

  .el-sub-menu .el-sub-menu__title {
    //border-bottom: none !important;
    border-top-color: #EAEAEA !important;
  }

  .el-menu.el-menu--inlin {
    //border-top: none;
  }
  .el-menu{
    padding-bottom: 16px;
  }


}


.el-custom-collapse{

  .el-collapse-item__header.is-active{
    background-color: #F8FAFC !important;

     border:1px solid !important;
     border-color: #EAEAEA !important;
     border-radius: 6px !important;
    color: #334155;
  }
  .el-collapse-item__header{
     border:1px solid !important;
     border-color: #EAEAEA !important;
     border-radius: 6px !important;
    background-color: #F8FAFC !important;
    color: #334155;
  }
  .el-collapse-item__content{
    padding: 0 !important;
    padding-top: 18px !important;
    padding-bottom: 8px !important;
    padding-left: 9px !important;
    padding-right: 9px !important;
    border : 1px solid !important;
    border-color: #EAEAEA !important;
    border-radius: 6px !important;
    margin-top: 12px !important;
    margin-bottom: 12px !important;

  }

}
