html,
body,
#app {
  height: 100%;
  user-select: none;
}

button,
[type=‘button’],
[type=‘reset’],
[type=‘submit’] {
  -webkit-appearance: button;
}

body {
  @apply font-sans;
  font-size: 0.875rem;
}

.splitpanes.default-theme {
  .splitpanes__pane {
    background-color: #fff;
    overflow: inherit;
  }

  &.splitpanes--horizontal {
    .splitpanes__splitter {
      height: 10px;
      border-top: 1px solid #cbd5e1; // slate-300
      border-bottom: 1px solid #cbd5e1; // slate-300
    }
  }
}

.grid-tabs {
  display: flex;
  flex-flow: column;
  height: 100%;

  .el-tabs__content {
    flex-grow: 1;
    padding: 0 !important;
  }
}

.w-full-important {
  width: 100% !important;
}

.max-w-100 {
  max-width: 480px;
}

.el-icon svg {
  margin: 0 auto;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.pac-container {
  z-index: 99999 !important;
}

.loading--background {
  background-color: rgba(255, 255, 255, 0.6);
}

.custom--badge--text {
  @apply bg-slate-700 px-1 py-0.5 rounded-md text-xs text-white
}

.auth--container {
  //background: ;
  background: transparent url('../images/route.svg') 0 0 no-repeat padding-box;
}

.connect-brand-sibling {
  background: #F0FDF4;
  color: #22C55E;
  font-size: 11px;
}

.applications-header {
  .el-input__inner {
    border-left: none;
    border-right: none;
    border-top: none;
  }
}

.custom-table {
  thead {
    background-color: #F8FAFC;
    @apply border border-slate-300;
  }

  th {
    @apply text-sm font-bold text-slate-700;
    padding: 10px;
  }

  td {
    @apply text-slate-700;
    padding: 10px;
  }
}

.order-detail-table {
  thead {
    @apply border-b border-slate-300;

  }

  th {
    @apply text-sm font-bold text-slate-700;
    padding: 10px;
  }

  td {
    @apply text-slate-700;
    padding: 10px;
  }
}


.-z-10 {
  z-index: -10;
}

.connect-height {
  height: 2000px !important;
}

body {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    cursor: pointer;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(13deg, $slate-300 14%, $slate-300 14%);
    border-radius: 1px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(13deg, $slate-300 14%, $slate-300 64%);
  }

  ::-webkit-scrollbar-track {
    background: #ffffff;
    border-radius: 2px;
    box-shadow: inset 7px 10px 12px #f0f0f0;
  }

}

.custom--date--piccer--mini {
  width: 8.75rem !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}


.pick-image-remove-button {
  top: -10px;
  right: -8px;
}

.custom-el-popover {
  padding: 0 !important;
}

.custom-el-popover-border {
  border-radius: 18px !important;
}


//button.gm-ui-hover-effect {
//  visibility: hidden;
//}
//
//.gm-style {
//  .gm-style-iw-d {
//    overflow: hidden !important;
//  }
//
//  .gm-style-iw-c {
//    padding: 0 !important;
//    border-radius: 2px !important;
//    box-shadow: 0 1px 5px 1px rgb(0 0 0 / 10%) !important;
//  }
//
//  .gm-style-iw-t::after {
//    left: -9px !important;
//  }
//}

.delivery_detail_tab {
  .el-tabs__content {
    overflow: auto;
  }
}

.courier-list-filter-content {
  .el-form-item__label {
    font-size: 0.6rem !important;
    padding-bottom: 0.1rem !important;
  }
}


.quick-delivery-form {
  .el-form-item__label {
    font-size: 1rem !important;
    @apply text-slate-700 font-semibold mb-1.5
  }
}

.custom-button__color {
  background-color: var(--custom-primary-color);
}

.custom-primary-color {
  color: var(--custom-primary-color);
}

.select-all {
  user-select: all;
}

.custom-aggrid-date-input {
  background-color: #ffffff;
  height: 25px;
  color: #334155;
  padding: 0 4px;
  border: 1px solid #cbd5e1;

  & input {
    outline: none;
  }
}


.min-260 {
  min-width: 260px;
}

.el-dialog__header {


  padding: 0px !important;
  margin-right: 0 !important;
}

.custom-main-datepicker {

  width: 120px !important;

  .el-input__wrapper {
    @apply h-7;
  }

  input.el-input__inner {
    @apply text-slate-700 font-semibold text-sm;
    cursor: pointer;
  }
}

.custom-hub-select-input {

  width: 240px !important;

  .el-input__wrapper {
    @apply h-7;
  }

  input.el-input__inner {
    @apply text-slate-700 font-semibold text-sm;
    cursor: pointer;
  }
}


.custom-action-popover {

  .el-popover {
    padding: 0 !important;

    .el-popper {
      padding: 0 !important;
    }
  }

}

.custom-routing-select{

     height: 100% !important;
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-bottom: 8px !important;

}

.custom-company-select {
  .el-select__wrapper {
    background-color: var(--custom-primary-color) !important;
    box-shadow: none !important;
  }

  .el-input__inner {
    font-size: 14px !important;
    color: white !important;
    font-weight: 600 !important;
  }
  .el-select__selected-item {
    color: white !important;
  }
}

.custom-register-tabs{

    display: flex;
    align-items: center;
    justify-content: center;

}
