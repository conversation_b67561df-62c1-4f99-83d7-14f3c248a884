<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="15" height="15" viewBox="0 0 15 15" xml:space="preserve">
<desc>Created with Fabric.js 4.6.0</desc>
<defs>
</defs>
<g transform="matrix(0.2 0 0 0.2 7.5 7.5)" id="N4B8s8JnfbQdQGtfR7lxQ"  >
<path style="stroke: rgb(255,255,255); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(217, 119 ,6); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(0, 0)" d="M 0 -31.77756 C 17.54121 -31.77756 31.77756 -17.54121 31.77756 0 C 31.77756 17.54121 17.54121 31.77756 0 31.77756 C -17.54121 31.77756 -31.77756 17.54121 -31.77756 0 C -31.77756 -17.54121 -17.54121 -31.77756 0 -31.77756 z" stroke-linecap="round" />
</g>
</svg>
