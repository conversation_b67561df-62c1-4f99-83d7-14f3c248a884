<svg width="26" height="41" viewBox="0 0 26 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_133_5532)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25 12.6519C25 5.66445 19.6274 0 13 0C6.37258 0 1 5.66445 1 12.6519C1 14.3816 1.32924 16.0303 1.92526 17.5321C4.39733 24.6483 13.0631 29.5211 13.0631 29.5211C13.0631 29.5211 23.7079 23.5354 24.7673 15.1438C24.9199 14.3381 25 13.5049 25 12.6519Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.0863 14.9324L23.0754 14.9905L23.0681 15.0492C22.6244 18.6023 20.1102 21.8576 17.3971 24.3567C16.0751 25.5744 14.7787 26.5461 13.8115 27.2133C13.5305 27.4071 13.2785 27.5745 13.0631 27.7139C12.92 27.6212 12.7607 27.5162 12.5874 27.3993C11.7781 26.8532 10.6736 26.053 9.49832 25.0414C7.10317 22.9796 4.61028 20.2056 3.53504 17.0764L3.52171 17.0376L3.50675 16.9995C2.99687 15.7007 2.71436 14.2734 2.71436 12.7711C2.71436 6.71613 7.31943 1.80762 13.0001 1.80762C18.6807 1.80762 23.2858 6.71613 23.2858 12.7711C23.2858 13.5126 23.217 14.2351 23.0863 14.9324Z" fill="#D2D2D2"/>
<path d="M17.5713 12.6523C17.5713 9.9904 15.5246 7.83252 12.9999 7.83252C10.4752 7.83252 8.42847 9.9904 8.42847 12.6523C8.42847 15.3142 10.4752 17.4721 12.9999 17.4721C15.5246 17.4721 17.5713 15.3142 17.5713 12.6523Z" fill="white"/>
</g>
<g filter="url(#filter1_d_133_5532)">
<g filter="url(#filter2_d_133_5532)">
<ellipse cx="13" cy="31.6862" rx="1.2" ry="0.984036" fill="#D2D2D2"/>
<path d="M14 31.6862C14 32.0839 13.5913 32.4702 13 32.4702C12.4088 32.4702 12 32.0839 12 31.6862C12 31.2885 12.4088 30.9021 13 30.9021C13.5913 30.9021 14 31.2885 14 31.6862Z" stroke="white" stroke-width="0.4"/>
</g>
</g>
<rect x="9.40002" y="9.44678" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="11.8" y="16.5317" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="16.6" y="11.8086" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="7" y="11.8086" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="11.8" y="7.08496" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="9.40002" y="14.1699" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="14.2" y="14.1699" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="14.2" y="9.44678" width="2.4" height="2.36169" fill="#D2D2D2"/>
<rect x="11.8" y="11.8086" width="2.4" height="2.36169" fill="#D2D2D2"/>
<defs>
<filter id="filter0_d_133_5532" x="0" y="0" width="26" height="31.521" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_133_5532"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_133_5532" result="shape"/>
</filter>
<filter id="filter1_d_133_5532" x="7.80005" y="30.7021" width="10.4" height="9.96826" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_133_5532"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_133_5532" result="shape"/>
</filter>
<filter id="filter2_d_133_5532" x="10.8" y="30.7021" width="4.40002" height="3.96826" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_133_5532"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_133_5532" result="shape"/>
</filter>
</defs>
</svg>
