<template>
  <div class="w-full h-full flex flex-col overflow-none bg-white">
    <div class="flex items-center border-b border-slate-300" v-if="$props.visible">
<!--      <div class="justify-between items-center m-3 flex">-->
<!--        <p class="text-2xl font-bold text-slate-700">-->
<!--          <slot name="title" />-->
<!--        </p>-->
<!--      </div>-->
      <div class="flex-grow m-3 justify-between items-end">
        <slot name="actions" />
      </div>
    </div>
    <div class="h-full w-full">
      <slot name="content" />
    </div>
    <div>
      <slot name="drawers"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "PageLayout",
  props:{
    visible:{
      type: Boolean,
      default: true,
    },
    },
  setup(props) {},
};
</script>
