<script setup>
const props = defineProps({
  loading:{default:false,type:<PERSON><PERSON><PERSON>}
})
</script>

<template>
  <div class="w-full h-full relative">
    <div v-if="loading" class="flex w-full h-full absolute z-20 inset-0 justify-center items-center loading--background opacity-70">
        <img
          class="h-5"
          src="../assets/images/loading.svg"
          alt="loading"
        />
    </div>
    <slot></slot>
  </div>
</template>
