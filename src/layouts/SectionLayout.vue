<template>
  <Layout>
  <div class="flex-none w-full bg-white h-full relative z-0">
    <slot name="overlays" />
    <div class="relative h-full bg-gray-100">
      <div class="flex flex-col w-full h-full absolute z-50">
        <div
          v-if="$slots.header"
          class="flex items-center justify-between px-4 py-2 border-b border-gray-200"
        >
          <slot name="header"></slot>
        </div>

        <div class="flex flex-col overflow-hidden h-full w-full">
          <slot />
        </div>
      </div>
    </div>
  </div>
  </Layout>
</template>

<script>
import Layout from "@/layouts/Layout.vue";
export default {
  components: { Layout },
  setup() {},
};
</script>
