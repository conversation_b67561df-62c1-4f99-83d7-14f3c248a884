<script setup>
import Header from "@/components/header/Header.vue";
import NewHeader from "@/components/header/newHeader/index.vue";
import NewDelivery from "@/components/deliveries/NewDelivery.vue";
import { ref, inject, computed, onMounted } from "vue";
import { useStore } from "vuex";
import router from "@/router";
import NewCourier from "@/components/deliveries/NewCourier.vue";


const { dispatch, getters } = useStore();

const emitter = inject("emitter");
const me = computed(() => getters["auth/me"])
const infosetEnable = ref(JSON.parse(import.meta.env.VITE_INFOSET_ENABLE))

const destroy = computed(() => import.meta.env.MODE !== 'development')

const newTaskDrawerVisible = ref(false);
const newQuickTaskDrawerVisible = ref(false);
const newCourierDrawerVisible = ref(false);


onMounted(() => {
  if (me.value.active_company.company_product_type !== "qd" && infosetEnable.value) {
    const script = document.createElement('script');
    script.src = 'https://cdn.infoset.app/chat/icw.js';
    script.defer = true;
    script.onload = loadInfoset
    document.body.appendChild(script);
  }
})

function loadInfoset() {
  if (infosetEnable.value) {
    InfosetChat('boot', { widget: { apiKey: 'l020AZy8LZVxwCuSyXdPRBb5x4hBl5dBVQ8eDMGw', customLauncher: '#element_id', } });
  }
}

const closeNewTaskDrawer = () => {
  newTaskDrawerVisible.value = false;
};

const closeNewQuickTaskDrawer = () => {
  newQuickTaskDrawerVisible.value = false;
};

const closeNewCourierDrawer = () => {
  newCourierDrawerVisible.value = false
}

emitter.on("open_new_task", () => (newTaskDrawerVisible.value = true));
emitter.on("open_new_quick_task", () => (newQuickTaskDrawerVisible.value = true));
emitter.on("open_new_driver", () => (newCourierDrawerVisible.value = true));
emitter.on("open_import_excel", () => (router.push({ name: 'Import' })));

</script>
<template>
  <div class="w-full h-full flex flex-col bg-gray-100 relative">
    <NewHeader />
    <!--    <template v-if="me?.active_company?.company_product_type !== 'qd'">-->
    <!--      <Header/>-->
    <!--    </template>-->
    <!--    <template v-else>-->
    <!--    </template>-->
    <main class="h-full overflow-y-auto z-0">
      <slot></slot>
      <el-drawer
        v-model="newTaskDrawerVisible"
        class="customized-drawer customized-drawer--big"
        :title="$t('New Delivery')"
        append-to-body
        :destroy-on-close="destroy"
      >
        <NewDelivery
          @close="closeNewTaskDrawer"
          :visible="newTaskDrawerVisible"
        />
      </el-drawer>
      <el-drawer
        v-model="newCourierDrawerVisible"
        class="customized-drawer"
        :title="$t('New Driver')"
        append-to-body
        destroy-on-close
      >
        <NewCourier @close="closeNewCourierDrawer" />
      </el-drawer>

      <el-drawer
        v-model="newQuickTaskDrawerVisible"
        class="customized-drawer"
        :title="$t('New Delivery')"
        append-to-body
        destroy-on-close
      >
        <NewQuickDelivery @close="closeNewQuickTaskDrawer" />
      </el-drawer>
    </main>
<!--    <div-->
<!--      v-if="me.active_company.company_product_type !== 'qd' && infosetEnable"-->
<!--      id="element_id"-->
<!--      class="flex fixed z-50 right-0 bottom-1/4 items-center justify-center w-6 py-6 text-white cursor-pointer bg-green-400"-->
<!--      style="border-top-left-radius: 2px; border-bottom-left-radius: 2px;"-->
<!--    >-->
<!--      <span-->
<!--        style="font-size: 12px; letter-spacing: 1px; transform: rotate(90deg) scale(-1); transform-origin: center center;"-->
<!--      >-->
<!--        {{ $t('Support') }}-->
<!--      </span>-->
<!--    </div>-->
  </div>
</template>

