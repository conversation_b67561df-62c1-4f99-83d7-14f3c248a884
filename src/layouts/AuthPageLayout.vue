<script setup>
import router from "@/router";
import Layout from "@/layouts/Layout.vue";
import {onMounted, ref, watch} from "vue";
import {i18n} from "@/plugins/vuei18n";
import {Options} from "@/class";

const lang = ref()

const gotoHome = () => {
    router.push({name: "Home"});
};
const props = defineProps({
  loading: {default: false, type: Boolean},
});

onMounted(() => {
  let localLang = sessionStorage.getItem("lang")
  let userLang = navigator.language || navigator.userLanguage;
  lang.value = localLang ? localLang : userLang.split("-")[0]
})
watch(lang, () => {
  sessionStorage.setItem("lang", lang.value)
  i18n.global.locale.value = lang.value
})
</script>

<template>
  <Layout :loading="loading">
    <div class="flex flex-col w-full h-full">
      <div class="flex flex-grow flex-col relative">
        <div
            class="flex w-full justify-between items-center bg-indigo-700 px-2.5 lg:px-32 xl:px-64"
        >
          <div @click="gotoHome" class="hidden md:block cursor-pointer z-10">
            <img
                class="h-16 my-4 w-auto"
                src="../assets/images/qdelivery-logo-with-text-white.svg"
                alt="logo"
            />
          </div>

          <div class="block md:hidden">
            <img
                class="h-16 my-4 w-auto"
                src="../assets/images/logo-wr.png"
                alt="logo"
            />
          </div>
          <slot name="header"></slot>
        </div>
        <div class="flex-grow z-10">
          <div class="w-full h-full py-12 px-2.5 lg:px-32 xl:px-64">
            <slot name="content-text"></slot>
            <div class="max-w-100">
              <slot name="content"></slot>
            </div>
          </div>
        </div>
        <div class="absolute auth--container inset-0 z-0"></div>
      </div>
      <div
          class="w-full bg-white px-2.5 lg:px-32 xl:px-64 border-t border-slate-300 py-5 lg:py-12"
      >
        <el-form label-position="top" class="space-y-6">
          <el-form-item :label="$t('Language')">
            <el-select
                v-model="lang"
                :placeholder="$t('Language')"
            >
              <el-option
                  v-for="item in Options.lang_list"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </Layout>
</template>
