import { createRouter, createWebHistory } from "vue-router";
import routes from "./routes";
import store from "@/store";
import axios from "axios";

const router = createRouter({
  history: createWebHistory(),
  routes
});

const getProfile = (token) => {
  return new Promise((resolve, reject) => {

    let me = store?.getters["auth/me"];

    if (me && me.customer_companies_count) return resolve(me);

    axios(import.meta.env.VITE_APP_API + "customer/profile", {
      headers: {
        "Authorization": `Bearer ${token}`,
        "X-Requested-With": "XMLHttpRequest"
      }
    })
      .then((res) => resolve(res.data))
      .catch((err) => reject(err));
  });
};


router.beforeEach((to, from, next) => {
  document.title = to.name;
  from.fullPath.includes("login") && store?.dispatch("ui/setLoading", true);

  let token = localStorage.getItem("authToken");

  if (!to.meta.requiresAuth) {
    store?.dispatch("ui/setLoading", false);
    return next();
  }

  if (!token) {
    store?.dispatch("ui/setLoading", false);
    return next({ name: "Login" });
  }

  getProfile(token)
    .then((response) => {
      store?.dispatch("auth/setMe", response);
      store?.dispatch("auth/setActiveCompany", response.active_company);
      store?.dispatch("operations/setSelectedHub", response.active_hub);

      if (response.google_id && !response.phone) {
        return next("/google-account-confirmation");
      }

      if (!to.meta.requiresVerify) return next();

      if (!response.email_verified_at) return next("/account-confirmation");

      if (!to.meta.requiresHaveCompany) return next();

      if (!response.customer_companies_count && response.role_slug === "customer") return next("/create-company/step1");

      if (!to.meta.requiresActiveCompany) return next();

      if (!response.active_company) return next("/select-active-company");

      const { role_slug, active_company } = response;

      if (to.meta.type.includes(active_company.company_product_type) && to.meta.role.includes(role_slug)) {
        return next();
      }

      if (to.fullPath.includes("operation")) {
        return next({ name: "Connect" });
      }

      if (to.fullPath.includes("connect")) {
        return next({ name: "Operation" });
      }

      if (to.fullPath.includes("applications")) {
        return next({ name: "Operation" });
      }

      next();


    })
    .catch((error) => {
      if (error.response && error.response.status === 401) {
        localStorage.removeItem("authToken");
        next({
          name: "Login"
        });
      }
    })
    .finally(() => {
      store?.dispatch("ui/setLoading", false);
    });

})

;
export default router;
