import Home from "@/views/Home/HomeIndex.vue";
import Login from "@/views/Login/LoginIndex.vue";
import SelectActiveCompany from "@/views/Login/SelectActiveCompany.vue";
import Tracking from "@/views/Tracking/TrackingIndex.vue";
import TaskRating from "@/views/TaskRating/TaskRatingIndex.vue"
import NotFound from "@/views/NotFound/NotFoundIndex.vue"
import Register from "@/views/Register/RegisterIndex.vue"
import TabFlow from '@/views/Register/TabFlow/TabFlow.vue'
import VerifyEmail from "@/views/Register/VerifyEmail.vue"
import GoogleAuth from "@/views/Register/GoogleAuth.vue"
import LinkedAuth from "@/views/Register/LinkedAuth.vue"
import ConfirmAccount from "@/views/Register/ConfirmAccount.vue"
import GoogleConfirmAccount from "@/views/Register/GoogleConfirmAccount.vue"
import CourierApp from "@/views/CourierApp/CourierAppIndex.vue"
import RequestPasswordReset from "@/views/PassswordReset/RequestPasswordReset.vue"
import PasswordReset from "@/views/PassswordReset/PasswordReset.vue"
import PaymentResponse from "@/views/Payment/PaymentResponse.vue"
import LoginWithImpersonation from "@/views/Login/LoginWithImpersonation.vue";
import settings from "@/router/settings";
import operation from "@/router/operation";
import connect from "@/router/connect";
import applications from "@/router/applications";
import couriers from "@/router/couriers";
import deliveries from "@/router/deliveries";
import statistic from "@/router/statistic";
import KuikRes from "@/views/KuikRessApp/KuikRes.vue";
import KuikRedirect from "@/views/KuikRessApp/KuikRedirect.vue";
import KuikWalletTransactionsRedirect from "@/views/KuikRessApp/KuikWalletTransactionsRedirect.vue";
import KuikWalletTransactions from "@/views/KuikRessApp/KuikWalletTransactions.vue";
import TabFlow2 from "@/views/Register/TabFlow2/TabFlow2.vue";


const routes = [
    {

        path: "/",
        name: "Home",
        redirect: '/operation',
        component: Home,
        meta: { requiresAuth: true, requiresActiveCompany: true, requiresVerify: true, requiresHaveCompany: true },
        children: [
            operation,
            {
                path: "/import",
                name: "Import",
                hidden: true,
                component: () => import("@/components/settings/ImportExcel.vue"),
                meta: {
                    role: ["admin", "customer", "manager", "dispatcher"],
                    type: ["qd", "kuik", "qd-kuik"],
                },
            },
            connect,
            applications,
            couriers,
            deliveries,
            settings,
            statistic,
        ]
    },

    {
        path: "/login",
        name: "Login",
        component: Login,
    },
    {
        path: "/select-active-company",
        name: "SelectActiveCompany",
        component: SelectActiveCompany,
        meta: { requiresAuth: true, requiresVerify: true, requiresHaveCompany: true }
    },
    {
        path: "/register/:plan?",
        name: "Register",
        component: Register,

    },
    {
        path: "/register/createcompany",
        name: "Register2",
        component: TabFlow2,

    },

    {
        path: "/account-confirmation",
        name: "AccountConfirmation",
        component: ConfirmAccount,
        meta: { requiresAuth: true }
    },

    {
        path: "/google-account-confirmation",
        name: "GoogleAccountConfirmation",
        component: GoogleConfirmAccount,
        meta: { requiresAuth: true }
    },
    // {
    //     path: "/create-company",
    //     name: "CreateCompany",
    //     component: TabFlow,
    //     meta: { requiresAuth: true, requiresVerify: true }
    // },
    {
        path: "/create-company/step1",
        name: "RegisterStep1",
        component: TabFlow,
        meta: { requiresAuth: true, requiresVerify: true }
    },
    {
        path: "/create-company/step2",
        name: "RegisterStep2",
        component: TabFlow,
        meta: { requiresAuth: true, requiresVerify: true }
    },
    {
        path: "/create-company/step3",
        name: "RegisterStep3",
        component: TabFlow,
        meta: { requiresAuth: true, requiresVerify: true }
    },
    {
        path: "/create-company/step4",
        name: "RegisterStep4",
        component: TabFlow,
        meta: { requiresAuth: true, requiresVerify: true }
    },
    {
        path: "/tracking/",
        name: "Tracking",
        component: Tracking,
    },
    {
        path: "/kuik-q",
        name: "KuikRes",
        component: KuikRes,
    },
    {
        path: "/kuikrest/impersonation",
        name: "KuikRest",
        component: KuikRedirect,
    },
    {
        path: "/kuikrest-wallet-transactions/impersonation",
        name: "KuikWalletTransactionss",
        component: KuikWalletTransactions
    },
    {
        path: "/kuikrest-wallet/impersonation",
        name: "KuikWalletTransactions",
        component: KuikWalletTransactionsRedirect
    },
    {
        path: "/task-rating",
        name: "TaskRating",
        component: TaskRating,
    },
    {
        path: "/courier-app",
        name: "CourierApp",
        component: CourierApp,
    },
    {
        path: "/verify-email",
        name: "VerifyEmail",
        component: VerifyEmail
    },
    {
        path: "/request-password-reset",
        name: "RequestPasswordReset",
        component: RequestPasswordReset
    },
    {
        path: "/password-reset",
        name: "PasswordReset",
        component: PasswordReset
    },
    {
        path: "/google-auth",
        name: "GoogleAuth",
        component: GoogleAuth
    },
    {
        path: "/linkedin-auth",
        name: "LinkedAuth",
        component: LinkedAuth
    },
    {
        path: "/payment-response",
        name: "PaymentResponse",
        component: PaymentResponse
    },
    {
        path: "/login-with-impersonation",
        name: "LoginWithImpersonation",
        component: LoginWithImpersonation
    },
    {
        path: "/:pathMatch(.*)*",
        component: NotFound,
    }
];

export default routes;
