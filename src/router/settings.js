import MetaBase from "@/views/Register/MetaBase.vue";
import statistic from "@/router/statistic";

export default {
    path: "/settings",
    name: "<PERSON><PERSON><PERSON>",
    redirect: '/settings/hubs',
    hidden: false,
    component: () => import("@/components/settings/index.vue"),
    meta: {
        role: ["admin", "customer", "manager"],
        type: ["qd", "kuik", "qd-kuik"],
    },
    children: [
        {
            path: "/settings/hubs",
            name: "<PERSON><PERSON>",
            hidden: false,
            component: () => import("@/components/settings/HubSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/teams",
            name: "Teams",
            hidden: false,
            component: () => import("@/components/settings/TeamSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd", "qd-kuik"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/regions",
            name: "Regions",
            hidden: false,
            component: () => import("@/components/settings/RegionSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd", "qd-kuik"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/managers",
            name: "Managers",
            hidden: false,
            component: () => import("@/components/settings/ManagerSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Users",
        },
        {
            path: "/settings/dispatchers",
            name: "Dispatchers",
            hidden: false,
            component: () => import("@/components/settings/DispatcherSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd", "kuik", "qd-kuik",]
            },
            subMenuTitle:"Users",
        },
        {
            path: "/settings/company",
            name: "Company Information",
            hidden: false,
            component: () => import("@/components/settings/CompanySettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Company Settings",
        },
        // {
        //     path:"/analiz/test",
        //     name: "MetaBase",
        //   component:()=> import("@/views/Register/MetaBase.vue"),
        //     meta: {
        //         role: ["admin", "customer"],
        //         type: ["qd", "kuik", "qd-kuik"],
        //     },
        //
        // },
        // {
        //     path:"/analiz/courier-test",
        //     name: "CourierListTest",
        //     component:()=> import("@/views/Register/CourierAnalizeTest.vue"),
        //     meta: {
        //         role: ["admin", "customer"],
        //         type: ["qd", "kuik", "qd-kuik"],
        //     },
        //
        // },
        {
            path: "/settings/working-plan",
            name: "Working Plan",
            hidden: false,
            component: () => import("@/components/settings/WorkingSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/application",
            name: "Application",
            hidden: false,
            component: () => import("@/components/settings/ApplicationSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd"],
            },
            subMenuTitle:"Company Settings",
        },
        {
            path: "/settings/statuses",
            name: "Statuses",
            hidden: false,
            component: () => import("@/components/settings/StatusSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/status-flows",
            name: "Status Flows",
            hidden: false,
            component: () => import("@/components/settings/StatusFlowSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/status-flows/:id",
            name: "Show Status Flow",
            hidden: true,
            component: () => import("@/components/settings/NewFlowSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/tags",
            name: "Tags",
            hidden: false,
            component: () => import("@/components/settings/TagsSettings.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd"],
            },
            subMenuTitle:"Configuration",
        },
        {
            path: "/settings/brand-customization",
            name: "Brand Management",
            hidden: false,
            component: () => import("@/components/settings/BrandCustomizationSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Company Settings",

        },
        {
            path: "/settings/api",
            name: "API Settings",
            hidden: false,
            component: () => import("@/components/settings/ApiSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd"],
            },
            subMenuTitle:"Company Settings",
        },
        {
            path: "/settings/webhook-logs",
            name: "Webhook Logs",
            hidden: false,
            component: () => import("@/components/settings/WebhookLogs.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd"],
            },
            subMenuTitle:"Company Settings",
        },
        {
            path: "/settings/activities",
            name: "Activities",
            hidden: false,
            component: () => import("@/components/settings/Activities.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Company Settings",
        },
        {
            path: "/settings/exports",
            name: "Exports",
            hidden: false,
            component: () => import("@/components/settings/Exports.vue"),
            meta: {
                role: ["admin", "customer", "manager"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Reports",
        },
        {
            path: "/settings/profile",
            name: "Profile",
            hidden: false,
            component: () => import("@/components/settings/Profile.vue"),
            meta: {
                role: ["admin", "customer", "manager", "dispatcher"],
                type: ["qd", "kuik", "qd-kuik"],
            },
            subMenuTitle:"Users",
        },
        {
            path: "/settings/employee-wallets",
            name: "Employee Wallets",
            hidden: false,
            component: () => import("@/components/settings/EmployeeWallets.vue"),
            meta: {
                role: ["admin", "customer", "manager", "dispatcher"],
                type: ["qd","kuik","qd-kuik"],
            },
            subMenuTitle:"Financial Management",
        },
        {
            path: "/settings/withdrawal-requests",
            name: "Withdrawal Requests",
            hidden: false,
            component: () => import("@/components/settings/WithdrawalRequests.vue"),
            meta: {
                role: ["admin", "customer", "manager", "dispatcher"],
                type: ["qd","kuik","qd-kuik"],
            },
            subMenuTitle:"Financial Management",
        },
        {
            path: "/settings/subscription-billing",
            name: "Subscription & Billing",
            hidden: false,
            component: () => import("@/components/settings/SubscriptionBillingSettings.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd","kuik", "qd-kuik"],
            },
            subMenuTitle:"Subscription & Billing",
        },
        {
            path: "/settings/notification-templates",
            name: "Notification Templates",
            hidden: false,
            component: () => import("@/components/settings/NotificationTemplates.vue"),
            meta: {
                role: ["admin", "customer"],
                type: ["qd","kuik", "qd-kuik"],
            },
            subMenuTitle:"Notification Templates",
        }
    ]
}
