export default {
    path: "/drivers",
    name: "Drivers",
    redirect: "/drivers/list",
    hidden: false,
    component: () => import("@/views/Home/MainTabs/TabCouriers.vue"),
    meta: {
        role: ["admin", "customer", "manager", "dispatcher"],
        type: ["qd", "qd-kuik"],
    },
    children: [
        {
            path: "/drivers/list",
            name: "Drivers",
            hidden: false,
            component: () => import("@/components/couriers/Couriers.vue"),
        },
        {
            path: "/drivers/invitations",
            name: "Invites",
            hidden: false,
            component: () => import("@/components/couriers/Invites.vue"),
        },
        {
            path: "/drivers/day-off-sheet",
            name: "Day Off Sheet",
            hidden: false,
            component: () => import("@/components/couriers/DayOffSheet.vue"),
        }
    ]
}
