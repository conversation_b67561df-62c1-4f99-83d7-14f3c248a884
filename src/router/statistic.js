export default {
  path: "/statistics",
  redirect: "/statistics/courier-task",
  name: "Statistics",
  hidden: true,
  component: () => import("@/components/settings/statistics/index.vue"),
  meta: {
    role: ["admin", "customer"],
    type: ["qd","kuik", "qd-kuik"]
  },
  subMenuTitle: "Statistics",
  children: [
    {
      path: "/statistics/courier-task",
      name: "Performans",
      hidden: false,
      component: () => import("@/components/settings/statistics/CourierAnalizeTest.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Driver"
    },
    {
      path: "/statistics/courier-on-time-report",
      name: "On Time Report",
      hidden: false,
      component: () => import("@/components/settings/statistics/CourierOnTimeReport.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Driver"
    },
    {
      path: "/statistics/courier-taks-distance",
      name: "Task Distance",
      hidden: false,
      component: () => import("@/components/settings/statistics/CourierTaskDistance.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Driver"
    },

    {
      path: "/statistics/courier-performance",
      name: "Tasks",
      hidden: false,
      component: () => import("@/components/settings/statistics/CourierTask.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Driver"
    },
    {
      path: "/statistics/hub-task",
      name: "Hub Task",
      hidden: false,
      component: () => import("@/components/settings/statistics/HubTask.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Hub"
    },
    {
      path: "/statistics/hub-distance-report",
      name: "Hub Distance Report",
      hidden: false,
      component: () => import("@/components/settings/statistics/HubDistanceReport.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Hub"
    },
    {
      path: "/statistics/hub-on-time-report",
        name: "On Time Report",
      hidden: false,
      component: () => import("@/components/settings/statistics/HubOnTimeReport.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Hub"
    },
    {
      path: "/statistics/hub-performance-report",
      name: "Hub Performance Report",
      hidden: false,
      component: () => import("@/components/settings/statistics/HubPerformanceReport.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Hub"
    },
    {
      path: "/statistics/team-on-time-report",
      name: "Team On Time Report",
      hidden: false,
      component: () => import("@/components/settings/statistics/TeamOnTimeReport.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Team"
    },
    {
      path: "/statistics/team-performance",
      name: "Team Performance",
      hidden: false,
      component: () => import("@/components/settings/statistics/TeamPerformance.vue"),
      meta: {
        role: ["admin", "customer"],
        type: ["qd","kuik", "qd-kuik"]
      },
      subMenuTitle: "Team"
    },

  ]
};