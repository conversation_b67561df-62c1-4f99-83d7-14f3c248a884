// import {computed} from "vue";
//
// export default {
//     template: `
//       <span>
//         <span>
//            <FontAwesomeIcon :icon="unlockedAt   ? 'lock-open' : 'lock'" class="text-md" :class="[
//                unlockedAt  ? 'text-green-500':'text-slate-300'
//            ]"/>
//         </span>
//         <span>
//            <FontAwesomeIcon icon="check-square" class="text-md ml-2.5"
//                             :class="[confirmedAt ? 'text-green-500' : 'text-slate-300']"/>
//         </span>
//       </span>
//     `, setup(props) {
//         const unlockedAt = computed(() => props.params.data.unlocked_at)
//         const confirmedAt = computed(() => props.params.data.confirmed_at)
//         return {
//             unlockedAt,
//             confirmedAt,
//         };
//     },
// };
