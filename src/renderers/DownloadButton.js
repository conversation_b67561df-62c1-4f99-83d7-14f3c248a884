// import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
//
// export default {
//     components: {FontAwesomeIcon},
//     template: `
//       <span>
//         <div class="flex justify-around items-center">
//           <div class="cursor-pointer" @click="btnClickedHandler"><FontAwesomeIcon :class="[
//               params.data.url ? '':'opacity-5'
//           ]" icon="download"/></div>
//           <div class="px-2 cursor-pointer" @click="onLogDetail"><FontAwesomeIcon icon="magnifying-glass"/></div>
//           <div class="cursor-pointer" @click="onDelete"><FontAwesomeIcon class="text-red-600" icon="trash"/></div>
//         </div>
//
//       </span>
//     `,
//     methods: {
//         btnClickedHandler() {
//             if (this.params?.data?.url) {
//                 window.open(this.params.data.url, "_blank")
//
//             }
//         },
//         onDelete(){
//             this.params.onDelete(this.params.value)
//         },
//         onLogDetail(){
//             this.params.onLogDetail(this.params.data.meta)
//         }
//
//     },
// };
