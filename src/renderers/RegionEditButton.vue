<script setup>

const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})


const clickedEdit =()=>{
  props.params.clickedEdit(props.params.data)
}

const deleteButton =()=>{
  props.params.clickedDelete(props.params.data)
}
</script>
<template>
 <span class="flex justify-around items-center">
             <FontAwesomeIcon
               @click="clickedEdit"
               icon="fa-pen" class="pr-1.5 pt-1"/>


             <FontAwesomeIcon
               class="text-red-600 pt-1"
               @click="deleteButton"
               icon="fa-trash" />
        </span>
</template>




