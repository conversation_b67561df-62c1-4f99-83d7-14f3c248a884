<template>
  <div>
      <span v-if="props.params.data?.courier" class="flex items-center justify-center">
        {{ props.params.data?.courier?.name || "" }}
      </span>
    <span v-if="props.params.data?.assignment_queue?.courier" class="striped-progress flex items-center justify-center mt-0.5 text-xs font-medium truncate rounded px-3 py-1
     h-5 'px-5 py-2 px-2 py-0.5 bg-amber-50 text-amber-600 border border-amber-300"
    >
      <el-tooltip placement="top" content="Kuyrukta" effect="light">
            {{props.params.data?.assignment_queue?.courier?.name}}
      </el-tooltip>
  </span>


  </div>

</template>

<script setup>
import { defineProps, onMounted } from "vue";

const props = defineProps({
  params: {
    type: [Object, Array],
    default: () => ({})
  }
});

</script>

<style scoped>
@keyframes progressBar {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}
.striped-progress {
  background: linear-gradient(
    135deg,
    #fde047 33%, /* Canlı açık sarı */
    #fce96b 33%, /* Daha pastel bir sarı */
    #ffecb3 34% /* Açık pastel sarı */
  );
  background-size: 400% 100%;
  animation: progressBar 1s linear infinite;
}

</style>