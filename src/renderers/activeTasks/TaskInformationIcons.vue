<script setup>
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const props = defineProps({
  params: {
    type: Object,
    default: () => {
    }
  }
})


const onFilterByMergedTaskId = () => {
  props.params.onFilterByMergedTaskId(props.params.data.merged_task_id)
}
const onClick = () => {
  props.params.openDestination(props.params.data)

}
const originClick = () => {
  props.params.onpenOrigin(props.params.data)
}

</script>

<template>
  <div class="grid grid-cols-6 ">
    <div>
      <el-tooltip v-if="params.data.unlocked_at" placement="top" content="Kilit Açık" effect="light">
        <FontAwesomeIcon
            icon="lock-open"
            class="text-green-500"
        />
      </el-tooltip>
      <el-tooltip v-if="!params.data.unlocked_at" placement="top" content="Kilit Kapalı" effect="light">
        <FontAwesomeIcon
            icon="lock"
            class="text-slate-300"
        />
      </el-tooltip>

    </div>
    <div>
      <el-tooltip v-if="params.data.confirmed_at" placement="top" content="Onaylı" effect="light">
        <FontAwesomeIcon
            icon="check-square"
            class="text-green-500"
        />
      </el-tooltip>
      <el-tooltip v-if="!params.data.confirmed_at" placement="top" content="Onaysız" effect="light">
        <FontAwesomeIcon
            icon="check-square"
            class="text-slate-300"
        />
      </el-tooltip>



    </div>
    <div>

      <div
          v-if="params.data.courier_id"
          class="text-green-500">
        <el-tooltip placement="top" content="Atandı" effect="light">
          <FontAwesomeIcon icon="user"/>
        </el-tooltip>

      </div>
      <div
          v-if="!params.data.courier_id && !params.data.assignment_queue"
          class="text-slate-300">
        <el-tooltip placement="top" content="Atanmadı" effect="light">
          <FontAwesomeIcon icon="user"/>
        </el-tooltip>

      </div>
      <div
        v-if="!params.data.courier_id && params.data.assignment_queue"
        class="text-slate-300">
        <el-tooltip placement="top" content="Kuyrukta" effect="light">
          <FontAwesomeIcon icon="user"/>
        </el-tooltip>

      </div>
      <div v-else-if="params.data.assignment_queue && params.data.assignment_queue.courier">
        <el-popover
            placement="top-start"
            trigger="hover"
            :width="320"
        >
          <template #default>
            <div class="flex flex-col space-y-1 text-slate-700">
              <div>
                <span class="font-bold">{{ $t('Estimated Driver') }}:</span>
                {{ params.data.assignment_queue?.courier?.name }}
              </div>
              <div>
                <span class="font-bold">{{ $t('Estimated Time') }}:</span>
                {{ (params.data.assignment_queue?.arrival_time / 60).toFixed(1) }}
                {{ $t('Min') }}
              </div>
              <div>
                <span class="font-bold">{{ $t('Arrival Distance') }}:</span>
                {{ (params.data.assignment_queue?.distance / 1000).toFixed(2) }} Km
              </div>
            </div>
          </template>
          <template #reference>
            <span :class="[
              params.data.courier_id
              ? 'text-green-500'
              : 'text-slate-300'
            ]">
            <FontAwesomeIcon icon="user"/>
            </span>
          </template>
        </el-popover>
      </div>

    </div>

    <div
        v-if="params.data.merged_task_id"
        @click="onFilterByMergedTaskId"
        class="cursor-pointer"
    >
      <el-tooltip placement="top" content="Birleştirilmiş" effect="light">
        <FontAwesomeIcon
            icon="link"
            class="text-green-500"
        />
      </el-tooltip>

    </div>
    <div
        v-if="!params.data.merged_task_id"

    >
      <el-tooltip placement="top" content="Birleştirilmemiş" effect="light">
        <FontAwesomeIcon
            icon="link"
            class="text-slate-300"
        />
      </el-tooltip>



    </div>
    <div class="ml-0.5">
        <el-tooltip v-if="props.params.data.needs_destination_coordinates" placement="top" content="Varış Noktası Yok" effect="light">
          <FontAwesomeIcon

              @click="onClick"
              icon="fa-triangle-exclamation"
              class="cursor-pointer text-red-600"
          />
        </el-tooltip>
        <el-tooltip v-if="!props.params.data.needs_destination_coordinates" placement="top" content="Varış Noktası Mevcut" effect="light">
          <FontAwesomeIcon

              icon="fa-circle-check"
              class="text-green-500 "
          />
        </el-tooltip>


    </div>
    <div>
      <el-tooltip v-if="props.params.data.needs_origin_coordinates"  placement="top" content="Çıkış Noktası Yok" effect="light">
        <FontAwesomeIcon

            @click="originClick"
            icon="fa-triangle-exclamation"
            class="text-red-600  cursor-pointer"
        />
      </el-tooltip>
      <el-tooltip v-if="!props.params.data.needs_origin_coordinates"  placement="top" content="Çıkış Noktası Mevcut" effect="light">
        <FontAwesomeIcon
            icon="fa-circle-check"
            class="text-green-500 "
        />
      </el-tooltip>
    </div>
  </div>
</template>
