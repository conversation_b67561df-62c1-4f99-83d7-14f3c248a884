<script setup>

import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})
const value = props.params.value
</script>
<template>
  <span v-if="value">
   <span class="text-red-700 mr-3"><PERSON><PERSON><PERSON><PERSON></span><FontAwesomeIcon class="text-red-700" fade icon="circle"></FontAwesomeIcon>
  </span>

</template>

