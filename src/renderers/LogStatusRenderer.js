// import LogStatusTag from "@/components/ui/LogStatusTag.vue";
//
// export default {
//     template: `
//       <LogStatusTag :value="value">
//       {{ code }}
//       </LogStatusTag>
//     `,
//     components: {
//         LogStatusTag,
//     },
//     setup(props) {
//         const code = props.params.value
//         const value = props.params.data.is_successful
//         return {
//             value,
//             code,
//         };
//     },
// };
