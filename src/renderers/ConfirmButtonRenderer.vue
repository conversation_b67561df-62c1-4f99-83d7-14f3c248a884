<script setup>

const props = defineProps({
  params: {
    type: [Object, Array], default: () => {
    }
  }
});
const btnClickedHandler = () => {
  props.params.clicked(props.params.data);
};
</script>
<template>
	<span v-if="props.params.data.type_slug === 'withdrawal'">
            <button @click="btnClickedHandler()"
                    style="background: rgba(255, 255,255, 0.0)">{{ $t("Approve") }}</button>
        </span>
</template>