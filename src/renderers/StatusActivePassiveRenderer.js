// import StatusActivePassiveTag from "@/components/operations/components/StatusActivePassiveTag.vue";
//
// export default {
//     template: `
//       <StatusActivePassiveTag :statusSlug="statusSlug">
//       {{ statusName ? $t('Active') : $t('Passive') }}
//       </StatusActivePassiveTag>
//     `,
//     components: {
//         StatusActivePassiveTag,
//     },
//     setup(props) {
//         const statusSlug =  props.params.data;
//         const statusName = props.params.data.is_active ;
//         return {
//             statusSlug,
//             statusName,
//         };
//     },
// };
