<script setup>
import { computed, inject, onMounted, reactive, ref } from "vue";
import { useToast } from "vue-toastification";

const toast = useToast()

const props = defineProps({
  params: {
    type: [Object, Array], default: () => {
    }
  }
});

const import_id = props.params.data.import_id;
const id = props.params.data.id;

const handleChange = (val) => {
  
};
</script>
<template>
  <div>
    <el-checkbox
      v-model="props.params.data.allow_import"
      size="small"
      @change="handleChange"
    >
    </el-checkbox>
  </div>
</template>