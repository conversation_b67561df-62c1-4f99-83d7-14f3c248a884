<script setup>
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";
import {computed} from "vue";

const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})
const unlockedAt = computed(() => props.params.data.unlocked_at)
const confirmedAt = computed(() => props.params.data.confirmed_at)
</script>
<template>
<span>
        <span>
           <FontAwesomeIcon :icon="unlockedAt   ? 'lock-open' : 'lock'" class="text-md" :class="[
               unlockedAt  ? 'text-green-500':'text-slate-300'
           ]"/>
        </span>
        <span>
           <FontAwesomeIcon icon="check-square" class="text-md ml-2.5"
                            :class="[confirmedAt ? 'text-green-500' : 'text-slate-300']"/>
        </span>
      </span>
</template>
