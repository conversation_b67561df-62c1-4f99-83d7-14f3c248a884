<script setup>
import StatusActivePassiveTag from "@/components/operations/components/StatusActivePassiveTag.vue";


const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})
const statusSlug = props.params.data
const statusName = props.params.data.is_active
</script>
<template>
  <StatusActivePassiveTag :statusSlug="statusSlug">
    {{ statusName ? $t('Active') : $t('Passive') }}
  </StatusActivePassiveTag>
</template>