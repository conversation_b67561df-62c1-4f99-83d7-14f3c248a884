import StatusTag from "@/components/operations/components/StatusTag.vue";

export default {
    template: `
      <StatusTag :statusSlug="statusSlug">
      {{ statusName }}
      </StatusTag>
    `,
    components: {
        StatusTag,
    },
    setup(props) {
        const statusSlug = props.params.value
        const statusName = props.params.data?.status?.name
            ? props.params.data.status.name.charAt(0).toUpperCase() + props.params.data.status.name.slice(1)
            : props.params.data?.status_name && props.params.data.status_name.charAt(0).toUpperCase() + props.params.data.status_name.slice(1);

        return {
            statusSlug,
            statusName,
        };
    },
};
