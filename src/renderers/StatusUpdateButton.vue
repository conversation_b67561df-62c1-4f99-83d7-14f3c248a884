<script setup>

const props = defineProps({
  params: {
    type: [Object, Array], default: () => {
    }
  }
})

const category_slug = props.params.data.status_category_slug
const onComplete = () => {
  props.params.onComplete(props.params.value)
}
const onCancel = () => {
  props.params.onCancel(props.params.data)
}

</script>
<template>
  <div v-if="category_slug === 'failed'" class="flex items-center justify-around">
    <div class="text-lg text-red-600">
      <button @click="onCancel()" style="background: rgba(255, 255,255, 0.0)">
        <FontAwesomeIcon icon="times"/>
      </button>
    </div>
    <div class="text-lg text-lime-600">
      <button @click="onComplete()" style="background: rgba(255, 255,255, 0.0)">
        <FontAwesomeIcon icon="check"/>
      </button>
    </div>
  </div>
</template>
