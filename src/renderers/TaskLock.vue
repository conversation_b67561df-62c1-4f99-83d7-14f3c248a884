<script setup>

const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})
const unlocked_at = props.params.data.unlocked_at
</script>
<template>
	<span>
        <span>
           <FontAwesomeIcon :icon="unlocked_at ? 'lock-open' : 'lock'" class="text-md" :class="[
               unlocked_at  ? 'text-green-500':'text-slate-300'
           ]"/>
        </span>
      </span>
</template>
