export default {
    template: `
      <span v-if="value"
            class="font-bold"
            :class="[
                params.data.approved_at
                 ? type ? 'text-lime-500' : 'text-red-600'
                 :'text-slate-300'
      ]">
           {{ params.data.approved_at ? type ? "+ " : "- " : null }} {{ value }}
        </span>
    `,
    setup(props) {



        const type = props.params.data.is_receiver;

        const formatCurrency = (value, currency = "TRY", locale = "tr-TR") => {
            if (value) {
                return Intl.NumberFormat(locale, {style: "currency", currency}).format(value)
            } else {
                return Intl.NumberFormat(locale, {style: "currency", currency}).format(0)
            }
        }
        const value = props.params.value ? formatCurrency(props.params.value, props.params.data.currency_unit) : null

        return {
            value,
            type
        };
    },
};
