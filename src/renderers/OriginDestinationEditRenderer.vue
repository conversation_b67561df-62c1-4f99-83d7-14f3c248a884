<script setup>
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const props = defineProps({
  params: {
    type: Object,
    default: () => {
    }
  }
})

const onClick = () => {
  props.params.openDestination(props.params.data)

}
const originClick = () => {
  props.params.onpenOrigin(props.params.data)
}
</script>

<template>
  <div>
      <el-tooltip placement="top" content="Varış Noktası Düzenleme" effect="light">
        <FontAwesomeIcon
            v-if="props.params.data.needs_destination_coordinates"
            @click="onClick"
            icon="triangle-exclamation"
            class="cursor-pointer text-red-600"
        />
      </el-tooltip>
    <el-tooltip placement="top" content="Varış Noktası Düzenleme" effect="light">
      <FontAwesomeIcon
          v-if="!props.params.data.needs_destination_coordinates"
          icon="triangle-exclamation"
          class="text-slate-300 cursor-pointer"
      />
    </el-tooltip>
    <el-tooltip   placement="top" content="Çıkış Noktası Düzenleme" effect="light">
      <FontAwesomeIcon
          v-if="props.params.data.needs_origin_coordinates"
          @click="originClick"
          icon="triangle-exclamation"
          class="text-red-600 ml-2 cursor-pointer"
      />
    </el-tooltip>
    <el-tooltip   placement="top" content="Çıkış Noktası Düzenleme" effect="light">
      <FontAwesomeIcon
          v-if="!props.params.data.needs_origin_coordinates"

          icon="triangle-exclamation"
          class="text-slate-300 ml-2 cursor-pointer"
      />
    </el-tooltip>
  </div>



</template>