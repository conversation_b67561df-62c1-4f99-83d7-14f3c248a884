<script setup>
import {FontAwesomeIcon} from "@fortawesome/vue-fontawesome";

const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})

const url = props.params.data.url
const btnClickedHandler =()=>{
  if (props.params?.data?.url) {
    window.open(props.params.data.url, "_blank")

  }
}

const onDelete =()=>{
  props.params.onDelete(props.params.value)
}

const onLogDetail =()=>{
  props.params.onLogDetail(props.params.data.meta)
}
</script>
<template>
	 <span>
        <div class="flex justify-around items-center">
          <div class="cursor-pointer" @click="btnClickedHandler"><FontAwesomeIcon :class="[
              url ? '':'opacity-5'
          ]" icon="download"/></div>
          <div class="px-2 cursor-pointer" @click="onLogDetail"><FontAwesomeIcon icon="magnifying-glass"/></div>
          <div class="cursor-pointer" @click="onDelete"><FontAwesomeIcon class="text-red-600" icon="trash"/></div>
        </div>

      </span>
</template>
