<script setup>
import StatusTag from "@/components/operations/components/StatusTag.vue";


const props = defineProps({
  params: {
    type: [Object,Array], default: () => {
    }
  }
})
const statusSlug = props.params.value
const statusName = props.params.data?.status?.name
    ? props.params.data.status.name.charAt(0).toUpperCase() + props.params.data.status.name.slice(1)
    : props.params.data?.status_name && props.params.data.status_name.charAt(0).toUpperCase() + props.params.data.status_name.slice(1);

</script>
<template>
  <StatusTag :statusSlug="statusSlug">
    {{ statusName }}
  </StatusTag>
</template>