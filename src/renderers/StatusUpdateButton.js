// export default {
//     template: `
//       <div v-if="this.params.data.status_category_slug === 'failed'" class="flex items-center justify-around">
//           <div class="text-lg text-red-600">
//             <button @click="onCancel()" style="background: rgba(255, 255,255, 0.0)">
//               <FontAwesomeIcon icon="times"/>
//             </button>
//           </div>
//           <div class="text-lg text-lime-600">
//             <button @click="onComplete()" style="background: rgba(255, 255,255, 0.0)">
//               <FontAwesomeIcon icon="check"/>
//             </button>
//           </div>
//       </div>
//     `,
//     methods: {
//         onComplete() {
//             this.params.onComplete(this.params.value);
//         },
//         onCancel() {
//             this.params.onCancel(this.params.data);
//         },
//     },
// };
