export default {
    template: `
      <span>
        <div @click="btnClickedHandler" class="cursor-pointer">
          <FontAwesomeIcon icon="times"/>
        </div>
        <!--            <button @click="btnClickedHandler()" style="background: rgba(255, 255,255, 0.0)">Detail</button>-->
        <!--            <button @click="btnClickedHandler()" style="background: rgba(255, 255,255, 0.0)">Detail</button>-->
        <!--            <button @click="btnClickedHandler()" style="background: rgba(255, 255,255, 0.0)">Detail</button>-->
        </span>
    `,
    methods: {
        btnClickedHandler() {
            this.params.clicked(this.params.value);
        },
    },
};
