<script setup>

const props = defineProps({
  params: {
    type: [Object, Array], default: () => {
    }
  }
})

const onComplete = () => {
  props.params.onComplete(props.params.data)
}
const onDelete = () => {
  props.params.onDelete(props.params.data)
}

</script>
<template>
  <div class="flex items-center justify-around">
    <div class="mr-2">
        <FontAwesomeIcon @click="onDelete()" style="color: red" icon="trash"/>
    </div>
    <div >
        <FontAwesomeIcon @click="onComplete()" style="background: rgba(255, 255,255, 0.0)" icon="edit"/>
    </div>
  </div>
</template>
