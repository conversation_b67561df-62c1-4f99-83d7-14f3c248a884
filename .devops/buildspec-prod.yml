version: 0.2

env:
  variables:
    PROJECT: qdelivery-fe
    ENVIRONMENT: prod

phases:
  pre_build:
    commands:
      - ECR_REPOSITORY_URI=$(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$PROJECT
      - ACR_REPOSITORY_URI=qdelivery.azurecr.io/$PROJECT
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - NEW_IMAGE=$ACR_REPOSITORY_URI/$ENVIRONMENT-$IMAGE_TAG-$CODEBUILD_BUILD_NUMBER
      - LATEST_IMAGE=$ACR_REPOSITORY_URI/$ENVIRONMENT-latest
      - echo $CODEBUILD_SOURCE_VERSION
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password | docker login --username AWS --password-stdin $(echo "$ECR_REPOSITORY_URI" | sed 's|\(.*\)/.*|\1|')
      - docker login $(echo "$ACR_REPOSITORY_URI" | sed 's|\(.*\)/.*|\1|') -u qdelivery -p ****************************************************
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg ENVIRONMENT=$ENVIRONMENT -f .devops/Dockerfile -t $NEW_IMAGE -t $LATEST_IMAGE .
      - echo Build completed.
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $NEW_IMAGE
      - docker push $LATEST_IMAGE
      - echo $NEW_IMAGE > imagedefinitions.txt
      - echo Post build completed.
artifacts:
  files:
    - imagedefinitions.txt