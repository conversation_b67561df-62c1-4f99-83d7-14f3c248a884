version: 0.2

env:
  variables:
    REFRESH_TOKEN: gM2hY2yJOz1VIjbkIwrF132oxSQP6KcgtXTqvKivfBojuveZNncYFMCKrXBY0W6P
    CLUSTER_NAME: qdelivery-main-eks-cluster-prod
    PROJECT_NAME: qdelivery-fe
    ENVIRONMENT: prod

phases:
  pre_build:
    commands:
    - echo Preparing cURL...
  build:
    commands:
    - echo Deploying...
    - |
      curl -X POST https://deployer.qdelivery.app/update-deployment \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer DcFhwyFEHYH3.s2evyJcT4Gcp3p*koaZ" \
      -d "{\"name\": \"$PROJECT_NAME-$ENVIRONMENT\", \"namespace\": \"$PROJECT_NAME-$ENVIRONMENT\", \"image_uri\": \"$(cat $CODEBUILD_SRC_DIR_BuildArtifact/imagedefinitions.txt)\"}"
    - echo Pipeline successfully passed!