version: 0.2

env:
  variables:
    REFRESH_TOKEN: gM2hY2yJOz1VIjbkIwrF132oxSQP6KcgtXTqvKivfBojuveZNncYFMCKrXBY0W6P
    CLUSTER_NAME: qdelivery-main-eks-cluster-dev
    PROJECT_NAME: qdelivery-fe
    ENVIRONMENT: sand

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon EKS...
      - aws eks update-kubeconfig --name $CLUSTER_NAME
      - echo Checking tool versions...
      - aws --version
      - kubectl version
      - helm version
  build:
    commands:
      - echo Deploying helm chart...
      - helm upgrade --install --create-namespace $PROJECT_NAME-$EN<PERSON>RONMENT deploy/helm/qdelivery-fe -f deploy/helm/qdelivery-fe/$ENVIRONMENT-values.yaml -n qd-$ENVIRONMENT --set env.configmapEnv=$ENVIRONMENT --set image.uri=$(cat $CODEBUILD_SRC_DIR_BuildArtifact/imagedefinitions.txt)
      - kubectl rollout status deploy/$PROJECT_NAME-$ENVIRONMENT -n qd-$ENVIRONMENT --timeout=300s
      - echo Helm chart successfully deployed!
      - curl -X POST -H "Accept:application/json" -H "Authorization:Bearer $REFRESH_TOKEN" https://api.sandbox.qdelivery.app/api/development/frontend-post-deploy -v
      - echo Pipeline successfully passed!