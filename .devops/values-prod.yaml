# Default values for Magicorn Deployment Chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
#
# Author: <EMAIL>
# v0.3.3

# Determines which templates to use during deployment. Valid values are: aws, datacenter. (for now) 
destination: datacenter

# Anything related to deployment object itself. Must. Fill. This!
deployment:
  image:
    uri: __INSERTURIHERE__
    pullPolicy: Always
    imagePullSecrets: []
    #command: 
    #  - /usr/bin/supervisord
    #args:
    #  - '-c'
    #  - 'supervisor.conf'
  env:
  - name: ENV
    value: prod
  # This field is ignored if autoscaling is enabled.
  replicaCount: 1
  # Resources will also be used in prehooks and cronjobs.
  resources:
    requests:
      cpu: 100m # Means 25% of a single CPU core.
      memory: 128Mi
    limits:
      # We don't use CPU limits anymore. neither should you, unless... you have a nasty greedy application you want to stop devouring all the compute power.
      # Suggested memory limit is the same as the request limit.
      memory: 128Mi
  # Readiness is evaluated first, adjust accordingly.
  readiness:
    httpGet:
      # Port: "one" must be equal to service port "one" below.
      port: one
      path: /login
    failureThreshold: 2
    initialDelaySeconds: 15
    periodSeconds: 15
    successThreshold: 1
    timeoutSeconds: 5
  # Liveness is evaluated later, if deployment is ready. So, deployment time + liveness time + load balancer health checks = deployment starts receiving real traffic.
  liveness:
    httpGet:
      # Port: "one" must be equal to service port "one" below.
      port: one
      path: /login
    failureThreshold: 2
    initialDelaySeconds: 30
    periodSeconds: 15
    successThreshold: 1
    timeoutSeconds: 5
  # If we don't like cluster autoscaler to poke around on several deployments, then we make this false.
  podAnnotations:
    cluster-autoscaler.kubernetes.io/safe-to-evict: 'true'
  # If you want your application to die before it deploys, type: Recreate. Maybe you have a race condition or something, who knows.
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  # We use lifecycle so that there is enough time for leftover HTTP calls to be finalized and drained thoroughly.
  lifecycle:
    preStop:
      exec:
        command: ["/bin/bash", "-c", "sleep 60"]
  deregistrationTime: 60
  failSeconds: 600
  revisionHistory: 25
  nodeSelector: {}
  #  eks.amazonaws.com/capacityType: ON_DEMAND
  tolerations: []
  affinity: {}

# Cluster-wide service object details.
# If you only use your deployment as an application which does not receive traffic inside or outside cluster, you can disable this.
service:
  enabled: true
  annotations: {} # You can add additional annotations if you'd like.
  # If you are deploying a OSI Layer 4 socket application, making this type: LoadBalancer would create an internet-facing Network Load Balancer and traffic is forwarded to outer service of "one". Check templates/service.yaml for details.
  type: ClusterIP
  ports:
    one:
      # Inner port is the port that is listening inside container.
      inner: 8081
      # Outer port is the port that is forwarding the port from to container. like "docker run -p 8080:80" means 8080 -> 80 inside.
      outer: 8081

# External access to the service object above but you can't only receive external traffic, you must also enable your application for internal traffic as well.
# By default, ingress traffic is forwarded to "outer" service port of "one".
ingress:
  enabled: true
  annotations: {} # You can add additional annotations if you'd like.
  className: nginx # Ignored if "destination == aws".
  hosts:
  - host: dashboard.qdelivery.app
    paths:
    - path: /
      pathType: Prefix

# If you enable this, please read about Kubernetes HPA Behaviors. It will change the way you look at scaling inside the cluster.
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 100
  targetCPUUtilizationPercentage: 50
  # targetMemoryUtilizationPercentage: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 120
      selectPolicy: Max
      policies:
      - type: Pods
        value: 4
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      selectPolicy: Min
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60

# Set true here if you have a ConfigMap already deployed into the cluster, must reside inside the same namespace within the deployment though.
configMap:
  enabled: false
  name: "qdelivery-fe-settings-backend-config-"
  fileName: .env.dev
  mountPath: /var/www/html

prehooks:
  # Set true here, if your application has database migrations and you want them to apply before deployment.
  dbMigrations:
    enabled: false
    command:
    - /usr/local/bin/php
    args:
    - artisan
    - migrate
    - '--force'

  # Set true here, if you have any other prerequisite rather than or in addition to database migrations. If both enabled, database migrations will run first.
  otherPrehooks:
    enabled: false
    command:
    - /usr/local/bin/python
    args:
    - manage.py
    - some_action_here

# Enable crons of the deployment. You can use as many crons as you want.
cronjobs:
- name: schedule
  enabled: false
  schedule: "* * * * *"
  command: ["/usr/local/bin/php"]
  args:
  - "artisan"
  - "schedule:run"
  failedJobsHistoryLimit: 3
  successfulJobsHistoryLimit: 5
  concurrencyPolicy: Forbid
  restartPolicy: OnFailure
- name: cron2
  enabled: false
  schedule: "0 5 * * *"
  command: ["/usr/local/bin/php"]
  args:
  - "artisan"
  - "schedule:run"
  failedJobsHistoryLimit: 3
  successfulJobsHistoryLimit: 5
  concurrencyPolicy: Forbid
  restartPolicy: OnFailure

security:
  # Set true, if you'd need Kubernetes API permissions in your application.
  # Basically, this is going to manage RBAC access of your deployment.
  serviceAccount:
    enabled: false
    annotations: {}
    # Cluster-wide or only within namespace.
    clusterWideAccess: false
    # Resource access rules, set as many as you would need.
    rules:
    - resources: ["configmaps", "events", "pods", "pods/attach", "pods/exec", "secrets", "services"]
      verbs: ["get", "list", "watch", "create", "patch", "update", "delete"]
    - apiGroups: [""]
      resources: ["pods/exec"]
      verbs: ["create", "patch", "delete"]

  # Set true, if you'd like to utilize AWS Security Groups to gain granular network access control to your deployment.
  # Security group must be managed outside Helm/Terraform.
  sgPolicy:
    enabled: false
    securityGroupId: sg-0123456789abcdef

  podSecurityContext: {}
  #  fsGroup: 2000
  securityContext: {}
  #  capabilities:
  #    drop:
  #    - ALL
  #  readOnlyRootFilesystem: true
  #  runAsNonRoot: true
  #  runAsUser: 1000
